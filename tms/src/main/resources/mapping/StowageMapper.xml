<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dlcg.tms.mapper.StowageMapper">
<select id="CheckGoodsDetail" resultType="com.dlcg.tms.bean.CheckGoodsDeatilBean">
    SELECT swharf.wharf as qi, ewharf.wharf as zhong,cgc.tonnage as dun,costname.`value`as costname,
           cgcd.company_name as gongying,cgcd.invoice_type as tax
    FROM customer_goods_cost as cgc
             LEFT JOIN customer_goods_cost_detail as cgcd on cgcd.customer_goods_cost_id = cgc.id and cgcd.income_out = 0
             LEFT JOIN sys_wharf as swharf on swharf.id = cgc.load_wharf
             LEFT JOIN sys_wharf as ewharf on ewharf.id = cgc.final_wharf
             LEFT JOIN sys_dictionary as costname on costname.code = cgcd.cost_name and costname.dictionary_key = "cost_type"
    WHERE cgc.shipline_id = #{id}
         and cgcd.id is not null
      and (cgcd.company_name is null or cgcd.invoice_type is null or cgcd.company_name = "" or cgcd.invoice_type = "")

</select>
    <select id="CheckShiplineCost" resultType="java.lang.String">
        SELECT sd.`value`
        FROM ship_line_ship_cost as slsc
                 LEFT JOIN sys_dictionary as sd on sd.code = slsc.cost_project and sd.dictionary_key = "items_occurred"
        WHERE slsc.ship_line_id = #{id}
            and  (slsc.sys_supplier_id is null or slsc.tax is null or slsc.sys_supplier_id = "" or slsc.tax = "")
          and slsc.is_income = 0
          and slsc.cost_bear = "20"
          and slsc.shenpi != 1
    </select>

    <select id="getCostDetailContractByShipLineId" resultType="com.dlcg.tms.entity.CustomerGoodsCostVo">
         SELECT
            cgc.id as customerGoodsCostId,
            s.`name` as shipName,
            s.id as shipId,
            sl.eparture_time_date as departureTimeDate,
            sl.ship_time,
            sl.voyage_number,
            sl.on_account_num,
            sco. `name` as oneName,
            sco.`id` as oneId,
            scs. `abbreviation_name` as twoName,
            scs.`id` as twoId,
            cgc.tonnage,
            cgc.freight_no,
            cgc.tax_freight,
            sdfre.`value` as taxFreightStr,
            cgc.daili_no,
            cgc.tax_daili,
            sddai.`value` as taxDailiStr,
            cgc.goods_cost_contract_id as goodsCostContractId,
            cgcc.url as goodsCostContractUrls,
            cgcc.url_name as goodsCostContractUrlNames,
            cgcc.signed_url as goodsCostContractSignedUrls,
            cgcc.signed_url_name as goodsCostContractSignedUrlNames,
            cgs.danzheng_user as danZhengUserId,
            su.`name` as danZhengUserName,
            cgcc.contract_name as contractName,
            cgcc.contract_type as contractType,
            sd.`value` as contractTypeName,
            cgcc.model_type as contractModelType,
            sdm.`value` as contractModelTypeName,
            cgcc.contract_remark as contractRemark,
            cgcc.sp_status as contractSpStatus,
            ifnull(sdsp.`value`,'未审批') as contractSpStatusName,
            DATE_FORMAT(cgcc.expired_time,'%Y-%m-%d') as contractExpiredTime,
            cgc.sup_goods_cost_contract_id as supGoodsCostContractId,
            cgc.section_goods_cost_contract_id as sectionGoodsCostContractId
        FROM
            customer_goods_cost cgc
            LEFT JOIN ship_line sl ON sl.id = cgc.shipline_id
            LEFT JOIN ship s ON sl.ship_id = s.id
            LEFT JOIN customer_goods_source cgs ON cgs.id = cgc.customer_goods_source_id
            LEFT JOIN sys_customer_onelevel sco ON sco.id = cgs.customer_onelevel_id
            LEFT JOIN sys_customer_secondlevel scs ON scs.id = cgs.customer_secondlevel_id
            left join sys_dictionary sddai on sddai.code=cgc.tax_daili and sddai.dictionary_key='bill_tax'
            left join sys_dictionary sdfre on sdfre.code=cgc.tax_freight and sdfre.dictionary_key='bill_tax'
            left join customer_goods_cost_contract cgcc on cgcc.id=cgc.goods_cost_contract_id
            left join sys_dictionary sd on sd.spare2 = cgcc.contract_type and sd.dictionary_key = 'cost_contract_type'
            left join sys_dictionary sdm on sdm.spare2 = cgcc.model_type and sdm.dictionary_key = 'cost_contract_model_type'
            left join sys_dictionary sdsp on sdsp.code = cgcc.sp_status and sdsp.dictionary_key = 'cost_contract_sp_status'
            left join sys_user su on su.id=cgs.danzheng_user

        where
            cgc.del_flag = '0'
            and sl.id = #{id}
            and if(ifnull(cgcc.sp_status,0) != 2 and ifnull(cgcc.sp_status,0) != 5,1,0)
         ORDER BY
            cgc.create_date DESC
    </select>
    <select id="isNoApprCostDetailContractByShipLineId" resultType="java.lang.Boolean" >
        select count(cgc.id)
        FROM
            customer_goods_cost cgc
            LEFT JOIN ship_line sl ON sl.id = cgc.shipline_id
            left join customer_goods_cost_contract cgcc on cgcc.id=cgc.goods_cost_contract_id
        where cgc.del_flag = '0'
            and sl.id = #{id}
            and if(ifnull(cgcc.sp_status,0) != 2 and ifnull(cgcc.sp_status,0) != 5,1,0)

    </select>
</mapper>
