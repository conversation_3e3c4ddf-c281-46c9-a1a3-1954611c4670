<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dlcg.tms.mapper.CarLineMapper">

    <resultMap id="BaseResultMap" type="com.dlcg.tms.entity.CarLine">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="onAccountNum" column="on_account_num" jdbcType="VARCHAR"/>
            <result property="shipId" column="ship_id" jdbcType="VARCHAR"/>
            <result property="shipIntoTime" column="ship_into_time" jdbcType="DATE"/>
            <result property="shipOutTime" column="ship_out_time" jdbcType="DATE"/>
            <result property="contractPrice" column="contract_price" jdbcType="DOUBLE"/>
            <result property="contractMoney" column="contract_money" jdbcType="DOUBLE"/>
            <result property="contractTunnage" column="contract_tunnage" jdbcType="DOUBLE"/>
            <result property="businessType" column="business_type" jdbcType="INTEGER"/>
            <result property="yewuUser" column="yewu_user" jdbcType="VARCHAR"/>
            <result property="danzhengUser" column="danzheng_user" jdbcType="VARCHAR"/>
            <result property="tijaocaiwu" column="tijaocaiwu" jdbcType="INTEGER"/>
            <result property="tijiao" column="tijiao" jdbcType="INTEGER"/>
            <result property="reason" column="reason" jdbcType="VARCHAR"/>
            <result property="gaiIsApply" column="gai_is_apply" jdbcType="INTEGER"/>
            <result property="feiyongCommiter" column="feiyong_commiter" jdbcType="VARCHAR"/>
            <result property="guaTime" column="gua_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="auditStatus" column="audit_status" jdbcType="INTEGER"/>
            <result property="auditRemarks" column="audit_remarks" jdbcType="VARCHAR"/>
            <result property="voyageNumber" column="voyage_number" jdbcType="VARCHAR"/>
            <result property="tonnage" column="tonnage" jdbcType="DOUBLE"/>
            <result property="dingjin" column="dingjin" jdbcType="DOUBLE"/>
            <result property="contractCode" column="contract_code" jdbcType="INTEGER"/>
            <result property="costIsCompleteDate" column="cost_is_complete_date" jdbcType="TIMESTAMP"/>
            <result property="payBill" column="pay_bill" jdbcType="INTEGER"/>
            <result property="onAccountDiff" column="on_account_diff" jdbcType="INTEGER"/>
            <result property="queren" column="queren" jdbcType="INTEGER"/>
            <result property="costIsComplete" column="cost_is_complete" jdbcType="INTEGER"/>
            <result property="goodsCostContractId" column="goods_cost_contract_id" jdbcType="INTEGER"/>
            <result property="customerOnelevelId" column="customer_onelevel_id" jdbcType="VARCHAR"/>
            <result property="customerOnelevelName" column="customer_onelevel_name" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="goodsType" column="goods_type" jdbcType="INTEGER"/>
            <result property="customerType" column="customer_type" jdbcType="INTEGER"/>
            <result property="goodsOwnerId" column="goods_owner_id" jdbcType="VARCHAR"/>
            <result property="goodsOwnerName" column="goods_owner_name" jdbcType="VARCHAR"/>
            <result property="loadAddress" column="load_address" jdbcType="VARCHAR"/>
            <result property="unloadAddress" column="unload_address" jdbcType="VARCHAR"/>
            <result property="wuliuYewuUser" column="wuliu_yewu_user" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="productNameName" column="product_name_name" jdbcType="VARCHAR"/>

            <result property="twoCustomerId" column="two_customer_id" jdbcType="VARCHAR"/>
            <result property="twoCustomerName" column="two_customer_name" jdbcType="VARCHAR"/>
            <result property="twoContractPrice" column="two_contract_price" jdbcType="DOUBLE"/>
            <result property="twoContractMoney" column="two_contract_money" jdbcType="DOUBLE"/>
            <result property="contractNoBillMoney" column="contract_no_bill_money" jdbcType="DOUBLE"/>
            <result property="twoPayBill" column="two_pay_bill" jdbcType="INTEGER"/>
            <result property="wanglianBindStatus" column="wanglian_bind_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,on_account_num,ship_id,queren,load_address,unload_address,goods_cost_contract_id,contract_no_bill_money,
        ship_into_time,ship_out_time,contract_price,two_customer_id,two_customer_name,two_contract_price,
        two_contract_money,two_pay_bill,contract_money,contract_tunnage,business_type,customer_type
        yewu_user,danzheng_user,tijaocaiwu,product_name,product_name_name,
        tijiao,reason,gai_is_apply,
        feiyong_commiter,gua_time,create_by,
        create_date,update_by,update_date,
        del_flag,audit_status,audit_remarks,
        voyage_number,tonnage,dingjin,
        contract_code,cost_is_complete_date,pay_bill,
        on_account_diff,cost_is_complete,customer_onelevel_id,
        customer_onelevel_name,customer_id,customer_name,
        goods_type,goods_owner_id,goods_owner_name,wanglian_bind_status
    </sql>
    <select id="getPageDate" resultType="com.dlcg.tms.entity.CarLine">
        select cl.*,ship.name as ship_name,ysu.name as yewu_user_name,dsu.name as danzheng_user_name,pi.global_param2 global_param2_pi
        FROM car_line cl
            LEFT JOIN ship ship on ship.id = cl.ship_id
            left join sys_user  ysu  on ysu.id = cl.yewu_user
            left join sys_user  dsu  on dsu.id = cl.danzheng_user
            left join process_instance  pi  on pi.global_param1 = cl.id and pi.flow_code = 'carOnAccount' and pi.`status` = 2
        where cl.audit_status = #{status} and cl.del_flag = '0'
              <if test="ship_name != null and ship_name != ''">
                  and ship.name like CONCAT('%',#{ship_name},'%')
              </if>
              <if test="shipJinGangTime != null ">
                  and cl.ship_into_time = #{shipJinGangTime}
              </if>
              <if test="danzhengUser != null and danzhengUser != ''">
                  and dsu.name like CONCAT('%',#{danzhengUser},'%')
              </if>
        order by cl.create_date desc
    </select>
    <select id="carStatistics" resultType="com.dlcg.tms.bean.CarStatisticsBean">
        select clf.goods_owner_id,
               clf.goods_owner_name as kehu,
               SUM(clf.tunnage) as yunliang,
               SUM(clf.income_money) yingyeshouru,
               SUM(clf.income_money)/SUM(clf.tunnage) danjia,
               SUM(clf.money)/SUM(clf.tunnage)  yunshudanjia,
               SUM(clf.money) yunshuchengben,
               SUM((select sum(money) from car_line_flow_cost where car_line_flow_id = clf.id)) tihuofei
        from car_line_flow clf
            left join car_line  cl on cl.id = clf.car_line_id
        where clf.del_flag = '0'
            <if test="startDate != null and startDate != ''">
                and cl.ship_into_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                and  #{endDate} >= cl.ship_into_time
            </if>
            <if test="supplierCustomerId != null and supplierCustomerId != ''">
                and  cl.customer_id = #{supplierCustomerId}
            </if>
        GROUP BY clf.goods_owner_id
    </select>
</mapper>
