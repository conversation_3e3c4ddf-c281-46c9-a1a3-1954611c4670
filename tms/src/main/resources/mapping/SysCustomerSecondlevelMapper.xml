<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dlcg.tms.mapper.SysCustomerSecondlevelMapper">

    <select id="getSecondList" resultType="com.dlcg.tms.entity.SysCustomerSecondlevel">
        SELECT
            scs.*
        FROM
            sys_customer_secondlevel scs
            LEFT JOIN sys_customer_one_second scos ON scs.id = scos.second_level_id
            WHERE scos.one_level_id = #{oneId}
    </select>

    <select id="getNoOneSecondList" resultType="com.dlcg.tms.entity.SysCustomerSecondlevel">
        SELECT * FROM sys_customer_secondlevel WHERE id NOT IN (SELECT second_level_id FROM sys_customer_one_second)
    </select>

    <select id="getReceiveList" resultType="com.dlcg.tms.entity.SysCustomerSecondlevel">
        SELECT
            scs.*
        FROM
            sys_customer_secondlevel scs
            LEFT JOIN sys_customer_second_receive scsr ON scs.id = scsr.receive_id
        WHERE
            scsr.second_level_id = #{secondId}
    </select>
</mapper>
