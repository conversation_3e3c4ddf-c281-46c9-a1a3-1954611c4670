<?xml version="1.0" encoding="UTF-8" ?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
    <contextName>dlcg-logistics</contextName>
    <property name="log.dir" value="./" />
    <property name="file.name" value="dlcg-logistics" />

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </layout>
    </appender>

    <!-- INFO级别日志 appender -->
    <appender name="INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天回滚 daily -->
            <fileNamePattern>${log.dir}/logs/%d{yyyy-MM-dd}/${file.name}.log</fileNamePattern>
            <!-- 日志最大的历史 60天 -->
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{35} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- MyBatis show sql -->
    <logger name="com.ibatis" level="ERROR"/>
    <logger name="com.ibatis.common.jdbc.SimpleDataSource" level="ERROR"/>
    <logger name="com.ibatis.common.jdbc.ScriptRunner" level="ERROR"/>
    <logger name="com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate" level="ERROR"/>
    <logger name="java.sql.Connection" level="ERROR"/>
    <logger name="java.sql.Statement" level="ERROR"/>
    <logger name="java.sql.PreparedStatement" level="ERROR"/>
    <!--这里指定logger name 是为jmx设置日志级别做铺垫 -->
    <logger name="org.springframework">
        <level value="ERROR"/>
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO"/>
    </logger>



    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO"/>
    </root>

</configuration>
