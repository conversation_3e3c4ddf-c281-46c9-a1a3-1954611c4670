{"modeName": "船期", "entityName": "ShipLine", "entityName-Notes": "实体类名称", "attributes": [{"name": "船型", "field": "shipType", "type": "string", "addisrequired": true}, {"name": "船期", "field": "shipTime", "type": "datetime", "datefmt": "yyyy/MM/dd", "addisrequired": false}, {"name": "船名", "field": "shipName", "type": "string", "addisrequired": true}, {"name": "指导价格", "field": "freightRate", "type": "int", "addisrequired": false}, {"name": "航线", "field": "sysShiplineId", "type": "string", "addisrequired": true}, {"name": "租船员", "field": "findShipUser", "type": "string", "addisrequired": true}], "queryattributes": ["shipTime"], "queryattributes-Notes": "query中查询需要配置的信息", "showattributes": ["shipType", "shipTime", "shipName", "freightRate", "sysShiplineId", "findShipUser"], "showattributes-Notes": "table表格中需要展示的字段与汉字名称，", "addleftattributes": ["shipType", "shipTime", "shipName"], "addrightattributes": ["freightRate", "sysShiplineId", "findShipUser"], "addtributes-Notes": "新增数据时展示的字段，分为左右两侧", "havePermission": false}