{"modeName": "公司账户", "entityName": "SysCompanyAccount", "entityName-Notes": "实体类名称", "havePermission": false, "queryattributes": ["fullName", "taxpayerIdentificationNumber", "registeredAddress", "registrationNumber"], "queryattributes-Notes": "query中查询需要配置的信息", "showattributes": ["fullName", "abbreviationName", "code", "taxpayerIdentificationNumber", "registeredAddress", "registrationNumber"], "showattributes-Notes": "table表格中需要展示的字段与汉字名称，", "addleftattributes": ["fullName", "abbreviationName", "code"], "addrightattributes": ["taxpayerIdentificationNumber", "registeredAddress", "registrationNumber"], "addtributes-Notes": "新增数据时展示的字段，分为左右两侧", "attributes": [{"name": "公司全称", "field": "fullName", "type": "string", "addisrequired": true}, {"name": "公司简称", "field": "abbreviationName", "type": "string", "addisrequired": true}, {"name": "代码", "field": "code", "type": "string", "addisrequired": false}, {"name": "纳税人识别码", "field": "taxpayerIdentificationNumber", "type": "string", "addisrequired": false}, {"name": "注册地址", "field": "registeredAddress", "type": "string", "addisrequired": false}, {"name": "注册电话", "field": "registrationNumber", "type": "string", "addisrequired": false}]}