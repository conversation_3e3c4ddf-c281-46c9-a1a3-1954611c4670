{"modeName": "运费支出", "entityName": "ShipLineTransPay", "entityName-Notes": "实体类名称", "attributes": [{"name": "船价", "field": "shipPay", "type": "double", "addisrequired": true}, {"name": "是否开票", "field": "isBill", "type": "int", "addisrequired": false}, {"name": "合同公司", "field": "company", "type": "string", "addisrequired": true}, {"name": "进票价格", "field": "ticketPrice", "type": "double", "addisrequired": false}, {"name": "进项发票", "field": "inputBill", "type": "string", "datefmt": "yyyy-MM-dd HH:mm:ss", "addisrequired": false}, {"name": "虚拟成本", "field": "virtualCost", "type": "double", "addisrequired": false}, {"name": "备注", "field": "remark", "type": "string", "addisrequired": false}], "queryattributes": ["company", "shipPay"], "queryattributes-Notes": "query中查询需要配置的信息", "showattributes": ["shipPay", "isBill", "company", "ticketPrice", "inputBill", "virtualCost", "remark"], "showattributes-Notes": "table表格中需要展示的字段与汉字名称，", "addleftattributes": ["shipPay", "isBill", "inputBill", "remark"], "addrightattributes": ["company", "ticketPrice", "virtualCost"], "addtributes-Notes": "新增数据时展示的字段，分为左右两侧", "havePermission": false}