<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generate.mapper.CarLineFlowMapper">

    <resultMap id="BaseResultMap" type="generate.domain.CarLineFlow">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="carLineId" column="car_line_id" jdbcType="VARCHAR"/>
            <result property="customerOnelevelId" column="customer_onelevel_id" jdbcType="VARCHAR"/>
            <result property="customerOnelevelName" column="customer_onelevel_name" jdbcType="VARCHAR"/>
            <result property="goodsOwnerId" column="goods_owner_id" jdbcType="VARCHAR"/>
            <result property="goodsOwnerName" column="goods_owner_name" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="goodsType" column="goods_type" jdbcType="INTEGER"/>
            <result property="loadAddress" column="load_address" jdbcType="VARCHAR"/>
            <result property="unloadAddress" column="unload_address" jdbcType="VARCHAR"/>
            <result property="price" column="price" jdbcType="DOUBLE"/>
            <result property="tunnage" column="tunnage" jdbcType="DOUBLE"/>
            <result property="money" column="money" jdbcType="DOUBLE"/>
            <result property="payBill" column="pay_bill" jdbcType="INTEGER"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="shoukuanHexiaoStatus" column="shoukuan_hexiao_status" jdbcType="INTEGER"/>
            <result property="yewuUser" column="yewu_user" jdbcType="VARCHAR"/>
            <result property="loadDate" column="load_date" jdbcType="DATE"/>
            <result property="unloadDate" column="unload_date" jdbcType="DATE"/>
            <result property="carCompanyId" column="car_company_id" jdbcType="VARCHAR"/>
            <result property="carCompanyName" column="car_company_name" jdbcType="VARCHAR"/>
            <result property="dingjin" column="dingjin" jdbcType="DOUBLE"/>
            <result property="dingjinIsPay" column="dingjin_is_pay" jdbcType="INTEGER"/>
            <result property="dingjinApply" column="dingjin_apply" jdbcType="INTEGER"/>
            <result property="contractCode" column="contract_code" jdbcType="INTEGER"/>
            <result property="queren" column="queren" jdbcType="INTEGER"/>
            <result property="costIsCompleteDate" column="cost_is_complete_date" jdbcType="TIMESTAMP"/>
            <result property="costIsComplete" column="cost_is_complete" jdbcType="INTEGER"/>
            <result property="goodsCostContractId" column="goods_cost_contract_id" jdbcType="INTEGER"/>
            <result property="loadDay" column="load_day" jdbcType="INTEGER"/>
            <result property="contractSpStatus" column="contract_sp_status" jdbcType="TINYINT"/>
            <result property="danzhengUser" column="danzheng_user" jdbcType="VARCHAR"/>
            <result property="carNumber" column="car_number" jdbcType="VARCHAR"/>
            <result property="carNo" column="car_no" jdbcType="VARCHAR"/>
            <result property="incomePrice" column="income_price" jdbcType="DOUBLE"/>
            <result property="incomeNoBillPrice" column="income_no_bill_price" jdbcType="DOUBLE"/>
            <result property="incomeBill" column="income_bill" jdbcType="DOUBLE"/>
            <result property="incomeMoney" column="income_money" jdbcType="DOUBLE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,car_line_id,customer_onelevel_id,
        customer_onelevel_name,goods_owner_id,goods_owner_name,
        customer_id,customer_name,goods_type,
        load_address,unload_address,price,
        tunnage,money,pay_bill,
        create_by,create_date,update_by,
        update_date,del_flag,shoukuan_hexiao_status,
        yewu_user,load_date,unload_date,
        car_company_id,car_company_name,dingjin,
        dingjin_is_pay,dingjin_apply,contract_code,
        queren,cost_is_complete_date,cost_is_complete,
        goods_cost_contract_id,load_day,contract_sp_status,
        danzheng_user,car_nunber,car_no,
        income_price,income_no_bill_price,income_bill,
        income_money
    </sql>
</mapper>
