<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="generate.mapper.CarLineFlowCostMapper">

    <resultMap id="BaseResultMap" type="generate.domain.CarLineFlowCost">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="carLineId" column="car_line_id" jdbcType="VARCHAR"/>
            <result property="carLineFlowId" column="car_line_flow_id" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="INTEGER"/>
            <result property="typeName" column="type_name" jdbcType="VARCHAR"/>
            <result property="tax" column="tax" jdbcType="DOUBLE"/>
            <result property="money" column="money" jdbcType="DOUBLE"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,car_line_id,car_line_flow_id,
        type,type_name,tax,
        money,create_by,create_date,
        update_by,update_date,del_flag
    </sql>
</mapper>
