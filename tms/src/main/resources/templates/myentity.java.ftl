package com.dlcg.tms.controller;


import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dlcg.oa.bean.AdminUser;
import com.dlcg.tms.entity.${entityName};
import com.dlcg.oa.interceptor.UserMenuPermission;
import com.dlcg.tms.service.I${entityName}Service;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.zthzinfo.common.ResponseMapBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Map;

/**
* <p>
    *  前端控制器
    * </p>
*
* <AUTHOR>
* @since 2020-04-26
*/
@RestController
@RequestMapping("/${controllerRootPath}")
public class ${entityName}Controller {

    @Autowired
    I${entityName}Service i${entityName}Service;


    @RequestMapping("/get${entityName}List")
<#if havePermission>
    @UserMenuPermission({${javaPermission}})
</#if>
    public Map<String , Object> get${entityName}List(
<#list queryattributes as item>
        <#if attributes[item].type == 'int'>
            @RequestParam(value = "${attributes[item].field}",required = false) Integer ${attributes[item].field},
        <#elseif attributes[item].type == 'string'>
            @RequestParam(value = "${attributes[item].field}",required = false) String ${attributes[item].field},
        <#elseif attributes[item].type == 'dictionary'>
            @RequestParam(value = "${attributes[item].field}",required = false) Integer ${attributes[item].field},
        <#elseif attributes[item].type == 'datetime'>
            @RequestParam(value = "start${attributes[item].field}",required = false) Date start${attributes[item].field},
            @RequestParam(value = "end${attributes[item].field}",required = false) Date end${attributes[item].field},
        <#else>
        </#if>
</#list>
        @RequestParam(value = "pageNo",required = false) Integer pageNo,
        @RequestParam(value = "pageSize",required = false) Integer pageSize
    ) {
        if(pageNo==null||pageNo<1){
            pageNo = 1;
        }
        if(pageSize==null||pageSize<0){
            pageSize = 10;
        }
        QueryWrapper<${entityName}> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag","0");


<#list queryattributes as item>
    <#if attributes[item].type == 'string'>
        if(${attributes[item].field}!=null&&${attributes[item].field}.trim().length()>0){
            queryWrapper.like("${attributes[item].sqlfield}",${attributes[item].field});
        }
    <#elseif attributes[item].type == 'int'>
        if(${attributes[item].field}!=null){
            queryWrapper.eq("${attributes[item].sqlfield}",${attributes[item].field});
        }
    <#elseif attributes[item].type == 'dictionary'>
        if(${attributes[item].field}!=null){
            queryWrapper.eq("${attributes[item].sqlfield}",${attributes[item].field});
        }
    <#elseif attributes[item].type == 'datetime'>
        if(start${attributes[item].field}!=null){
            queryWrapper.ge("${attributes[item].sqlfield}",start${attributes[item].field});
        }
        if(end${attributes[item].field}!=null){
            queryWrapper.le("${attributes[item].sqlfield}",end${attributes[item].field});
        }
    <#else>
    </#if>
</#list>
        queryWrapper.orderByDesc("create_date");
        IPage<${entityName}> page = i${entityName}Service.page(new Page<>(pageNo,pageSize),queryWrapper);
        return ResponseMapBuilder.newBuilder()
        .put("page",page)
        .putSuccess()
        .getResult();
    }
    @RequestMapping("/update${entityName}")
    <#if havePermission>
    @UserMenuPermission({${javaPermission}})
    </#if>
    public Map<String , Object> update${entityName}(
        @ModelAttribute ${entityName} ${controllerRootPath}
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        ${controllerRootPath}.setUpdateBy(adminUser.getId());
        ${controllerRootPath}.setUpdateDate(new Date());
        i${entityName}Service.updateById(${controllerRootPath});
        return ResponseMapBuilder.newBuilder()
        .putSuccess()
        .getResult();
    }

    @RequestMapping("/del${entityName}")
    <#if havePermission>
    @UserMenuPermission({${javaPermission}})
    </#if>
    public Map<String , Object> del${entityName}(
        @RequestParam(value = "id",required = false) String id
    ) {

        i${entityName}Service.removeById(id);
        return ResponseMapBuilder.newBuilder()
        .putSuccess()
        .getResult();
    }

    @RequestMapping("/save${entityName}")
    <#if havePermission>
    @UserMenuPermission({${javaPermission}})
    </#if>
    public Map<String , Object> save${entityName}(
        @ModelAttribute ${entityName} ${controllerRootPath}
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        ${controllerRootPath}.setId(IdUtil.simpleUUID());
        ${controllerRootPath}.setCreateBy(adminUser.getId());
        ${controllerRootPath}.setCreateDate(new Date());
        i${entityName}Service.save(${controllerRootPath});
        return ResponseMapBuilder.newBuilder()
        .putSuccess()
        .getResult();
    }
}