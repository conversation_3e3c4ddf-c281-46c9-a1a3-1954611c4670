package com.dlcg.tms.dbchange.listener;

import com.alibaba.fastjson.JSONObject;
import com.dlcg.tms.bean.ChangesBean;
import com.dlcg.tms.bean.MongoUpdateStowageBean;
import com.dlcg.tms.bean.ReceordsBean;
import com.dlcg.tms.dbchange.CostAggregationEvent;
import com.dlcg.tms.entity.CostAggregation;
import com.dlcg.tms.entity.ProcessInstance;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/*
 * 改账记录保存
 * */
@Component
public class CostAggregationListener extends DBChangeBaseListenerSync<CostAggregationEvent> {

    @Autowired
    MongoTemplate mongoTemplate;

    @Override
    public void onInsert(JSONObject data) {

    }

    @Override
    public void onDelete(JSONObject data) {

    }

    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        CostAggregation costAggregation = data.toJavaObject(CostAggregation.class);
        String updateby = costAggregation.getUpdateBy();
        int i =0;
        if (updateby == null){
            return;
        }
        JSONObject goodstr = fromJson(updateby);
        if (goodstr != null){
            ProcessInstance processInstance = goodstr.toJavaObject(ProcessInstance.class);
            Criteria criteria = Criteria.where("ship_line_id").is(costAggregation.getShipLineId());
            Query query = new Query(criteria);
            MongoUpdateStowageBean mongoUpdateStowageBean = mongoTemplate.findOne(query,MongoUpdateStowageBean.class,"update_stowage");
            if (mongoUpdateStowageBean != null) {
                List<ReceordsBean> receordsBeans = mongoUpdateStowageBean.getReceords();
                if (receordsBeans != null){
                    for (ReceordsBean receordsBean :receordsBeans){
                        if (receordsBean.getCostPayId().equals(costAggregation.getCostPayId()) &&
                                receordsBean.getCostTypeName().equals(costAggregation.getCostTypeName())){
                            ChangesBean changesBean = new ChangesBean();
                            changesBean.setOldValue(old);
                            CostAggregation costAggregationnew = new CostAggregation();
                            costAggregationnew.setIsCus(costAggregation.getIsCus());
                            costAggregationnew.setCusSupId(costAggregation.getCusSupId());
                            costAggregationnew.setCostTypeName(costAggregation.getCostTypeName());
                            costAggregationnew.setPriceWithTax(costAggregation.getPriceWithTax());
                            costAggregationnew.setPriceWithoutTax(costAggregation.getPriceWithoutTax());
                            costAggregationnew.setBillTax(costAggregation.getBillTax());
                            changesBean.setNewValue(costAggregationnew);
                            changesBean.setRequester(processInstance.getSponsorId());
                            changesBean.setRequestTime(processInstance.getSponsorTime());
                            changesBean.setPassTime(processInstance.getFinishTime());
                            changesBean.setReason(processInstance.getGlobalParam3());
                            List<ChangesBean> changesBeans = receordsBean.getChangesBeanList();
                            changesBeans.add(changesBean);
                            receordsBean.setChangesBeanList(changesBeans);
                            i = 1;
                        }
                    }
                }
                if (i == 0){
                    ReceordsBean receordsBean = new ReceordsBean();
                    receordsBean.setCostAggregationId(costAggregation.getId());
                    receordsBean.setCostPayId(costAggregation.getCostPayId());
                    receordsBean.setCostTypeName(costAggregation.getCostTypeName());
                    ChangesBean changesBean = new ChangesBean();
                    changesBean.setOldValue(old);
                    CostAggregation costAggregationnew = new CostAggregation();
                    costAggregationnew.setIsCus(costAggregation.getIsCus());
                    costAggregationnew.setCusSupId(costAggregation.getCusSupId());
                    costAggregationnew.setCostTypeName(costAggregation.getCostTypeName());
                    costAggregationnew.setPriceWithTax(costAggregation.getPriceWithTax());
                    costAggregationnew.setPriceWithoutTax(costAggregation.getPriceWithoutTax());
                    costAggregationnew.setBillTax(costAggregation.getBillTax());
                    changesBean.setNewValue(costAggregationnew);
                    changesBean.setRequester(processInstance.getSponsorId());
                    changesBean.setRequestTime(processInstance.getSponsorTime());
                    changesBean.setPassTime(processInstance.getFinishTime());
                    changesBean.setReason(processInstance.getGlobalParam3());
                    List<ChangesBean> changesBeans = new ArrayList<>();
                    changesBeans.add(changesBean);
                    receordsBean.setChangesBeanList(changesBeans);
                    receordsBeans.add(receordsBean);
                }
                Update update = new Update();
                update.set("receords",receordsBeans);
                mongoTemplate.updateFirst(query,update,"update_stowage");
            } else {
                List<ReceordsBean> receordsBeans = new ArrayList<>();
                Map<String, Object> save = new HashMap<>();
                save.put("ship_line_id", costAggregation.getShipLineId());
                ReceordsBean receordsBean = new ReceordsBean();
                receordsBean.setCostAggregationId(costAggregation.getId());
                receordsBean.setCostPayId(costAggregation.getCostPayId());
                receordsBean.setCostTypeName(costAggregation.getCostTypeName());
                ChangesBean changesBean = new ChangesBean();
                changesBean.setOldValue(old);
                CostAggregation costAggregationnew = new CostAggregation();
                costAggregationnew.setIsCus(costAggregation.getIsCus());
                costAggregationnew.setCusSupId(costAggregation.getCusSupId());
                costAggregationnew.setCostTypeName(costAggregation.getCostTypeName());
                costAggregationnew.setPriceWithTax(costAggregation.getPriceWithTax());
                costAggregationnew.setPriceWithoutTax(costAggregation.getPriceWithoutTax());
                costAggregationnew.setBillTax(costAggregation.getBillTax());
                changesBean.setNewValue(costAggregationnew);
                changesBean.setRequester(processInstance.getSponsorId());
                changesBean.setRequestTime(processInstance.getSponsorTime());
                changesBean.setPassTime(processInstance.getFinishTime());
                changesBean.setReason(processInstance.getGlobalParam3());
                List<ChangesBean> changesBeans = new ArrayList<>();
                changesBeans.add(changesBean);
                receordsBean.setChangesBeanList(changesBeans);
                receordsBeans.add(receordsBean);
                save.put("receords", receordsBeans);
                mongoTemplate.save(save, "update_stowage");
            }

        }

    }

    public JSONObject fromJson(String content) {
        try {
            JSONObject jsonObject = JSONObject.parseObject(content);
            return jsonObject;
        } catch (Exception e) {
        }
        return null;
    }
}
