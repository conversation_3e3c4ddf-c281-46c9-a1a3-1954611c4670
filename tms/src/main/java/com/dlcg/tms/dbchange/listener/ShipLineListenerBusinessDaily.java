package com.dlcg.tms.dbchange.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dlcg.tms.bean.CostProfitBean;
import com.dlcg.tms.bean.ShipLineBean;
import com.dlcg.tms.dbchange.ShipLineEvent;
import com.dlcg.tms.entity.*;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import com.dlcg.tms.mapper.CustomerGoodsCostDetailMapper;
import com.dlcg.tms.mapper.CustomerGoodsCostMapper;
import com.dlcg.tms.mapper.ShipLineShipCostMapper;
import com.dlcg.tms.service.*;
import com.zthzinfo.libs.dictionary.mongo.MongoDictionaryService;
import com.zthzinfo.libs.dictionary.mongo.entity.MongoDictionary;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ShipLineListenerBusinessDaily
        extends DBChangeBaseListenerSync<ShipLineEvent> {

    @Autowired
    IShipLineService iShipLineService;

    @Autowired
    IBusinessDailyService businessDailyService;

    @Autowired
    IShipLineTransPayService iShipLineTransPayService;

    @Autowired
    ICustomerGoodsCostService customerGoodsCostService;

    @Autowired
    CustomerGoodsCostDetailMapper customerGoodsCostDetailMapper;

    @Autowired
    ShipLineShipCostMapper shipLineShipCostMapper;

    @Autowired
    IShipLineShipCostService shipLineShipCostService;

    @Autowired
    IStowageProfitService stowageProfitService;
    @Autowired
    IStowageService iStowageService;
    @Autowired
    CustomerGoodsCostMapper customerGoodsCostMapper;
    @Autowired
    ICustomerGoodsProfitService customerGoodsProfitService;
    @Autowired
    ICustomerGoodsProfitDetailService customerGoodsProfitDetailService;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    MongoDictionaryService mongoDictionaryService;


    @Override
    public void onInsert(JSONObject data) {
        super.onInsert(data);
    }

    @Override
    public void onDelete(JSONObject data) {
        super.onDelete(data);
    }

    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        if (!old.containsKey("tijaocaiwu")) {
            return;
        }
        ShipLine shipLine = data.toJavaObject(ShipLine.class);
        if (shipLine.getTijaocaiwu() != 2) {
            return;
        }

        getProfit(shipLine.getId());
    }

    public void getProfit(String id){

        if(!StringUtils.isNotBlank(id)){
            return;
        }

        List<CostProfitBean> listc = customerGoodsCostService.getCostProfit(id);
        QueryWrapper<ShipLineShipCost> query = new QueryWrapper<>();
        query.eq("ship_line_id",id);

        List<ShipLineShipCost> shipcostlist = shipLineShipCostService.list(query);
        List<ShipLineShipCost> collect1 = shipcostlist.stream().filter(item -> item.getIsIncome() == 0 && "20".equals(item.getCostBear())).collect(Collectors.toList());
        List<ShipLineShipCost> collect1in= shipcostlist.stream().filter(item -> (item.getIsIncome() == 1 && !"船代费收入".equals(item.getCostProject()))).collect(Collectors.toList());
        Double totalShipCost =  collect1.stream().map(item -> item.getCostPrice()).reduce(0.0, Double::sum);
        Double totalShipCostin =  collect1in.stream().map(item -> item.getCostPrice()).reduce(0.0, Double::sum);


        QueryWrapper<ShipLineTransPay> query1 = new QueryWrapper<>();
        query1.eq("ship_line_id",id);
        query1.orderByAsc("create_date");
        List<ShipLineTransPay> shippay = iShipLineTransPayService.list(query1);

        ShipLine shipLine = iShipLineService.getById(id);

        Double tonnage = 0.0;
        for (CostProfitBean costProfitBean : listc) {
            tonnage += panduanD(costProfitBean.getTonnage());
        }

        Double shippaymoney = 0.0;
        for (ShipLineTransPay shipLineTransPay : shippay) {
           shippaymoney += panduanD(shipLineTransPay.getSettleTonnage()) * panduanD(shipLineTransPay.getShipPay());
        }

        Double shipCost = (!Objects.equals((double)tonnage,0.0)?(totalShipCost / tonnage):0.0);
        Double shipCostin = (!Objects.equals((double)tonnage,0.0)?(totalShipCostin / tonnage):0.0);
        Double shippaum = (!Objects.equals((double)tonnage,0.0)?(shippaymoney / tonnage):0.0);
        ShipLineBean shipLineBean = new ShipLineBean();
        BeanUtils.copyProperties(shipLine, shipLineBean);
        //   整船利润
        //  含税：

        QueryWrapper<CustomerGoodsCost> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shipline_id", id);
        List<CustomerGoodsCost> list = customerGoodsCostService.list(queryWrapper);
        QueryWrapper<ShipLineTransPay> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("ship_line_id", id);
        queryWrapper1.orderByDesc("create_date");
        List<ShipLineTransPay> list1 = iShipLineTransPayService.list(queryWrapper1);
        Double heTax = 0.0;
        Double he = 0.0;
        Double alltonnageTax = 0.0;
        String allShippay = "";
        Double num = 0.0;
        Double sum = 0.0;
        Double sumTax = 0.0;
//        外账不含税收入
        for (CustomerGoodsCost customerGoodsCost : list) {
            heTax = heTax + customerGoodsCost.getTonnage() * shuino(customerGoodsCost.getTaxFreight(), customerGoodsCost.getFreightNo())
                    + customerGoodsCost.getTonnage() * shuino(customerGoodsCost.getTaxDaili(), customerGoodsCost.getDailiNo());
//            alltonnageTax = alltonnageTax + customerGoodsCost.getTonnage();
        }
        //  外账含税收入
        for (CustomerGoodsCost customerGoodsCost : list) {
            he = he + panduanD(customerGoodsCost.getTonnage()) * panduanD(customerGoodsCost.getFreightNo()) + panduanD(customerGoodsCost.getTonnage()) * (customerGoodsCost.getDailiNo() == null ? 0 : customerGoodsCost.getDailiNo());
//            alltonnage = alltonnageTax + customerGoodsCost.getTonnage();
        }

        List<String> customerGoodsCostIds = list.stream().map(CustomerGoodsCost::getId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(customerGoodsCostIds)) {
            return;
        }
        Map<String,Double> maptonnage = new HashMap<>();
        list.forEach(item -> {
            maptonnage.put(item.getId(),item.getTonnage());
        });
        LambdaQueryWrapper<CustomerGoodsCostDetail> out = new LambdaQueryWrapper<CustomerGoodsCostDetail>().in(CollectionUtil.isNotEmpty(customerGoodsCostIds), CustomerGoodsCostDetail::getCustomerGoodsCostId, customerGoodsCostIds);
        out.eq(CustomerGoodsCostDetail::getIncomeOut, 0);
        List<CustomerGoodsCostDetail> customerGoodsCostDetails = customerGoodsCostDetailMapper.selectList(out);
        LambdaQueryWrapper<CustomerGoodsCostDetail> in = new LambdaQueryWrapper<CustomerGoodsCostDetail>().in(CollectionUtil.isNotEmpty(customerGoodsCostIds), CustomerGoodsCostDetail::getCustomerGoodsCostId, customerGoodsCostIds);
        in.eq(CustomerGoodsCostDetail::getIncomeOut, 1);
        List<CustomerGoodsCostDetail> customerGoodsCostDetailsin = customerGoodsCostDetailMapper.selectList(in);
        Map<String, List<CustomerGoodsCostDetail>> collect = customerGoodsCostDetails.stream().collect(Collectors.groupingBy(CustomerGoodsCostDetail::getCustomerGoodsCostId));
        Map<String, List<CustomerGoodsCostDetail>> collectin = customerGoodsCostDetailsin.stream().collect(Collectors.groupingBy(CustomerGoodsCostDetail::getCustomerGoodsCostId));

        List<Double> totalSum = new ArrayList<>();
        collect.forEach((k, v) -> {
            if (maptonnage.containsKey(k)) {
                Double aDouble = maptonnage.get(k);
                Double reduce = v.stream().map(item -> item.getPrice().doubleValue() * aDouble).reduce(0.0, Double::sum);
                totalSum.add(reduce);
            }
        });
        List<Double> totalSumin = new ArrayList<>();
        collectin.forEach((k, v) -> {
            if (maptonnage.containsKey(k)) {
                Double aDouble = maptonnage.get(k);
                Double reduce = v.stream().map(item -> item.getPrice().doubleValue() * aDouble).reduce(0.0, Double::sum);
                totalSumin.add(reduce);
            }
        });
        List<Double> totalSuminshui = new ArrayList<>();
        collectin.forEach((k, v) -> {
            if (maptonnage.containsKey(k)) {
                Double aDouble = maptonnage.get(k);
                Double reduce = v.stream().map(item -> item.getPriceNo().doubleValue() * aDouble).reduce(0.0, Double::sum);
                totalSuminshui.add(reduce);
            }
        });
        //  获取船上结算吨位之和
        Double settlementTonnage = 0.0;
        for (ShipLineTransPay shipLineTransPay : list1) {
            settlementTonnage += panduanD(shipLineTransPay.getSettleTonnage());
        }
    
        MongoDictionary dic = mongoDictionaryService.queryDictionary("bill_num");
        
        Double billNum = Double.parseDouble(dic.getStr1());

        //  获取船上运费成本
        Double neiShipSum = 0.0;
        Double neiShipSumshuino = 0.0;
        Double waiShipSum = 0.0;
            Double waiShipSumshuino = 0.0;

        if (shipLine.getDianMoney() != null && shipLine.getDianMoney() != 0) {
            waiShipSum = shipLine.getDianMoney() * settlementTonnage;
                waiShipSumshuino = shipLine.getDianMoneyNo() * settlementTonnage;
            for (ShipLineTransPay shipLineTransPay : list1) {
                neiShipSum += panduanD(shipLineTransPay.getShipPay()) * billNum * panduanD(shipLineTransPay.getSettleTonnage());
                neiShipSumshuino += panduanD(shipLineTransPay.getShipPay()) * panduanD(shipLineTransPay.getSettleTonnage());
            }
        } else if (shipLine.getMaiMoney() != null && shipLine.getMaiMoney() != 0) {
            waiShipSum = shipLine.getMaiMoney() * settlementTonnage;
                waiShipSumshuino = shipLine.getMaiMoneyNo() * settlementTonnage;
            for (ShipLineTransPay shipLineTransPay : list1) {
                neiShipSum += panduanD(shipLineTransPay.getShipPay()) * billNum * panduanD(shipLineTransPay.getSettleTonnage());
                neiShipSumshuino += panduanD(shipLineTransPay.getShipPay()) * panduanD(shipLineTransPay.getSettleTonnage());
            }
        } else {
            for (ShipLineTransPay shipLineTransPay : list1) {
                neiShipSum += panduanD(shipLineTransPay.getShipPay()) * panduanD(shipLineTransPay.getSettleTonnage());
                neiShipSumshuino += panduanD(shipLineTransPay.getShipPayNo()) * panduanD(shipLineTransPay.getSettleTonnage());
            }
            waiShipSum = neiShipSum;
                waiShipSumshuino = neiShipSumshuino;
        }

        //  货物费用不含税金额之和
            Double huoWuSum = totalSum.stream().reduce(0.0, Double::sum);
            Double huoWuinSum = totalSumin.stream().reduce(0.0, Double::sum);
        List<Double> totalTaxSum = new ArrayList<>();
        collect.forEach((k, v) -> {
            if (maptonnage.containsKey(k)) {
                Double aDouble = maptonnage.get(k);
                Double reduce = v.stream().map(item -> item.getPriceNo().doubleValue() * aDouble).reduce(0.0, Double::sum);
                totalTaxSum.add(reduce);
            }
        });
//        货物费用含税金额之和
        Double huoWuTaxSum = totalTaxSum.stream().reduce(0.0, Double::sum);
        Double huoWuTaxSumin = totalSuminshui.stream().reduce(0.0, Double::sum);

        //  计算船上费用：
        List<ShipLineShipCost> shipLineShipCosts = shipLineShipCostMapper.selectList(new LambdaQueryWrapper<ShipLineShipCost>().eq(ShipLineShipCost::getShipLineId, id));

        // 船上费用不含税金额之和
        List<ShipLineShipCost> collect1c = shipLineShipCosts.stream().filter(item -> item.getIsIncome() == 0 && "20".equals(item.getCostBear())).collect(Collectors.toList());
        List<ShipLineShipCost> collect1inc = shipLineShipCosts.stream().filter(item -> item.getIsIncome() == 1).collect(Collectors.toList());
            Double costPriceNoSum = collect1.stream().map(item -> shuino(item.getTax(), item.getCostPriceNo())).reduce(0.0, Double::sum);
            Double costPriceNoSumin = collect1in.stream().map(item -> shuino(item.getTax(), item.getCostPriceNo())).reduce(0.0, Double::sum);
//         船上费用含税金额之和
        Double costPriceSum = collect1c.stream().map(item -> item.getCostPriceNo()).reduce(0.0, Double::sum);
        Double costPriceSumin = collect1inc.stream().map(item -> item.getCostPriceNo()).reduce(0.0, Double::sum);

//        外账不含税费用成本
            Double noTaxCost = 0.0;
            noTaxCost = huoWuSum + costPriceNoSum - huoWuinSum - costPriceNoSumin;
//        外账含税费用成本
        Double taxCost = 0.0;
        taxCost = huoWuTaxSum + costPriceSum - huoWuTaxSumin - costPriceSumin;

//            // 整船利润不含税(内账)
//            Double noTaxIncomnei = heTax - noTaxCost - neiShipSumshuino;
//            // 整船利润含税(内账)
//            Double taxIncomnei = he - taxCost - neiShipSum;
            // 整船利润不含税(外账)
            Double noTaxIncomwai = heTax - noTaxCost - waiShipSumshuino;
//        // 整船利润含税(外账)
//        Double taxIncomwai = he - taxCost - waiShipSum;
        for (CostProfitBean costProfitBean : listc) {
            Double single = panduanD(costProfitBean.getYunfei()) + panduanD(costProfitBean.getCgcdOInMoney())
                    - panduanD(costProfitBean.getCgcdOutMoney()) + shipCostin - shipCost - shippaum;
            LambdaQueryWrapper<BusinessDaily> queryWrapper2 = new LambdaQueryWrapper<>();
            queryWrapper2.eq(BusinessDaily::getCostId,costProfitBean.getCgcid());
            List<BusinessDaily> bdlist = businessDailyService.list(queryWrapper2);
            if (bdlist.size()>0){
                BusinessDaily businessDaily = bdlist.get(0);
                businessDaily.setSingleTonProfit(single);
                businessDaily.setGrossProfit(noTaxIncomwai/tonnage);
                businessDaily.setGoodsType(costProfitBean.getGoodtype());
                businessDaily.setSysShiplineId(shipLine.getSysShiplineId());
                businessDaily.setTonnage(costProfitBean.getTonnage());
                businessDaily.setCreateDate(new Date());
                businessDailyService.updateById(businessDaily);

            } else {
                BusinessDaily businessDaily = new BusinessDaily();
                businessDaily.setId(IdUtil.simpleUUID());
                businessDaily.setShipLineId(id);
                businessDaily.setCostId(costProfitBean.getCgcid());
                businessDaily.setGoodsType(costProfitBean.getGoodtype());
                businessDaily.setSysShiplineId(shipLine.getSysShiplineId());
                businessDaily.setSingleTonProfit(single);
                businessDaily.setTonnage(costProfitBean.getTonnage());
                businessDaily.setGrossProfit(noTaxIncomwai/tonnage);
                businessDaily.setCreateDate(new Date());
                businessDailyService.save(businessDaily);
            }
        }


    }
    public Double shuino(String tax,Double price){
        Double v = 0.0;
        if (price!=null){
            v = price;
        }
        if (tax!=null&&!"".equals(tax)&&!"10".equals(tax)){
            if ("20".equals(tax)){
                v = v / 1.01;
            }else if ("30".equals(tax)){
                v = v/ 1.06;
            } else if ("40".equals(tax)){
                v = v/ 1.09;
            } else if ("50".equals(tax)){
                v = v/ 1.13;
            } else if ("25".equals(tax)){
                v = v/ 1.03;
            }
        }

        return v;
    }
    public Double panduanD(Double a){
        if (a == null){
            return 0.0;
        }else {
            return a;
        }
    }
    public Float number(Float i){
        if (i == null){
            return Float.valueOf(0);
        } else{
            return i;
        }
    }
}
