package com.dlcg.tms.dbchange.listener;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.oa.client.SysUserClient;
import com.dlcg.oa.entity.SysUser;
import com.dlcg.oa.service.SysDictionaryService;
import com.dlcg.tms.dbchange.ShipLineTransPayEvent;
import com.dlcg.tms.entity.*;
import com.dlcg.tms.enums.CostModuleSourceEnums;
import com.dlcg.tms.enums.OtherCostTypeEnums;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import com.dlcg.tms.service.*;
import com.zthzinfo.libs.dictionary.mongo.MongoDictionaryService;
import com.zthzinfo.libs.dictionary.mongo.entity.MongoDictionary;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.logging.Logger;

/*
* 监听船价表
* */
@Slf4j
@Component
public class ShipLineTransPayListener extends DBChangeBaseListenerSync<ShipLineTransPayEvent> {

    @Autowired
    IShipLineService iShipLineService;
    @Autowired
    IShipLineTransPayService iShipLineTransPayService;
    @Autowired
    ICostAggregationService iCostAggregationService;
    @Autowired
    ISysSupplierService iSysSupplierService;
    @Autowired
    SysUserClient sysUserClient;
    @Autowired
    SysDictionaryService sysDictionaryService;
    @Autowired
    MongoDictionaryService mongoDictionaryService;
    @Autowired
    ICustomerGoodsCostService iCustomerGoodsCostService;

    @Override
    public void onInsert(JSONObject data) {
		try {
			ShipLineTransPay shipLineTransPay = data.toJavaObject(ShipLineTransPay.class);
			ShipLine shipLine = iShipLineService.getById(shipLineTransPay.getShipLineId());
            SysSupplier sysSupplierSettle = new SysSupplier();
            SysSupplier sysSupplierOwner =  new SysSupplier();
            if(StrUtil.isNotBlank(shipLineTransPay.getSettleCompany())){
                sysSupplierSettle = iSysSupplierService.getById(shipLineTransPay.getSettleCompany());
            }else if(StringUtils.isNotBlank(shipLine.getSettleCompany())){
                sysSupplierSettle = iSysSupplierService.getById(shipLine.getSettleCompany());
            }
            if(StringUtils.isNotBlank(shipLine.getShipownerCompanyId())){
                sysSupplierOwner = iSysSupplierService.getById(shipLine.getShipownerCompanyId());
            }
            MongoDictionary mongoDictionary = mongoDictionaryService.queryDictionary("default_user");
            String defaultUser = mongoDictionary.getStr1();
			SysUser sysUser = sysUserClient.getUserInfo(StringUtils.isNotBlank(shipLine.getFindShipUser())?shipLine.getFindShipUser():defaultUser);
			Double settleTon = shipLineTransPay.getSettleTonnage() == null ? 0.0 : shipLineTransPay.getSettleTonnage();
			Double bottomTon = shipLineTransPay.getBottomTonnage() == null ? 0.0 : shipLineTransPay.getBottomTonnage();
			Double tonnage = settleTon < bottomTon ? bottomTon : settleTon;
            if(shipLineTransPay.getShipPay()==null){
                shipLineTransPay.setShipPay(0.0);
            }

			CostAggregation costAggregation = new CostAggregation();
			costAggregation.setId(IdUtil.simpleUUID());
			costAggregation.setShipLineId(shipLineTransPay.getShipLineId());
			costAggregation.setCostPayId(shipLineTransPay.getId());
			costAggregation.setIsPayment(1);
            if(StrUtil.isNotBlank(shipLineTransPay.getSettleCompany())){
                costAggregation.setCusSupId(shipLineTransPay.getSettleCompany());
                costAggregation.setCusSupName(sysSupplierSettle.getSimpleName());
            }
			else if(StringUtils.isNotBlank(shipLine.getSettleCompany())){
                costAggregation.setCusSupId(shipLine.getSettleCompany());
                costAggregation.setCusSupName(sysSupplierSettle.getSimpleName());
            } else {
                costAggregation.setCusSupId(shipLine.getShipownerCompanyId());
                costAggregation.setCusSupName(sysSupplierOwner.getSimpleName());
            }
			costAggregation.setIsCus(0);
			costAggregation.setCostType(OtherCostTypeEnums.CHUANJIA.getKey());
			costAggregation.setCostTypeName(OtherCostTypeEnums.CHUANJIA.getDesc());
			costAggregation.setSettleTonnage(tonnage);
			costAggregation.setPriceWithTax(shipLineTransPay.getShipPay());
			costAggregation.setPriceWithoutTax(shipLineTransPay.getShipPayNo());
			if(StringUtils.isNotBlank(shipLineTransPay.getPayBill())){
			    costAggregation.setBillTax(sysDictionaryService.getDictionaryValue("bill_tax", Integer.parseInt(shipLineTransPay.getPayBill())));
            }
			costAggregation.setSurplus(tonnage * shipLineTransPay.getShipPay());
			costAggregation.setPayable(0.0);
			costAggregation.setTotalPrice(tonnage * shipLineTransPay.getShipPay());
			costAggregation.setIsNeedApply(0);
			costAggregation.setCostModuleSource(CostModuleSourceEnums.SHIPMODULE.getKey());
			if(shipLineTransPay.getGoodsType() != null){
                costAggregation.setGoodsType(Integer.parseInt(shipLineTransPay.getGoodsType()));
                costAggregation.setGoodsTypeName(sysDictionaryService.getDictionaryValue("goods_type", Integer.parseInt(shipLineTransPay.getGoodsType())));
            }
			if(shipLine.getContractCode()!= null){
                if(StrUtil.isNotBlank(shipLineTransPay.getCompany())){
                    costAggregation.setContractCompany(Integer.parseInt(shipLineTransPay.getCompany()));
                    costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company", Integer.parseInt(shipLineTransPay.getCompany())));
                }else{
                    costAggregation.setContractCompany(shipLine.getContractCode());
                    costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company", shipLine.getContractCode()));
                }

            }
			costAggregation.setBusinessUser(StringUtils.isNotBlank(shipLine.getFindShipUser())?shipLine.getFindShipUser():defaultUser);
			costAggregation.setBusinessUserName(sysUser.getName());
			if(StringUtils.isNotBlank(shipLine.getShipownerCompanyId())){
                costAggregation.setShipOwnerCompany(shipLine.getShipownerCompanyId());
                costAggregation.setShipOwnerCompanyName(sysSupplierOwner.getSimpleName());
            }
//			if (shipLine.getCostIsComplete() != 0) {
//				costAggregation.setIsQueren(0);
//				costAggregation.setIsOnAccount(0);
//			} else {
//				costAggregation.setIsQueren(1);
//				if (shipLine.getTijaocaiwu() != 1) {
//					costAggregation.setIsOnAccount(0);
//				} else {
//					costAggregation.setIsOnAccount(1);
//				}
//			}
            Integer queren = Optional.ofNullable(shipLine.getCostIsComplete()).orElse(0);   // 是否确认
            Integer tijiaocaiwu = Optional.ofNullable(shipLine.getTijaocaiwu()).orElse(0);  // 挂账

            costAggregation.setIsQueren(1 - queren);
            costAggregation.setIsOnAccount(tijiaocaiwu);

//            if (!Objects.equals(shipLine.getCostIsComplete(), 0)) {
//                costAggregation.setIsQueren(0);
//            } else {
//                costAggregation.setIsQueren(1);
//            }
//
//            if (!Objects.equals(shipLine.getTijaocaiwu(), 0)) {
//                costAggregation.setIsOnAccount(1);
//            } else {
//                costAggregation.setIsOnAccount(0);
//            }
			costAggregation.setCreateBy(shipLineTransPay.getCreateBy());
			costAggregation.setCreateDate(new Date());
			iCostAggregationService.save(costAggregation);
            //处理 先删除船价再新增船价后流向的 逻辑
            LambdaQueryWrapper<CustomerGoodsCost> updateWrapper = new LambdaQueryWrapper<>();
            updateWrapper.eq(CustomerGoodsCost::getShiplineId,shipLineTransPay.getShipLineId());
            updateWrapper.eq(CustomerGoodsCost::getShipLinePay,"");
            CustomerGoodsCost customerGoodsCost = new CustomerGoodsCost();
            customerGoodsCost.setShipLinePay(shipLineTransPay.getId());
            iCustomerGoodsCostService.update(customerGoodsCost,updateWrapper);

		} catch (Throwable e) {
			e.printStackTrace();
		}
    }

    @Override
    public void onDelete(JSONObject data) {
        ShipLineTransPay shipLineTransPay = data.toJavaObject(ShipLineTransPay.class);
        LambdaQueryWrapper<CostAggregation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAggregation::getCostPayId,shipLineTransPay.getId());
        iCostAggregationService.remove(queryWrapper);

        //当删除的时候要处理下 货物流向
        List<ShipLineTransPay> shipLineTransPayList = iShipLineTransPayService.lambdaQuery().eq(ShipLineTransPay::getShipLineId, shipLineTransPay.getShipLineId()).list();
        //如果有其他的运价，直接绑定到另外一个
        LambdaQueryWrapper<CustomerGoodsCost> updateWrapper = new LambdaQueryWrapper<>();
        updateWrapper.eq(CustomerGoodsCost::getShipLinePay,shipLineTransPay.getId());
        if(shipLineTransPayList != null && shipLineTransPayList.size() > 0){
            CustomerGoodsCost customerGoodsCost = new CustomerGoodsCost();
            customerGoodsCost.setShipLinePay(shipLineTransPayList.get(0).getId());
            iCustomerGoodsCostService.update(customerGoodsCost,updateWrapper);

        }else { //没有的话，先置 "" 等待录入新的船价
            CustomerGoodsCost customerGoodsCost = new CustomerGoodsCost();
            customerGoodsCost.setShipLinePay("");
            iCustomerGoodsCostService.update(customerGoodsCost,updateWrapper);
        }


    }

    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        if(processIfDelFlagDefault(data,old)){
            return;
        }
        ShipLineTransPay shipLineTransPay = data.toJavaObject(ShipLineTransPay.class);
        ShipLine shipLine = iShipLineService.getById(shipLineTransPay.getShipLineId());
        SysSupplier sysSupplierSettle = new SysSupplier();
        SysSupplier sysSupplierOwner =  new SysSupplier();
        if(StrUtil.isNotBlank(shipLineTransPay.getSettleCompany())){
            sysSupplierSettle = iSysSupplierService.getById(shipLineTransPay.getSettleCompany());
        }else if(StringUtils.isNotBlank(shipLine.getSettleCompany())){
            sysSupplierSettle = iSysSupplierService.getById(shipLine.getSettleCompany());
        }
        if(StrUtil.isNotBlank(shipLineTransPay.getShipownerCompanyId())){
            sysSupplierOwner = iSysSupplierService.getById(shipLineTransPay.getShipownerCompanyId());
        }else if(StringUtils.isNotBlank(shipLine.getShipownerCompanyId())){
            sysSupplierOwner = iSysSupplierService.getById(shipLine.getShipownerCompanyId());
        }
        MongoDictionary mongoDictionary = mongoDictionaryService.queryDictionary("default_user");
        String defaultUser = mongoDictionary.getStr1();
        SysUser sysUser = sysUserClient.getUserInfo(StringUtils.isNotBlank(shipLine.getFindShipUser())?shipLine.getFindShipUser():defaultUser);
        LambdaQueryWrapper<CostAggregation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAggregation::getCostPayId,shipLineTransPay.getId());
        CostAggregation costAggregation = iCostAggregationService.getOne(queryWrapper);
        Double settleTon = shipLineTransPay.getSettleTonnage()==null?0.0:shipLineTransPay.getSettleTonnage();
        Double bottomTon = shipLineTransPay.getBottomTonnage()==null?0.0:shipLineTransPay.getBottomTonnage();
        Double tonnage = settleTon<bottomTon?bottomTon:settleTon;
        if(costAggregation != null){
            Double price1 = costAggregation.getTotalPrice() ==null?0:costAggregation.getTotalPrice();
            Double price2 = costAggregation.getSurplus()==null?0:costAggregation.getSurplus();
            Double price = price1-price2;
//            if(StrUtil.isNotBlank(shipLineTransPay.getSettleCompany())){
//                costAggregation.setCusSupId(shipLineTransPay.getSettleCompany());
//                costAggregation.setCusSupName(sysSupplierSettle.getSimpleName());
//            }else
//            if(StringUtils.isNotBlank(shipLine.getSettleCompany())){
//                costAggregation.setCusSupId(shipLine.getSettleCompany());
//                costAggregation.setCusSupName(sysSupplierSettle.getSimpleName());
//            }
            if(sysSupplierSettle!=null && StrUtil.isNotBlank(sysSupplierSettle.getId())){
                costAggregation.setCusSupId(sysSupplierSettle.getId());
                costAggregation.setCusSupName(sysSupplierSettle.getSimpleName());
            }
            else {
//                costAggregation.setCusSupId(shipLine.getShipownerCompanyId());
                if(sysSupplierOwner!=null && StrUtil.isNotBlank(sysSupplierOwner.getId())){
                    costAggregation.setCusSupId(sysSupplierOwner.getId());
                    costAggregation.setCusSupName(sysSupplierOwner.getSimpleName());
                }

            }
            costAggregation.setSettleTonnage(tonnage);
            costAggregation.setPriceWithTax(shipLineTransPay.getShipPay());
            costAggregation.setPriceWithoutTax(shipLineTransPay.getShipPayNo());
            if(shipLineTransPay.getPayBill()!= null){
                costAggregation.setBillTax(sysDictionaryService.getDictionaryValue("bill_tax",Integer.parseInt(shipLineTransPay.getPayBill())));
            }
            if(shipLineTransPay.getShipPay()!=null){
                costAggregation.setSurplus(tonnage*shipLineTransPay.getShipPay() - price);
                costAggregation.setTotalPrice(tonnage*shipLineTransPay.getShipPay());

            }
            if(StrUtil.isNotBlank(shipLineTransPay.getGoodsType())) {
                costAggregation.setGoodsType(Integer.parseInt(shipLineTransPay.getGoodsType()));
                costAggregation.setGoodsTypeName(sysDictionaryService.getDictionaryValue("goods_type", Integer.parseInt(shipLineTransPay.getGoodsType())));
            }
            if(StrUtil.isNotBlank(shipLineTransPay.getCompany())){
                costAggregation.setContractCompany(Integer.parseInt(shipLineTransPay.getCompany()));
                costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company", Integer.parseInt(shipLineTransPay.getCompany())));
            }else if(shipLine.getContractCode()!= null){
                    costAggregation.setContractCompany(shipLine.getContractCode());
                    costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",shipLine.getContractCode()));
            }
            costAggregation.setBusinessUser(StringUtils.isNotBlank(shipLine.getFindShipUser())?shipLine.getFindShipUser():defaultUser);
            costAggregation.setBusinessUserName(sysUser.getName());
            costAggregation.setUpdateBy(shipLineTransPay.getUpdateBy());
            costAggregation.setUpdateDate(shipLineTransPay.getUpdateDate());
//            if(StringUtils.isNotBlank(shipLine.getShipownerCompanyId())){
            if(sysSupplierOwner != null && StringUtils.isNotBlank(sysSupplierOwner.getId())){
                costAggregation.setShipOwnerCompany(sysSupplierOwner.getId());
                costAggregation.setShipOwnerCompanyName(sysSupplierOwner.getSimpleName());
            }
            iCostAggregationService.updateById(costAggregation);
        }
    }
}
