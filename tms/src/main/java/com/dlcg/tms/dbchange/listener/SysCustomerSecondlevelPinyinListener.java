package com.dlcg.tms.dbchange.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dlcg.tms.dbchange.SysCustomerSecondlevelEvent;
import com.dlcg.tms.entity.Ship;
import com.dlcg.tms.entity.SysCustomerSecondlevel;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import com.dlcg.tms.service.ISysCustomerSecondlevelService;
import com.github.houbb.pinyin.constant.enums.PinyinStyleEnum;
import com.github.houbb.pinyin.util.PinyinHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class SysCustomerSecondlevelPinyinListener extends DBChangeBaseListenerSync<SysCustomerSecondlevelEvent> {
    @Autowired
    ISysCustomerSecondlevelService iSysCustomerSecondlevelService;
    @Override
    public void onInsert(JSONObject data) {
        SysCustomerSecondlevel sysCustomerSecondlevel= data.toJavaObject(SysCustomerSecondlevel.class);
        String name= sysCustomerSecondlevel.getAbbreviationName();
        setPinYin(sysCustomerSecondlevel.getId(),name);
    }
    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        String oldname = old.getString("abbreviation_name");
        if(StrUtil.isBlank(oldname)){
            return;
        }
        SysCustomerSecondlevel sysCustomerSecondlevel= data.toJavaObject(SysCustomerSecondlevel.class);
        String name= sysCustomerSecondlevel.getAbbreviationName();
        setPinYin(sysCustomerSecondlevel.getId(),name);
    }

    private void setPinYin(String id,String name){
        if(StrUtil.isBlank(name) || StrUtil.isBlank(id)){
            return;
        }
        String pyin = getPinYinAll(name,"|");
        if(StrUtil.isNotBlank(pyin)){
            LambdaUpdateWrapper<SysCustomerSecondlevel> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(SysCustomerSecondlevel::getId,id);
            updateWrapper.set(SysCustomerSecondlevel::getPinYin,pyin);
            iSysCustomerSecondlevelService.update(updateWrapper);
        }

    }
    private List<String> pylist(List<String> py, List<String> bases){
        List<String> plist=new ArrayList<>();
        if(bases!=null && bases.size()>0){
            for(String str:bases){
                for(String p:py){
                    plist.add(str+p);
                }
            }
            return plist;
        }
        return py;
    }
    private  String getPinYinAll(String name,String split){
        // 多音字 拼接
        // 宁长重乌3  chang   zhang    zhong
        List<String> pinyin = new ArrayList<>();
        for(int i=0;i<name.length();i++){
            List<String> plist=  PinyinHelper.toPinyinList(name.charAt(i), PinyinStyleEnum.NORMAL);
            // 去重
            plist= plist.stream().distinct().collect(Collectors.toList());
            // 循环添加
            pinyin = pylist(plist,pinyin);
        }
        return pinyin.stream().collect(Collectors.joining(split));
    }
}
