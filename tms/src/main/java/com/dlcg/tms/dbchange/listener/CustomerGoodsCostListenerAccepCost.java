package com.dlcg.tms.dbchange.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.bms.client.AdvanceReceiveClient;
import com.dlcg.bms.entity.ReceiveWater;
import com.dlcg.bms.enums.DelFlagEnum;
import com.dlcg.oa.service.SysDictionaryService;
import com.dlcg.tms.dbchange.CustomerGoodsCostEvent;
import com.dlcg.tms.entity.*;
import com.dlcg.tms.enums.BillTaxEnums;
import com.dlcg.tms.enums.CostModuleSourceEnums;
import com.dlcg.tms.enums.OtherCostTypeEnums;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import com.dlcg.tms.service.*;
import com.dlcg.tms.service.client.ReceiveWaterClient;
import com.zthzinfo.common.DelFlagUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Component
/*
 * 监听流向是否确认费用，同步修改汇总表 cost_aggregation
 * */
public class CustomerGoodsCostListenerAccepCost
        extends DBChangeBaseListenerSync<CustomerGoodsCostEvent> {

    @Autowired
    ICostAggregationService iCostAggregationService;
    @Autowired
    IReceiveBillOutApplyService iReceiveBillOutApplyService;
    @Autowired
    IReceiveBillOutDetailService iReceiveBillOutDetailService;
    @Autowired
    AdvanceReceiveClient advanceReceiveClient;
    @Autowired
    ICustomerGoodsSourceService customerGoodsSourceService;
    @Autowired
    ISysCustomerSecondlevelService sysCustomerSecondlevelService;
    @Autowired
    SysDictionaryService sysDictionaryService;
    @Autowired
    ISysSupplierService sysSupplierService;

    @Override
    public void onInsert(JSONObject data) {
    }

    @Override
    public void onDelete(JSONObject data) {
    }

    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        if(!old.containsKey("payment_date") && !old.containsKey("accep_price") && !old.containsKey("accep_discount")
                && !old.containsKey("accep_month_rate") && !old.containsKey("platform_fee") && !old.containsKey("interest_rate") && !old.containsKey("cost_is_complete") && !old.containsKey("tonnage")
        ){
           return;
        }

        CustomerGoodsCost customerGoodsCost = data.toJavaObject(CustomerGoodsCost.class);
        Assert.notNull(customerGoodsCost, "customerGoodsCost不能为空");
//        Assert.notNull(customerGoodsCost.getPaymentDate(), "customerGoodsCost 付款时间不能为空");
        if(customerGoodsCost.getPaymentDate() == null){
            return;
        }
        Integer isComplete = Optional.ofNullable(customerGoodsCost.getCostIsComplete()).orElse(0);
        // 二级客户
        String secondLevelId = null;
        String secondLevelName = null;
        // 供应商
        String supperId=null;
        String supperName = null;
        if(StrUtil.isNotBlank(customerGoodsCost.getCustomerGoodsSourceId())){
            CustomerGoodsSource customerGoodsSource= customerGoodsSourceService.getById(customerGoodsCost.getCustomerGoodsSourceId());
            if(customerGoodsSource!=null){
                secondLevelId = customerGoodsSource.getCustomerSecondlevelId();
                if(StrUtil.isNotBlank(secondLevelId)){
                    SysCustomerSecondlevel sysCustomerSecondlevel= sysCustomerSecondlevelService.getById(secondLevelId);
                    if(sysCustomerSecondlevel!=null){
                        secondLevelName = sysCustomerSecondlevel.getAbbreviationName();
                        // 根据名称查询供应商
                        LambdaQueryWrapper<SysSupplier> supplierLambdaQueryWrapper=new LambdaQueryWrapper<>();
                        supplierLambdaQueryWrapper.eq(SysSupplier::getSimpleName,secondLevelName);
                        SysSupplier sysSupplier=sysSupplierService.getOne(supplierLambdaQueryWrapper,false);
                        if(sysSupplier!=null){
                            supperId=sysSupplier.getId();
                            supperName=sysSupplier.getSimpleName();
                        }
                    }
                }
            }
        }
        if(secondLevelId==null || supperId==null){
            return;
        }

        // 生成结算数据
        // 成本利息+平台费用+承兑费用利息
        // 获取成本 收款信息
        // 根据goods 查询收款信息
        LambdaQueryWrapper<CostAggregation> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAggregation::getCustomerGoodsCostId,customerGoodsCost.getId()); // 货物id
        queryWrapper.eq(CostAggregation::getIsPayment,0);// 收入
        queryWrapper.eq(CostAggregation::getCostModuleSource, CostModuleSourceEnums.GOODSMODULE.getKey()); // 货物类型
        queryWrapper.eq(CostAggregation::getIsCus,1);// 客户
        queryWrapper.eq(CostAggregation::getCostType, OtherCostTypeEnums.YUNFEI.getKey());
        queryWrapper.eq(CostAggregation::getDelFlag,DelFlagUtil.effective());

        List<CostAggregation> listCostAgg = iCostAggregationService.list(queryWrapper);
        if(listCostAgg==null || listCostAgg.size()==0){
            return;
        }
        // 平台费用
        BigDecimal platformFeeSum =  customerGoodsCost.getPlatformFee().multiply(BigDecimal.valueOf(customerGoodsCost.getTonnage()));
        // 承兑付款金额
        BigDecimal fuSum = customerGoodsCost.getAccepPrice();
        if(customerGoodsCost.getAccepPrice()==null){
            fuSum =  BigDecimal.valueOf(customerGoodsCost.getTonnage()).multiply(BigDecimal.valueOf(customerGoodsCost.getFreightNo()));
        }
        // 承兑利息费用
        Integer accepDisc=0; // 贴息天数
        if(customerGoodsCost.getAccepDiscount()!=null){
            accepDisc=customerGoodsCost.getAccepDiscount();
        }
        BigDecimal accepSum = new BigDecimal("0");
        if(0!= accepDisc){
            // 承兑利率
            BigDecimal monthRate = customerGoodsCost.getAccepMonthRate();
            if(BigDecimal.ZERO.compareTo(monthRate)!=0){
                monthRate = monthRate.divide(BigDecimal.valueOf(100),10,RoundingMode.HALF_UP);
                BigDecimal accepDay = BigDecimal.valueOf(accepDisc.longValue()).divide(BigDecimal.valueOf(30),8,RoundingMode.HALF_UP);
                accepSum = monthRate.multiply(accepDay).multiply(fuSum);
            }

        }
        // 收款利息
        BigDecimal caSum=new BigDecimal("0");


        // 货物结算数据
            for(CostAggregation ca:listCostAgg){
                // ca
                // 根据 ca 获取收款金额 及 收款时间
                // 获取相关付款信息
                LambdaQueryWrapper<ReceiveBillOutDetail> billOutDetailLambdaQueryWrapper=new LambdaQueryWrapper<>();
                billOutDetailLambdaQueryWrapper.eq(ReceiveBillOutDetail::getCostAggregationId,ca.getId());
                billOutDetailLambdaQueryWrapper.eq(ReceiveBillOutDetail::getDelFlag, DelFlagUtil.effective());
                List<ReceiveBillOutDetail> billOutDetails= iReceiveBillOutDetailService.list(billOutDetailLambdaQueryWrapper);

                List<String> applyIds= billOutDetails.stream().map(ReceiveBillOutDetail::getReceiveBillOutApplyId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
                if(applyIds.size()==0){
                    continue;
                }

                LambdaQueryWrapper<ReceiveBillOutApply> applyLambdaQueryWrapper=new LambdaQueryWrapper<>();
                applyLambdaQueryWrapper.eq(ReceiveBillOutApply::getIsFinish,1);
                applyLambdaQueryWrapper.in(ReceiveBillOutApply::getId,applyIds);
                applyLambdaQueryWrapper.eq(ReceiveBillOutApply::getDelFlag,DelFlagUtil.effective());
                applyLambdaQueryWrapper.isNotNull(ReceiveBillOutApply::getReceiveWaterId);

                List<ReceiveBillOutApply> listApply = iReceiveBillOutApplyService.list(applyLambdaQueryWrapper);

                // 有结算数据 的 则生成数据
                if(listApply!=null && listApply.size()>0){
                    // 有结算数据
                   Map<String,List<ReceiveBillOutDetail>> mapDetail = billOutDetails.stream().collect(Collectors.groupingBy(ReceiveBillOutDetail::getReceiveBillOutApplyId));
                   for(ReceiveBillOutApply rba:listApply){
                       // 根据rba 获取收款时间
                       if(StrUtil.isBlank(rba.getReceiveWaterId())){
                           continue;
                       }
                       ReceiveWater receiveWater= advanceReceiveClient.getReceiveWaterById(rba.getReceiveWaterId());
                       // 收款时间
                       Date createDate = receiveWater.getCreateTime();
                       // 收款金额
                       double money = 0;
                       List<ReceiveBillOutDetail> listDetails = mapDetail.get(rba.getId());
                       if(listDetails!=null && listDetails.size()>0){
                           money= listDetails.stream().map(ReceiveBillOutDetail::getMoney).filter(Objects::nonNull).reduce(0.0,Double::sum);
                       }
//                       String shipLineIds= listDetails.stream().map(ReceiveBillOutDetail::getShipLineId).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));


//                       costAggSave(rba.getId(),money,createDate,shipLineIds,secondLevelId,secondLevelName,customerGoodsCost);
                      BigDecimal goodsSum =  goodsPriceCost(money,createDate,customerGoodsCost);
                       caSum= caSum.add(goodsSum);
                   }
                }

            }

        System.out.println("caSum:"+caSum);
        System.out.println("platformFeeSum:"+platformFeeSum);
        System.out.println("accepSum:"+accepSum);

        BigDecimal bigDecimal = caSum.setScale(2,RoundingMode.HALF_UP).add(accepSum.setScale(2,RoundingMode.HALF_UP)).add(platformFeeSum.setScale(2,RoundingMode.HALF_UP)).setScale(2,RoundingMode.HALF_UP);
        System.out.println("sum:"+bigDecimal);
        costAggSave(bigDecimal,customerGoodsCost.getShiplineId(),supperId,supperName,customerGoodsCost,1-isComplete);
    }

    private BigDecimal goodsPriceCost(Double sumDou,Date time,CustomerGoodsCost custGoods){
        // 根据时间计算金额
        Date fuDate = custGoods.getPaymentDate(); // 付款时间
        BigDecimal interest = custGoods.getInterestRate();//
        if(BigDecimal.ZERO.compareTo(interest)==0){
            return BigDecimal.ZERO;
        }
        interest = interest.divide(BigDecimal.valueOf(100),10,RoundingMode.HALF_UP);
        System.out.println("interest:"+interest);
//        计算时间差
        LocalDate localDate= time.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate fuLocalDate = fuDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        System.out.println("fuk:"+localDate);
        System.out.println("endk:"+fuLocalDate);

        long days = fuLocalDate.toEpochDay()-localDate.toEpochDay();
        System.out.println("diffDay:"+days);
        if(days<=0){
            return new BigDecimal(0);
        }
        BigDecimal dayBig = BigDecimal.valueOf(days).divide(BigDecimal.valueOf(30),8,RoundingMode.HALF_UP);
//        if(BigDecimal.ZERO.compareTo(dayBig)==0){
//            return BigDecimal.ZERO;
//        }
        BigDecimal daysInterest = interest.multiply(dayBig);
        System.out.println("daysInterest:"+daysInterest);
        System.out.println("sumDou:"+sumDou);

        return BigDecimal.valueOf(sumDou).multiply(daysInterest).setScale(2,RoundingMode.HALF_UP);
    }



    private void costAggSave(BigDecimal sumDou,String shipLineIds,String cusSupId,String cusSupName,CustomerGoodsCost custGoods,int queren){

        // 查询是否有历史
        LambdaQueryWrapper<CostAggregation> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CostAggregation::getCostType,OtherCostTypeEnums.ACCEPCOST.getKey());
        lambdaQueryWrapper.eq(CostAggregation::getCostModuleSource,CostModuleSourceEnums.GOODSMODULE.getKey());
        lambdaQueryWrapper.eq(CostAggregation::getIsPayment,1);
        lambdaQueryWrapper.eq(CostAggregation::getIsCus,0);
        lambdaQueryWrapper.eq(CostAggregation::getCostPayId,custGoods.getId());
        lambdaQueryWrapper.eq(CostAggregation::getCustomerGoodsCostId,custGoods.getId());
        CostAggregation aggregation = iCostAggregationService.getOne(lambdaQueryWrapper,false);
        if(aggregation==null){
            aggregation = new CostAggregation();
            aggregation.setId(IdUtil.simpleUUID());
            aggregation.setShipLineId(shipLineIds);
            aggregation.setCostPayId(custGoods.getId());
            aggregation.setIsPayment(1);
            aggregation.setIsCus(0);
            aggregation.setCostType(OtherCostTypeEnums.ACCEPCOST.getKey());
            aggregation.setCostTypeName(OtherCostTypeEnums.ACCEPCOST.getDesc());
            aggregation.setIsNeedApply(0);
            aggregation.setCostModuleSource(CostModuleSourceEnums.GOODSMODULE.getKey());
            aggregation.setIsOnAccount(0);
//            aggregation.setBillTax(BillTaxEnums.TAXRATE6.getDesc());
            aggregation.setCreateBy("CustomerGoodsCostListenerAccepCost");
            aggregation.setCreateDate(new Date());
            aggregation.setPayable(0.0);
        }
        aggregation.setBillTax(BillTaxEnums.TAXRATE6.getDesc());
        aggregation.setIsQueren(queren);
        Double price1shou = aggregation.getTotalPrice() ==null?0:aggregation.getTotalPrice();
        Double price2shou = aggregation.getSurplus()==null?0:aggregation.getSurplus();
        double priceshou = price1shou-price2shou;
        aggregation.setSettleTonnage(custGoods.getTonnage());
        aggregation.setCusSupId(cusSupId);
        aggregation.setCusSupName(cusSupName);
        aggregation.setCustomerGoodsCostId(custGoods.getId());
        aggregation.setTotalPrice(sumDou.doubleValue());
        aggregation.setSurplus(sumDou.subtract(BigDecimal.valueOf(priceshou)).doubleValue());
        aggregation.setContractCompany(10);
        aggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company", 10));

        iCostAggregationService.saveOrUpdate(aggregation);

    }
}
