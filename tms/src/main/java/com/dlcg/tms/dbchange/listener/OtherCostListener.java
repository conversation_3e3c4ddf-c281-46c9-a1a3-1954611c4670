package com.dlcg.tms.dbchange.listener;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.oa.service.SysDictionaryService;
import com.dlcg.tms.dbchange.OtherCostEvent;
import com.dlcg.tms.entity.*;
import com.dlcg.tms.enums.CostModuleSourceEnums;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import com.dlcg.tms.service.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/*
* 监听其他费用表
*/
@Component
public class OtherCostListener
        extends DBChangeBaseListenerSync<OtherCostEvent> {

    @Autowired
    ICustomerGoodsConfirmService customerGoodsConfirmService;
    @Autowired
    IShipLineService shipLineService;
    @Autowired
    ICustomerGoodsCostService iCustomerGoodsCostService;
    @Autowired
    ICostAggregationService iCostAggregationService;
    @Autowired
    ISysSupplierService iSysSupplierService;
    @Autowired
    SysDictionaryService sysDictionaryService;
    @Autowired
    ISysCustomerSecondlevelService iSysCustomerSecondlevelService;


    @Override
    public void onInsert(JSONObject data) {
        OtherCost otherCost = data.toJavaObject(OtherCost.class);
        SysSupplier sysSupplier = iSysSupplierService.getById(otherCost.getSysSupplierId());
        SysCustomerSecondlevel sysCustomerSecondlevel = iSysCustomerSecondlevelService.getById(otherCost.getSysSupplierId());
        CostAggregation costAggregation = new CostAggregation();
        costAggregation.setId(IdUtil.simpleUUID());
        costAggregation.setShipLineId(otherCost.getShipLineId());
        costAggregation.setCostPayId(otherCost.getId());
        costAggregation.setIsPayment(otherCost.getType()==0?1:0);
        costAggregation.setCusSupId(otherCost.getSysSupplierId());
        String cusName = "";
        int cusType = 0;
        if(sysSupplier != null){
            cusName = sysSupplier.getSimpleName();
            cusType = 0;
        }else {
            cusName = sysCustomerSecondlevel.getAbbreviationName();
            cusType = 1;
        }
        costAggregation.setCusSupName(cusName);
        costAggregation.setIsCus(cusType);
        costAggregation.setCostType(otherCost.getCostType());
        if(otherCost.getCostType() != null){
            if(otherCost.getType()==0){
                costAggregation.setCostTypeName(sysDictionaryService.getDictionaryValue("other_expenditure",otherCost.getCostType()));
            } else {
                costAggregation.setCostTypeName(sysDictionaryService.getDictionaryValue("other_income",otherCost.getCostType()));
            }
        }
        costAggregation.setSettleTonnage(otherCost.getTonnage());
        costAggregation.setPriceWithTax(otherCost.getCostPrice());
        costAggregation.setPriceWithoutTax(otherCost.getCostPriceno());
        if(otherCost.getCostTax() != null){
            costAggregation.setBillTax(sysDictionaryService.getDictionaryValue("bill_tax",otherCost.getCostTax()));
        }
        costAggregation.setSurplus(otherCost.getCostPrice());
        costAggregation.setTotalPrice(otherCost.getCostPrice());
        costAggregation.setPayable(0.0);
        costAggregation.setIsNeedApply(0);
        costAggregation.setCostModuleSource(CostModuleSourceEnums.OTHERCOSTMODULE.getKey());
        if(StringUtils.isNotBlank(otherCost.getContractCompany())){
            costAggregation.setContractCompany(Integer.parseInt(otherCost.getContractCompany()));
            costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",Integer.parseInt(otherCost.getContractCompany())));
        }
        costAggregation.setCreateBy(otherCost.getCreateBy());
        costAggregation.setCreateDate(new Date());
        if(StringUtils.isNotBlank(otherCost.getShipLineId())){
            ShipLine shipLine = shipLineService.getById(otherCost.getShipLineId());
            if(shipLine.getCostIsComplete()!=null && shipLine.getCostIsComplete()!=0){
                costAggregation.setIsQueren(0);
                if(shipLine.getTijaocaiwu() !=1){
                    costAggregation.setIsOnAccount(0);
                } else {
                    costAggregation.setIsOnAccount(1);
                }
            }else{
                costAggregation.setIsQueren(1);
                costAggregation.setIsOnAccount(0);
            }
//            if(shipLine.getQueren() != 0){
//                costAggregation.setIsQueren(0);
//                costAggregation.setIsOnAccount(0);
//            } else {
//                costAggregation.setIsQueren(1);
//                if(shipLine.getTijaocaiwu() !=1){
//                    costAggregation.setIsOnAccount(0);
//                } else {
//                    costAggregation.setIsOnAccount(1);
//                }
//            }
        } else {
            costAggregation.setIsQueren(0);
            costAggregation.setIsOnAccount(0);
        }
        iCostAggregationService.save(costAggregation);
    }

    @Override
    public void onDelete(JSONObject data) {
        OtherCost otherCost = data.toJavaObject(OtherCost.class);
        LambdaQueryWrapper<CostAggregation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAggregation::getCostPayId,otherCost.getId());
        iCostAggregationService.remove(queryWrapper);
    }

    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        if(processIfDelFlagDefault(data,old)){
            return;
        }
        OtherCost otherCost = data.toJavaObject(OtherCost.class);
//        ShipLine shipLine = new ShipLine();
//        if(StringUtils.isNotBlank(otherCost.getShipLineId())){
//            shipLine = shipLineService.getById(otherCost.getShipLineId());
//        }
        SysSupplier sysSupplier = iSysSupplierService.getById(otherCost.getSysSupplierId());
        SysCustomerSecondlevel sysCustomerSecondlevel = iSysCustomerSecondlevelService.getById(otherCost.getSysSupplierId());
        String cusName = "";
        if(sysSupplier != null){
            cusName = sysSupplier.getSimpleName();
        }else {
            cusName = sysCustomerSecondlevel.getAbbreviationName();
        }
        LambdaQueryWrapper<CostAggregation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAggregation::getCostPayId,otherCost.getId());
        CostAggregation costAggregation = iCostAggregationService.getOne(queryWrapper);
        if(costAggregation == null){
            return;
        }
        Double price1 = costAggregation.getTotalPrice() ==null?0:costAggregation.getTotalPrice();
        Double price2 = costAggregation.getSurplus()==null?0:costAggregation.getSurplus();
        Double price = price1-price2;
        costAggregation.setIsPayment(otherCost.getType()==0?1:0);
        costAggregation.setCusSupId(otherCost.getSysSupplierId());
        costAggregation.setCusSupName(cusName);
        costAggregation.setCostType(otherCost.getCostType());
        if(otherCost.getCostType() != null){
            if(otherCost.getType()==0){
                costAggregation.setCostTypeName(sysDictionaryService.getDictionaryValue("other_expenditure",otherCost.getCostType()));
            } else {
                costAggregation.setCostTypeName(sysDictionaryService.getDictionaryValue("other_income",otherCost.getCostType()));
            }
        }
        costAggregation.setSettleTonnage(otherCost.getTonnage());
        costAggregation.setPriceWithTax(otherCost.getCostPrice());
        costAggregation.setPriceWithoutTax(otherCost.getCostPriceno());
        if(otherCost.getCostTax() != null){
            costAggregation.setBillTax(sysDictionaryService.getDictionaryValue("bill_tax",otherCost.getCostTax()));
        }
        costAggregation.setSurplus(otherCost.getCostPrice()-price);
        costAggregation.setTotalPrice(otherCost.getCostPrice());
        costAggregation.setPayable(0.0);
        if(StringUtils.isNotBlank(otherCost.getContractCompany())){
            costAggregation.setContractCompany(Integer.parseInt(otherCost.getContractCompany()));
            costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",Integer.parseInt(otherCost.getContractCompany())));
        }
        costAggregation.setUpdateBy(otherCost.getUpdateBy());
        costAggregation.setUpdateDate(otherCost.getUpdateDate());
        if(old.containsKey("ship_line_id") && StringUtils.isBlank(old.getString("ship_line_id"))){
            ShipLine shipLine = shipLineService.getById(otherCost.getShipLineId());
            if(shipLine.getCostIsComplete()!=null && shipLine.getCostIsComplete()!=0){
                costAggregation.setIsQueren(0);
                if(shipLine.getTijaocaiwu() !=1){
                    costAggregation.setIsOnAccount(0);
                } else {
                    costAggregation.setIsOnAccount(1);
                }
            }else{
                costAggregation.setIsQueren(1);
                costAggregation.setIsOnAccount(0);
            }
//            if(shipLine.getQueren() != 0){
//                costAggregation.setIsQueren(0);
//                costAggregation.setIsOnAccount(0);
//            } else {
//                costAggregation.setIsQueren(1);
//                if(shipLine.getTijaocaiwu() !=1){
//                    costAggregation.setIsOnAccount(0);
//                } else {
//                    costAggregation.setIsOnAccount(1);
//                }
//            }
        }
        if(StringUtils.isBlank(costAggregation.getShipLineId())){
            costAggregation.setIsQueren(0);
            if (Objects.equals(otherCost.getGuazhang(), 2)) {
                costAggregation.setIsOnAccount(1);
            } else {
                costAggregation.setIsOnAccount(0);
            }
        }
        iCostAggregationService.updateById(costAggregation);
    }
}
