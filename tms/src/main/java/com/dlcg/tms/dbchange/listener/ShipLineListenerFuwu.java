package com.dlcg.tms.dbchange.listener;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.oa.service.SysDictionaryService;
import com.dlcg.tms.dbchange.ShipLineEvent;
import com.dlcg.tms.entity.CostAggregation;
import com.dlcg.tms.entity.ShipLine;
import com.dlcg.tms.entity.SysSupplier;
import com.dlcg.tms.enums.BillTaxEnums;
import com.dlcg.tms.enums.CostModuleSourceEnums;
import com.dlcg.tms.enums.OtherCostTypeEnums;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import com.dlcg.tms.service.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;


/*
* 监听ShipLine 船代服务费
*/
@Component
public class ShipLineListenerFuwu
        extends DBChangeBaseListenerSync<ShipLineEvent> {

    @Autowired
    ICustomerGoodsConfirmService customerGoodsConfirmService;
    @Autowired
    IShipLineService shipLineService;
    @Autowired
    ICustomerGoodsCostService iCustomerGoodsCostService;
    @Autowired
    ICostAggregationService iCostAggregationService;
    @Autowired
    ISysSupplierService iSysSupplierService;
    @Autowired
    SysDictionaryService sysDictionaryService;


    @Override
    public void onInsert(JSONObject data) {
        ShipLine shipLine = data.toJavaObject(ShipLine.class);
        SysSupplier sysSupplier = iSysSupplierService.getById(StringUtils.isBlank(shipLine.getSettleCompany())?shipLine.getShipownerCompanyId():shipLine.getSettleCompany());
        if(shipLine.getLineType() == 1){
            CostAggregation costAggregation = new CostAggregation();
            costAggregation.setId(IdUtil.simpleUUID());
            costAggregation.setShipLineId(shipLine.getId());
            costAggregation.setCostPayId(shipLine.getId());
            costAggregation.setIsPayment(0);
            costAggregation.setCusSupId(StringUtils.isBlank(shipLine.getSettleCompany())?shipLine.getShipownerCompanyId():shipLine.getSettleCompany());
            costAggregation.setCusSupName(sysSupplier.getSimpleName());
            costAggregation.setIsCus(0);
            costAggregation.setCostType(OtherCostTypeEnums.FUWU.getKey());
            costAggregation.setCostTypeName(OtherCostTypeEnums.FUWU.getDesc());
            costAggregation.setPriceWithTax(shipLine.getFuwuMoney());
            costAggregation.setPriceWithoutTax(shipLine.getFuwuMoney());
            costAggregation.setBillTax(BillTaxEnums.TAXRATENONE.getDesc());
            costAggregation.setSurplus(shipLine.getFuwuMoney());
            costAggregation.setPayable(0.0);
            costAggregation.setTotalPrice(shipLine.getFuwuMoney());
            costAggregation.setIsNeedApply(0);
            costAggregation.setCostModuleSource(CostModuleSourceEnums.SHIPMODULE.getKey());
            if(shipLine.getContractCode()!= null){
                costAggregation.setContractCompany(shipLine.getContractCode());
                costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",shipLine.getContractCode()));
            }
            if(shipLine.getCostIsComplete()!=null && shipLine.getCostIsComplete()!=0){
                costAggregation.setIsQueren(0);
                if(shipLine.getTijaocaiwu() !=1){
                    costAggregation.setIsOnAccount(0);
                } else {
                    costAggregation.setIsOnAccount(1);
                }
            }else{
                costAggregation.setIsQueren(1);
                costAggregation.setIsOnAccount(0);
            }
//            if(shipLine.getQueren() != 0){
//                costAggregation.setIsQueren(0);
//                costAggregation.setIsOnAccount(0);
//            } else {
//                costAggregation.setIsQueren(1);
//                if(shipLine.getTijaocaiwu() !=1){
//                    costAggregation.setIsOnAccount(0);
//                } else {
//                    costAggregation.setIsOnAccount(1);
//                }
//            }
            costAggregation.setCreateDate(new Date());
            costAggregation.setCreateBy(shipLine.getCreateBy());
            iCostAggregationService.save(costAggregation);
        }
    }

    @Override
    public void onDelete(JSONObject data) {
        ShipLine shipLine = data.toJavaObject(ShipLine.class);
        if(shipLine.getLineType() == 1){
            LambdaQueryWrapper<CostAggregation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CostAggregation::getCostPayId,shipLine.getId());
            iCostAggregationService.remove(queryWrapper);
        }
    }

    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        if(processIfDelFlagDefault(data,old)){
            return;
        }
        if(!old.containsKey("fuwu_money")&&!old.containsKey("fuwu_surplus")&&!old.containsKey("fuwu_payable")){
            return;
        }
        ShipLine shipLine = data.toJavaObject(ShipLine.class);
        SysSupplier sysSupplier = iSysSupplierService.getById(StringUtils.isBlank(shipLine.getSettleCompany())?shipLine.getShipownerCompanyId():shipLine.getSettleCompany());
        LambdaQueryWrapper<CostAggregation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAggregation::getCostPayId,shipLine.getId());
        CostAggregation costAggregation = iCostAggregationService.getOne(queryWrapper);
        if(shipLine.getLineType() ==1){
            Double price1 = costAggregation.getTotalPrice() ==null?0:costAggregation.getTotalPrice();
            Double price2 = costAggregation.getSurplus()==null?0:costAggregation.getSurplus();
            Double price = price1-price2;
            costAggregation.setCusSupId(StringUtils.isBlank(shipLine.getSettleCompany())?shipLine.getShipownerCompanyId():shipLine.getSettleCompany());
            costAggregation.setCusSupName(sysSupplier.getSimpleName());
            costAggregation.setCostType(OtherCostTypeEnums.FUWU.getKey());
            costAggregation.setCostTypeName(OtherCostTypeEnums.FUWU.getDesc());
            costAggregation.setPriceWithTax(shipLine.getFuwuMoney());
            costAggregation.setPriceWithoutTax(shipLine.getFuwuMoney());
            costAggregation.setBillTax(BillTaxEnums.TAXRATENONE.getDesc());
            costAggregation.setSurplus(shipLine.getFuwuMoney()-price);
            costAggregation.setTotalPrice(shipLine.getFuwuMoney());
            if(shipLine.getContractCode()!= null){
                costAggregation.setContractCompany(shipLine.getContractCode());
                costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",shipLine.getContractCode()));
            }
            costAggregation.setUpdateBy(shipLine.getUpdateBy());
            costAggregation.setUpdateDate(shipLine.getUpdateDate());
            iCostAggregationService.updateById(costAggregation);
        }
    }
}
