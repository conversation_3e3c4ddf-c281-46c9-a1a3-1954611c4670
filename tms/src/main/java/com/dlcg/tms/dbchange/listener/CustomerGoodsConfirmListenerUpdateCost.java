package com.dlcg.tms.dbchange.listener;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.tms.dbchange.CustomerGoodsConfirmEvent;
import com.dlcg.tms.entity.CostAggregation;
import com.dlcg.tms.entity.CustomerGoodsConfirm;
import com.dlcg.tms.entity.CustomerGoodsCost;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import com.dlcg.tms.service.ICostAggregationService;
import com.dlcg.tms.service.ICustomerGoodsConfirmService;
import com.dlcg.tms.service.ICustomerGoodsCostService;
import com.dlcg.tms.service.IShipLineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/*
* 确认表变化影响货源费用
* */
@Component
public class CustomerGoodsConfirmListenerUpdateCost
        extends DBChangeBaseListenerSync<CustomerGoodsConfirmEvent> {

    @Autowired
    ICustomerGoodsConfirmService customerGoodsConfirmService;
    @Autowired
    IShipLineService shipLineService;
    @Autowired
    ICustomerGoodsCostService iCustomerGoodsCostService;
    @Autowired
    ICostAggregationService iCostAggregationService;


    @Override
    public void onInsert(JSONObject data) {
        String shipLineId = String.valueOf(data.getString("ship_line_id"));
        String customerGoodsId = String.valueOf(data.getString("customer_goods_id"));
        LambdaQueryWrapper<CustomerGoodsCost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerGoodsCost::getShiplineId,shipLineId);
        queryWrapper.eq(CustomerGoodsCost::getCustomerGoodsSourceId,customerGoodsId);
        List<CustomerGoodsCost> list = iCustomerGoodsCostService.list(queryWrapper);
        List<String> costIds = list.stream().map(CustomerGoodsCost::getId).collect(Collectors.toList());
        if(costIds.size()>0){
            LambdaQueryWrapper<CostAggregation> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.in(CostAggregation::getCustomerGoodsCostId,costIds);
            List<CostAggregation> costAggregations = iCostAggregationService.list(queryWrapper1);
            Map<String,CustomerGoodsCost> map = new HashMap<>();
            for(CustomerGoodsCost customerGoodsCost:list){
                map.put(customerGoodsCost.getId(),customerGoodsCost);
            }
            if(costAggregations != null && costAggregations.size()>0){
                for(CostAggregation costAggregation:costAggregations){
                    costAggregation.setShipLineId(shipLineId);
                    costAggregation.setSettleTonnage(map.get(costAggregation.getCustomerGoodsCostId()).getTonnage());
                    costAggregation.setSurplus(costAggregation.getSettleTonnage()*costAggregation.getPriceWithTax());
                    costAggregation.setTotalPrice(costAggregation.getSettleTonnage()*costAggregation.getPriceWithTax());
                }
                iCostAggregationService.updateBatchById(costAggregations);
            }
        }
    }

    @Override
    public void onDelete(JSONObject data) {
        CustomerGoodsConfirm customerGoodsConfirm = data.toJavaObject(CustomerGoodsConfirm.class);
        LambdaQueryWrapper<CustomerGoodsCost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerGoodsCost::getShiplineId,"");
        queryWrapper.eq(CustomerGoodsCost::getCustomerGoodsSourceId,customerGoodsConfirm.getCustomerGoodsId());
        List<CustomerGoodsCost> list = iCustomerGoodsCostService.list(queryWrapper);
        List<String> costIds = list.stream().map(CustomerGoodsCost::getId).collect(Collectors.toList());
        if(costIds.size()>0){
            LambdaQueryWrapper<CostAggregation> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.in(CostAggregation::getCustomerGoodsCostId,costIds);
            List<CostAggregation> costAggregations = iCostAggregationService.list(queryWrapper1);
            Map<String,CustomerGoodsCost> map = new HashMap<>();
            for(CustomerGoodsCost customerGoodsCost:list){
                map.put(customerGoodsCost.getId(),customerGoodsCost);
            }
            if(costAggregations != null && costAggregations.size()>0){
                for(CostAggregation costAggregation:costAggregations){
                    costAggregation.setShipLineId("");
                    costAggregation.setIsQueren(0);
                    costAggregation.setSettleTonnage(map.get(costAggregation.getCustomerGoodsCostId()).getTonnage());
                    costAggregation.setSurplus(costAggregation.getSettleTonnage()*costAggregation.getPriceWithTax());
                    costAggregation.setTotalPrice(costAggregation.getSettleTonnage()*costAggregation.getPriceWithTax());
                }
                iCostAggregationService.updateBatchById(costAggregations);
            }

        }
    }

    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        if(processIfDelFlagDefault(data,old)){
            return;
        }
        Integer oldkey = old.getInteger("queren");
        if(oldkey != null){
            CustomerGoodsConfirm customerGoodsConfirm = data.toJavaObject(CustomerGoodsConfirm.class);
            if(customerGoodsConfirm.getQueren() != 1){
                String shipLineId = String.valueOf(data.getString("ship_line_id"));
                String customerGoodsId = String.valueOf(data.getString("customer_goods_id"));
                LambdaQueryWrapper<CustomerGoodsCost> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CustomerGoodsCost::getShiplineId,shipLineId);
                queryWrapper.eq(CustomerGoodsCost::getCustomerGoodsSourceId,customerGoodsId);
                List<CustomerGoodsCost> list = iCustomerGoodsCostService.list(queryWrapper);
                List<String> costIds = list.stream().map(CustomerGoodsCost::getId).collect(Collectors.toList());
                if(costIds.size()>0){
                    LambdaQueryWrapper<CostAggregation> queryWrapper1 = new LambdaQueryWrapper<>();
                    queryWrapper1.in(CostAggregation::getCustomerGoodsCostId,costIds);
                    List<CostAggregation> costAggregations = iCostAggregationService.list(queryWrapper1);
                    Map<String,CustomerGoodsCost> map = new HashMap<>();
                    for(CustomerGoodsCost customerGoodsCost:list){
                        map.put(customerGoodsCost.getId(),customerGoodsCost);
                    }
                    if(costAggregations != null && costAggregations.size()>0){
                        for(CostAggregation costAggregation:costAggregations){
                            costAggregation.setIsQueren(1);
                            costAggregation.setSettleTonnage(map.get(costAggregation.getCustomerGoodsCostId()).getTonnage());
                            costAggregation.setSurplus(costAggregation.getSettleTonnage()*costAggregation.getPriceWithTax());
                            costAggregation.setTotalPrice(costAggregation.getSettleTonnage()*costAggregation.getPriceWithTax());
                        }
                        iCostAggregationService.updateBatchById(costAggregations);
                    }
                }
            } else {
                String shipLineId = String.valueOf(data.getString("ship_line_id"));
                String customerGoodsId = String.valueOf(data.getString("customer_goods_id"));
                LambdaQueryWrapper<CustomerGoodsCost> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CustomerGoodsCost::getShiplineId,shipLineId);
                queryWrapper.eq(CustomerGoodsCost::getCustomerGoodsSourceId,customerGoodsId);
                List<CustomerGoodsCost> list = iCustomerGoodsCostService.list(queryWrapper);
                List<String> costIds = list.stream().map(CustomerGoodsCost::getId).collect(Collectors.toList());
                if(costIds.size()>0){
                    LambdaQueryWrapper<CostAggregation> queryWrapper1 = new LambdaQueryWrapper<>();
                    queryWrapper1.in(CostAggregation::getCustomerGoodsCostId,costIds);
                    List<CostAggregation> costAggregations = iCostAggregationService.list(queryWrapper1);
                    Map<String,CustomerGoodsCost> map = new HashMap<>();
                    for(CustomerGoodsCost customerGoodsCost:list){
                        map.put(customerGoodsCost.getId(),customerGoodsCost);
                    }
                    if(costAggregations != null && costAggregations.size()>0){
                        for(CostAggregation costAggregation:costAggregations){
                            costAggregation.setIsQueren(0);
                            costAggregation.setSettleTonnage(map.get(costAggregation.getCustomerGoodsCostId()).getTonnage());
                            costAggregation.setSurplus(costAggregation.getSettleTonnage()*costAggregation.getPriceWithTax());
                            costAggregation.setTotalPrice(costAggregation.getSettleTonnage()*costAggregation.getPriceWithTax());
                        }
                        iCostAggregationService.updateBatchById(costAggregations);
                    }
                }
            }
        }
    }
}
