package com.dlcg.tms.dbchange.listener;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.oa.client.SysUserClient;
import com.dlcg.oa.entity.SysUser;
import com.dlcg.oa.service.SysDictionaryService;
import com.dlcg.tms.bean.UpdateDataBean;
import com.dlcg.tms.dbchange.CustomerGoodsCostDetailEvent;
import com.dlcg.tms.entity.*;
import com.dlcg.tms.enums.CostModuleSourceEnums;
import com.dlcg.tms.enums.OtherCostTypeEnums;
import com.dlcg.tms.kafka.DBChangeBaseListenerSync;
import com.dlcg.tms.service.*;
import com.dlcg.tms.util.GoodsCostDetailUpdateUtil;
import com.dlcg.tms.util.UpdateParams;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.Date;

/*
* 监听 货物费用表
* */
@Component
public class CustomerGoodsCostDetailListener
        extends DBChangeBaseListenerSync<CustomerGoodsCostDetailEvent> {

    @Autowired
    ISysSupplierService iSysSupplierService;
    @Autowired
    ICustomerGoodsCostService iCustomerGoodsCostService;
    @Autowired
    ICustomerGoodsConfirmService iCustomerGoodsConfirmService;
    @Autowired
    IShipLineService iShipLineService;
    @Autowired
    ICostAggregationService iCostAggregationService;
    @Autowired
    SysDictionaryService sysDictionaryService;
    @Autowired
    ICustomerGoodsSourceService iCustomerGoodsSourceService;
    @Autowired
    SysUserClient sysUserClient;
    @Autowired
    ISysCustomerSecondlevelService iSysCustomerSecondlevelService;
    @Autowired
    MongoTemplate mongoTemplate;

    @Override
    public void onInsert(JSONObject data) {
        CustomerGoodsCostDetail customerGoodsCostDetail = data.toJavaObject(CustomerGoodsCostDetail.class);
        CustomerGoodsCost customerGoodsCost = iCustomerGoodsCostService.getById(customerGoodsCostDetail.getCustomerGoodsCostId());
        LambdaQueryWrapper<CustomerGoodsSource> queryWrapper1 = new LambdaQueryWrapper<>();
        queryWrapper1.eq(CustomerGoodsSource::getId,customerGoodsCost.getCustomerGoodsSourceId());
        CustomerGoodsSource customerGoodsSource = iCustomerGoodsSourceService.getOne(queryWrapper1);
        SysUser sysUser = sysUserClient.getUserInfo(customerGoodsSource.getFindGoodsUser());
        CostAggregation costAggregation = new CostAggregation();
        costAggregation.setId(IdUtil.simpleUUID());
        costAggregation.setCustomerGoodsCostId(customerGoodsCostDetail.getCustomerGoodsCostId());
        costAggregation.setShipLineId(customerGoodsCost.getShiplineId());
        costAggregation.setCostPayId(customerGoodsCostDetail.getId());
        costAggregation.setIsPayment(customerGoodsCostDetail.getIncomeOut()==0?1:0);
        costAggregation.setCusSupId(customerGoodsCostDetail.getCompanyName());
        SysSupplier sysSupplier = iSysSupplierService.getById(customerGoodsCostDetail.getCompanyName());
        SysCustomerSecondlevel sysCustomerSecondlevel = iSysCustomerSecondlevelService.getById(customerGoodsCostDetail.getCompanyName());
        String cusSupNmae = "";
        Integer iscus = 0;
        if(sysSupplier != null){
            cusSupNmae = sysSupplier.getSimpleName();
            iscus = 0;
        } else {
            cusSupNmae = sysCustomerSecondlevel.getAbbreviationName();
            iscus = 1;
        }
        costAggregation.setCusSupName(cusSupNmae);
        costAggregation.setIsCus(iscus);
        String costType = "";
        String modelUpdateDetail = UpdateParams.UPDATE_DETAIL_MODEL;
        if(StringUtils.isNotBlank(customerGoodsCostDetail.getCostName())){
            costAggregation.setCostType(Integer.parseInt(customerGoodsCostDetail.getCostName()));
            modelUpdateDetail+=customerGoodsCostDetail.getCostName();
            if(customerGoodsCostDetail.getIncomeOut()==0){
                costType = sysDictionaryService.getDictionaryValue("cost_type",Integer.parseInt(customerGoodsCostDetail.getCostName()));
            } else {
                costType = sysDictionaryService.getDictionaryValue("cost_in",Integer.parseInt(customerGoodsCostDetail.getCostName()));
            }
        }
        costAggregation.setCostTypeName(costType);
        costAggregation.setSettleTonnage(customerGoodsCost.getTonnage());
        if(customerGoodsCostDetail.getPriceNo() != null){
            costAggregation.setPriceWithTax(customerGoodsCostDetail.getPriceNo().doubleValue());
            costAggregation.setPriceWithoutTax(customerGoodsCostDetail.getPrice().doubleValue());
        }
        if(StringUtils.isNotBlank(customerGoodsCostDetail.getInvoiceType())){
            costAggregation.setBillTax(sysDictionaryService.getDictionaryValue("bill_tax",Integer.parseInt(customerGoodsCostDetail.getInvoiceType())));
        }
        Double zong = 0.0;
        // 优先取合计
        if(customerGoodsCostDetail.getTotalPriceNo() != null ){
            zong = customerGoodsCostDetail.getTotalPriceNo().doubleValue();
        }
        if(zong==0){
            if ("1".equals(customerGoodsCostDetail.getCostName())){
                zong = Math.rint(customerGoodsCostDetail.getPriceNo().doubleValue()*customerGoodsCost.getTonnage());
            } else {
                zong = customerGoodsCostDetail.getPriceNo().doubleValue()*customerGoodsCost.getTonnage();
            }
        }
        costAggregation.setSurplus(zong);
        // 获取数据update_data
        Query queryUpdate=new Query();
        queryUpdate.addCriteria(Criteria.where("vaId").is(customerGoodsCostDetail.getId()));
        queryUpdate.addCriteria(Criteria.where("model").is(modelUpdateDetail));
        queryUpdate.addCriteria(Criteria.where("status").is("1"));
        UpdateDataBean updateYun = mongoTemplate.findOne(queryUpdate, UpdateDataBean.class, UpdateParams.UPDATE_DATA);
        if(updateYun!=null){
            costAggregation.setPayable(Double.parseDouble(updateYun.getVa()));
        }else{
            costAggregation.setPayable(0.0);
        }
        costAggregation.setTotalPrice(zong);
        costAggregation.setIsNeedApply(customerGoodsCostDetail.getShenpi());
        costAggregation.setCostModuleSource(CostModuleSourceEnums.GOODSMODULE.getKey());
        if("不开".equals(costAggregation.getBillTax())){
            costAggregation.setContractCompany(25);
            costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",25));
        } else {
            if(StringUtils.isNotBlank(customerGoodsCost.getContractCompany())){
                costAggregation.setContractCompany(Integer.parseInt(customerGoodsCost.getContractCompany()));
                costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",Integer.parseInt(customerGoodsCost.getContractCompany())));
            }
        }
        costAggregation.setBusinessUser(customerGoodsSource.getFindGoodsUser());
        costAggregation.setBusinessUserName(sysUser == null ? null : sysUser.getName());
        if(StringUtils.isBlank(customerGoodsCost.getShiplineId()) && !"null".equals(customerGoodsCost.getShiplineId())){
            costAggregation.setIsQueren(0);
            costAggregation.setIsOnAccount(0);
        } else {
//            LambdaQueryWrapper<CustomerGoodsConfirm> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(CustomerGoodsConfirm::getShipLineId,customerGoodsCost.getShiplineId());
//            queryWrapper.eq(CustomerGoodsConfirm::getCustomerGoodsId,customerGoodsCost.getCustomerGoodsSourceId());
//            CustomerGoodsConfirm customerGoodsConfirm = iCustomerGoodsConfirmService.getOne(queryWrapper);
            costAggregation.setIsQueren(1 - customerGoodsCost.getCostIsComplete());
            ShipLine shipLine = iShipLineService.getById(customerGoodsCost.getShiplineId());
            costAggregation.setIsOnAccount(shipLine.getTijaocaiwu());
        }
        costAggregation.setCreateBy(customerGoodsCostDetail.getCreateBy());
        costAggregation.setCreateDate(new Date());
        iCostAggregationService.save(costAggregation);
        if(updateYun!=null){
            // 修改mongo状态
            Update update=new Update();
            update.set("status","0");
            mongoTemplate.updateFirst(queryUpdate,update,UpdateParams.UPDATE_DATA);
        }
    }

    @Override
    public void onDelete(JSONObject data) {
        CustomerGoodsCostDetail customerGoodsCostDetail = data.toJavaObject(CustomerGoodsCostDetail.class);
        LambdaQueryWrapper<CostAggregation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostAggregation::getCostPayId,customerGoodsCostDetail.getId());
        iCostAggregationService.remove(queryWrapper);
    }

    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        if(processIfDelFlagDefault(data,old)){
            return;
        }
        CustomerGoodsCostDetail customerGoodsCostDetail = data.toJavaObject(CustomerGoodsCostDetail.class);
//        updateGoodsCostDetail(customerGoodsCostDetail);
        GoodsCostDetailUpdateUtil.updateGoodsCostDetail(customerGoodsCostDetail,iCustomerGoodsCostService,iCostAggregationService,iCustomerGoodsSourceService,sysUserClient,iSysSupplierService,iSysCustomerSecondlevelService,sysDictionaryService);
    }

//    private void updateGoodsCostDetail(CustomerGoodsCostDetail customerGoodsCostDetail){
//        CustomerGoodsCost customerGoodsCost = iCustomerGoodsCostService.getById(customerGoodsCostDetail.getCustomerGoodsCostId());
//        LambdaQueryWrapper<CostAggregation> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(CostAggregation::getCostPayId,customerGoodsCostDetail.getId());
//        CostAggregation costAggregation = iCostAggregationService.getOne(queryWrapper);
//        LambdaQueryWrapper<CustomerGoodsSource> queryWrapper1 = new LambdaQueryWrapper<>();
//        queryWrapper1.eq(CustomerGoodsSource::getId,customerGoodsCost.getCustomerGoodsSourceId());
//        CustomerGoodsSource customerGoodsSource = iCustomerGoodsSourceService.getOne(queryWrapper1);
//        SysUser sysUser = sysUserClient.getUserInfo(customerGoodsSource.getFindGoodsUser());
//
//        if(costAggregation != null){
//            Double price1 = costAggregation.getTotalPrice() ==null?0:costAggregation.getTotalPrice();
//            Double price2 = costAggregation.getSurplus()==null?0:costAggregation.getSurplus();
//            Double price = price1-price2;
//            costAggregation.setCustomerGoodsCostId(customerGoodsCostDetail.getCustomerGoodsCostId());
//            costAggregation.setShipLineId(customerGoodsCost.getShiplineId());
//            costAggregation.setCostPayId(customerGoodsCostDetail.getId());
//            costAggregation.setCusSupId(customerGoodsCostDetail.getCompanyName());
//            SysSupplier sysSupplier = iSysSupplierService.getById(customerGoodsCostDetail.getCompanyName());
//            SysCustomerSecondlevel sysCustomerSecondlevel = iSysCustomerSecondlevelService.getById(customerGoodsCostDetail.getCompanyName());
//            String cusSupNmae = "";
//            if(sysSupplier != null){
//                cusSupNmae = sysSupplier.getSimpleName();
//            } else {
//                cusSupNmae = sysCustomerSecondlevel.getAbbreviationName();
//            }
//            costAggregation.setCusSupName(cusSupNmae);
//            String costType = "";
//            if(StringUtils.isNotBlank(customerGoodsCostDetail.getCostName())){
//                costAggregation.setCostType(Integer.parseInt(customerGoodsCostDetail.getCostName()));
//                if(customerGoodsCostDetail.getIncomeOut()==0){
//                    costType = sysDictionaryService.getDictionaryValue("cost_type",Integer.parseInt(customerGoodsCostDetail.getCostName()));
//                } else {
//                    costType = sysDictionaryService.getDictionaryValue("cost_in",Integer.parseInt(customerGoodsCostDetail.getCostName()));
//                }
//            }
//            costAggregation.setCostTypeName(costType);
//            costAggregation.setSettleTonnage(customerGoodsCost.getTonnage());
//            costAggregation.setPriceWithTax(customerGoodsCostDetail.getPriceNo());
//            costAggregation.setPriceWithoutTax(customerGoodsCostDetail.getPrice().doubleValue());
//            if(StringUtils.isNotBlank(customerGoodsCostDetail.getInvoiceType())){
//                costAggregation.setBillTax(sysDictionaryService.getDictionaryValue("bill_tax",Integer.parseInt(customerGoodsCostDetail.getInvoiceType())));
//            }
//            Double zong = 0.0;
//            if ("1".equals(customerGoodsCostDetail.getCostName())){
//                zong = Math.rint(customerGoodsCostDetail.getPriceNo()*customerGoodsCost.getTonnage());
//            } else {
//                zong = customerGoodsCostDetail.getPriceNo()*customerGoodsCost.getTonnage();
//            }
//            costAggregation.setSurplus(zong-price);
//            costAggregation.setTotalPrice(zong);
//            costAggregation.setIsNeedApply(customerGoodsCostDetail.getShenpi());
//            if("不开".equals(costAggregation.getBillTax())){
//                costAggregation.setContractCompany(25);
//                costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",25));
//            } else {
//                if(StringUtils.isNotBlank(customerGoodsCost.getContractCompany())){
//                    costAggregation.setContractCompany(Integer.parseInt(customerGoodsCost.getContractCompany()));
//                    costAggregation.setContractCompanyName(sysDictionaryService.getDictionaryValue("contract_company",Integer.parseInt(customerGoodsCost.getContractCompany())));
//                }
//            }
//            costAggregation.setBusinessUser(customerGoodsSource.getFindGoodsUser());
//            costAggregation.setBusinessUserName(sysUser == null ? null : sysUser.getName());
//            costAggregation.setUpdateBy(customerGoodsCostDetail.getUpdateBy());
//            costAggregation.setUpdateDate(customerGoodsCostDetail.getUpdateDate());
//            iCostAggregationService.updateById(costAggregation);
//        }
//    }
}
