package com.dlcg.tms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dlcg.tms.entity.SysCustomerInvoice;
import com.dlcg.tms.entity.SysCustomerInvoicecontacts;
import com.dlcg.tms.mapper.SysCustomerInvoicecontactsMapper;
import com.dlcg.tms.service.ISysCustomerInvoicecontactsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-08
 */
@Service
public class SysCustomerInvoicecontactsServiceImpl extends ServiceImpl<SysCustomerInvoicecontactsMapper, SysCustomerInvoicecontacts> implements ISysCustomerInvoicecontactsService {

    @Override
    public SysCustomerInvoicecontacts getBySysCustomerInvoiceid(String sysCustomerInvoiceid) {
        QueryWrapper<SysCustomerInvoicecontacts> query = new QueryWrapper();
        query.eq("sys_customer_invoiceid",sysCustomerInvoiceid);
        query.eq("del_flag","0");
        List<SysCustomerInvoicecontacts> list = this.list(query);
        if(list!=null&&list.size()>0){
            return list.get(0);
        }else{
            return null;
        }
    }
}
