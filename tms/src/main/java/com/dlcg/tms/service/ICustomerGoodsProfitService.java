package com.dlcg.tms.service;

import com.dlcg.tms.bean.ProfitsSummaryBean;
import com.dlcg.tms.bean.StowageProfitByYearBean;
import com.dlcg.tms.entity.CustomerGoodsProfit;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-06
 */
public interface ICustomerGoodsProfitService extends IService<CustomerGoodsProfit> {

    List<StowageProfitByYearBean> getCustomerGoodsProfit();
    
    List<ProfitsSummaryBean> getProfitsByHuoyuan(Date start,Date end);
}
