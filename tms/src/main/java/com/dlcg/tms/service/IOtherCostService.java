package com.dlcg.tms.service;

import com.dlcg.tms.bean.OtherCostBean;
import com.dlcg.tms.bean.OtherCostStandingBookBean;
import com.dlcg.tms.entity.OtherCost;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dlcg.tms.mapper.OtherCostMapper;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
public interface IOtherCostService extends IService<OtherCost> {
    
    List<OtherCostBean> getOtherCostList(Integer type,Integer costType,String urole,String userId,String shiplineId);
    
    List<OtherCostStandingBookBean> getStandingBook(Date startTime,Date endTime);
}
