package com.dlcg.tms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.dlcg.tms.service.OcrService;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.ocr.v20181119.OcrClient;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.SmartStructuralOCRResponse;
import com.tencentcloudapi.ocr.v20181119.models.VatInvoiceOCRRequest;
import com.tencentcloudapi.ocr.v20181119.models.VatInvoiceOCRResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class OcrServiceImpl implements OcrService {

    @Value("${txcloud.secret.id}")
    String SecretId;
    @Value("${txcloud.secret.key}")
    String SecretKey;
    //    地域	取值
//    亚太东南(曼谷)	ap-bangkok
//    华北地区(北京)	ap-beijing
//    华南地区(广州)	ap-guangzhou
//    港澳台地区(中国香港)	ap-hongkong
//    亚太东北(首尔)	ap-seoul
//    华东地区(上海)	ap-shanghai
//    亚太东南(新加坡)	ap-singapore
//    北美地区(多伦多)	na-toronto
    @Value("${txcloud.region}")
    String Region;


    @Override
    public String invoiceOcrByImgUrl(String imgUrl) throws TencentCloudSDKException {
        if(StrUtil.isBlank(imgUrl)){
            return null;
        }
        Credential cred = new Credential(SecretId, SecretKey);
        OcrClient client = new OcrClient(cred, Region);
        VatInvoiceOCRRequest req = new VatInvoiceOCRRequest();
        req.setImageUrl(imgUrl);
        // 判断pdf
        if(imgUrl.endsWith(".pdf")){
            req.setIsPdf(true);
        }
        VatInvoiceOCRResponse resp = client.VatInvoiceOCR(req);
        return VatInvoiceOCRResponse.toJsonString(resp);
    }

    @Override
    public String textOcrByImgUrl(String imgUrl) throws TencentCloudSDKException {
        if(StrUtil.isBlank(imgUrl)){
            return null;
        }
        Credential cred = new Credential(SecretId, SecretKey);
//        // 实例化一个http选项，可选的，没有特殊需求可以跳过
//        HttpProfile httpProfile = new HttpProfile();
//        httpProfile.setEndpoint("ocr.tencentcloudapi.com");

//        // 实例化一个client选项，可选的，没有特殊需求可以跳过
//        ClientProfile clientProfile = new ClientProfile();
//        clientProfile.setHttpProfile(httpProfile);

        // 实例化要请求产品的client对象,clientProfile是可选的
//        OcrClient client = new OcrClient(cred, "ap-shanghai", clientProfile);
        OcrClient client = new OcrClient(cred, Region);


        // 实例化一个请求对象,每个接口都会对应一个request对象
        SmartStructuralOCRRequest req = new SmartStructuralOCRRequest();
        req.setImageUrl(imgUrl);
        // 判断pdf
        if(imgUrl.endsWith(".pdf")){
            req.setIsPdf(true);
        }
        // 返回的resp是一个SmartStructuralOCRResponse的实例，与请求对象对应
        SmartStructuralOCRResponse resp = client.SmartStructuralOCR(req);
        // 输出json格式的字符串回包
        return SmartStructuralOCRResponse.toJsonString(resp);
    }
}
