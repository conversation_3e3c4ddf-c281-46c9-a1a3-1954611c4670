package com.dlcg.tms.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.tms.entity.SysSupplierSimpleFull;
import com.dlcg.tms.mapper.SysSupplierSimpleFullMapper;
import com.dlcg.tms.service.ISysSupplierSimpleFullService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Service
public class SysSupplierSimpleFullServiceImpl extends ServiceImpl<SysSupplierSimpleFullMapper, SysSupplierSimpleFull> implements ISysSupplierSimpleFullService {

    @Override
    public void delBySimpleIdAndFullId(String simpleId, String fullId) {
        LambdaQueryWrapper<SysSupplierSimpleFull> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysSupplierSimpleFull::getSimpleId, simpleId);
        wrapper.eq(SysSupplierSimpleFull::getFullId, fullId);
        this.remove(wrapper);
    }
}
