package com.dlcg.tms.service.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(value = "caiwu")
@RequestMapping(value = "/restapi/contractclient")
public interface ContractClient {

    /**
     * 根据id(s)批量获取合同
     *
     * @param ids 合同id集合
     */
    @PostMapping("/queryContracts")
    List<Map> queryContracts(@RequestParam(value = "ids") String[] ids);

    @PostMapping("/getShipContractListPage")
    Map<String, Object> getShipContractListPage(@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                @RequestParam(value = "contractNo", required = false) String contractNo,
                                                @RequestParam(value = "partyAName", required = false) String partyAName,
                                                @RequestParam(value = "partyBName", required = false) String partyBName,
                                                @RequestParam(value = "contractModule", required = false) Integer contractModule,
                                                @RequestParam(value = "signStartDate", required = false) String signStartDate,
                                                @RequestParam(value = "signEndDate", required = false) String signEndDate,
                                                @RequestParam(value = "spStatus", required = false) Integer spStatus
    );
}
