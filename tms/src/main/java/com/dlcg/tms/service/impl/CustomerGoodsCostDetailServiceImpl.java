package com.dlcg.tms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dlcg.tms.bean.*;
import com.dlcg.tms.entity.CustomerGoodsCostDetail;
import com.dlcg.tms.mapper.CostAggregationMapper;
import com.dlcg.tms.mapper.CustomerGoodsCostDetailMapper;
import com.dlcg.tms.service.ICustomerGoodsCostDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-18
 */
@Service
public class CustomerGoodsCostDetailServiceImpl extends ServiceImpl<CustomerGoodsCostDetailMapper, CustomerGoodsCostDetail> implements ICustomerGoodsCostDetailService {

    @Autowired
    CustomerGoodsCostDetailMapper customerGoodsCostDetailMapper;
    @Autowired
    CostAggregationMapper costAggregationMapper;

    @Override
    public List<Hwszhibean2> feiyonglist() {
        return customerGoodsCostDetailMapper.feiyonglist();
    }

    @Override
    public List<Hwszhibean2> feiyonglistByCostIdType(String costId, String type) {
        return customerGoodsCostDetailMapper.feiyonglistByCostIdType(costId, type);
    }

    @Override
    public List<Hwszhibean2> feiyonglistByGoodsCostId(String goodsCostId) {
        return customerGoodsCostDetailMapper.feiyonglistByGoodsCostId(goodsCostId);
    }


    @Override
    public List<Hwszhibean2> feiyonglistByListId(List<String> cgcid) {
        return customerGoodsCostDetailMapper.feiyonglistByListId(cgcid);
    }

    @Override
    public List<PaymentApplyBean> getPaymentList(String sysSupplierId,Integer type) {
        return customerGoodsCostDetailMapper.getPaymentList(sysSupplierId,type);
    }

    @Override
    public List<GoodsDetailBusinessPayableBean> getGoodsDetailBusiness(String userid, Date startTime, Date endTime, Date startTime2, Date endTime2,String costtype,String name,String gname,Integer iftax,String shipName,Integer payer,String company,Integer accountingType, String departureDate,String departureStartDate,String sortby, String onAccountNum) {
        if(StrUtil.isNotBlank(sortby)){
            sortby = Arrays.stream(sortby.split("\\|")).map(s ->{
                if(s.endsWith("desc")){
                    // name,desc| namefwtw,asc
//                    return s.substring("")+" desc";
                    return s.substring(0,s.length()-5)+" desc";
                }else{
                    return s.substring(0,s.length()-4)+" asc";
                }
            }).collect(Collectors.joining(","));
        }
        List<GoodsDetailBusinessPayableBean> goodsDetailBusiness = customerGoodsCostDetailMapper
                .getGoodsDetailBusiness(userid, startTime, endTime, startTime2, endTime2, costtype, name, gname, iftax,
                        shipName,payer,company,accountingType,departureDate,departureStartDate,sortby, onAccountNum);
        List<String> costIds = goodsDetailBusiness.stream().map(GoodsDetailBusinessPayableBean::getCostId)
                .collect(Collectors.toList());
        if(costIds == null || costIds.size()==0){
            return goodsDetailBusiness;
        }
        List<MoneyInApplyBean> list = costAggregationMapper.getMoneyInApplyList(costIds);
        Map<String,Double> map = new HashMap<>();
        for(MoneyInApplyBean moneyInApplyBean:list){
            map.put(moneyInApplyBean.getCostId(),moneyInApplyBean.getMoney());
        }
        for(GoodsDetailBusinessPayableBean goodsDetailBusinessPayableBean:goodsDetailBusiness){
            goodsDetailBusinessPayableBean.setMoneyInApply(map.get(goodsDetailBusinessPayableBean.getCostId()));
        }
        return goodsDetailBusiness;
    }

    @Override
    public List<ShouMoneyAndTimeBean> getJinTime(List<String> ids) {
        return customerGoodsCostDetailMapper.getJinTime(ids);
    }

    @Override
    public List<ShouMoneyAndTimeBean> getFuTime(List<String> ids) {
        return customerGoodsCostDetailMapper.getFuTime(ids);
    }

    @Override
    public List<GoodsDetailForYearBean> getGoodsDetailForYear(String userid, Date startTime2, Date endTime2,Integer accountingType) {
        return customerGoodsCostDetailMapper.getGoodsDetailForYear(userid,startTime2,endTime2,accountingType);
    }
    
    @Override
    public List<CustomerGoodsCostDetailBean> getDetails(List<String> goodsCostIds,Integer incomeOut) {
        return customerGoodsCostDetailMapper.getDetails(goodsCostIds,incomeOut);
    }

    @Override
    public void setContactIdByDetailId(String detailId, String contractId,String userId) {
        LambdaUpdateWrapper<CustomerGoodsCostDetail> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CustomerGoodsCostDetail::getId,detailId);
        updateWrapper.set(CustomerGoodsCostDetail::getGoodsCostContractId,contractId);
        updateWrapper.set(CustomerGoodsCostDetail::getUpdateBy,userId);
        updateWrapper.set(CustomerGoodsCostDetail::getUpdateDate,new Date());
        this.update(updateWrapper);
    }


}
