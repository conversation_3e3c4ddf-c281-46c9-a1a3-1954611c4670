package com.dlcg.tms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dlcg.tms.entity.CountGlobal;
import com.dlcg.tms.mapper.CountGlobalMapper;
import com.dlcg.tms.service.ICountGlobalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dlcg.tms.util.ParamsKey;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Service
public class CountGlobalServiceImpl extends ServiceImpl<CountGlobalMapper, CountGlobal> implements ICountGlobalService {

    @Override
    public String getOnAccountNum(String accountType) {
        // 获取挂账号
        // 加1 是否成功，成功则返回，失败则重建并返回
        // 默认年月
        String year= LocalDate.now().getYear()+"";
        String month= LocalDate.now().getMonthValue()+"";
        if(month.length()==1){
            month="0"+month;
        }
        // 1 正常  2 过驳  3 垫料费收入，德邻陆港服务费支出
        String accountTypeStr=null;
        String beforeStr="";
       switch (accountType){
           case "1":
               accountTypeStr= ParamsKey.COUNTER_TYPE_NORMAL_ONACCOUNT;
//               beforeStr="";
               break;
           case "2":
               accountTypeStr= ParamsKey.COUNTER_TYPE_GB_ONACCOUNT;
               beforeStr="GB";
               break;
           case "3":
               accountTypeStr= ParamsKey.COUNTER_TYPE_QT_ONACCOUNT;
                beforeStr="QT";
               break;
           case "4":
               accountTypeStr= ParamsKey.COUNTER_TYPE_SD_ONACCOUNT;
               beforeStr="SD";
               break;
           case "5":
               accountTypeStr= ParamsKey.COUNTER_TYPE_QY_ONACCOUNT;
               beforeStr="QY";
               break;
         }
         if(StrUtil.isBlank(accountTypeStr)){
             return null;
         }
        String yearMonth=year+month;
        // 判断是否已确认结账
        Integer isConfirm = getCountByTypeAndTime(ParamsKey.COUNTER_TYPE_IS_CONFIRM,yearMonth);
        if(isConfirm!=null&&isConfirm>=1){
//            走下一月份
            month= LocalDate.now().plusMonths(1).getMonthValue()+"";
            if(month.length()==1){
                month="0"+month;
            }
            yearMonth=year+month;
        }
        Integer countNum=addCountByTypeAndTime(accountTypeStr,yearMonth);
        if(countNum==null){
            countNum=createCountByTypeAndTime(accountTypeStr,yearMonth);
        }
        if(countNum==null){
            return null;
        }
        String fmtApp="%03d";// 补位
        String countNumStr=String.format(fmtApp,countNum);
        return beforeStr+month+countNumStr;
    }

    @Override
    public void confirmCount() {
        String year= LocalDate.now().getYear()+"";
        String month= LocalDate.now().getMonthValue()+"";
        if(month.length()==1){
            month="0"+month;
        }
        String yearMonth=year+month;
        // 判断是否已确认结账
        Integer isConfirm = getCountByTypeAndTime(ParamsKey.COUNTER_TYPE_IS_CONFIRM,yearMonth);
        if(isConfirm!=null&&isConfirm>=1){
            return;
        }
        // 确认结账
        LambdaUpdateWrapper<CountGlobal> queryWrapper=new LambdaUpdateWrapper<>();
        queryWrapper.eq(CountGlobal::getType,ParamsKey.COUNTER_TYPE_IS_CONFIRM);
        queryWrapper.eq(CountGlobal::getCountTime,yearMonth);
        queryWrapper.isNull(CountGlobal::getDelTime);
        CountGlobal countGlobal=this.getOne(queryWrapper,false);
        if(countGlobal==null){
            countGlobal=new CountGlobal();
            countGlobal.setType(ParamsKey.COUNTER_TYPE_IS_CONFIRM);
            countGlobal.setCountTime(yearMonth);
            countGlobal.setCountNum(1);
            this.save(countGlobal);
        }else{
            countGlobal.setCountNum(1);
            this.updateById(countGlobal);
        }
    }

    @Override
    public void cancelConfirmCount() {
        String year= LocalDate.now().getYear()+"";
        String month= LocalDate.now().getMonthValue()+"";
        if(month.length()==1){
            month="0"+month;
        }
        String yearMonth=year+month;
        // 判断是否已确认结账
        Integer isConfirm = getCountByTypeAndTime(ParamsKey.COUNTER_TYPE_IS_CONFIRM,yearMonth);
        if(isConfirm==null||isConfirm<=0){
            return;
        }
        // 确认结账
        LambdaUpdateWrapper<CountGlobal> queryWrapper=new LambdaUpdateWrapper<>();
        queryWrapper.eq(CountGlobal::getType,ParamsKey.COUNTER_TYPE_IS_CONFIRM);
        queryWrapper.eq(CountGlobal::getCountTime,yearMonth);
        queryWrapper.isNull(CountGlobal::getDelTime);
        CountGlobal countGlobal=this.getOne(queryWrapper,false);
        if(countGlobal!=null){
            countGlobal.setCountNum(0);
            this.updateById(countGlobal);
        }
    }

    @Override
    public Integer confirmCountStatus() {
        String year= LocalDate.now().getYear()+"";
        String month= LocalDate.now().getMonthValue()+"";
        if(month.length()==1){
            month="0"+month;
        }
        String yearMonth=year+month;
        // 判断是否已确认结账
        Integer isConfirm = getCountByTypeAndTime(ParamsKey.COUNTER_TYPE_IS_CONFIRM,yearMonth);
        if(isConfirm==null||isConfirm<=0){
            return 0;
        }
        return 1;
    }

    private Integer getCountByTypeAndTime(String accountTypeStr,String yearMonth){
        // 获取当前类型的当前月份的计数
        LambdaQueryWrapper<CountGlobal> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(CountGlobal::getType,accountTypeStr);
        queryWrapper.eq(CountGlobal::getCountTime,yearMonth);
        queryWrapper.isNull(CountGlobal::getDelTime);
        CountGlobal countGlobal=this.getOne(queryWrapper,false);
        if(countGlobal==null){
            return null;
        }
        return countGlobal.getCountNum();
    }
    private Integer addCountByTypeAndTime(String accountTypeStr,String yearMonth){
        // 获取当前类型的当前月份的计数
        LambdaUpdateWrapper<CountGlobal> queryWrapper=new LambdaUpdateWrapper<>();
        queryWrapper.eq(CountGlobal::getType,accountTypeStr);
        queryWrapper.eq(CountGlobal::getCountTime,yearMonth);
        queryWrapper.isNull(CountGlobal::getDelTime);
        queryWrapper.setSql("count_num=count_num+1");
        boolean update=this.update(queryWrapper);
        if(update){
            return getCountByTypeAndTime(accountTypeStr,yearMonth);
        }
        return null;
    }

    private Integer createCountByTypeAndTime(String accountTypeStr,String yearMonth){
        // 获取当前类型的当前月份的计数
        CountGlobal countGlobal=new CountGlobal();
        countGlobal.setType(accountTypeStr);
        countGlobal.setCountTime(yearMonth);
        countGlobal.setCountNum(1);
        boolean save=this.save(countGlobal);
        if(save){
            return 1;
        }
        return null;
    }

}
