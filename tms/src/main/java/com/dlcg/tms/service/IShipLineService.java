package com.dlcg.tms.service;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dlcg.tms.bean.*;
import com.dlcg.tms.entity.GaizhangFeiyong;
import com.dlcg.tms.entity.ShipLine;
import com.dlcg.tms.entity.ShipLineHandover;
import com.dlcg.tms.entity.ShipPort;
import com.dlcg.tms.model.dto.StandingBookSyncDataDto;
import com.dlcg.tms.model.vo.ShipLineVo;
import com.dlcg.tms.service.client.bean.CaiwuContractBean;
import com.dlcg.tms.vo.*;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-16
 */
public interface IShipLineService extends IService<ShipLine> {


    List<ShipLineVo> getShipLineList();

    /**
     * 查询船期详细信息列表，
     * @return
     */
    Page<ShipLineBean> getShipLineDetail(Boolean isShowVirtual,Date startTime, Date endTime, Date shipLiGangTime,
                                         String voyageNumber, String shipName,
                                         String findShipUser, Integer businessType,Integer linetype,
                                         Integer pageNo, Integer pageSize,String sysShipLineId,Integer notEqPreStatus,String orderby,Integer contractCode,Integer selectStatus,String month);

    Page<ShipLineBean> getShipLineDetail1(Boolean isShowVirtual,Date startTime, Date endTime, Date shipLiGangTime,
                                         String voyageNumber, String shipName,
                                         String findShipUser, Integer businessType,Integer linetype,
                                         Integer pageNo, Integer pageSize,String sysShipLineId,Integer notEqPreStatus,String orderby,Integer contractCode,Boolean sguazhang,Integer guatype);

    List<ShipLineBean> getShipLineList(String sysShiplineId);

    List<ShipLineBean> getAutoShipLine(Date now,Date startTime,Date endTime,String lineid);

    List<SelectShipLineBean> getShipLineSelect(String inputStr);

    List<ChuanqizhanshiBean> getluruchuanqi(Integer linetype,Date startshipTime ,Date endshipTime,Integer queren,Integer querentype,String name, Date listartshipTime ,Date liendshipTime,String startPortName,String endPortName,String findUserName);

    List<ChuanqizhanshiBean> getluruchuanqidian(Integer linetype,Date startshipTime ,Date endshipTime,Integer queren,Integer querentype,String shipName);

    Page<ShipLineHandoverBean> getList(String shipName,String shiplineId,Integer pageNo,Integer pageSize);

    ShipLineHandoverBean getHandoverDetail(String shiplineId);

    List<StandingBookSyncDataDto> getSyncDataList(LocalDate startday);

    List<ReceiveApplyBean> getShipFuWu(String sysSupplierId);

    List<ShipLineContractBean> getDingjinList();

    ShipLineMessageBean getShiplinemessage(String shipLineId);

    List<GangKouBean> getportbyid (String shipLineId);

    List<ShipLine> getShipLineByShipName(String name);

    List<YewuShipLineBean> getYewuShipLine ( Date startTime,Date endTime,String shipName);

    YewuShipLineBean getYewuShipLineById(String shipLineId);
    
    ShowShipLineBean getShipLineBean(String shipLineId);
    ShipLineCharteringInventoryVo getCharteringinventoryById(String shipLineId);

    List<ShipLineBean> getShipLineListByShipLineIds(List<String> shipLineIds);
    
    Page<ShipLineBean> getShipLineStowageDetail(Date shipLiGangTime, String shipName,
            Integer pageNo, Integer pageSize,String sysShipLineId,Integer selectStatus,String month,Double floatTon,String danzhengUser,String kehuName,String orderSt,String currentUserId,Integer guanzhangStatus,Date startTime,Date endTime);

    Page<ShipLineBean> shipLinePageByStatus(String shipChuanQiTime, String shipName, String status, Integer pageNum, Integer pageSize);
    String getChartererWxUserIdByShipLineId(String shipLineId);

    String getGenVoyageNumber(Integer linetype);

    List<ShipLineBean> isDaiWanShanShipLineByShipLineId(String shipLineId);

    Page<ShipLineBean> getShipLineByShipNameAndShipDate(String keyword,Integer pageNo,Integer pageSize);

    List<FullShipLineInfo> getFullShiplineInfo(String shipLineId);

    Boolean getIsCompleteShipByFundId(String fundId);

    List<Map<String,Object>> getIsCompleteShipByFundIds(List<String> fundIds);
    // 统计 客户
    CompletionMonthVo getCompletionMonth(String year,String month);
    // 统计 经营
    CompletionOperatingVo getCompletionOperating(String year,String month);

    List<CompletionShipLineVo> getCompletionShipLineAbbAg(String year, String month, String goodsType);
    List<CompletionShipLineVo> getCompletionShipLineAbbBg(String year,String month,String goodsType);
    List<CompletionShipLineVo> getCompletionShipLineAbbOther(String year,String month,String goodsType);

    Map<String,List<CompletionShipLineVo>> tjNewHangXian(String year, String month);

    List<CapacityVo> tjCapacity(String year, String month);

    List<CustomerVo> tjMainCustomer(String year, String month);

    List<ReceivableVo> tjYearMonthReceivable(String year);

    List<ReceivableDetailVo> tjReceivableDetail(String year, String month, String customerId,String salesmanId,String secondCustomerId,String costType,String invoiceStatus,String onAccountNum,String startYearMonth,String endYearMonth);

    boolean isDeleteShipLine(String shipLineId,String userId);

    void ship_unberthing(ShipStatusSubscriptionVo shipStatusSubscriptionVo);

    String areaNameByPortName(String portNames);
    String areaNameByPortName(String portNames,JSONArray ja);


    /**
     * 获取船期的所有改账费用
     * @param shipLineId
     * @param userid
     * @return
     */
    GaizhangFeiyong getAllFeiyong(String shipLineId,String userid);

    /**
     * 根据船期下所有改账费用 判断是否都在审批中
     * @return
     */
    boolean checkAllFeiyongAudit(String shipLineId,String userid);


    /**
     * 补充船舶合同展示所需信息
     * @param list 船舶合同ID列表
     */
    List<CaiwuContractBean> queryShipContractAdditionInfo(List<String> list);

    List<CostAggregationBean> getPaymentListByShipName(String shipName,String yearMonth);
}
