package com.dlcg.tms.service.impl;

import com.dlcg.tms.entity.CheckCanal;
import com.dlcg.tms.mapper.CheckCanalMapper;
import com.dlcg.tms.service.ICheckCanalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.netflix.discovery.converters.Auto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-19
 */
@Service
public class CheckCanalServiceImpl extends ServiceImpl<CheckCanalMapper, CheckCanal> implements ICheckCanalService {

	@Autowired
	CheckCanalMapper checkCanalMapper;


	@Override
	public Date selectMaxTime() {
		return checkCanalMapper.selectMaxTime();
	}

	@Override
	public void deleteAll() {
		checkCanalMapper.deleteAll();
	}
}
