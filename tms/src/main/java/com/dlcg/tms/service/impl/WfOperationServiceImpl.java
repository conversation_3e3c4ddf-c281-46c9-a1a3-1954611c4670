package com.dlcg.tms.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dlcg.oa.bean.AdminUser;
import com.dlcg.tms.bean.WfLinkBean;
import com.dlcg.tms.bean.WfWorkflowBean;
import com.dlcg.tms.entity.WfLink;
import com.dlcg.tms.entity.WfOperation;
import com.dlcg.tms.entity.WfWorkflow;
import com.dlcg.tms.mapper.WfOperationMapper;
import com.dlcg.tms.service.IWfLinkService;
import com.dlcg.tms.service.IWfOperationService;
import com.dlcg.tms.service.IWfWorkflowService;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.tms.util.ReflectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-07
 */
@Slf4j
@Service
public class WfOperationServiceImpl extends ServiceImpl<WfOperationMapper, WfOperation> implements IWfOperationService {

    @Autowired
    IWfWorkflowService iWfWorkflowService;
    @Autowired
    IWfLinkService iWfLinkService;

    @Override
    public boolean haveApply(String wfkey) {

        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        QueryWrapper<WfWorkflow> wkq = new QueryWrapper<>();
        wkq.eq("del_flag","0");
        wkq.eq("workflow_key",wfkey);
        WfWorkflow wfWorkflow = iWfWorkflowService.getOne(wkq);

        QueryWrapper<WfLink> linkq = new QueryWrapper<>();
        linkq.eq("del_flag","0");
        linkq.eq("workflow_id",wfWorkflow.getId());
        linkq.orderByAsc("link_index");

        List<WfLink> wfLinkList = iWfLinkService.list(linkq);

        WfLink first = wfLinkList.get(0);

        return checkRole(first.getRoleIds(),adminUser.getRoleIds());
    }


    @Override
    public <T> void setLinkBean(String wfkey, List<T> list, String keyname,String beanname) {
        if(list==null||list.size()<=0){
            log.info("数据列表为空！未处理，，，");
            return;
        }
        long start = System.currentTimeMillis();
        List<String> keys = new ArrayList<>();
        for(int i=0;i<list.size();i++){
            T tmp = list.get(i);
            String key = (String)ReflectUtil.getProperty(tmp,keyname);
            keys.add(key);
        }
        QueryWrapper<WfWorkflow> wkq = new QueryWrapper<>();
        wkq.eq("del_flag","0");
        wkq.eq("workflow_key",wfkey);
        WfWorkflow wfWorkflow = iWfWorkflowService.getOne(wkq);

        QueryWrapper<WfLink> linkq = new QueryWrapper<>();
        linkq.eq("del_flag","0");
        linkq.eq("workflow_id",wfWorkflow.getId());
        linkq.orderByAsc("link_index");
        List<WfLink> wfLinkList = iWfLinkService.list(linkq);
        Map<String,WfLink> wfLinkMap = wfLinkList.stream().collect(Collectors.toMap(WfLink::getId, m -> m, (k1, k2) -> k1));



        QueryWrapper<WfOperation> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("tag_id",keys);
        queryWrapper.eq("del_flag","0");
        List<WfOperation> wfOperations = this.list(queryWrapper);
        Map<String,WfOperation> wfOperationMap = wfOperations.stream().collect(Collectors.toMap(WfOperation::getTagId, m -> m, (k1, k2) -> k1));

        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();



        for(int i=0;i<list.size();i++){
            T tmp = list.get(i);
            String key = keys.get(i);
            WfOperation wfOperation = wfOperationMap.get(key);

            WfLink clink = null;
            if(wfOperation==null){
                if(wfLinkList.size()>=2){
                    clink = wfLinkList.get(1);
                }else{
                    clink = null;
                }

            }else{
                String lid = wfOperation.getCurrentWfLink();
                if("0".equals(lid)){//环节已经执行完成
                    clink = null;
                }else{
                    clink = wfLinkMap.get(lid);
                }

            }


            WfWorkflowBean bean = new WfWorkflowBean();

            if(clink==null){
                bean.setCurrentLinkName("已结束");
                bean.setHaverole(false);
                bean.setEnd(true);
            }else{
                bean.setCurrentLinkName(clink.getName());
                bean.setHaverole(checkRole(clink.getRoleIds(),adminUser.getRoleIds()));
                bean.setOperationType(clink.getOperationType());
                bean.setEnd(false);
            }

            ReflectUtil.setProperty(tmp,beanname,bean);
        }

        long ofset = System.currentTimeMillis() - start;
        log.info("设置工作流耗时："+ofset);
    }
    private boolean checkRole(String roleenname, Set<String> roles){
        String [] rs = roleenname.split(",");
        if(roleenname!=null&&roleenname.length()>0){
            for(int i = 0;i<rs.length;i++){
                if(roles.contains(rs[i])){
                    return true;
                }
            }
        }
        return false;
    }



    @Override
    public void operation(String wfkey, String tagId, String status,String remarks) {
        QueryWrapper<WfWorkflow> wkq = new QueryWrapper<>();
        wkq.eq("del_flag","0");
        wkq.eq("workflow_key",wfkey);
        WfWorkflow wfWorkflow = iWfWorkflowService.getOne(wkq);

        QueryWrapper<WfLink> linkq = new QueryWrapper<>();
        linkq.eq("del_flag","0");
        linkq.eq("workflow_id",wfWorkflow.getId());
        linkq.orderByAsc("link_index");
        List<WfLink> wfLinkList = iWfLinkService.list(linkq);
        Map<String,WfLink> wfLinkMap = wfLinkList.stream().collect(Collectors.toMap(WfLink::getId, m -> m, (k1, k2) -> k1));

        QueryWrapper<WfOperation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tag_id",tagId);
        queryWrapper.eq("del_flag","0");
        WfOperation wfOperation = this.getOne(queryWrapper);

        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        WfLink clink = null;
        if(wfOperation==null){
            if(wfLinkList.size()>=2){
                clink = wfLinkList.get(1);
            }else{
                clink = null;
            }

            WfLinkBean b = null;
            if(clink!=null){
                if("1".equals(status)){
                    b = getNextOrPre(wfLinkList,clink.getId(),1);
                }else{
                    b = getNextOrPre(wfLinkList,clink.getId(),0);
                }
            }
            if(b!=null){
                wfOperation = new WfOperation();
                wfOperation.setId(IdUtil.simpleUUID());
                wfOperation.setTagId(tagId);
                wfOperation.setCurrentWfLink(b.getWfLink().getId());
                wfOperation.setCreateBy(adminUser.getId());
                wfOperation.setCreateDate(new Date());
                wfOperation.setIsEnd(b.isEnd()?1:0);
                this.save(wfOperation);
            }else {
                if("1".equals(status)){//开始执行下一步，已经无下一环节
                    wfOperation = new WfOperation();
                    wfOperation.setId(IdUtil.simpleUUID());
                    wfOperation.setTagId(tagId);
                    wfOperation.setCurrentWfLink("0");
                    wfOperation.setCreateBy(adminUser.getId());
                    wfOperation.setCreateDate(new Date());
                    wfOperation.setIsEnd(1);
                    this.save(wfOperation);
                }
            }



        }else{
            String lid = wfOperation.getCurrentWfLink();
            clink = wfLinkMap.get(lid);
            WfLinkBean b = null;
            if("1".equals(status)){
                b = getNextOrPre(wfLinkList,clink.getId(),1);
            }else{
                b = getNextOrPre(wfLinkList,clink.getId(),0);
            }
            if(b!=null){
                wfOperation.setCurrentWfLink(b.getWfLink().getId());
                wfOperation.setIsEnd(b.isEnd()?1:0);
                wfOperation.setUpdateBy(adminUser.getId());
                wfOperation.setUpdateDate(new Date());
                this.updateById(wfOperation);
            }else {
                if("1".equals(status)){//开始执行下一步，已经无下一环节
                    wfOperation.setCurrentWfLink("0");
                    wfOperation.setIsEnd(1);
                    wfOperation.setUpdateBy(adminUser.getId());
                    wfOperation.setUpdateDate(new Date());
                    this.updateById(wfOperation);
                }
            }
        }


    }

    /**
     *
     * @param wfLinkList
     * @param type 0 获得上一步，1，获得下一步
     * @return
     */
    private WfLinkBean getNextOrPre(List<WfLink> wfLinkList, String currentId, int type){
        if(wfLinkList==null||wfLinkList.size()<=0){
            return null;
        }

        for(int i=0;i<wfLinkList.size();i++){
            WfLink t = wfLinkList.get(i);
            if(currentId.equals(t.getId())){
                if(type==0){//获得上一步
                    int stepi = i-1;
                    return getLB(wfLinkList,stepi);
                }else{//获得下一步
                    int stepi = i+1;
                    return getLB(wfLinkList,stepi);
                }
            }
        }
        return null;

    }
    private WfLinkBean getLB(List<WfLink> wfLinkList,int idx){
        WfLinkBean bean = new WfLinkBean();
        if(idx<0||idx>=wfLinkList.size()){
            return null;
        }
        WfLink k = wfLinkList.get(idx);
        bean.setWfLink(k);
        if(idx==0){
            bean.setFirst(true);
        }else if(idx == (wfLinkList.size()-1)  ){
            bean.setEnd(true);
        }
        return bean;
    }

}
