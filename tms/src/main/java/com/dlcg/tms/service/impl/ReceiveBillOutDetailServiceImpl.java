package com.dlcg.tms.service.impl;

import com.dlcg.tms.bean.ReceiveProcessBean;
import com.dlcg.tms.entity.ReceiveBillOutDetail;
import com.dlcg.tms.mapper.ReceiveBillOutDetailMapper;
import com.dlcg.tms.service.IReceiveBillOutDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
@Service
public class ReceiveBillOutDetailServiceImpl extends ServiceImpl<ReceiveBillOutDetailMapper, ReceiveBillOutDetail> implements IReceiveBillOutDetailService {
    
    @Autowired
    ReceiveBillOutDetailMapper receiveBillOutDetailMapper;
    @Override
    public List<ReceiveProcessBean> getProcessList(String costId, Integer isReceive) {
        return receiveBillOutDetailMapper.getProcessList(costId,isReceive);
    }


    @Override
    public List<ReceiveBillOutDetail> getDetailByReceiveWaterId(String receiveWaterId) {
        return receiveBillOutDetailMapper.getDetailByReceiveWaterId(receiveWaterId);
    }


}
