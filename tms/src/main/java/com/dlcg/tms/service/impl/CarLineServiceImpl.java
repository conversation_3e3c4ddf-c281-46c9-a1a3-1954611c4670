package com.dlcg.tms.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.tms.bean.CarStatisticsBean;
import com.dlcg.tms.bean.ShipLineBean;
import com.dlcg.tms.entity.*;
import com.dlcg.tms.enums.CarLineAuditStatusEnums;
import com.dlcg.tms.enums.CostModuleSourceEnums;
import com.dlcg.tms.enums.CostNameEnums;
import com.dlcg.tms.enums.CostTypeNameEnums;
import com.dlcg.tms.mapper.CarLineFlowMapper;
import com.dlcg.tms.mapper.CarLineMapper;
import com.dlcg.tms.service.ICarLineFlowService;
import com.dlcg.tms.service.ICarLineService;
import com.dlcg.tms.service.ICostAggregationService;
import com.dlcg.tms.service.ISysDictionaryService;
import com.dlcg.tms.util.BigNumChain;
import com.dlcg.tms.util.SendUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.xml.crypto.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【car_line(车的承运)】的数据库操作Service实现
* @createDate 2023-05-25 17:04:03
*/
@Service
public class CarLineServiceImpl extends ServiceImpl<CarLineMapper, CarLine>
    implements ICarLineService{
    @Autowired
    CarLineMapper carLineMapper;
    @Autowired
    ICostAggregationService iCostAggregationService;
    @Autowired
    ISysDictionaryService iSysDictionaryService;
    @Autowired
    SysUserServiceImpl sysUserService;
    @Autowired
    ICarLineFlowService iCarLineFlowService;
    @Autowired
    CarLineFlowMapper carLineFlowMapper;



    @Override
    public Page<CarLine> getPageDate(Integer pageNo, Integer pageSize, String shipName,Integer status,
                                     String danzhengUser,
                                     Date shipJinGangTime) {
        Page<CarLine> page = PageHelper.startPage(pageNo,pageSize).doSelectPage(
                ()->carLineMapper.getPageDate(shipName,status,danzhengUser, shipJinGangTime)
        );

        for (CarLine carLine :page.getResult() ) {
            carLine.setAuditStatusName(CarLineAuditStatusEnums.getDescByKey(carLine.getAuditStatus()));
        }
        return page;
    }
    @Override
    public String getGenVoyageNumber(Integer linetype) {
        if (linetype == null) {
            linetype = 0;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date d = new Date();
        String pix = sdf.format(d);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM");
        String month = simpleDateFormat.format(d);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, Integer.parseInt(month) - 1);

        int firstDay = calendar.getActualMinimum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DAY_OF_MONTH, firstDay);
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        String begin = sdf1.format(calendar.getTime()) + " 00:00:00";

        int lastDay = 0;
        if (Integer.parseInt(month) == 2) {
            lastDay = calendar.getLeastMaximum(Calendar.DAY_OF_MONTH);
        } else {
            lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        }
        calendar.set(Calendar.DAY_OF_MONTH, lastDay);
        String end = sdf1.format(calendar.getTime()) + " 23:59:59";

        QueryWrapper<CarLine> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("voyage_number");
        queryWrapper.eq("del_flag", 0);
        queryWrapper.ge("create_date", begin);
        queryWrapper.ge("length(voyage_number)", 8);
        queryWrapper.le("create_date", end);
        queryWrapper.ne("create_by", "system_init");
        List<CarLine> list = this.list(queryWrapper);

        int idx = 1;
        if (list != null && list.size() > 0) {
            String vn = list.get(0).getVoyageNumber();
            if (vn != null && vn.trim().length() > 0) {
                try {
                    String st = vn.substring(8);
                    Integer oldidx = Integer.valueOf(st);
                    idx = oldidx + 1;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        DecimalFormat df = new DecimalFormat("000");
        String vnnum = pix + df.format(idx);
        return vnnum;
    }

    @Override
    public void addIncome(String car_line_id) {

        String currentUserId = CurrentUserInfoAdmin.getAdminUserValue().getId();
        CarLine carLine = this.getById(car_line_id);

        CostAggregation costAggregation = new CostAggregation();

        //costAggregation.setCustomerGoodsCostId();
        costAggregation.setShipLineId(car_line_id);
        //costAggregation.setCostPayId();
        costAggregation.setIsPayment(0);

        costAggregation.setIsCus(1);

        costAggregation.setCostType(CostTypeNameEnums.QIYUN.getKey());
        costAggregation.setCostTypeName(CostTypeNameEnums.QIYUN.getDesc());

        costAggregation.setSettleTonnage(carLine.getContractTunnage());


        costAggregation.setIsOnAccount(0);
        costAggregation.setIsNeedApply(0);
        costAggregation.setGoodsType(carLine.getGoodsType());
        if(carLine.getGoodsType() != null){
            costAggregation.setGoodsTypeName(iSysDictionaryService.getGoodsType(carLine.getGoodsType()).getValue());
        }
        costAggregation.setCreateBy(currentUserId);
        costAggregation.setCreateDate(new Date());
        costAggregation.setUpdateDate(new Date());
        costAggregation.setCostModuleSource(CostModuleSourceEnums.CARMODULE.getKey());
        if(carLine.getContractCode() != null){
            costAggregation.setContractCompany(carLine.getContractCode());
            SysDictionary contractCompany = iSysDictionaryService.getContract(carLine.getContractCode());
            costAggregation.setContractCompanyName(contractCompany.getValue());
        }
        costAggregation.setBusinessUser(carLine.getYewuUser());
        SysUser sys_user = sysUserService.getById(carLine.getYewuUser());
        costAggregation.setBusinessUserName(sys_user.getName());
        costAggregation.setBusinessType(20);



        //客户1
        CostAggregation one = iCostAggregationService.lambdaQuery()
                .eq(CostAggregation::getShipLineId, car_line_id)
                .eq(CostAggregation::getCusSupId, carLine.getCustomerId())
                .ne(CostAggregation::getBillTax, "不开")
                .one();
        if(one != null){
            costAggregation.setId(one.getId());
        }else {
            costAggregation.setId(IdUtil.simpleUUID());
            costAggregation.setIsQueren(1);
        }
        costAggregation.setCusSupId(carLine.getCustomerId());
        costAggregation.setCusSupName(carLine.getCustomerName());
        //计算单价
        carLine.setContractPrice(BigNumChain.divide(carLine.getContractMoney(),carLine.getContractTunnage()).doubleValue());
        costAggregation.setPriceWithTax( carLine.getContractPrice());
        double priceWithoutTax = carLine.getContractPrice();
        String paybillstr = "不开";
        if(carLine.getPayBill() != null){
            SysDictionary sysDictionary = iSysDictionaryService.getBillTax(carLine.getPayBill());
            if(sysDictionary != null){
                paybillstr = sysDictionary.getValue();
            }
            if(sysDictionary.getValue().equals("不开") || sysDictionary.getValue().equals("0%")){

            }else {
                String tax = sysDictionary.getValue().replace("%","");
                Integer tax_d = 100 -  Integer.parseInt(tax);
                Double b = BigNumChain.startOf(carLine.getContractPrice())
                        .multiply(new BigDecimal(tax_d)).getDouble();
                priceWithoutTax =  BigNumChain.startOf(b).divide(100).getDouble();
            }
        }
        costAggregation.setPriceWithoutTax( priceWithoutTax);
        costAggregation.setBillTax(paybillstr);
        costAggregation.setSurplus(carLine.getContractMoney());
        costAggregation.setTotalPrice(carLine.getContractMoney());
        iCostAggregationService.saveOrUpdate(costAggregation);
        //处理不开的金额
        if(carLine.getTwoCustomerId() == null || StringUtils.isEmpty(carLine.getTwoCustomerId())) {
            if (carLine.getContractNoBillMoney() != null && carLine.getContractNoBillMoney() > 0) {
                one = iCostAggregationService.lambdaQuery()
                        .eq(CostAggregation::getShipLineId, car_line_id)
                        .eq(CostAggregation::getBillTax, "不开")
                        .eq(CostAggregation::getCusSupId, carLine.getCustomerId())
                        .one();
                if (one != null) {
                    costAggregation.setId(one.getId());
                } else {
                    costAggregation.setId(IdUtil.simpleUUID());
                    costAggregation.setIsQueren(1);
                }
                costAggregation.setCusSupId(carLine.getCustomerId());
                costAggregation.setCusSupName(carLine.getCustomerName());
                Double bukai = BigNumChain.divide(carLine.getContractNoBillMoney(), carLine.getContractTunnage()).doubleValue();
                costAggregation.setPriceWithTax(bukai);
                costAggregation.setPriceWithoutTax(bukai);
                costAggregation.setBillTax("不开");
                costAggregation.setSurplus(carLine.getContractNoBillMoney());
                costAggregation.setTotalPrice(carLine.getContractNoBillMoney());
                iCostAggregationService.saveOrUpdate(costAggregation);
            }
        }


        //客户2
        if(carLine.getTwoCustomerId() != null && !StringUtils.isEmpty(carLine.getTwoCustomerId())){
            one = iCostAggregationService.lambdaQuery()
                    .eq(CostAggregation::getShipLineId, car_line_id)
                    .eq(CostAggregation::getCusSupId, carLine.getTwoCustomerId())
                    .one();
            if(one != null){
                costAggregation.setId(one.getId());
            }else {
                costAggregation.setId(IdUtil.simpleUUID());
                costAggregation.setIsQueren(1);
            }
            costAggregation.setCusSupId(carLine.getTwoCustomerId());
            costAggregation.setCusSupName(carLine.getTwoCustomerName());
            carLine.setTwoContractPrice(BigNumChain.divide(carLine.getContractNoBillMoney(),carLine.getContractTunnage()).doubleValue());
            costAggregation.setPriceWithTax( carLine.getTwoContractPrice());
//            priceWithoutTax = carLine.getTwoContractPrice();
            paybillstr = "不开";
            if(carLine.getTwoPayBill() != null){
                SysDictionary sysDictionary = iSysDictionaryService.getBillTax(carLine.getTwoPayBill());
                if(sysDictionary != null){
                    paybillstr = sysDictionary.getValue();
                }
                if(sysDictionary.getValue().equals("不开") || sysDictionary.getValue().equals("0%")){

                }else {
                    String tax = sysDictionary.getValue().replace("%","");
                    Integer tax_d = 100 -  Integer.parseInt(tax);
                    Double b = BigNumChain.startOf(carLine.getTwoContractPrice())
                            .multiply(new BigDecimal(tax_d)).getDouble();
                    priceWithoutTax =  BigNumChain.startOf(b).divide(100).getDouble();
                }
            }
            costAggregation.setPriceWithoutTax( priceWithoutTax);
            costAggregation.setBillTax(paybillstr);
            costAggregation.setSurplus(carLine.getTwoContractMoney());
            costAggregation.setTotalPrice(carLine.getTwoContractMoney());
            iCostAggregationService.saveOrUpdate(costAggregation);
        }


    }

    @Override
    public boolean checkDataQueren(String car_line_id) {
        //查看是否都确认费用了
        CarLine byId = this.getById(car_line_id);
        if(byId.getCostIsComplete() == 0){
            return  false;
        }
        Integer queren_count = iCarLineFlowService.lambdaQuery().eq(CarLineFlow::getCarLineId, car_line_id)
                .eq(CarLineFlow::getCostIsComplete, 1).count();
        Integer data_count = iCarLineFlowService.lambdaQuery().eq(CarLineFlow::getCarLineId, car_line_id).count();

        if(queren_count != data_count){
            return  false;
        }
        return true;
    }

    @Override
    public boolean checkDataTonnage(String car_line_id) {
        CarLine info = this.getById(car_line_id);
        //List<CarLineFlow> list = iCarLineFlowService.lambdaQuery().eq(CarLineFlow::getCarLineId, car_line_id).list();
        QueryWrapper<CarLineFlow> wrapper = new QueryWrapper<>();
        wrapper.select("sum(tunnage) as tunnage");
        wrapper.lambda().eq(CarLineFlow::getCarLineId,car_line_id);
        CarLineFlow carLineFlow = carLineFlowMapper.selectOne(wrapper);


//        if(carLineFlow.getTunnage().compareTo(info.getContractTunnage()) == 0 ){
//            return true;
//        }
//        return  false;

        //Double sum1 = list.stream().mapToDouble(CarLineFlow::getTunnage).sum();

        Double sum = carLineFlow.getTunnage();
        BigDecimal data1 = new BigDecimal(sum);
        Double data1_f = data1.setScale(3,BigDecimal.ROUND_HALF_UP).doubleValue();
        BigDecimal data2 = new BigDecimal(info.getContractTunnage());
        Double data2_f = data2.setScale(3,BigDecimal.ROUND_HALF_UP).doubleValue();

        if(data1_f.compareTo(data2_f) == 0 ){
            return true;
        }
        return false;
    }

    @Override
    public List<CarStatisticsBean> carStatistics(String startDate,String endDate,String supplierCustomerId) {

       List<CarStatisticsBean> carStatisticsBeans =  carLineMapper.carStatistics(startDate,endDate,supplierCustomerId);

       return carStatisticsBeans;
    }
}




