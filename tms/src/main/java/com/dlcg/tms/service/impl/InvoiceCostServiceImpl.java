package com.dlcg.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dlcg.bms.client.AdvancePaymentClient;
import com.dlcg.bms.entity.CustomerAdvancePaymentAccount;
import com.dlcg.oa.bean.AdminUser;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.tms.bean.CostAggregationBean;
import com.dlcg.tms.config.IdSnowflakeComponent;
import com.dlcg.tms.entity.InvoiceCost;
import com.dlcg.tms.entity.InvoiceOcrRecord;
import com.dlcg.tms.entity.PaymentBillSettleApply;
import com.dlcg.tms.entity.PaymentCost;
import com.dlcg.tms.enums.InvoiceEnums;
import com.dlcg.tms.mapper.InvoiceCostMapper;
import com.dlcg.tms.model.po.InvoiceCostAIPO;
import com.dlcg.tms.model.po.InvoiceCostRelPO;
import com.dlcg.tms.service.*;
import com.dlcg.tms.util.BigNum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/5
 */
@Slf4j
@Service
public class InvoiceCostServiceImpl extends ServiceImpl<InvoiceCostMapper, InvoiceCost> implements IInvoiceCostService {

    @Resource
    ICostAggregationService costAggregationService;
    @Resource
    IdSnowflakeComponent idSnowflakeComponent;
    @Resource
    IPaymentCostService paymentCostService;
    @Resource
    IPaymentBillSettleApplyService paymentBillSettleApplyService;
    @Resource
    InvoiceOcrRecordService invoiceOcrRecordService;

    @Autowired
    AdvancePaymentClient advancePaymentClient;

    private Double advanceBalance(String sysSupplierId){
        Double balan=null;
        Map<String,Object> map= advancePaymentClient.queryAccount(sysSupplierId);
        if(map!=null && map.containsKey("data")){
            CustomerAdvancePaymentAccount customerAdvancePaymentAccount = map.get("data") == null ? null : JSON.parseObject(JSON.toJSONString(map.get("data")), CustomerAdvancePaymentAccount.class);
            if(customerAdvancePaymentAccount!=null){
                if(customerAdvancePaymentAccount.getBalance()!=null && customerAdvancePaymentAccount.getBalance().doubleValue()>0){
                    balan=customerAdvancePaymentAccount.getBalance().doubleValue();
                }else{
                    balan=0.0;
                }
                Double dod = paymentBillSettleApplyService.getPriceSumBySysSupplierIdAndGoing(sysSupplierId);
                if(dod!=null && dod>0){
                    balan=balan-dod;
                }
            }
        }
        return balan;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void invoiceCostRel(InvoiceCostRelPO po) {
        // 费用确认
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        PaymentBillSettleApply paymentBillSettleApply = JSONObject.parseObject(po.getCostFormData(),PaymentBillSettleApply.class);
        paymentBillSettleApply.setId(idSnowflakeComponent.snowflakeIdStr());
        paymentBillSettleApply.setCreateBy(adminUser.getId());
        paymentBillSettleApply.setCreateDate(new Date());
        paymentBillSettleApply.setIsPayment("1");
        paymentBillSettleApply.setIsFinish(0);
        List<PaymentCost> costs = new ArrayList<>();
        for (CostAggregationBean costAggregationBean : Optional.ofNullable(po.getCostData()).orElse(new ArrayList<>())) {
            PaymentCost paymentCost = JSONObject.parseObject(JSONObject.toJSONString(costAggregationBean), PaymentCost.class);
            paymentCost.setId(idSnowflakeComponent.snowflakeIdStr());
            paymentCost.setCostAggregationId(costAggregationBean.getId());
            paymentCost.setCreateBy(adminUser.getId());
            paymentCost.setCreateDate(new Date());
            paymentCost.setPaymentBillSettleId(paymentBillSettleApply.getId());
            costs.add(paymentCost);
        }
        paymentBillSettleApplyService.save(paymentBillSettleApply);
        paymentCostService.saveBatch(costs);

        // 船名｜费用类型｜进票金额
        StringJoiner body = new StringJoiner("\n");
        for (CostAggregationBean costAggregationBean : Optional.ofNullable(po.getCostData()).orElse(new ArrayList<>())) {
            StringJoiner row = new StringJoiner("|");
            row.add(Optional.ofNullable(costAggregationBean.getShipName()).orElse(""))
                    .add(Optional.ofNullable(costAggregationBean.getCostTypeName()).orElse(""))
                    .add(Optional.ofNullable(costAggregationBean.getCurrentAmount()).map(String::valueOf).orElse(""));
            body.add(row.toString());
        }

        // 发票和费用的关系
        String groupId = idSnowflakeComponent.snowflakeIdStr();
        List<InvoiceCost> list = new ArrayList<>();
        for (String invoiceId : po.getInvoiceIds()) {
            for (String costId : po.getCostIds()) {
                InvoiceCost entity = new InvoiceCost();
                entity.setInvoiceId(invoiceId);
                entity.setCostId(costId);
                entity.setCreateBy(CurrentUserInfoAdmin.getAdminUserValue().getId());
                entity.setGroupId(groupId);
                entity.setCostData(body.toString());
                entity.setCostFormData(po.getCostFormData());
                entity.setPaymentId(paymentBillSettleApply.getId());
                list.add(entity);
            }
        }
        saveBatch(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer aiMatcher(InvoiceCostAIPO po) {
        // 根据船名和金额匹配对应的费用
        Collection<InvoiceOcrRecord> invoiceOcrRecords = invoiceOcrRecordService.listByIds(po.getInvoiceIds());
        List<InvoiceCost> ics = new ArrayList<>();
        List<PaymentCost> costs = new ArrayList<>();
        List<PaymentBillSettleApply> pbsas = new ArrayList<>();
        String userId = CurrentUserInfoAdmin.getAdminUserValue().getId();
        for (InvoiceOcrRecord item : invoiceOcrRecords) {
            if (StrUtil.isBlank(item.getSellerId()) || StrUtil.isBlank(item.getShipName()) || item.getInvoicePriceTaxSum() == null) {
                continue;
            }
            List<CostAggregationBean> list = costAggregationService.getCostAggregationBeanList(po.getRouteName(), item.getSellerId(), 10, null, null, new String[]{item.getShipName()}, new JSONObject(),null);
            if (CollUtil.isEmpty(list)) {
                continue;
            }
            // 查询预付款余额，判断金额是否足够
            // 货物费用 船上费用 走预付款支付
            Double advBalPrice=null;
            if(Objects.equals("applyinterticketgoods",po.getRouteName()) || Objects.equals("applyinterticketshipcost",po.getRouteName())){
                advBalPrice = advanceBalance(item.getSellerId());
            }

            // 记录费用总额
            PaymentBillSettleApply paymentBillSettleApply = new PaymentBillSettleApply();
            paymentBillSettleApply.setSysSupplierId(item.getSellerId());
            paymentBillSettleApply.setTicketPayment(item.getInvoicePriceTaxSum().doubleValue());
            paymentBillSettleApply.setInvoiceCompany("10");
            paymentBillSettleApply.setInvoiceNumber(item.getInvoiceNo());
            paymentBillSettleApply.setSysSupplierFullName(item.getSellerName());
            paymentBillSettleApply.setRouteName(po.getRouteName());
            paymentBillSettleApply.setAdvanceBalance(null);
            paymentBillSettleApply.setAmountPayable(0D);
            if(advBalPrice!=null && advBalPrice>0){
                paymentBillSettleApply.setPrice(advBalPrice);
            }

            paymentBillSettleApply.setId(idSnowflakeComponent.snowflakeIdStr());
            paymentBillSettleApply.setCreateBy(userId);
            paymentBillSettleApply.setCreateDate(new Date());
            paymentBillSettleApply.setIsPayment("1");
            paymentBillSettleApply.setIsFinish(0);

            String groupId = idSnowflakeComponent.snowflakeIdStr();
            List<PaymentCost> itemCosts = new ArrayList<>();
            List<InvoiceCost> itemIcs = new ArrayList<>();
            for (CostAggregationBean cost : list) {
                if (cost.getLeftOver() == null) {
                    continue;
                }
                try {
                    if (!BigNum.equalTo(item.getInvoicePriceTaxSum(), BigNum.round(cost.getLeftOver(), 2))) {
                        continue;
                    }
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }

                cost.setCurrentAmount(cost.getLeftOver());

                // 船名｜费用类型｜进票金额
                StringJoiner row = new StringJoiner("|");
                row.add(Optional.ofNullable(cost.getShipName()).orElse(""))
                        .add(Optional.ofNullable(cost.getCostTypeName()).orElse(""))
                        .add(Optional.ofNullable(cost.getCurrentAmount()).map(String::valueOf).orElse(""));

                // 发票和费用的关系
                InvoiceCost entity = new InvoiceCost();
                entity.setInvoiceId(item.getId());
                entity.setCostId(cost.getId());
                entity.setCreateBy(userId);
                entity.setGroupId(groupId);
                entity.setCostData(row.toString());
                entity.setCostFormData(JSONObject.toJSONString(paymentBillSettleApply));
                entity.setPaymentId(paymentBillSettleApply.getId());
                itemIcs.add(entity);

                // 费用明细
                PaymentCost paymentCost = JSONObject.parseObject(JSONObject.toJSONString(cost), PaymentCost.class);
                paymentCost.setId(idSnowflakeComponent.snowflakeIdStr());
                paymentCost.setCostAggregationId(cost.getId());
                paymentCost.setCreateBy(userId);
                paymentCost.setCreateDate(new Date());
                paymentCost.setPaymentBillSettleId(paymentBillSettleApply.getId());
                itemCosts.add(paymentCost);
            }
            if (CollUtil.isEmpty(itemCosts) || CollUtil.isEmpty(itemIcs)) {
                continue;
            }
            costs.addAll(itemCosts);
            ics.addAll(itemIcs);
            pbsas.add(paymentBillSettleApply);
        }
        if (CollUtil.isEmpty(ics) || CollUtil.isEmpty(pbsas) || CollUtil.isEmpty(costs)) {
            return 0;
        }
        paymentBillSettleApplyService.saveBatch(pbsas);
        paymentCostService.saveBatch(costs);
        saveBatch(ics);
        return ics.size();
    }

    /**
     * 删除发票，并且删除发票绑定的费用。
     * 如果涉及到多个发票绑定了多个费用的情况，则不进行处理
     * @param invoiceId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeInvoice(String invoiceId) {
        // 找出发票关联的费用
        List<InvoiceCost> list = list(new LambdaQueryWrapper<InvoiceCost>()
                .eq(InvoiceCost::getInvoiceId, invoiceId)
                .eq(InvoiceCost::getDelFlag, 0)
        );
        List<String> groupIds = list.stream().map(InvoiceCost::getGroupId)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(list)) {
            // 如果发票关联了费用
            // 寻找该发票是否和其他发票同时绑定了多个费用的情况，如果是则抛出异常不进行处理
            long count = list(new LambdaQueryWrapper<InvoiceCost>()
                    .in(InvoiceCost::getGroupId, groupIds)
                    .eq(InvoiceCost::getDelFlag, 0)
            ).stream().map(InvoiceCost::getInvoiceId)
                    .distinct()
                    .count();
            Assert.isTrue(count == 1, "删除失败，该发票和其他发票同时绑定了多个费用");

            // 删除费用
            List<String> paymentIds = list.stream().map(InvoiceCost::getPaymentId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(paymentIds)) {
                paymentBillSettleApplyService.removeByIds(paymentIds);
                paymentCostService.remove(new LambdaQueryWrapper<PaymentCost>().in(PaymentCost::getPaymentBillSettleId, paymentIds));
            }

            // 删除发票和费用的绑定关系
            removeByIds(list.stream().map(InvoiceCost::getId).collect(Collectors.toList()));
        }

        // 删除发票
        invoiceOcrRecordService.removeById(invoiceId);
    }

    /**
     * 根据发票id删除发票和费用的关系并且删除费用
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void renewInvoiceCost(String invoiceIds) {
        List<String> invoiceIdList = Arrays.asList(invoiceIds.split(","));
        if (CollUtil.isEmpty(invoiceIdList)) {
            log.info("发票id为空，不进行处理");
            return;
        }

        // 修改发票状态为未提交审批
        invoiceOcrRecordService.update(new LambdaUpdateWrapper<InvoiceOcrRecord>()
            .in(InvoiceOcrRecord::getId, invoiceIdList)
            .set(InvoiceOcrRecord::getStatus, InvoiceEnums.UN_APPROVE.getKey())
        );
    }
}
