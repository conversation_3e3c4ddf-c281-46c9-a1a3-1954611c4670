package com.dlcg.tms.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dlcg.tms.bean.ShipTransIncomeBean;
import com.dlcg.tms.bean.SysCustomerSecondlevelBean;
import com.dlcg.tms.bean.SysCustomerSecondlevelBean2;
import com.dlcg.tms.entity.SysCustomerSecondlevel;
import com.dlcg.tms.mapper.SysCustomerSecondlevelMapper;
import com.dlcg.tms.service.ISysCustomerSecondlevelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dlcg.tms.service.client.SysExternalCustomerClient;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zthzinfo.common.ResponseMapBuilder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-08
 */
@Service
public class SysCustomerSecondlevelServiceImpl extends ServiceImpl<SysCustomerSecondlevelMapper, SysCustomerSecondlevel> implements ISysCustomerSecondlevelService {

    @Autowired
    SysCustomerSecondlevelMapper sysCustomerSecondlevelMapper;
    @Autowired
    SysExternalCustomerClient sysExternalCustomerClient;

    @Override
    public Page<SysCustomerSecondlevelBean> getCustomerInvoice(
            int pageNo, int pageSize, String abbreviationName, String contractTitle) {
        
        Page<SysCustomerSecondlevelBean> page = PageHelper.startPage(pageNo,pageSize)
                .doSelectPage(()->sysCustomerSecondlevelMapper.getCustomerInvoice(abbreviationName,
                        contractTitle)
        );

        return page;
    }
    
    @Override
    public List<SysCustomerSecondlevelBean2> getAllBeanList() {
        List<SysCustomerSecondlevelBean2> list = sysCustomerSecondlevelMapper.getBeanList();
        return list;
    }
    
    @Override
    public List<ShipTransIncomeBean> getIncomeBeanList(String name, String title) {
        List<ShipTransIncomeBean> list = sysCustomerSecondlevelMapper.getShipTransIncomeList(name,title);
        return list;
    }
    
    @Override
    public List<SysCustomerSecondlevel> getSecondList(String oneId) {
        if(StringUtils.isNotBlank(oneId)){
            return sysCustomerSecondlevelMapper.getSecondList(oneId);
        } else {
            LambdaQueryWrapper<SysCustomerSecondlevel> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysCustomerSecondlevel::getDelFlag,"0");
            return sysCustomerSecondlevelMapper.selectList(queryWrapper);
//            放开这个限制（如果用户没有选择一级客户，允许从所有客户里面选择二级客户） 金工要求
//            return sysCustomerSecondlevelMapper.getNoOneSecondList();
        }
    }
    
    @Override
    public List<SysCustomerSecondlevel> getReceiveList(String secondId) {
        List<SysCustomerSecondlevel> resList = new ArrayList<>();
        List<SysCustomerSecondlevel> list = sysCustomerSecondlevelMapper.getReceiveList(secondId);
        if(list != null && list.size()>0){
            resList = list;
        } else {
            SysCustomerSecondlevel sysCustomerSecondlevel = sysCustomerSecondlevelMapper.selectById(secondId);
            resList.add(sysCustomerSecondlevel);
        }
        return resList;
    }

    @Override
    public String saveCustomer(SysCustomerSecondlevel sysCustomer,SysCustomerSecondlevelBean sysCustomerSecondlevelBean) {
        // 判断是否有重复名称或简称
        LambdaQueryWrapper<SysCustomerSecondlevel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysCustomerSecondlevel::getDelFlag,"0");
//        queryWrapper.eq(SysCustomerSecondlevel::getName,sysCustomer.getName()).or().eq(SysCustomerSecondlevel::getAbbreviationName,sysCustomer.getAbbreviationName());
        queryWrapper.apply("(REPLACE(REPLACE(name,'（','('),'）',')') = REPLACE(REPLACE({0},'（','('),'）',')') or REPLACE(REPLACE(abbreviation_name,'（','('),'）',')') = REPLACE(REPLACE({1},'（','('),'）',')'))",sysCustomer.getName(),sysCustomer.getAbbreviationName());
        List<SysCustomerSecondlevel> list = sysCustomerSecondlevelMapper.selectList(queryWrapper);
        if(list != null && list.size()>0){
            return list.get(0).getId();
        }
        this.save(sysCustomer);
        // 同步更新综合管控系统客户
        sysExternalCustomerClient.updateCustomerNameAndShortNameByCgwlId(customerToJson(sysCustomer,sysCustomerSecondlevelBean));
        return sysCustomer.getId();
    }

    private JSONObject customerToJson(SysCustomerSecondlevel sysCustomer,SysCustomerSecondlevelBean sysCustomerSecondlevelBean){
        JSONObject json = new JSONObject();
        json.put("cgwlId", "1-"+sysCustomer.getId());
        json.put("name", sysCustomer.getName());
        json.put("shortName", sysCustomer.getAbbreviationName());
        if(sysCustomerSecondlevelBean!=null){
            if(StrUtil.isNotBlank(sysCustomerSecondlevelBean.getContractTitle())){
                json.put("contractHeader", sysCustomerSecondlevelBean.getContractTitle());
            }
            if(StrUtil.isNotBlank(sysCustomerSecondlevelBean.getTaxIdentificationNumber())){
                json.put("taxIdNumber", sysCustomerSecondlevelBean.getTaxIdentificationNumber());
            }
            if(StrUtil.isNotBlank(sysCustomerSecondlevelBean.getRegisteredAddress())){
                json.put("registeredAddress", sysCustomerSecondlevelBean.getRegisteredAddress());
            }
            if(StrUtil.isNotBlank(sysCustomerSecondlevelBean.getRegisteredPhone())){
                json.put("spare1", sysCustomerSecondlevelBean.getRegisteredPhone());
            }
            if(StrUtil.isNotBlank(sysCustomerSecondlevelBean.getAccount())){
                json.put("spare3", sysCustomerSecondlevelBean.getAccount());
            }
            if(StrUtil.isNotBlank(sysCustomerSecondlevelBean.getOpeningBank())){
                json.put("spare2", sysCustomerSecondlevelBean.getOpeningBank());
            }

        }
        return json;
    }

    @Override
    public Boolean updateCustomer(SysCustomerSecondlevel sysCustomer,SysCustomerSecondlevelBean sysCustomerSecondlevelBean) {
        // 判断是否有重复名称或简称
        LambdaQueryWrapper<SysCustomerSecondlevel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysCustomerSecondlevel::getDelFlag,"0");
        queryWrapper.ne(SysCustomerSecondlevel::getId,sysCustomer.getId());
        queryWrapper.and(wrapper -> wrapper.eq(SysCustomerSecondlevel::getName,sysCustomer.getName()).or().eq(SysCustomerSecondlevel::getAbbreviationName,sysCustomer.getAbbreviationName()));
        List<SysCustomerSecondlevel> list = sysCustomerSecondlevelMapper.selectList(queryWrapper);
        if(list != null && list.size()>0){
            return false;
        }
        this.updateById(sysCustomer);
        // 同步更新综合管控系统客户
        sysExternalCustomerClient.updateCustomerNameAndShortNameByCgwlId(customerToJson(sysCustomer,sysCustomerSecondlevelBean));
        return true;
    }

    @Override
    public boolean isCustomerSecondByName(String name) {
        LambdaQueryWrapper<SysCustomerSecondlevel> queryWrapper = new LambdaQueryWrapper<>();
        // getAbbreviationName or getname  == name
//        queryWrapper.eq(SysCustomerSecondlevel::getAbbreviationName, name).or().eq(SysCustomerSecondlevel::getName, name);
        queryWrapper.apply("(REPLACE(REPLACE(name,'（','('),'）',')') = REPLACE(REPLACE({0},'（','('),'）',')') or REPLACE(REPLACE(abbreviation_name,'（','('),'）',')') = REPLACE(REPLACE({1},'（','('),'）',')'))",name,name);

        List<SysCustomerSecondlevel> list = this.list(queryWrapper);
        if (list != null && list.size() > 0) {
           return true;
        }
        return false;
    }
}
