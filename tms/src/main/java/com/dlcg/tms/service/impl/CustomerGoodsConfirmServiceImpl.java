package com.dlcg.tms.service.impl;

import com.dlcg.tms.bean.CustomerGoodsConfirmBean;
import com.dlcg.tms.bean.GetBiaotouBean;
import com.dlcg.tms.entity.CustomerGoodsConfirm;
import com.dlcg.tms.mapper.CustomerGoodsConfirmMapper;
import com.dlcg.tms.service.ICustomerGoodsConfirmService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-18
 */
@Service
public class CustomerGoodsConfirmServiceImpl extends ServiceImpl<CustomerGoodsConfirmMapper, CustomerGoodsConfirm> implements ICustomerGoodsConfirmService {

    @Autowired
    CustomerGoodsConfirmMapper customerGoodsConfirmMapper;


    @Override
    public List<CustomerGoodsConfirmBean> goodsconfirm(Integer ctype,String cusName,Integer tijiao,Integer querentype,String currentUserId,String name1,String danzhengid,
            Date startshipTime, Date endshipTime) {
        return customerGoodsConfirmMapper.goodsconfirm(ctype,cusName,tijiao,querentype,currentUserId,name1,danzhengid,startshipTime,endshipTime);
    }

    @Override
    public List<CustomerGoodsConfirmBean> goodsconfirmNew(Integer ctype, String cusName, Integer tijiao,
            Integer querentype, String currentUserId, String name1, String danzhengid,Date startshipTime, Date endshipTime) {
        return customerGoodsConfirmMapper.goodsconfirmNew(ctype,cusName,tijiao,querentype,currentUserId,name1,danzhengid,startshipTime,endshipTime);
    }

    @Override
    public List<GetBiaotouBean> getbiaotou(String id) {
        return customerGoodsConfirmMapper.getbiaotou(id);
    }

    @Override
    public List<String> leixingbyid(String shipLineId) {
        return customerGoodsConfirmMapper.leixingbyid(shipLineId);
    }

    @Override
    public List<CustomerGoodsConfirm> getBadgeNum(String userid) {
        return customerGoodsConfirmMapper.getBadgeNum(userid);
    }
}
