package com.dlcg.tms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.dlcg.tms.entity.PaymentBillSettleApply;
import com.dlcg.tms.mapper.PaymentBillSettleApplyMapper;
import com.dlcg.tms.service.IPaymentBillSettleApplyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 付款表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@Service
public class PaymentBillSettleApplyServiceImpl extends ServiceImpl<PaymentBillSettleApplyMapper, PaymentBillSettleApply> implements IPaymentBillSettleApplyService {
    @Autowired
    PaymentBillSettleApplyMapper paymentBillSettleApplyMapper;
    @Override
    public Double getPriceSumBySysSupplierIdAndGoing(String sysSupplierId) {
        if(StrUtil.isNotBlank(sysSupplierId)){
            return paymentBillSettleApplyMapper.getPriceSumBySysSupplierIdAndGoing(sysSupplierId);
        }
        return null;
    }
}
