package com.dlcg.tms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dlcg.tms.entity.CustomerGoodsCostContract;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dlcg.tms.entity.CustomerGoodsCostContractVo;
import com.dlcg.tms.entity.CustomerGoodsCostVo;
import com.github.pagehelper.Page;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【customer_goods_cost_contract(货物合同表)】的数据库操作Service
* @createDate 2023-04-11 15:44:04
*/
public interface CustomerGoodsCostContractService extends IService<CustomerGoodsCostContract> {
    //  返回合同列表、货物流向列表、货物收入列表、货物支出列表
    // contractName,remark,oneName,twoName,abbName,costName,shipName,shipTime,departureTimeDate
    // custSupName,tonnage,farePrice,fareTaxRate,agencyFee,agencyFeeTaxRate,goodsCostPrice,goodsCostPriceTax,custSupType
    Page<CustomerGoodsCostContractVo> getCustomerGoodsCostContractVoList(Integer pageNo,Integer pageSize,String type,String contractName,String remark,String oneName,String twoName
            ,String abbName,String costName,String shipName,String shipTime
            ,String departureTimeDate,String danZhengUserName,String custSupName,String tonnage,String farePrice,String fareTaxRate,String agencyFee,String agencyFeeTaxRate
            ,String goodsCostPrice,String goodsCostPriceTax,String custSupType,String createDate,String ifRealCon, String modelType);

    Page<CustomerGoodsCostContractVo> getCustomerGoodsCostContractVoCarList(Integer pageNo, Integer pageSize, String contractName, String remark, String supplierName, String type);

    List<CustomerGoodsCostVo> getCustomerGoodsCostContractVoListByContractId(String contractId,String goodsCostId);

    List<CustomerGoodsCostVo> getCustomerGoodsCostContractVoListByContractIdAndType(String contractId,String type);

    List<CustomerGoodsCostVo> getCustomerGoodsCostContractVoListByDetailId(String detailId);

    void updateSpStatusById(Integer id,String spStatus);

    Page<CustomerGoodsCostContractVo> contractListByGoodsCostId(Integer pageNo,Integer pageSize,String goodsCostId,String jiaFang,String yiFang,String shipName,String departureTimeDate,String contractName,String contractRemark,String modelType,String othId);

    String isSpContractByGoodsCostId(String goodsCostId);

    String isSpContractByCustId(String custOneId,String custTwoId);

    void skipSpStatusById(String goodsCostId,Integer contractId);
    void skipSpStatusByCustId(String custOneId,String custTwoId,Integer contractId);
    void upSigneUrlByContractId(String contractId,String signeUrl,String signeUserName);

    boolean isOnlyContractNo(String contractNo,String contractType,String modelType,String contractId);

    // 根据goodsCostId 查询预估信息
    Map findCustomizeInfoByGoodsCostId(String goodsCostId);
    // 根据goodsCostId 保存预估信息
    void saveCustomizeInfoByGoodsCostId(String goodsCostId, Map<String,Object> map);

    void removeCustomizeInfoByGoodsCostId(String goodsCostId);

    void saveCustomizeListByContractId(String contractId,String processId,List list);

    List findCustomizeListByContractId(String contractId,String processId);

    // 增加 自定义信息
//    @RequestParam(value = "type",required = false) String type,
//    @RequestParam(value = "price",required = false) String price,
//    @RequestParam(value = "inAndOut",required = false) String inAndOut,
//    @RequestParam(value = "goodsCostId",required = false) String goodsCostId,
//    @RequestParam(value = "mainGoodsCostId",required = false) String mainGoodsCostId
    void addCustomizeInfoByGoodsCostId(String type,String price,String inAndOut,String goodsCostId,String mainGoodsCostId);
    // 删除 自定义信息
    void deleteCustomizeInfoByGoodsCostId(String customId,String inAndOut,String goodsCostId,String mainGoodsCostId);
    // 修改 自定义信息
//    @RequestParam(value = "customId",required = false) String customId,
//    @RequestParam(value = "type",required = false) String type,
//    @RequestParam(value = "price",required = false) String price,
//    @RequestParam(value = "goodsCostId",required = false) String goodsCostId,
//    @RequestParam(value = "mainGoodsCostId",required = false) String mainGoodsCostId
    void updateCustomizeInfoByGoodsCostId(String customId,String type,String price,String inAndOut,String goodsCostId,String mainGoodsCostId);



}
