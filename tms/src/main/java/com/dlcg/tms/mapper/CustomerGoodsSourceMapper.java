package com.dlcg.tms.mapper;

import com.dlcg.tms.bean.*;
import com.dlcg.tms.entity.CustomerGoodsCost;
import com.dlcg.tms.entity.CustomerGoodsSource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dlcg.tms.model.vo.CustomerGoodsSourceVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-09
 */
public interface CustomerGoodsSourceMapper extends BaseMapper<CustomerGoodsSource> {

    List<CustomerGoodsCostContractBean> getCustomerGoodsCostContractList();

    List<CustomerGoodsCost> getdaili();


    @Select("<script>"+
            "select " +
            "cgs.sys_shipline_id as sysShiplineId, " +
            "count(*) as unFinalNum " +
            "from customer_goods_source cgs " +
            "where cgs.is_del=0  " +
            "and cgs.stowage_status = 0  " +
            "group by cgs.sys_shipline_id "+
            "</script>")
    List<CustomerGoodsSourceBean> getUnFinalNum();


    @Select("<script>"+
            "select sg.id as stowageGoodsId," +
            "cg.source_id as cgSourceId \n" +
            "from stowage_goods sg\n" +
            "left join customer_goods cg on cg.id = sg.goods_id\n" +
            "where sg.del_flag=0\n" +
            "and cg.is_del = 0\n"


            +"<if test='customer_goods_source_ids!=null and  customer_goods_source_ids.size>0 '> "

            + " and cg.source_id IN "
            + " <foreach collection=\"customer_goods_source_ids\" item=\"listItem\" open=\"(\" close=\")\" separator=\",\" >\n"
            + " #{listItem} "
            + " </foreach> "

            + "</if> " +

            "</script>")
    List<CustomerGoodsSourceBean> getIsStowage(@Param("customer_goods_source_ids") List<String> customerGoodsSourceIds);



    @Select("SELECT cgs.*\n"
                    + "FROM `customer_goods_source` cgs\n"
                    + "left join (select cg.source_id as source_id from customer_goods cg  where cg.is_del=0 group by cg.source_id)\n"
                    + "cggy on cgs.id = cggy.source_id\n"
                    + "                WHERE cgs.is_del = 0\n"
                    + "\n"
                    + "                and cggy.source_id is  null\n"
                    + "\n"
                    + "                AND cgs.sys_shipline_id = #{lineid}\n"
                    + "                AND cgs.transport_time >= #{startTime}\n"
                    + "                AND cgs.transport_time <= #{endTime}")
    List<CustomerGoodsSource> getAutoMatchList(@Param("startTime") Date startTime,@Param("endTime") Date endTime,@Param("lineid") String lineid);

    @Select("<script>"
                    +"SELECT\n"
                    + "\tcgs.*,\n"
                    + "\tscs.abbreviation_name \n"
                    + "FROM\n"
                    + "\tcustomer_goods_source cgs\n"
                    + "\tLEFT JOIN sys_customer_secondlevel scs ON cgs.customer_secondlevel_id = scs.id \n"
                    + "WHERE\n"
                    + "\tcgs.is_del = 0 \n"
                    + "\tAND cgs.sys_shipline_id = #{ssid} \n"
                    +"<if test='goodsType!=null and goodsType != \"\" '>  \n"
                    + "\tAND cgs.goods_type = #{goodsType} \n"
                    +"</if> \n"
                    +"<if test='startTime!=null '>  \n"
                    + "\tAND cgs.transport_time &gt;= #{startTime} \n"
                    +"</if> \n"
                    +"<if test='endTime !=null '>  \n"
                    + "\tAND cgs.transport_time &lt;= #{endTime}\n"
                    +"</if> \n"
                    +"<if test='name !=null and name != \"\" '>  \n"
                    + "\tAND scs.abbreviation_name LIKE #{name} \n"
                    +"</if> \n"
                    + "ORDER BY\n"
                    + "\tcgs.stowage_status,\n"
                    + "\tcgs.transport_time"
                    +"</script>")
    List<CustomerGoodsSource> getCustomerByPage(@Param("startTime") Date startTime,@Param("endTime") Date endTime,@Param("ssid") String ssid,
            @Param("goodsType") String goodsType,@Param("name") String name);

    List<CustomerGoodsSourceWithNameBean> selectWithNameByIds(@Param("ids") List<String> ids);

    List<CustomerGoodsSourceVo> selectGoodsSourceByShipLineId(@Param("id") String id);

    @Select("SELECT DISTINCT\n"
                    + "\tcgs.* \n"
                    + "FROM\n"
                    + "\tcustomer_goods_source cgs\n"
                    + "\tLEFT JOIN customer_goods_cost cgc ON cgc.customer_goods_source_id = cgs.id \n"
                    + "WHERE\n"
                    + "\tcgc.cost_type = #{costType} \n"
                    + "\tAND cgs.pre_status != 2 \n"
                    + "\tAND cgs.is_del = 0 \n"
                    + "\tAND cgs.create_time >= #{startTime}\n"
                    + "\tAND cgs.create_time < #{endTime}\n"
                    + "ORDER BY\n"
                    + "\tcgs.create_time desc")
    List<CustomerGoodsSource> getNumList(@Param("costType") Integer costType,@Param("startTime") String startTime,@Param("endTime") String endTime);

    List<GoodsCostByYearBean> getGoodsCostByYear(@Param("startTime") Date startTime,@Param("accountingType") Integer accountingType);
    
    List<CustomerGoodsCostBean> getSource(@Param("shipLineId") String shipLineId);
}
