package com.dlcg.tms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dlcg.tms.bean.*;
import com.dlcg.tms.entity.ShipLineShipCost;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-17
 */
public interface ShipLineShipCostMapper extends BaseMapper<ShipLineShipCost> {


    @Select("<script>"
            +"SELECT DISTINCT\n"
                    + "\tsl.id,\n"
                    + "\tslsc.id AS costDetailId,\n"
                    + "\tsl.ship_time AS transportTime,\n"
                    + "\tsl.line_type AS costType,\n"
                    + "\tsl.ship_id AS shipId,\n"
                    + "\ts.`name` AS shipName,\n"
                    + "\tsl.voyage_number AS voyageNumber,\n"
                    + "\tslsc.cost_project AS costProject,\n"
                    + "\tslsc.surplus AS amountDue,\n"
                    + "\tslsc.tax AS payBill,\n"
                    + "\tslsc.payable AS payable\n"
                    + "FROM\n"
                    + "\tship_line_ship_cost slsc\n"
                    + "\tLEFT JOIN ship_line sl ON slsc.ship_line_id = sl.id\n"
                    + "\tLEFT JOIN ship s ON s.id = sl.ship_id \n"
                    + "WHERE\n"
                    + "\tslsc.sys_supplier_id = #{sysSupplierId} \n"
                    + "\tAND sl.queren = 0\n"
                    + "\tAND slsc.is_income = 0\n"
                    + "\tAND slsc.shenpi = 0\n"
                    +"<if test='param == 0 '>  \n"
                    + "\tAND slsc.surplus IS NOT NULL AND slsc.surplus != 0"
                    +"</if> \n"
                    +"<if test='param == 1 '>  \n"
                    + "AND ((slsc.surplus IS NOT NULL AND slsc.surplus != 0 ) OR (slsc.payable IS NOT NULL AND slsc.payable != 0) OR \n"
                    + "\t(slsc.surplus IS NOT NULL AND slsc.surplus != 0 AND  slsc.payable IS NOT NULL AND slsc.payable != 0 AND slsc.surplus != ABS(slsc.payable)))"
                    +"</if> \n"
                    + "</script>")
    List<PaymentApplyBean> getPaymentList(
            @Param("sysSupplierId")String sysSupplierId,
            @Param("param")Integer param);


    @Select("SELECT DISTINCT\n"
                    + "\tsl.id,\n"
                    + "\tslsc.id AS costDetailId,\n"
                    + "\tsl.ship_time AS transportTime,\n"
                    + "\tsl.line_type AS costType,\n"
                    + "\ts.`name` AS shipName,\n"
                    + "\tsl.voyage_number AS voyageNumber,\n"
                    + "\tslsc.cost_project AS costProject,\n"
                    + "\tslsc.cost_price_no AS advanceAmount\n"
                    + "FROM\n"
                    + "\tship_line_ship_cost slsc\n"
                    + "\tLEFT JOIN ship_line sl ON sl.id = slsc.ship_line_id\n"
                    + "\tLEFT JOIN ship s ON s.id = sl.ship_id\n"
                    + "\tWHERE slsc.cost_bear = \"10\"\n"
                    + "\tAND slsc.is_income = 0\n"
                    + "\tAND sl.shipowner_company_id = #{sysSupplierId}\n"
                    + "\tAND  sl.queren = 0\n"
                    + "\tAND slsc.shenpi = 0\n"
                    + "\tAND (slsc.is_deduct IS NULL || slsc.is_deduct = 0)")
    List<PaymentApplyBean> getDeductList(@Param("sysSupplierId")String sysSupplierId);

    @Select("SELECT DISTINCT\n"
                    + "\tcgcf.id AS costDetailId,\n"
                    + "\tcgcf.c_type AS costType,\n"
                    + "\tcgs.transport_time AS transportTime,\n"
                    + "\ts.NAME AS shipName,\n"
                    + "\tsl.voyage_number AS voyageNumber,\n"
                    + "\tcgcf.surplus_bill AS surplusBill,\n"
                    + "\tcgcf.surplus AS receivable\n"
                    + "FROM\n"
                    + "\tcustomer_goods_cost cgc\n"
                    + "\tLEFT JOIN customer_goods_source cgs ON cgs.id = cgc.customer_goods_source_id\n"
                    + "\tLEFT JOIN customer_goods_confirm cgcf ON cgc.customer_goods_source_id = cgcf.customer_goods_id \n"
                    + "\tAND cgcf.ship_line_id = cgc.shipline_id\n"
                    + "\tLEFT JOIN ship_line sl ON cgc.shipline_id = sl.id\n"
                    + "\tLEFT JOIN ship s ON s.id = sl.ship_id \n"
                    + "WHERE\n"
                    + "\tcgs.customer_secondlevel_id = #{customerId} \n"
                    + "\tAND cgcf.queren = 1 \n"
                    + "\tAND cgcf.surplus IS NOT NULL \n"
                    + "\tAND cgcf.surplus != 0")
    List<ReceiveApplyBean>  getCustomerIncome(@Param("customerId") String customerId);


    @Select("SELECT DISTINCT\n"
                    + "slsc.id AS costDetailId,\n"
                    + "sl.ship_time AS transportTime,\n"
                    + "s.`name` AS shipName,\n"
                    + "sl.voyage_number AS voyageNumber,\n"
                    + "sl.line_type AS costType,\n"
                    + "slsc.cost_project AS costName,\n"
                    + "slsc.surplus_bill AS surplusBill,\n"
                    + "slsc.surplus AS receivable\n"
                    + "FROM\n"
                    + "\tship_line_ship_cost slsc\n"
                    + "\tLEFT JOIN ship_line sl ON sl.id = slsc.ship_line_id\n"
                    + "\tLEFT JOIN ship s ON s.id = sl.ship_id\n"
                    + "\tWHERE \n"
                    + "\tsl.queren = 0\n"
                    + "\tAND slsc.is_income = 1\n"
                    + "\tAND slsc.is_deduct = 0\n"
                    + "\tAND slsc.cost_bear = \"10\"\n"
                    + "\tAND sl.shipowner_company_id = #{sysSupplierId}\n"
                    + "\tAND slsc.surplus IS NOT NULL AND slsc.surplus != 0")
    List<ReceiveApplyBean> getShipIncome(@Param("sysSupplierId")String sysSupplierId);

    @Select("<script>"
            +"SELECT "
            +"slsc.*,"
            +"ss.simple_name as gongsiname,"
            +"ctax.`value` as taxs "
            +"FROM "
            +"ship_line_ship_cost slsc "
            +"LEFT JOIN sys_supplier as ss on ss.id = slsc.sys_supplier_id "
            +"LEFT JOIN sys_dictionary as ctax on ctax.dictionary_key = 'bill_tax' and ctax.code = slsc.tax "
            +"WHERE "
            +"slsc.del_flag = 0"
            +" order by slsc.create_date desc"
            + "</script>")
    List<ShipCostBean> feiyonglist();

    List<ShipCostBusinessPayablesBean> getShipCostBusinessPayables(
            @Param("userid")String userid,
            @Param("startTime") Date startTime,
            @Param("endTime")Date endTime,
            @Param("startTime2") Date startTime2,
            @Param("endTime2")Date endTime2,
            @Param("costAggregationId")String costAggregationId,
            @Param("costName")String costName,
            @Param("gname")String gname,
            @Param("iftax")Integer iftax,
            @Param("ban")Integer ban,
            @Param("contractCompany")Integer contractCompany,
            @Param("shipName")String shipName,
            @Param("accountingType")Integer accountingType,
            @Param("guaYear")String guaYear
    );

    List<ShipCostBusinessPayablesBean> getTallyReceiveList(
            @Param("tallyIds")List<String> tallyIds
    );

    List<ShipCostBean> getCostListByIds(@Param("shipLineIds")List<String> shipLineIds);

    List<ShipCostBean> getCostListByPayerId(@Param("payerId")String payerId,@Param("isDeduct")Integer isDeduct,
                                            @Param("shipName")String shipName, @Param("startShipTime")String startShipTime, @Param("endShipTime")String endShipTime);

    List<ShipCostReportBean> getShipCostReport(
            @Param("userid")String userid,
            @Param("startTime") Date startTime,
            @Param("endTime")Date endTime,
            @Param("startTime2") Date startTime2,
            @Param("endTime2")Date endTime2,
            @Param("accountingType")Integer accountingType
    );

    List<ShipFeeBean> shipFeeList(@Param("startTime") Date startTime,
                                  @Param("endTime")Date endTime,
                                  @Param("receiveDate")Date receiveDate,
                                  @Param("companycode")String companycode,
                                  @Param("billDate")Date billDate,
                                  @Param("guaMonth")String guaMonth,
                                  @Param("iftax")Integer iftax,
                                  @Param("shipName")String shipName,
                                  @Param("shipOwnerSimpleName")String shipOwnerSimpleName,
                                  @Param("year")Integer year);
}
