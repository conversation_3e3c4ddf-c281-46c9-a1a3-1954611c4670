package com.dlcg.tms.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.mobile.model.ResultBody;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.tms.base.BaseController;
import com.dlcg.tms.constants.TmsConstants;
import com.dlcg.tms.entity.InvoiceOcrRecord;
import com.dlcg.tms.entity.Ship;
import com.dlcg.tms.entity.SysDictionary;
import com.dlcg.tms.entity.SysSupplier;
import com.dlcg.tms.exception.Assert;
import com.dlcg.tms.model.dto.InvoiceOcrRecordDto;
import com.dlcg.tms.service.IShipService;
import com.dlcg.tms.service.ISysDictionaryService;
import com.dlcg.tms.service.ISysSupplierService;
import com.dlcg.tms.service.InvoiceOcrRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 发票识别系统接口
 *
 * <AUTHOR>
 * @since 2022/6/22
 */
@RestController
@RequestMapping(value = "/invoiceOcr")
public class InvoiceOCRController extends BaseController {

    @Resource
    ISysSupplierService supplierService;
    @Resource
    ISysDictionaryService dictionaryService;
    @Resource
    IShipService shipService;
    @Resource
    InvoiceOcrRecordService invoiceOcrRecordService;

    /**
     * 获取全部公司
     */
    @GetMapping("/companys")
    public ResultBody companys() {
        List<SysSupplier> sups = supplierService.list(new LambdaQueryWrapper<SysSupplier>()
                .eq(SysSupplier::getDelFlag, TmsConstants.DELFLAG_NORMAL)
        );
        return ResultBody.ok().data(sups);
    }

    /**
     * 查询字典
     */
    @GetMapping("/dict")
    public ResultBody dict(String dictKey) {
        Assert.notBlank(dictKey, "字典key不能为空");
        List<SysDictionary> list = dictionaryService.list(new LambdaQueryWrapper<SysDictionary>()
            .eq(SysDictionary::getDictionaryKey, dictKey)
            .eq(SysDictionary::getDelFlag, TmsConstants.DELFLAG_NORMAL)
        );
        return ResultBody.ok().data(list);
    }

    /**
     * 获取全部船舶
     */
    @GetMapping("/ships")
    public ResultBody ships() {
        List<Ship> list = shipService.list(new LambdaQueryWrapper<Ship>()
                .eq(Ship::getDelFlag, TmsConstants.DELFLAG_NORMAL)
        );
        return ResultBody.ok().data(list);
    }

    /**
     * 存储发票记录
     */
    @PostMapping("/submitToBusinessSystem")
    public ResultBody submitToBusinessSystem(@RequestBody InvoiceOcrRecord entity) {
        invoiceOcrRecordService.save(entity);
        return ResultBody.ok();
    }

    @PostMapping("/submitToBusinessSystemBatch")
    public ResultBody submitToBusinessSystemBatch(@RequestBody List<InvoiceOcrRecord> entitys) {
        invoiceOcrRecordService.saveBatch(entitys);
        return ResultBody.ok();
    }


}
