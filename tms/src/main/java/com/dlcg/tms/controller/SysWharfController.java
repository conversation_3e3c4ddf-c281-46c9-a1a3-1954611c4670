package com.dlcg.tms.controller;


import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dlcg.oa.bean.AdminUser;
import com.dlcg.tms.entity.ShipPort;
import com.dlcg.tms.entity.SysCustomerOnelevel;
import com.dlcg.tms.entity.SysWharf;
import com.dlcg.oa.interceptor.UserMenuPermission;
import com.dlcg.tms.service.ISysWharfService;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.zthzinfo.common.ResponseMapBuilder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* <p>
    *  前端控制器
    * </p>
*
* <AUTHOR>
* @since 2020-04-26
*/
@RestController
@RequestMapping("/sysWharf")
public class SysWharfController {

    @Autowired
    ISysWharfService iSysWharfService;

    @RequestMapping("/saveWharf")
    public Map<String, Object> saveWharf(
            @RequestParam(value = "name",required = false) String name
    ) {
        LambdaQueryWrapper<SysWharf> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SysWharf::getWharf,name);
        lambdaQueryWrapper.eq(SysWharf::getDelFlag,"0");
        SysWharf sysWharfRes = iSysWharfService.getOne(lambdaQueryWrapper);
        SysWharf sysWharf = new SysWharf();
        if(sysWharfRes == null){
            AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
    
            sysWharf.setId(IdUtil.simpleUUID());
            sysWharf.setCreateDate(new Date());
            sysWharf.setCreateBy(adminUser.getId());
            sysWharf.setWharf(name);
            sysWharf.setIsPerfect(2);
            iSysWharfService.save(sysWharf);
        } else {
            BeanUtils.copyProperties(sysWharfRes,sysWharf);
        }
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .put("id", sysWharf.getId())
                .getResult();
    }


    @RequestMapping("/getAllSysWharf")
    public Map<String , Object> getAllCusTomer(
    ) {

        QueryWrapper<SysWharf> cuslist = new QueryWrapper<>();
        cuslist.eq("del_flag", "0");
        cuslist.orderByDesc("create_date");
        List<SysWharf> cuslist1 = iSysWharfService.list(cuslist);

        return ResponseMapBuilder.newBuilder()
                .put("list",cuslist1)
                .putSuccess()
                .getResult();
    }

    @RequestMapping("/getSysWharfList")
    public Map<String , Object> getSysWharfList(
            @RequestParam(value = "code2",required = false) String code2,
            @RequestParam(value = "wharf",required = false) String wharf,
        @RequestParam(value = "pageNo",required = false) Integer pageNo,
        @RequestParam(value = "pageSize",required = false) Integer pageSize
    ) {
        if(pageNo==null||pageNo<1){
            pageNo = 1;
        }
        if(pageSize==null||pageSize<0){
            pageSize = 10;
        }
        QueryWrapper<SysWharf> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag","0");


        if(code2!=null&&code2.trim().length()>0){
            queryWrapper.like("code2",code2);
        }
        if(wharf!=null&&wharf.trim().length()>0){
            queryWrapper.like("wharf",wharf);
        }
        queryWrapper.orderByDesc("create_date");
        IPage<SysWharf> page = iSysWharfService.page(new Page<>(pageNo,pageSize),queryWrapper);
        return ResponseMapBuilder.newBuilder()
        .put("page",page)
        .putSuccess()
        .getResult();
    }
    @RequestMapping("/updateSysWharf")
    public Map<String , Object> updateSysWharf(
        @ModelAttribute SysWharf sysWharf
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        sysWharf.setUpdateBy(adminUser.getId());
        sysWharf.setUpdateDate(new Date());
        if(StringUtils.isBlank(sysWharf.getRegion())){
            sysWharf.setRegion(null);
        }
        if(StringUtils.isBlank(sysWharf.getPort())){
            sysWharf.setPort(null);
        }
        iSysWharfService.updateById(sysWharf);
        return ResponseMapBuilder.newBuilder()
        .putSuccess()
        .getResult();
    }

    @RequestMapping("/delSysWharf")
    public Map<String , Object> delSysWharf(
        @RequestParam(value = "id",required = false) String id
    ) {

        iSysWharfService.removeById(id);
        return ResponseMapBuilder.newBuilder()
        .putSuccess()
        .getResult();
    }

    @RequestMapping("/saveSysWharf")
    public Map<String , Object> saveSysWharf(
        @ModelAttribute SysWharf sysWharf
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        sysWharf.setId(IdUtil.simpleUUID());
        sysWharf.setCreateBy(adminUser.getId());
        sysWharf.setCreateDate(new Date());
        iSysWharfService.save(sysWharf);
        return ResponseMapBuilder.newBuilder()
        .putSuccess()
        .getResult();
    }
    
    @RequestMapping("/getSysWharfByName")
    public Map<String , Object> getSysWharfByName(
            @RequestParam(value = "name",required = false) String name,
            @RequestParam(value = "region",required = false) Integer region,
            @RequestParam(value = "port",required = false) Integer port
            ){
        LambdaQueryWrapper<SysWharf> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysWharf::getDelFlag,"0");
        queryWrapper.eq(SysWharf::getWharf,name);
        queryWrapper.eq(SysWharf::getRegion,region);
        queryWrapper.eq(SysWharf::getPort,port);
        List<SysWharf> list = iSysWharfService.list(queryWrapper);
        return ResponseMapBuilder.newBuilder()
                .put("list",list)
                .putSuccess()
                .getResult();
    }
}