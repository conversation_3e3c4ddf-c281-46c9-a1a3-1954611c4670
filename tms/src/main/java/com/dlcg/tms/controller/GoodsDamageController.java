package com.dlcg.tms.controller;

import com.dlcg.bms.enums.ApprovalStatusEnum;
import com.dlcg.bms.enums.ProcessResultEnum;
import com.dlcg.bms.enums.ProcessStatusEnum;
import com.dlcg.tms.base.BaseController;
import com.dlcg.tms.enums.GoodsDamageTypeEnums;
import com.dlcg.tms.model.ResultBody;
import com.dlcg.tms.model.po.GoodsDamagePo;
import com.dlcg.tms.model.vo.GoodsDamageVo;
import com.dlcg.tms.service.IGoodsDamageService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 货损货差
 * <AUTHOR>
 */
@RestController
@RequestMapping("/goods-damage")
public class GoodsDamageController extends BaseController {

    @Resource
    private IGoodsDamageService goodsDamageService;

    @GetMapping("/list")
    public ResultBody list(
            @RequestParam(value = "customerName",required = false) String customerName,
            @RequestParam(value = "voyageNo",required = false) String voyageNo,
            @RequestParam(value = "damageType",required = false) Integer damageType,
            @RequestParam(value = "status",required = false) Integer status
    ){
        startPage();
        GoodsDamagePo po = new GoodsDamagePo();
        po.setCustomerName(customerName);
        po.setDamageType(damageType);
        po.setVoyageNo(voyageNo);
        po.setStatus(status);
        List<GoodsDamageVo> list = goodsDamageService.listPage(po);
        list.forEach(item -> {
            item.setDamageTypeStr(GoodsDamageTypeEnums.getDescByKey(item.getDamageType()));
            item.setStatusStr(ApprovalStatusEnum.getDescByKey(item.getStatus()));
        });
        return ResultBody.ok().data(getDataTable(list));
    }

}
