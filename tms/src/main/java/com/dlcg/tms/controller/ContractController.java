package com.dlcg.tms.controller;


import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dlcg.oa.interceptor.UserMenuPermission;
import com.dlcg.oa.bean.AdminUser;
import com.dlcg.tms.bean.ContractBean;
import com.dlcg.tms.entity.Contract;
import com.dlcg.tms.entity.ShipLine;
import com.dlcg.tms.model.ResultBody;
import com.dlcg.tms.service.IContractService;
import com.dlcg.tms.service.IShipLineService;
import com.dlcg.tms.service.IWfOperationService;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.tms.service.client.ContractClient;
import com.dlcg.tms.service.client.bean.CaiwuContractBean;
import com.zthzinfo.common.ResponseMapBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-22
 */
@RestController
@RequestMapping("/contract")
public class ContractController {

   @Autowired
   IContractService iContractService;
   @Autowired
    IWfOperationService iWfOperationService;
   @Resource
    ContractClient contractClient;
   @Autowired
   IShipLineService shipLineService;

    @RequestMapping("/getContract")
    @UserMenuPermission("/contract")
    public Map<String, Object> getContract(
            @RequestParam(value = "qName",required = false) String name,
            @RequestParam(value = "qContractType",required = false) Integer ContractType,
            @RequestParam(value = "pageNo",required = false) Integer pageNo,
            @RequestParam(value = "pageSize",required = false) Integer pageSize
    ) {
        if(pageNo==null||pageNo<1){
            pageNo = 1;
        }
        if(pageSize==null||pageSize<0){
            pageSize = 10;
        }

        IPage<Contract> page= iContractService.getContractList(pageNo,pageSize,name,ContractType);

        List<Contract> contracts = page.getRecords();
        List<ContractBean> contractBeans = new ArrayList<>();
        for(Contract contract:contracts){
            ContractBean contractBean = new ContractBean();
            BeanUtils.copyProperties(contract,contractBean);
            contractBeans.add(contractBean);
        }
        iWfOperationService.setLinkBean("htsh",contractBeans,"id","wfWorkflowBean");
        IPage<ContractBean> page1 = new Page<>();
        BeanUtils.copyProperties(page,page1);
        page1.setRecords(contractBeans);
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        boolean a = iWfOperationService.haveApply("htsh");
        return ResponseMapBuilder.newBuilder()
                .put("page",page1)
                .put("currentId",adminUser.getId())
                .put("haveApply",a)
                .putSuccess()
                .getResult();
    }


    @RequestMapping("/updateContract")
    @UserMenuPermission("/contract")
    public Map<String, Object> updateContract(
            @ModelAttribute Contract contract
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        contract.setUpdateBy(adminUser.getId());
        contract.setUpdateDate(new Date());
        iContractService.updateById(contract);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }
    @RequestMapping("/delContract")
    @UserMenuPermission("/contract")
    public Map<String, Object> delContract(
            @RequestParam(value = "contractid",required = false) String contractid
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        Contract contract = new Contract();
        contract.setId(contractid);
        contract.setUpdateBy(adminUser.getId());
        contract.setUpdateDate(new Date());
        contract.setDelFlag("1");
        iContractService.updateById(contract);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @RequestMapping("/saveContract")
    @UserMenuPermission("/contract")
    public Map<String, Object> saveContract(
            @ModelAttribute Contract contract
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        contract.setId(IdUtil.simpleUUID());
        contract.setCreateBy(adminUser.getId());
        contract.setCreateDate(new Date());
        iContractService.save(contract);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @RequestMapping("/passOrReject")
    @UserMenuPermission("/contract")
    public Map<String, Object> passOrReject(
            @RequestParam(value = "contractid",required = false) String contractid,
            @RequestParam(value = "status",required = false) String status,
            @RequestParam(value = "remarks",required = false) String remarks){
        iWfOperationService.operation("htsh",contractid,status,remarks);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @GetMapping("/getContractByIds")
    public Map<String, Object> getContractByIds(
            @RequestParam("ids") String ids
    ) {
        Assert.notBlank(ids);
        List<Map> maps = contractClient.queryContracts(StrUtil.split(ids, ","));
        return ResultBody.ok().data(maps);
    }
    @GetMapping("/getContractByShipLineId")
    public Map<String, Object> getContractByShipLineId(
            @RequestParam("ids") String ids
    ) {
        Assert.notBlank(ids);
        Collection<ShipLine> shipLines = shipLineService.listByIds(Arrays.asList(StrUtil.split(ids, ",")));
        String [] contractIds = shipLines.stream().map(ShipLine::getContractId).toArray(String[]::new);
        if(contractIds.length==0) {
            return ResultBody.ok().data(new ArrayList<>());
        }
        List<Map> maps = contractClient.queryContracts(contractIds);
        return ResultBody.ok().data(maps);
    }

    @GetMapping("/getShipContractListPage")
    public Map<String, Object> getShipContractListPage(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "contractNo", required = false) String contractNo,
            @RequestParam(value = "partyAName", required = false) String partyAName,
            @RequestParam(value = "partyBName", required = false) String partyBName,
            @RequestParam(value = "contractModule", required = false) Integer contractModule,
            @RequestParam(value = "signStartDate", required = false) String signStartDate,
            @RequestParam(value = "signEndDate", required = false) String signEndDate,
            @RequestParam(value = "spStatus", required = false) Integer spStatus
    ) {
        Map<String, Object> contractListPage = contractClient.getShipContractListPage(pageNo, pageSize, contractNo, partyAName, partyBName, contractModule, signStartDate, signEndDate, spStatus);
        if (null != contractListPage && null != contractListPage.get("data")) {
            List<CaiwuContractBean> list = JSONObject.parseArray(JSONObject.toJSONString(contractListPage.get("data")), CaiwuContractBean.class);
            List<String> ids = list.stream().map(CaiwuContractBean::getId).collect(Collectors.toList());
            List<CaiwuContractBean> addition = shipLineService.queryShipContractAdditionInfo(ids);
            for (CaiwuContractBean add : addition) {
                for (CaiwuContractBean bean : list) {
                    if (add.getId().equals(bean.getId())) {
                        bean.setShipTime(add.getShipTime());
                        bean.setEpartureTimeDate(add.getEpartureTimeDate());
                        bean.setShipName(add.getShipName());
                        bean.setDanzhengUserName(add.getDanzhengUserName());
                        bean.setFindShipUserName(add.getFindShipUserName());
                        break;
                    }
                }
            }
            contractListPage.put("data", list);
        }
        return contractListPage;
    }
}
