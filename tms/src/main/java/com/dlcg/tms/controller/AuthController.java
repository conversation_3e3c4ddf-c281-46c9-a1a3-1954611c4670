package com.dlcg.tms.controller;

import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.tms.model.ResultBody;
import com.zthzinfo.microservice.security.TokenUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = "/auth")
public class AuthController {
    @Value("${open.ship.url}")
    private String openShipUrl;
    @Value("${open.ship.key}")
    private String openShipKey;
    // 获取当前用户，请求权限
    @RequestMapping("/open-ship")
    @ResponseBody
    public Map<String, Object> openShip(){
        String name = CurrentUserInfoAdmin.getAdminUserValue().getWxUserId();
        Map<String, String> params = new HashMap<>();
        params.put("time",System.currentTimeMillis()+"");
        params.put("name",name);
        String token =  TokenUtil.generateToken(openShipKey, params.keySet(),params::get);
        params.put("token",token);
        return ResultBody.ok().data(params);
//        String url = openShipUrl + "/admin-api/open-user/openUser" + "?name=" + name + "&time=" + params.get("time") + "&token=" + token;
        // 5分钟内有效
/*        Map<String,String> result = new HashMap<>();
//        result.put("url",url);
        result.put("name",name);
        result.put("time",params.get("time"));
        result.put("token",token);
        return ResultBody.ok().data(result);*/
    }
}
