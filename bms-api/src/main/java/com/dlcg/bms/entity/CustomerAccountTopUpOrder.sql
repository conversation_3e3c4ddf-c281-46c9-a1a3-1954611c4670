-- DROP TABLE IF EXISTS `customer_account_top_up_order`;
CREATE TABLE customer_account_top_up_order(
    `id` VARCHAR(64) NOT NULL COMMENT 'id',
    `order_no` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '订单号',
    `account_id` VARCHAR(64) NOT NULL DEFAULT '' COMMENT '账户ID',
    `top_up_amount` DECIMAL(14,2) NOT NULL DEFAULT 0 COMMENT '充值金额',
    `top_up_remark` VARCHAR(255) DEFAULT '' COMMENT '充值说明',
    `approval_status` TINYINT (2) DEFAULT 0 COMMENT '审批状态',
    `approval_finish_time` DATETIME  DEFAULT NULL COMMENT '审批完成时间',
    `business_type` TINYINT(2) NOT NULL DEFAULT -1 COMMENT '业务类型 BusinessTypeEnum',

    `create_by` VARCHAR(64)  DEFAULT '' COMMENT '创建者',
    `create_time` DATETIME  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(64)  DEFAULT '' COMMENT '修改者',
    `update_time` DATETIME  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `remark` VARCHAR(128)  DEFAULT '' COMMENT '备注',
    `del_flag` TINYINT(2)  DEFAULT 1 COMMENT '逻辑删除标记，0：已删除，1：正常',
    PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '充值订单表';