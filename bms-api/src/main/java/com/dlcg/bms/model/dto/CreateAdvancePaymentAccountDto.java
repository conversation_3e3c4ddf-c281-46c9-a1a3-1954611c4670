package com.dlcg.bms.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class CreateAdvancePaymentAccountDto implements Serializable {

    /** 客户ID */
    private String customerId;
    /** 船舶ID */
    private String shipId;
    /** 充值金额 */
    private BigDecimal balance;
    /** 充值备注 */
    private String topUpRemark;
    /** 审批结果 */
    private Integer approvalResult;
    /** 账户ID */
    private Integer accountId;

    private Integer receiveType;
    
    private Integer advanceType;
    // 付款公司
    private String receiveCompany;

    public String getReceiveCompany() {
        return receiveCompany;
    }

    public void setReceiveCompany(String receiveCompany) {
        this.receiveCompany = receiveCompany;
    }

    public Integer getAdvanceType() {
        return advanceType;
    }
    
    public void setAdvanceType(Integer advanceType) {
        this.advanceType = advanceType;
    }
    
    public Integer getReceiveType() {
        return receiveType;
    }

    public void setReceiveType(Integer receiveType) {
        this.receiveType = receiveType;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    private static final long serialVersionUID = -8935727425194476109L;

    public String getShipId() {
        return shipId;
    }

    public void setShipId(String shipId) {
        this.shipId = shipId;
    }

    public Integer getApprovalResult() {
        return approvalResult;
    }

    public void setApprovalResult(Integer approvalResult) {
        this.approvalResult = approvalResult;
    }

    public String getTopUpRemark() {
        return topUpRemark;
    }

    public void setTopUpRemark(String topUpRemark) {
        this.topUpRemark = topUpRemark;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }
}
