package com.dlcg.bms.model.vo;

import com.dlcg.bms.entity.CustomerAccountWater;

public class CustomerAccountWaterVo extends CustomerAccountWater {

    private String businessTypeStr;
    private String processStatusStr;
    private String processResultStr;
    private String objectId;
    private String customerName;

    public String getBusinessTypeStr() {
        return businessTypeStr;
    }

    public void setBusinessTypeStr(String businessTypeStr) {
        this.businessTypeStr = businessTypeStr;
    }

    public String getProcessStatusStr() {
        return processStatusStr;
    }

    public void setProcessStatusStr(String processStatusStr) {
        this.processStatusStr = processStatusStr;
    }

    public String getProcessResultStr() {
        return processResultStr;
    }

    public void setProcessResultStr(String processResultStr) {
        this.processResultStr = processResultStr;
    }

    public String getObjectId() {
        return objectId;
    }

    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
}
