package com.dlcg.bms.enums;

/**
 * 业务类型 - 充值订单
 */
public enum BusinessTypeEnum {
    TOP_UP(1,"充值"),
    TO_USE(2,"抵用"),
    OPEN_ACCOUNT(3,"开户"),
    SPLIT(4,"拆分"),
    ;

    private final int key;
    private final String desc;

    BusinessTypeEnum(int key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static String getDescByKey(int key){
        for (BusinessTypeEnum value : BusinessTypeEnum.values()) {
            if (value.getKey() == key){
                return value.getDesc();
            }
        }
        return null;
    }

    public int getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}
