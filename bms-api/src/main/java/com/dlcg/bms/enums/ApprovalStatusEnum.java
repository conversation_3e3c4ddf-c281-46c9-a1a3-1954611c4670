package com.dlcg.bms.enums;

/**
 * 审批状态 - 账户充值
 */
public enum ApprovalStatusEnum {
    SPING(1,"审批中")
    ,AGREE(2,"审核通过")
    ,REJECT(3,"审核拒绝")
    ;

    private final int key;
    private final String desc;

    ApprovalStatusEnum(int key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static String getDescByKey(int key){
        for (ApprovalStatusEnum value : ApprovalStatusEnum.values()) {
            if (value.getKey() == key){
                return value.getDesc();
            }
        }
        return null;
    }

    public int getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}
