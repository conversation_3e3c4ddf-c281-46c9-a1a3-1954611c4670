package com.dlcg.bms.client;


import com.dlcg.bms.model.dto.AccountTopUpDto;
import com.dlcg.bms.model.dto.CreateAdvancePaymentAccountDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 预付款 API Client
 */
@FeignClient(value = "dlcg-bms", url = "${debug.dlcg-bms:}")
@RequestMapping(value = "/client/advance-payment")
public interface AdvancePaymentClient {

	/**
	 * 开户之前
	 */
	@PostMapping("/create-account-before")
	Map<String,Object> beforeCreateAccount(
			@RequestBody CreateAdvancePaymentAccountDto dto
	);

	/**
	 * 开户之后
	 */
	@PostMapping("/tcreate-account-after")
	Map<String,Object> afterCreateAccount(@RequestBody CreateAdvancePaymentAccountDto dto);

	/**
	 * 充值之前
	 */
	@PostMapping("/top-up-before")
	Map<String,Object> beforeTopUp(@RequestBody AccountTopUpDto dto);

	/**
	 * 充值之后
	 */
	@PostMapping("/top-up-after")
	Map<String,Object> afterTopUp(@RequestBody AccountTopUpDto dto);
	
	/**
	 * 更新账户余额，并创建流水
	 */
	@RequestMapping("/updateAccount")
	Map<String,Object> updateAccount(
			@RequestParam(value = "sysSupplierId", required = false) String sysSupplierId,
			@RequestParam(value = "price", required = false) Double price);

	@RequestMapping("/queryAccount")
	Map<String,Object> queryAccount(
			@RequestParam(value = "sysSupplierId", required = false) String sysSupplierId
			);
	
}
