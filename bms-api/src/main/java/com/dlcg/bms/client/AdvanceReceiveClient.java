package com.dlcg.bms.client;


import com.dlcg.bms.entity.CustomerAccountTopUpOrder;
import com.dlcg.bms.entity.ReceiveWater;
import com.dlcg.bms.model.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * 预付款 API Client
 */
@FeignClient(value = "dlcg-bms", url = "${debug.dlcg-bms:}")
@RequestMapping(value = "/client/advance-receive")
public interface AdvanceReceiveClient {

	/**
	 * 开户之前
	 */
	@PostMapping("/create-account-before")
	Map<String,Object> beforeCreateAccount(
			@RequestBody CreateAdvanceReceiveAccountDto dto
	);

	/**
	 * 开户之后
	 */
	@PostMapping("/tcreate-account-after")
	Map<String,Object> afterCreateAccount(@RequestBody CreateAdvanceReceiveAccountDto dto);

	/**
	 * 充值之前
	 */
	@PostMapping("/top-up-before")
	Map<String,Object> beforeTopUp(@RequestBody AdvanceReceiveAccountTopUpDto dto);

	/**
	 * 充值之后
	 */
	@PostMapping("/top-up-after")
	Map<String,Object> afterTopUp(@RequestBody AdvanceReceiveAccountTopUpDto dto);

	/**
	 * 查询预收款余额
	 */
	@RequestMapping("/queryCharge")
	Map<String,Object> queryCharge(
			@RequestParam(value = "customerId", required = false) String customerId);

	/**
	 * 更新账户余额，并创建流水
	 */
	@RequestMapping("/updateAccount")
	Map<String,Object> updateAccount(
			@RequestParam(value = "customerId", required = false) String customerId,
			@RequestParam(value = "totalDeduct", required = false) Double totalDeduct,
			@RequestParam(value = "waterIds", required = false) String waterIds);
	
	/**
	 * 更新账户余额，并创建流水
	 */
	@RequestMapping("/updateAccountByWaterId")
	Map<String,Object> updateAccountByWaterId(@RequestParam(value = "waterId", required = false) String waterId);

	/**
	 * 新增收款流水
	 */
	@PostMapping("/addReceiveWater")
	Map<String,Object> addReceiveWater(@RequestBody AddReceiveWaterDto dto);

	/**
	 * 更新收款流水
	 * @param dto
	 * @return
	 */
	@PostMapping("/updateAddReceiveWater")
	Map<String,Object> updateAddReceiveWater(@RequestBody AddReceiveWaterDto dto);
	/**
	 * 删除收款流水
	 * @param receiveWaterId
	 * @return
	 */
	@PostMapping("/deleteAddReceiveWater")
	Map<String,Object> deleteAddReceiveWater(@RequestBody String receiveWaterId);

	@PostMapping("/addReceiveWaterList")
	Map<String,Object> addReceiveWaterList(@RequestBody List<AddReceiveWaterDto> dtos);

	@RequestMapping("/updateReceiveWaterApplyStatus")
	Map<String,Object> updateReceiveWaterApplyStatus(
			@RequestParam(value = "waterId", required = false) String waterId,
			@RequestParam(value = "applyStatus", required = false) Integer applyStatus,
			@RequestParam(value = "isDeduct", required = false) Integer isDeduct,
			@RequestParam(value = "status", required = false) String status);


	@PostMapping("/addAdvanceAccountAndWater")
	Map<String,Object> addAdvanceAccountAndWater(@RequestBody AddReceiveWaterDto dto);
	
	@PostMapping("/getReceiveWater")
	Map<String,Object> getReceiveWater(@RequestParam(value = "companyIds", required = false) String companyIds);


	@PostMapping("/getReceiveWaterById")
	ReceiveWater getReceiveWaterById(@RequestParam(value = "id") String id);

	@PostMapping("/getReceiveWaterByFdbizId")
	Map<String,Object> getReceiveWaterByFdbizId(@RequestParam(value = "fdbizId", required = false) String fdbizId);

	/**
	 * 预付款核销审批完成后更新账户余额
	 */
	@PostMapping("/updateAdvancePaymentDeduct")
	Map<String,Object> updateAdvancePaymentDeduct(@RequestBody UpdateAdvancePaymentDto updateAdvancePaymentDto);
}
