2025-05-14 00:15:25.021 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:21, serverValue:92281}] to ************:27017
2025-05-14 00:49:36.477 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:22, serverValue:92288}] to ************:27017
2025-05-14 01:22:06.926 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:23, serverValue:92292}] to ************:27017
2025-05-14 01:54:48.305 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:24, serverValue:92297}] to ************:27017
2025-05-14 02:27:10.715 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:25, serverValue:92306}] to ************:27017
2025-05-14 03:01:34.177 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:26, serverValue:92310}] to ************:27017
2025-05-14 03:34:06.229 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:27, serverValue:92314}] to ************:27017
2025-05-14 03:50:51.659 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 03:50:52.581 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 04:07:10.098 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:28, serverValue:92319}] to ************:27017
2025-05-14 04:40:49.985 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:29, serverValue:92323}] to ************:27017
2025-05-14 04:55:30.853 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:30, serverValue:92327}] to ************:27017
2025-05-14 05:29:59.718 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:31, serverValue:92331}] to ************:27017
2025-05-14 06:03:48.248 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:32, serverValue:92335}] to ************:27017
2025-05-14 06:36:40.604 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:33, serverValue:92341}] to ************:27017
2025-05-14 06:56:27.081 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:34, serverValue:92346}] to ************:27017
2025-05-14 07:12:17.445 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:35, serverValue:92350}] to ************:27017
2025-05-14 07:44:46.448 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 07:44:50.044 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:36, serverValue:92354}] to ************:27017
2025-05-14 07:55:41.870 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:37, serverValue:92358}] to ************:27017
2025-05-14 07:55:46.649 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 07:55:47.627 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 08:15:53.264 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:38, serverValue:92363}] to ************:27017
2025-05-14 08:32:48.966 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:39, serverValue:92369}] to ************:27017
2025-05-14 08:39:30.295 [cluster-ClusterId{value='682297fb3f84426b6b3ceff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:40, serverValue:92370}] to ************:27017
2025-05-14 08:40:15.000 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 08:40:15.439 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 08:43:46.080 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 08:45:06.033 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 08:45:06.431 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 08:48:06.430 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 08:48:06.837 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 08:48:46.080 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 08:51:06.834 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 08:51:07.307 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 08:53:46.077 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 08:54:07.306 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 08:54:07.730 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 08:57:07.805 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 08:57:08.217 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 08:58:46.155 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:00:08.216 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:00:08.618 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:03:08.619 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:03:09.026 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:03:46.157 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:06:09.026 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:06:09.643 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:08:46.160 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:09:09.645 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:09:10.045 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:12:10.075 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:12:10.503 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:13:46.199 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:15:10.504 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:15:11.490 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:18:11.492 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:18:12.111 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:18:46.203 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:21:12.112 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:21:12.824 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:23:46.208 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:24:12.825 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:24:13.269 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:27:13.256 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:27:13.693 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:28:46.198 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:30:13.693 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:30:14.185 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:33:14.187 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:33:14.730 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:33:46.201 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:36:14.734 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:36:15.307 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:38:46.206 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:39:15.308 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:39:15.816 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:42:15.816 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:42:16.346 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:43:46.213 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:45:16.349 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:45:16.771 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:48:16.775 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:48:17.330 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:48:46.214 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:51:17.332 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:51:17.776 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:53:46.215 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 09:54:17.778 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:54:18.249 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:57:18.253 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 09:57:19.153 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 09:58:46.221 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:00:19.153 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:00:19.564 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:03:19.569 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:03:20.050 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:03:46.224 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:06:20.049 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:06:20.447 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:08:46.227 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:09:20.450 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:09:20.936 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:12:21.035 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:12:21.447 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:13:46.337 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:15:21.449 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:15:21.893 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:18:21.895 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:18:22.292 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:18:46.344 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:21:22.297 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:21:23.502 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:23:46.348 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:24:23.505 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:24:24.059 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:27:24.059 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:27:24.661 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:28:46.347 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:30:24.659 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:30:25.019 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:33:25.021 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:33:25.411 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:33:46.356 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:36:25.413 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:36:25.788 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:38:46.365 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:39:25.790 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:39:26.186 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:42:26.187 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:42:26.697 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:43:46.357 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:45:26.684 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:45:27.114 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:48:27.115 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:48:27.672 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:48:46.362 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:50:40.733 [http-nio-8073-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-14 10:51:27.675 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:51:28.084 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:53:46.367 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 10:54:28.085 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:54:28.497 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:57:28.500 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 10:57:28.857 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 10:58:46.374 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:00:28.917 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:00:29.309 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:03:29.313 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:03:29.709 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:03:46.436 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:06:29.715 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:06:31.460 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:08:46.445 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:09:31.465 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:09:31.947 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:12:31.951 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:12:32.353 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:13:46.454 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:15:32.385 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:15:32.843 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:18:32.853 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:18:33.301 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:18:46.493 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:21:33.311 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:21:33.766 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:23:46.504 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:24:33.771 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:24:34.151 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:27:34.159 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:27:34.574 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:28:46.516 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:30:34.590 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:30:35.028 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:33:35.038 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:33:35.453 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:33:46.537 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:36:35.458 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:36:35.875 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:38:46.554 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:39:35.882 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:39:36.305 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:42:36.312 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:42:36.740 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:43:46.568 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:45:36.748 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:45:37.126 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:48:37.059 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:48:37.509 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:48:46.511 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:51:37.512 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:51:37.902 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:53:46.525 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 11:54:37.910 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:54:38.317 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:57:38.324 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 11:57:38.740 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 11:58:46.539 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:00:38.743 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:00:39.183 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:03:39.185 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:03:39.618 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:03:46.540 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:06:39.620 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:06:40.058 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:08:46.545 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:09:40.063 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:09:40.494 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:12:40.495 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:12:40.884 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:13:46.557 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:15:40.886 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:15:41.299 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:18:41.296 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:18:41.738 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:18:46.560 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:21:41.744 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:21:42.277 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:23:46.568 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:24:42.282 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:24:42.930 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:27:42.935 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:27:43.368 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:28:46.582 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:30:43.371 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:30:43.763 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:33:43.769 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:33:44.319 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:33:46.588 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:36:44.321 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:36:44.695 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:38:46.601 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:39:44.697 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:39:45.080 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:42:45.082 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:42:45.472 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:43:46.615 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:45:45.478 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:45:46.084 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:48:46.108 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:48:46.487 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:48:46.649 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:51:46.491 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:51:46.873 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:53:46.653 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 12:54:46.877 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:54:47.261 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:57:47.265 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 12:57:47.626 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 12:58:46.668 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:00:47.632 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:00:48.005 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:03:46.660 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:03:47.990 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:03:48.381 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:06:48.370 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:06:48.903 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:08:46.638 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:09:48.882 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:09:49.269 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:12:49.272 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:12:49.646 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:13:46.644 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:15:49.648 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:15:50.056 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:18:46.639 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:18:50.047 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:18:50.478 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:19:34.633 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-05-14 13:19:34.654 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747199974654, current=DOWN, previous=UP]
2025-05-14 13:19:34.655 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073: registering service...
2025-05-14 13:19:34.667 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073 - registration status: 204
2025-05-14 13:19:34.853 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-14 13:19:34.853 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-14 13:19:34.879 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-14 13:19:34.880 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-14 13:19:37.888 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-14 13:19:37.892 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-05-14 13:19:37.893 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-14 13:19:37.894 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-05-14 13:19:37.895 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-14 13:19:37.895 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-14 13:19:37.906 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-14 13:19:39.626 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-14 13:19:42.226 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-14 13:19:43.910 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-05-14 13:19:45.924 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-05-14 13:19:45.924 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-14 13:19:45.924 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-14 13:19:46.174 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-14 13:19:46.708 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-14 13:19:49.995 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-14 13:19:53.589 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-14 13:19:53.805 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:92416}] to ************:27017
2025-05-14 13:19:53.840 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=33056750}
2025-05-14 13:19:59.483 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-14 13:19:59.500 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-14 13:19:59.501 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-14 13:19:59.510 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-14 13:19:59.510 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-14 13:20:00.993 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-14 13:20:01.061 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-14 13:20:01.061 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-14 13:20:01.292 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-14 13:20:01.292 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-14 13:20:01.727 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:20:01.778 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-14 13:20:01.778 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-14 13:20:01.778 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-14 13:20:01.778 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-14 13:20:01.778 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-14 13:20:01.778 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-14 13:20:01.778 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-14 13:20:01.999 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-14 13:20:02.000 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-14 13:20:02.002 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-14 13:20:02.005 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1747200002005 with initial instances count: 2
2025-05-14 13:20:02.012 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747200002012, current=UP, previous=STARTING]
2025-05-14 13:20:02.014 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073: registering service...
2025-05-14 13:20:02.018 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-05-14 13:20:02.036 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073 - registration status: 204
2025-05-14 13:20:02.065 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:20:02.068 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 22.898 seconds (JVM running for 23.43)
2025-05-14 13:20:02.196 [scheduling-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-14 13:20:02.214 [scheduling-1] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook installed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-14 13:20:02.215 [scheduling-1] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: dlcg-oa instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-05-14 13:20:02.220 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-05-14 13:20:02.224 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client dlcg-oa initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:org.springframework.cloud.netflix.ribbon.eureka.DomainExtractingServerList@1044386
2025-05-14 13:20:02.280 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:90)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at com.netflix.loadbalancer.LoadBalancerContext.getServerFromLoadBalancer(LoadBalancerContext.java:483)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:184)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:180)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:94)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:42)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber$1.call(OperatorRetryWithPredicate.java:127)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.enqueue(TrampolineScheduler.java:73)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.schedule(TrampolineScheduler.java:52)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:79)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:45)
	at rx.internal.util.ScalarSynchronousObservable$WeakSingleProducer.request(ScalarSynchronousObservable.java:276)
	at rx.Subscriber.setProducer(Subscriber.java:209)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:138)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:129)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.Observable.subscribe(Observable.java:10423)
	at rx.Observable.subscribe(Observable.java:10390)
	at rx.observables.BlockingObservable.blockForSingle(BlockingObservable.java:443)
	at rx.observables.BlockingObservable.single(BlockingObservable.java:340)
	at com.netflix.client.AbstractLoadBalancerAwareClient.executeWithLoadBalancer(AbstractLoadBalancerAwareClient.java:112)
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:83)
	... 19 common frames omitted
2025-05-14 13:20:02.281 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:20:33.257 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-14 13:23:02.283 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:23:02.840 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:25:01.785 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:26:02.840 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:26:03.232 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:29:03.234 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:29:03.714 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:30:01.789 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:32:03.717 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:32:04.152 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:35:01.761 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:35:04.119 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:35:04.536 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:38:04.537 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:38:04.934 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:40:01.763 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:41:04.936 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:41:05.396 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:44:05.395 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:44:05.813 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:45:01.768 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:47:05.816 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:47:06.211 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:50:01.820 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:50:06.258 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:50:06.692 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:53:06.694 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:53:07.168 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:55:01.826 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 13:56:07.170 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:56:07.624 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 13:59:07.626 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 13:59:07.997 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:01:14.278 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 14:03:20.439 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:03:20.839 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:06:14.276 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 14:06:20.842 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:06:21.413 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:09:21.412 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:09:21.816 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:11:14.277 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 14:12:21.816 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:12:22.221 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:24:58.541 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:92428}] to ************:27017
2025-05-14 14:30:03.448 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:30:03.805 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:30:55.504 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 14:33:03.809 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:33:04.332 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:43:48.254 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:92435}] to ************:27017
2025-05-14 14:45:29.639 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 14:45:38.459 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:45:38.836 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:48:38.835 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:48:43.391 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:50:32.659 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 14:51:43.392 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:51:43.770 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:54:43.770 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:54:44.142 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 14:55:32.662 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 14:57:44.140 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 14:57:45.602 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 15:04:04.265 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:92439}] to ************:27017
2025-05-14 15:05:51.637 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 15:06:04.574 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 15:06:05.044 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 15:10:13.686 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 15:10:14.039 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 15:12:00.285 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 15:16:58.921 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 15:16:59.470 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 15:37:21.274 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:92446}] to ************:27017
2025-05-14 15:38:29.699 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 15:38:32.168 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 15:39:15.399 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 15:48:37.030 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:92450}] to ************:27017
2025-05-14 15:49:07.335 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:92454}] to ************:27017
2025-05-14 15:49:36.839 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 15:49:38.222 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 15:52:20.073 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 15:52:38.225 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 15:52:39.526 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 15:55:39.567 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 15:55:40.119 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 15:57:20.118 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 16:12:47.163 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:92461}] to ************:27017
2025-05-14 16:13:26.718 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:13:27.934 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:16:27.937 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:16:28.518 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:17:06.720 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 16:19:28.519 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:19:29.144 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:22:21.616 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 16:22:44.035 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:22:44.473 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:29:58.616 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:92465}] to ************:27017
2025-05-14 16:31:57.194 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:31:57.736 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:33:34.345 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 16:34:57.740 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:34:58.336 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:37:58.338 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:37:58.771 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:38:34.354 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 16:40:58.775 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:40:59.392 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:43:34.361 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 16:43:59.396 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:43:59.929 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:46:59.957 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:47:00.636 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:48:34.394 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 16:50:00.638 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:50:01.152 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:53:01.155 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 16:53:01.762 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 16:53:34.401 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 16:56:56.040 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:129)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:502)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-05-14 16:57:09.343 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:11, serverValue:92467}] to ************:27017
2025-05-14 16:57:09.582 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=238117250}
2025-05-14 17:03:08.127 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 17:11:23.021 [scheduling-3] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (8485 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor152.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-14 17:11:23.022 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 17:56:22.551 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:12, serverValue:92476}] to ************:27017
2025-05-14 18:19:24.841 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 18:19:39.024 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:13, serverValue:92479}] to ************:27017
2025-05-14 18:20:02.259 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 18:36:42.274 [scheduling-8] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (8485 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor152.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-14 18:36:42.276 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 18:36:59.603 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:14, serverValue:92485}] to ************:27017
2025-05-14 18:58:27.497 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:15, serverValue:92489}] to ************:27017
2025-05-14 19:22:54.255 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:16, serverValue:92492}] to ************:27017
2025-05-14 19:57:34.718 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:17, serverValue:92498}] to ************:27017
2025-05-14 19:59:45.251 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 19:59:55.277 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (8485 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor152.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-14 19:59:55.279 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-14 20:15:07.109 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:18, serverValue:92501}] to ************:27017
2025-05-14 20:32:20.950 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:19, serverValue:92505}] to ************:27017
2025-05-14 21:00:18.147 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:20, serverValue:92511}] to ************:27017
2025-05-14 21:00:31.396 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-14 21:17:02.941 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:21, serverValue:92518}] to ************:27017
2025-05-14 21:33:52.675 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:22, serverValue:92521}] to ************:27017
2025-05-14 22:08:28.060 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:23, serverValue:92528}] to ************:27017
2025-05-14 22:40:24.481 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:24, serverValue:92532}] to ************:27017
2025-05-14 23:13:40.380 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:25, serverValue:92537}] to ************:27017
2025-05-14 23:45:39.445 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-14 23:45:39.842 [cluster-ClusterId{value='682427f9e4939439d947a1e8', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:26, serverValue:92543}] to ************:27017
