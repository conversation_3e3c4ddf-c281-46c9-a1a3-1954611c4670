2025-04-10 00:02:38.469 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:02:38.641 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:02:39.301 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:27, serverValue:86826}] to ************:27017
2025-04-10 00:08:35.036 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:09:00.744 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:09:00.950 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:12:00.952 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:12:01.257 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:13:35.040 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:15:01.261 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:15:01.476 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:18:01.477 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:18:01.680 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:18:35.035 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:21:01.656 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:21:01.840 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:23:35.031 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:24:01.841 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:24:02.071 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:27:02.071 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:27:02.263 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:28:35.032 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:30:02.264 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:30:02.480 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:33:02.482 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:33:02.664 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:33:35.039 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:36:02.693 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:36:02.897 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:38:35.075 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:39:02.902 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:39:03.080 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:42:03.082 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:42:03.287 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:43:35.076 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:45:03.289 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:45:03.471 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:48:03.472 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:48:03.732 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:48:35.079 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:51:03.733 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:51:03.913 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:53:35.087 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 00:54:03.915 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:54:04.091 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:57:04.093 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 00:57:04.259 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 00:58:35.098 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 01:00:04.260 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:00:04.436 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:03:04.438 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:03:04.603 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:03:35.109 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 01:06:04.604 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:06:04.822 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:08:35.114 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 01:09:04.824 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:09:04.998 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:25:58.247 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:28, serverValue:86835}] to ************:27017
2025-04-10 01:41:57.264 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:29, serverValue:86853}] to ************:27017
2025-04-10 01:42:52.873 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:42:53.069 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:44:22.999 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 01:45:53.071 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:45:53.330 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:48:53.330 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:48:53.516 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:49:23.006 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 01:51:53.517 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:51:53.744 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:54:23.017 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 01:54:53.746 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:54:53.955 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:57:53.951 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 01:57:54.155 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 01:59:23.002 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:00:54.138 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:00:54.328 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:03:54.329 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:03:54.516 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:04:23.012 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:06:54.517 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:06:54.750 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:09:23.016 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:09:54.751 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:09:54.936 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:12:54.936 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:12:55.120 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:14:23.018 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:15:55.124 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:15:55.319 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:18:55.319 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:18:55.523 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:19:23.021 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:21:55.527 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:21:55.710 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:24:23.023 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:24:55.712 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:24:55.912 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:27:55.917 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:27:56.121 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:29:23.034 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:30:56.122 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:30:56.584 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:33:56.586 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:33:56.771 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:34:23.038 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:36:56.771 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:36:56.940 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:39:23.045 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:39:56.940 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:39:57.108 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:42:57.113 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:42:57.292 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 02:44:23.074 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 02:45:57.315 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 02:45:57.490 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:01:18.225 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:30, serverValue:86865}] to ************:27017
2025-04-10 03:04:00.584 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:04:00.761 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:04:26.177 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:07:00.761 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:07:00.941 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:09:26.186 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:10:00.942 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:10:01.161 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:13:01.165 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:13:01.347 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:14:26.192 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:16:01.347 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:16:01.526 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:19:01.510 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:19:01.692 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:19:26.178 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:22:01.693 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:22:01.894 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:24:26.184 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:25:01.895 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:25:02.093 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:28:02.093 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:28:02.281 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:29:26.187 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:31:02.280 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:31:02.471 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:34:02.462 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:34:02.645 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:34:26.180 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:37:02.644 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:37:02.856 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:39:26.185 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:40:02.855 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:40:03.071 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:43:03.073 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:43:03.275 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:44:26.185 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:46:03.274 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:46:03.527 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:49:03.540 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:49:03.736 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:49:26.202 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:52:03.738 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:52:03.925 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:54:26.206 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 03:55:03.925 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:55:04.115 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:58:04.114 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 03:58:04.311 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 03:59:26.210 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 04:01:04.312 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 04:01:04.493 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 04:18:02.819 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:31, serverValue:86870}] to ************:27017
2025-04-10 04:36:33.884 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:32, serverValue:86880}] to ************:27017
2025-04-10 04:52:55.996 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:33, serverValue:86884}] to ************:27017
2025-04-10 04:53:14.649 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 04:53:15.106 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 04:53:36.374 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 05:03:04.046 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:34, serverValue:86891}] to ************:27017
2025-04-10 05:21:26.155 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:35, serverValue:86897}] to ************:27017
2025-04-10 05:39:21.214 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:36, serverValue:86903}] to ************:27017
2025-04-10 05:39:39.793 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 05:39:39.968 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 05:42:01.064 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 05:42:39.967 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 05:42:40.147 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 05:45:40.152 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 05:45:40.322 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 05:47:01.065 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 05:48:40.320 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 05:48:40.505 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 05:51:40.503 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 05:51:40.678 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 05:52:01.070 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 05:54:40.682 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 05:54:40.885 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 05:57:01.072 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 05:57:40.890 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 05:57:41.065 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:00:41.064 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:00:41.257 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:02:01.076 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 06:03:41.256 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:03:41.449 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:06:41.455 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:06:41.665 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:07:01.083 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 06:09:41.663 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:09:41.834 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:12:01.090 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 06:12:41.837 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:12:42.025 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:15:42.026 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:15:42.451 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:17:01.097 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 06:18:42.453 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:18:42.883 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:21:42.887 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:21:43.049 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:22:01.105 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 06:24:43.047 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:24:43.202 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:27:01.112 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 06:27:43.201 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:27:43.357 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:30:43.356 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:30:43.510 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:32:01.116 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 06:33:43.512 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:33:43.667 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:36:43.668 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:36:43.813 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:37:01.123 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 06:39:43.817 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 06:39:44.000 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 06:55:22.531 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:37, serverValue:86913}] to ************:27017
2025-04-10 07:03:55.089 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:38, serverValue:86920}] to ************:27017
2025-04-10 07:21:01.186 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:39, serverValue:86929}] to ************:27017
2025-04-10 07:21:03.890 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 07:21:46.756 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 07:21:46.947 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 07:39:14.985 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:40, serverValue:86935}] to ************:27017
2025-04-10 07:40:10.213 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 07:40:10.404 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 07:57:32.359 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:41, serverValue:86941}] to ************:27017
2025-04-10 07:57:44.081 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 07:59:27.325 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 07:59:27.508 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:02:27.512 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:02:27.683 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:02:44.090 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:05:27.684 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:05:27.860 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:07:44.099 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:08:27.862 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:08:28.033 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:11:28.035 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:11:28.217 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:12:44.119 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:14:28.237 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:14:28.424 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:17:28.426 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:17:28.602 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:17:44.128 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:20:28.609 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:20:28.792 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:22:44.132 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:23:28.794 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:23:28.969 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:26:28.971 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:26:29.143 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:27:44.142 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:29:29.146 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:29:29.328 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:32:29.332 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:32:29.510 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:32:44.151 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:35:29.511 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:35:29.698 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:37:44.155 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:38:29.703 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:38:29.883 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:41:29.886 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:41:30.081 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:42:44.168 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:44:30.083 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:44:30.283 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:47:30.284 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:47:30.479 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 08:47:44.176 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 08:50:30.481 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 08:50:30.680 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:11:27.209 [cluster-ClusterId{value='67f5e827e47a5e1625c8411c', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:42, serverValue:86949}] to ************:27017
2025-04-10 09:11:52.513 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:12:39.003 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:12:39.211 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:15:39.213 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:15:39.448 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:16:52.513 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:18:39.449 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:18:39.693 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:21:39.651 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:21:39.828 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:21:41.505 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-04-10 09:21:41.522 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1744248101521, current=DOWN, previous=UP]
2025-04-10 09:21:41.522 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073: registering service...
2025-04-10 09:21:41.533 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - registration status: 204
2025-04-10 09:21:41.802 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-04-10 09:21:41.802 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-04-10 09:21:41.817 [SpringContextShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:8, serverValue:86693}] to ************:27017 because there was a socket exception raised on another connection from this pool.
2025-04-10 09:21:41.823 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-10 09:21:41.824 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-04-10 09:21:44.828 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-04-10 09:21:44.831 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-04-10 09:21:44.833 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-04-10 09:21:44.834 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-04-10 09:21:44.835 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-04-10 09:21:44.836 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-04-10 09:21:44.859 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-04-10 09:21:48.583 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-04-10 09:21:51.322 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-04-10 09:21:53.229 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-04-10 09:21:54.818 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-04-10 09:21:54.819 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-10 09:21:54.819 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-04-10 09:21:54.955 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-10 09:21:55.437 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-10 09:21:57.198 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-10 09:21:59.193 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-04-10 09:21:59.317 [cluster-ClusterId{value='67f71d37238b444e01b5049e', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:86958}] to ************:27017
2025-04-10 09:21:59.336 [cluster-ClusterId{value='67f71d37238b444e01b5049e', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=16193458}
2025-04-10 09:22:02.929 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-04-10 09:22:02.969 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-10 09:22:02.969 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-10 09:22:02.974 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-10 09:22:02.974 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-10 09:22:04.607 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-04-10 09:22:04.676 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-04-10 09:22:04.676 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-04-10 09:22:04.850 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-04-10 09:22:04.850 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-04-10 09:22:05.180 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:22:05.206 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-04-10 09:22:05.206 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-04-10 09:22:05.206 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-04-10 09:22:05.206 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-04-10 09:22:05.206 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-04-10 09:22:05.206 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-04-10 09:22:05.206 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-04-10 09:22:05.396 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-04-10 09:22:05.398 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-04-10 09:22:05.402 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-04-10 09:22:05.406 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1744248125405 with initial instances count: 2
2025-04-10 09:22:05.413 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1744248125413, current=UP, previous=STARTING]
2025-04-10 09:22:05.426 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073: registering service...
2025-04-10 09:22:05.431 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-04-10 09:22:05.467 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - registration status: 204
2025-04-10 09:22:05.494 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:22:05.498 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 17.287 seconds (JVM running for 17.691)
2025-04-10 09:22:05.613 [scheduling-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-04-10 09:22:05.633 [scheduling-1] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook installed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-04-10 09:22:05.633 [scheduling-1] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: dlcg-oa instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-04-10 09:22:05.641 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-04-10 09:22:05.645 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client dlcg-oa initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:org.springframework.cloud.netflix.ribbon.eureka.DomainExtractingServerList@2ffa4cd8
2025-04-10 09:22:05.741 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:90)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at com.netflix.loadbalancer.LoadBalancerContext.getServerFromLoadBalancer(LoadBalancerContext.java:483)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:184)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:180)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:94)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:42)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber$1.call(OperatorRetryWithPredicate.java:127)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.enqueue(TrampolineScheduler.java:73)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.schedule(TrampolineScheduler.java:52)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:79)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:45)
	at rx.internal.util.ScalarSynchronousObservable$WeakSingleProducer.request(ScalarSynchronousObservable.java:276)
	at rx.Subscriber.setProducer(Subscriber.java:209)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:138)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:129)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.Observable.subscribe(Observable.java:10423)
	at rx.Observable.subscribe(Observable.java:10390)
	at rx.observables.BlockingObservable.blockForSingle(BlockingObservable.java:443)
	at rx.observables.BlockingObservable.single(BlockingObservable.java:340)
	at com.netflix.client.AbstractLoadBalancerAwareClient.executeWithLoadBalancer(AbstractLoadBalancerAwareClient.java:112)
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:83)
	... 19 common frames omitted
2025-04-10 09:22:05.742 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:22:36.876 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-04-10 09:25:05.741 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:25:06.063 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:27:05.213 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:28:06.063 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:28:06.251 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:31:06.253 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:31:06.440 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:32:05.220 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:34:06.441 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:34:06.650 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:37:05.229 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:37:06.661 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:37:06.828 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:40:06.830 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:40:07.024 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:42:05.230 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:43:07.026 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:43:07.273 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:46:07.273 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:46:07.451 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:47:05.233 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:49:07.453 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:49:07.669 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:52:05.233 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:52:07.666 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:52:07.877 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:55:07.878 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:55:08.120 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 09:57:05.244 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 09:58:08.120 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 09:58:08.342 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:01:08.344 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:01:09.338 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:02:05.248 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:04:09.341 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:04:09.617 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:07:05.256 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:07:09.621 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:07:09.858 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:10:09.860 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:10:10.096 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:12:05.261 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:13:10.097 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:13:10.308 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:16:10.308 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:16:10.500 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:17:05.267 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:19:10.501 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:19:10.762 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:22:05.274 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:22:10.760 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:22:10.970 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:25:10.973 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:25:11.447 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:27:05.283 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:28:11.450 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:28:11.655 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:31:11.655 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:31:11.842 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:32:05.286 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:34:11.841 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:34:12.030 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:37:05.291 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:37:12.032 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:37:12.238 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:40:12.238 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:40:12.419 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:42:05.292 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:43:12.420 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:43:12.626 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:46:12.627 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 10:46:12.875 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 10:47:00.101 [cluster-ClusterId{value='67f71d37238b444e01b5049e', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Can't connect to SOCKS proxy:Connection refused (Connection refused)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:428)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-04-10 10:47:05.896 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 10:47:07.711 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-04-10 10:47:07.725 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1744253227725, current=DOWN, previous=UP]
2025-04-10 10:47:07.734 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073: registering service...
2025-04-10 10:47:07.914 [DiscoveryClient-InstanceInfoReplicator-0] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.SocketException: Connection reset
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.register(AbstractJerseyEurekaHttpClient.java:57)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:161)
	at org.apache.http.impl.io.SocketInputBuffer.fillBuffer(SocketInputBuffer.java:82)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:276)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 29 common frames omitted
2025-04-10 10:47:07.918 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.SocketException: Connection reset
2025-04-10 10:47:07.925 [DiscoveryClient-InstanceInfoReplicator-0] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.register(AbstractJerseyEurekaHttpClient.java:57)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 30 common frames omitted
2025-04-10 10:47:07.927 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-04-10 10:47:07.927 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - registration failed Cannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-04-10 10:47:07.928 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.InstanceInfoReplicator - There was a problem with the instance info replicator
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-04-10 10:47:08.489 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-04-10 10:47:08.490 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-04-10 10:47:08.608 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-10 10:47:08.611 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-04-10 15:07:21.820 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-04-10 15:07:24.664 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-04-10 15:07:26.894 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-04-10 15:07:28.914 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-04-10 15:07:28.914 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-10 15:07:28.914 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-04-10 15:07:29.114 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-10 15:07:29.399 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-10 15:07:30.914 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-10 15:07:32.953 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-04-10 15:07:33.077 [cluster-ClusterId{value='67f76e34ce9404414d4c4f39', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:87016}] to ************:27017
2025-04-10 15:07:33.100 [cluster-ClusterId{value='67f76e34ce9404414d4c4f39', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=21207333}
2025-04-10 15:07:36.176 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-04-10 15:07:36.185 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-10 15:07:36.185 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-10 15:07:36.198 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-10 15:07:36.198 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-10 15:07:37.177 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-04-10 15:07:37.214 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-04-10 15:07:37.214 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-04-10 15:07:37.384 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-04-10 15:07:37.385 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-04-10 15:07:37.613 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 15:07:37.633 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-04-10 15:07:37.633 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-04-10 15:07:37.633 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-04-10 15:07:37.633 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-04-10 15:07:37.633 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-04-10 15:07:37.633 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-04-10 15:07:37.634 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-04-10 15:07:37.776 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-04-10 15:07:37.777 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-04-10 15:07:37.778 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-04-10 15:07:37.780 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1744268857779 with initial instances count: 0
2025-04-10 15:07:37.783 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1744268857783, current=UP, previous=STARTING]
2025-04-10 15:07:37.785 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073: registering service...
2025-04-10 15:07:37.787 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-04-10 15:07:37.831 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - registration status: 204
2025-04-10 15:07:37.836 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 15:07:37.838 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 16.312 seconds (JVM running for 16.781)
2025-04-10 15:07:37.919 [scheduling-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-04-10 15:07:37.928 [scheduling-1] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook installed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-04-10 15:07:37.929 [scheduling-1] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: dlcg-oa instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-04-10 15:07:37.933 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-04-10 15:07:37.935 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client dlcg-oa initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:org.springframework.cloud.netflix.ribbon.eureka.DomainExtractingServerList@28cc950f
2025-04-10 15:07:37.974 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:90)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at com.netflix.loadbalancer.LoadBalancerContext.getServerFromLoadBalancer(LoadBalancerContext.java:483)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:184)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:180)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:94)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:42)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber$1.call(OperatorRetryWithPredicate.java:127)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.enqueue(TrampolineScheduler.java:73)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.schedule(TrampolineScheduler.java:52)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:79)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:45)
	at rx.internal.util.ScalarSynchronousObservable$WeakSingleProducer.request(ScalarSynchronousObservable.java:276)
	at rx.Subscriber.setProducer(Subscriber.java:209)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:138)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:129)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.Observable.subscribe(Observable.java:10423)
	at rx.Observable.subscribe(Observable.java:10390)
	at rx.observables.BlockingObservable.blockForSingle(BlockingObservable.java:443)
	at rx.observables.BlockingObservable.single(BlockingObservable.java:340)
	at com.netflix.client.AbstractLoadBalancerAwareClient.executeWithLoadBalancer(AbstractLoadBalancerAwareClient.java:112)
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:83)
	... 19 common frames omitted
2025-04-10 15:07:37.974 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 15:08:07.781 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-04-10 15:08:07.781 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-04-10 15:08:07.781 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-04-10 15:08:07.781 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-04-10 15:08:07.781 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-04-10 15:08:07.781 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: false
2025-04-10 15:08:07.781 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-04-10 15:08:07.796 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-04-10 15:08:09.080 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-04-10 15:10:16.812 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-04-10 15:10:19.738 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-04-10 15:10:20.476 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-04-10 15:10:20.950 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-04-10 15:10:20.950 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-04-10 15:10:20.950 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-04-10 15:10:21.014 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-04-10 15:10:21.136 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-04-10 15:10:22.371 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-04-10 15:10:23.584 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-04-10 15:10:23.643 [cluster-ClusterId{value='67f76edf1faafa503288ebd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:87019}] to ************:27017
2025-04-10 15:10:23.657 [cluster-ClusterId{value='67f76edf1faafa503288ebd5', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=13108167}
2025-04-10 15:10:24.959 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-04-10 15:10:24.968 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-10 15:10:24.968 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-10 15:10:24.973 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-04-10 15:10:24.973 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-04-10 15:10:25.484 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-04-10 15:10:25.509 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-04-10 15:10:25.509 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-04-10 15:10:25.583 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-04-10 15:10:25.583 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-04-10 15:10:25.699 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-04-10 15:10:25.711 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-04-10 15:10:25.711 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-04-10 15:10:25.711 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-04-10 15:10:25.711 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-04-10 15:10:25.711 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-04-10 15:10:25.711 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-04-10 15:10:25.711 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-04-10 15:10:25.860 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-04-10 15:10:25.861 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-04-10 15:10:25.862 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-04-10 15:10:25.863 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1744269025863 with initial instances count: 5
2025-04-10 15:10:25.866 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1744269025866, current=UP, previous=STARTING]
2025-04-10 15:10:25.867 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073: registering service...
2025-04-10 15:10:25.867 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-04-10 15:10:25.893 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - registration status: 204
2025-04-10 15:10:25.907 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-10 15:10:25.907 [main] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-04-10 15:10:28.916 [main] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-04-10 15:10:28.929 [main] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - deregister  status: 200
2025-04-10 15:10:28.943 [main] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-04-10 15:10:28.945 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8073"]
2025-04-10 15:10:28.945 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-04-10 15:10:28.948 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8073"]
2025-04-10 15:10:28.948 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8073"]
2025-04-10 15:10:28.955 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8073 was already in use.

Action:

Identify and stop the process that's listening on port 8073 or configure this application to listen on another port.

2025-04-10 15:10:28.955 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8073 was already in use.

Action:

Identify and stop the process that's listening on port 8073 or configure this application to listen on another port.

2025-04-10 15:10:37.841 [DiscoveryClient-HeartbeatExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - Re-registering apps/DLCG-TMS
2025-04-10 15:10:37.842 [DiscoveryClient-HeartbeatExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073: registering service...
2025-04-10 15:10:37.848 [DiscoveryClient-HeartbeatExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - registration status: 204
2025-04-10 15:10:37.973 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-04-10 15:10:38.300 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-04-10 15:11:44.379 [cluster-ClusterId{value='67f76e34ce9404414d4c4f39', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Can't connect to SOCKS proxy:Connection refused (Connection refused)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:428)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-04-10 15:11:45.939 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-04-10 15:11:45.975 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1744269105975, current=DOWN, previous=UP]
2025-04-10 15:11:46.020 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073: registering service...
2025-04-10 15:11:46.081 [DiscoveryClient-InstanceInfoReplicator-0] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.SocketException: Connection reset
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.register(AbstractJerseyEurekaHttpClient.java:57)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:161)
	at org.apache.http.impl.io.SocketInputBuffer.fillBuffer(SocketInputBuffer.java:82)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:276)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 29 common frames omitted
2025-04-10 15:11:46.083 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.SocketException: Connection reset
2025-04-10 15:11:46.093 [DiscoveryClient-InstanceInfoReplicator-0] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.SocketException: Connection reset
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.register(AbstractJerseyEurekaHttpClient.java:57)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Connection reset
	at java.net.SocketInputStream.read(SocketInputStream.java:210)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.fillBuffer(AbstractSessionInputBuffer.java:161)
	at org.apache.http.impl.io.SocketInputBuffer.fillBuffer(SocketInputBuffer.java:82)
	at org.apache.http.impl.io.AbstractSessionInputBuffer.readLine(AbstractSessionInputBuffer.java:276)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:138)
	at org.apache.http.impl.conn.DefaultHttpResponseParser.parseHead(DefaultHttpResponseParser.java:56)
	at org.apache.http.impl.io.AbstractMessageParser.parse(AbstractMessageParser.java:259)
	at org.apache.http.impl.AbstractHttpClientConnection.receiveResponseHeader(AbstractHttpClientConnection.java:294)
	at org.apache.http.impl.conn.DefaultClientConnection.receiveResponseHeader(DefaultClientConnection.java:257)
	at org.apache.http.impl.conn.AbstractClientConnAdapter.receiveResponseHeader(AbstractClientConnAdapter.java:230)
	at org.apache.http.protocol.HttpRequestExecutor.doReceiveResponse(HttpRequestExecutor.java:273)
	at org.apache.http.protocol.HttpRequestExecutor.execute(HttpRequestExecutor.java:125)
	at org.apache.http.impl.client.DefaultRequestDirector.tryExecute(DefaultRequestDirector.java:679)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:481)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 30 common frames omitted
2025-04-10 15:11:46.094 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.SocketException: Connection reset
2025-04-10 15:11:46.095 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/********:dlcg-tms:8073 - registration failed Cannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-04-10 15:11:46.096 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.InstanceInfoReplicator - There was a problem with the instance info replicator
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-04-10 15:11:46.861 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-04-10 15:11:46.870 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-04-10 15:11:46.931 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-04-10 15:11:46.933 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
