2025-05-25 00:15:26.424 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 00:30:28.108 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:41, serverValue:95485}] to ************:27017
2025-05-25 01:02:54.004 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:42, serverValue:95491}] to ************:27017
2025-05-25 01:28:58.907 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:43, serverValue:95495}] to ************:27017
2025-05-25 01:29:42.625 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 01:29:43.699 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 01:46:22.323 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:44, serverValue:95499}] to ************:27017
2025-05-25 02:20:26.923 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:45, serverValue:95510}] to ************:27017
2025-05-25 02:52:49.498 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:46, serverValue:95516}] to ************:27017
2025-05-25 03:26:09.967 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:47, serverValue:95519}] to ************:27017
2025-05-25 03:58:23.460 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:48, serverValue:95523}] to ************:27017
2025-05-25 04:29:36.003 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:49, serverValue:95527}] to ************:27017
2025-05-25 04:46:20.292 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:50, serverValue:95531}] to ************:27017
2025-05-25 05:19:13.898 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:51, serverValue:95535}] to ************:27017
2025-05-25 05:33:41.132 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:52, serverValue:95540}] to ************:27017
2025-05-25 05:50:55.021 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:53, serverValue:95544}] to ************:27017
2025-05-25 06:22:25.262 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:54, serverValue:95548}] to ************:27017
2025-05-25 06:22:27.769 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 06:30:41.104 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:55, serverValue:95554}] to ************:27017
2025-05-25 06:31:13.561 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$InternalServerError: [500 ] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [{"resultCode":"99999","resultMsg":"服务器异常！请稍后再试"}]
	at feign.FeignException.serverErrorStatus(FeignException.java:231)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor278.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-25 06:31:13.561 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 06:47:32.948 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:56, serverValue:95560}] to ************:27017
2025-05-25 06:47:34.333 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 06:55:15.531 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:57, serverValue:95564}] to ************:27017
2025-05-25 07:12:08.498 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:58, serverValue:95567}] to ************:27017
2025-05-25 07:31:37.072 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:59, serverValue:95572}] to ************:27017
2025-05-25 07:49:54.925 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:60, serverValue:95575}] to ************:27017
2025-05-25 08:23:24.825 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:61, serverValue:95582}] to ************:27017
2025-05-25 08:32:39.689 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:62, serverValue:95587}] to ************:27017
2025-05-25 08:34:45.387 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 08:34:46.348 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 09:06:20.474 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:63, serverValue:95591}] to ************:27017
2025-05-25 09:23:56.486 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:64, serverValue:95597}] to ************:27017
2025-05-25 09:33:43.365 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:65, serverValue:95603}] to ************:27017
2025-05-25 09:41:58.847 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:66, serverValue:95605}] to ************:27017
2025-05-25 09:42:25.993 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 09:58:52.176 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:67, serverValue:95608}] to ************:27017
2025-05-25 10:06:42.594 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:68, serverValue:95612}] to ************:27017
2025-05-25 10:07:00.313 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 10:07:02.174 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 10:23:34.382 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:69, serverValue:95617}] to ************:27017
2025-05-25 10:34:34.825 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:70, serverValue:95621}] to ************:27017
2025-05-25 10:50:52.135 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:71, serverValue:95625}] to ************:27017
2025-05-25 11:26:16.757 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:72, serverValue:95629}] to ************:27017
2025-05-25 11:35:42.556 [cluster-ClusterId{value='683034e077654d2393228f3a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:73, serverValue:95633}] to ************:27017
2025-05-25 11:36:39.106 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 11:36:39.555 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 11:37:46.115 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 11:39:39.559 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 11:39:41.224 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 11:42:41.228 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 11:42:42.050 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 11:42:46.119 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 11:45:42.054 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 11:45:42.593 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 11:47:46.127 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 11:48:42.597 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 11:48:43.048 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 11:51:43.035 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 11:51:43.627 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 11:52:46.115 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 11:54:43.628 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 11:54:44.100 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 11:57:44.102 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 11:57:44.606 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 11:57:46.119 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:00:44.608 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:00:45.154 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:02:46.125 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:03:45.159 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:03:45.605 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:06:45.629 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:06:46.015 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:07:46.160 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:09:46.023 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:09:46.400 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:12:46.172 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:12:46.402 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:12:46.786 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:15:46.788 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:15:47.173 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:17:46.179 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:18:47.176 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:18:47.573 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:21:47.593 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:21:47.975 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:22:46.209 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:24:47.989 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:24:48.375 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:27:46.224 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:27:48.379 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:27:48.779 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:30:48.766 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:30:49.168 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:32:46.220 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:33:49.172 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:33:49.698 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:36:49.701 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:36:50.105 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:37:46.234 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:39:50.113 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:39:50.470 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:42:46.248 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:42:50.473 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:42:50.833 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:45:50.829 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:45:51.206 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:47:46.248 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:48:51.209 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:48:51.571 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:51:51.574 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:51:51.942 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:52:46.262 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:54:51.947 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:54:52.304 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 12:57:46.267 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 12:57:52.307 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 12:57:52.677 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:00:52.681 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:00:53.064 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:02:46.276 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:03:53.068 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:03:53.436 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:06:53.439 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:06:53.811 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:07:46.283 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:09:53.816 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:09:54.185 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:12:46.292 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:12:54.189 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:12:54.800 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:15:54.807 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:15:55.163 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:17:46.299 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:18:55.166 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:18:55.533 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:21:55.538 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:21:55.905 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:22:46.306 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:24:55.910 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:24:56.264 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:27:46.313 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:27:56.270 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:27:56.638 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:30:56.576 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:30:56.950 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:32:46.258 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:33:56.953 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:33:57.317 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:36:57.320 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:36:57.679 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:37:46.268 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:39:57.683 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:39:58.064 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:42:46.277 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:42:58.066 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:42:58.446 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:45:58.485 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:45:58.870 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:47:46.323 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:48:58.872 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:48:59.256 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:51:59.260 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:51:59.644 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:52:46.335 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:54:59.666 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:55:00.466 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 13:57:46.359 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 13:58:00.469 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 13:58:00.962 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:01:00.966 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:01:01.482 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:02:14.318 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-05-25 14:02:14.364 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748152934363, current=DOWN, previous=UP]
2025-05-25 14:02:14.368 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073: registering service...
2025-05-25 14:02:14.394 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - registration status: 204
2025-05-25 14:02:14.596 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-25 14:02:14.597 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-25 14:02:14.615 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-25 14:02:14.616 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-25 14:02:17.624 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-25 14:02:17.628 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-05-25 14:02:17.630 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-25 14:02:17.631 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-05-25 14:02:17.633 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-25 14:02:17.633 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-25 14:02:17.647 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-25 14:02:21.398 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-25 14:02:23.147 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-25 14:02:24.824 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-05-25 14:02:26.232 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-05-25 14:02:26.233 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-25 14:02:26.234 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-25 14:02:26.443 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-25 14:02:26.709 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-25 14:02:29.490 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-25 14:02:33.498 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-25 14:02:33.673 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:95639}] to ************:27017
2025-05-25 14:02:33.704 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=29583000}
2025-05-25 14:02:38.510 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-25 14:02:38.527 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-25 14:02:38.527 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-25 14:02:38.531 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-25 14:02:38.531 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-25 14:02:39.967 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-25 14:02:40.062 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-25 14:02:40.062 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-25 14:02:40.264 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-25 14:02:40.265 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-25 14:02:40.638 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:02:40.707 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-25 14:02:40.707 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-25 14:02:40.707 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-25 14:02:40.707 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-25 14:02:40.707 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-25 14:02:40.707 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-25 14:02:40.708 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-25 14:02:40.902 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-25 14:02:40.904 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-25 14:02:40.907 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-25 14:02:40.911 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1748152960910 with initial instances count: 1
2025-05-25 14:02:40.916 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748152960916, current=UP, previous=STARTING]
2025-05-25 14:02:40.918 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073: registering service...
2025-05-25 14:02:40.920 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-05-25 14:02:40.963 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - registration status: 204
2025-05-25 14:02:41.018 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:02:41.021 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 19.879 seconds (JVM running for 20.359)
2025-05-25 14:02:41.130 [scheduling-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-25 14:02:41.144 [scheduling-1] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook installed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-25 14:02:41.144 [scheduling-1] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: dlcg-oa instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-05-25 14:02:41.148 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-05-25 14:02:41.150 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client dlcg-oa initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:org.springframework.cloud.netflix.ribbon.eureka.DomainExtractingServerList@760537c5
2025-05-25 14:02:41.198 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:90)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at com.netflix.loadbalancer.LoadBalancerContext.getServerFromLoadBalancer(LoadBalancerContext.java:483)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:184)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:180)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:94)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:42)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber$1.call(OperatorRetryWithPredicate.java:127)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.enqueue(TrampolineScheduler.java:73)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.schedule(TrampolineScheduler.java:52)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:79)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:45)
	at rx.internal.util.ScalarSynchronousObservable$WeakSingleProducer.request(ScalarSynchronousObservable.java:276)
	at rx.Subscriber.setProducer(Subscriber.java:209)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:138)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:129)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.Observable.subscribe(Observable.java:10423)
	at rx.Observable.subscribe(Observable.java:10390)
	at rx.observables.BlockingObservable.blockForSingle(BlockingObservable.java:443)
	at rx.observables.BlockingObservable.single(BlockingObservable.java:340)
	at com.netflix.client.AbstractLoadBalancerAwareClient.executeWithLoadBalancer(AbstractLoadBalancerAwareClient.java:112)
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:83)
	... 19 common frames omitted
2025-05-25 14:02:41.198 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:03:12.312 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-25 14:05:41.202 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:05:41.626 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:07:40.717 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:08:41.628 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:08:41.962 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:11:41.977 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:11:42.388 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:12:40.737 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:14:42.390 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:14:42.784 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:17:40.744 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:17:42.787 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:17:43.142 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:20:43.144 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:20:43.501 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:22:40.758 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:23:43.509 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:23:43.864 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:26:43.871 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:26:44.223 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:27:40.771 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:29:44.226 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:29:44.602 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:32:40.792 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:32:44.614 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:32:44.958 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:32:45.555 [http-nio-8073-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-25 14:35:44.962 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:35:45.318 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:37:40.806 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:38:45.321 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:38:45.661 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:41:45.666 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:41:46.002 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:42:40.810 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:44:46.006 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:44:46.344 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:47:40.822 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:47:46.350 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:47:46.695 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:50:46.697 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:50:47.049 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:52:40.833 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:53:47.052 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:53:47.390 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:56:47.398 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:56:47.731 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 14:57:40.839 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 14:59:47.734 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 14:59:48.070 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:02:40.824 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:02:48.047 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:02:48.383 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:05:48.386 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:05:48.707 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:07:40.834 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:08:48.709 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:08:49.034 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:11:49.036 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:11:49.397 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:12:40.844 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:14:49.400 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:14:49.742 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:17:40.799 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:17:49.698 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:17:50.041 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:20:50.044 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:20:50.363 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:22:40.805 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:23:50.365 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:23:50.686 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:26:50.689 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:26:51.058 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:27:40.813 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:29:51.059 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:29:51.419 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:32:40.816 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:32:51.424 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:32:51.767 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:35:51.772 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:35:52.152 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:37:40.827 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:38:52.152 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:38:52.533 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:41:52.534 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:41:52.902 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:42:40.837 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:44:52.903 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:44:53.254 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:47:40.847 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:47:53.255 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:47:53.621 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:50:53.622 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:50:53.982 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:52:40.855 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:53:53.983 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:53:54.337 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:56:54.342 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:56:54.702 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 15:57:40.857 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 15:59:54.704 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 15:59:55.065 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:02:40.867 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:02:55.067 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:02:55.444 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:05:55.446 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:05:56.154 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:07:40.874 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:08:56.155 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:08:56.608 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:11:56.616 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:11:56.961 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:12:40.876 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:14:56.961 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:14:57.333 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:17:40.881 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:17:57.337 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:17:57.708 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:20:57.708 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:20:58.069 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:22:40.886 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:23:58.070 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:23:58.450 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:26:58.452 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:26:58.820 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:27:40.895 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:29:58.822 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:29:59.227 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:32:40.902 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:32:59.228 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:32:59.597 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:35:59.602 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:35:59.999 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:37:40.910 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:39:00.002 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:39:00.381 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:42:00.382 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:42:00.756 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:42:40.919 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:45:00.757 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:45:01.142 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:47:40.920 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:48:01.194 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:48:01.583 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:51:01.590 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:51:01.978 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:52:40.983 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 16:54:01.979 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:54:02.337 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:57:02.342 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 16:57:02.704 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 16:57:40.996 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:00:02.706 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:00:03.062 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:02:41.003 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:03:02.994 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:03:03.362 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:06:03.363 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:06:03.769 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:07:40.933 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:09:03.773 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:09:04.127 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:12:04.129 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:12:05.526 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:12:40.941 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:15:05.529 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:15:05.885 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:17:40.949 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:18:05.887 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:18:06.245 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:21:06.281 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:21:06.639 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:22:40.995 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:24:06.642 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:24:07.010 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:27:07.011 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:27:07.444 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:27:41.002 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:30:07.446 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:30:07.824 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:32:41.005 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:33:07.826 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:33:08.209 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:36:08.172 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:36:08.551 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:37:40.971 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:39:08.550 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:39:08.927 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:42:08.930 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:42:09.326 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:42:40.975 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:45:09.325 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:45:09.717 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:47:40.985 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:48:09.718 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:48:10.110 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:51:10.109 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:51:10.465 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:52:40.991 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 17:54:10.464 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:54:10.842 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:57:10.843 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 17:57:11.197 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 17:57:40.999 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:00:11.198 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:00:11.565 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:02:41.000 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:03:11.566 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:03:11.906 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:06:11.908 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:06:12.264 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:07:41.008 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:09:12.272 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:09:12.600 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:12:12.603 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:12:12.951 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:12:41.014 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:15:12.951 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:15:13.284 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:17:41.017 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:18:13.286 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:18:13.616 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:21:13.617 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:21:13.947 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:22:41.061 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:24:13.987 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:24:14.374 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:27:14.377 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:27:14.740 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:27:41.072 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:30:14.742 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:30:15.105 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:32:41.083 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:33:15.109 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:33:15.472 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:36:15.476 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:36:15.841 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:37:41.065 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:39:15.810 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:39:16.164 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:42:16.164 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:42:16.497 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:42:41.059 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:45:16.502 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:45:16.847 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:47:41.069 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:48:16.847 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:48:17.175 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:51:17.176 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:51:17.519 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:52:41.071 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 18:54:17.520 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:54:17.859 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:57:17.861 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 18:57:18.192 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 18:57:41.076 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:00:18.194 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:00:18.539 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:02:41.082 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:03:18.543 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:03:18.875 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:06:18.876 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:06:19.238 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:07:41.083 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:09:19.237 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:09:19.586 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:12:19.588 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:12:19.916 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:12:41.086 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:15:19.915 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:15:20.354 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:17:41.096 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:18:20.355 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:18:20.701 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:21:20.706 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:21:21.033 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:22:41.099 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:24:21.034 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:24:21.370 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:27:21.339 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:27:21.666 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:27:41.074 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:30:21.666 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:30:22.005 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:32:41.080 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:33:22.004 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:33:22.329 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:36:22.328 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:36:22.664 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:37:41.086 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:39:22.666 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:39:22.996 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:42:23.056 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:42:23.417 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:42:41.143 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:45:23.420 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:45:23.775 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:47:41.154 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:48:23.777 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:48:24.136 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:51:24.137 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:51:24.505 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:52:41.165 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 19:54:24.508 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:54:24.871 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:57:24.880 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 19:57:25.267 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 19:57:41.177 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:00:25.268 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:00:25.643 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:02:41.184 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:03:25.645 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:03:26.002 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:06:26.004 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:06:26.515 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:07:41.195 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:09:26.517 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:09:26.902 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:12:26.906 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:12:27.276 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:12:41.198 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:15:27.280 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:15:27.630 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:17:41.205 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:18:27.633 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:18:28.278 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:22:58.358 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:22:58.737 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:24:11.291 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:25:58.737 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:25:59.115 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:30:56.768 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:30:57.131 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:31:08.948 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:33:57.132 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:33:57.493 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:36:08.950 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:36:57.494 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:36:57.861 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:39:57.861 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:39:58.917 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:41:08.954 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:42:58.940 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:42:59.386 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:45:59.394 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:45:59.813 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:46:08.986 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:48:59.815 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:49:00.224 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:51:08.994 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:52:00.225 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:52:00.640 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:55:00.643 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:55:01.088 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 20:56:09.001 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 20:58:01.091 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 20:58:01.462 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:01:01.466 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:01:01.841 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:01:09.008 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:04:01.844 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:04:02.245 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:06:09.015 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:07:02.246 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:07:02.628 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:10:02.629 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:10:02.964 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:11:09.018 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:13:02.967 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:13:03.317 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:16:03.340 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:16:03.742 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:16:09.045 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:19:03.744 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:19:04.110 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:21:09.051 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:22:04.113 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:22:04.487 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:25:04.492 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:25:04.847 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:26:09.056 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:28:04.852 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:28:05.193 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:31:05.196 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:31:05.602 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:31:09.065 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:34:05.605 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:34:05.963 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:36:09.070 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:37:05.966 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:37:06.317 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:40:06.320 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:40:06.703 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:41:09.078 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:43:06.707 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:43:07.067 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:46:07.072 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:46:07.463 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:46:09.088 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:49:07.468 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:49:07.824 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:51:09.096 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:52:07.826 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:52:08.297 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:55:08.301 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:55:08.645 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 21:56:09.106 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 21:58:08.649 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 21:58:08.993 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:01:08.963 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:01:09.080 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:01:09.346 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:04:09.349 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:04:09.694 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:06:09.085 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:07:09.695 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:07:10.046 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:10:10.048 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:10:10.433 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:11:09.092 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:13:10.434 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:13:10.785 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:16:09.099 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:16:10.788 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:16:11.176 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:19:11.178 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:19:11.577 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:21:09.104 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:22:11.579 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:22:11.945 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:25:11.948 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:25:12.329 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:26:09.109 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:28:12.331 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:28:12.692 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:31:09.114 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:31:12.696 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:31:13.074 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:34:13.102 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:34:13.450 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:36:09.149 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:37:13.453 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:37:13.845 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:40:13.847 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:40:14.231 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:41:09.158 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:43:14.234 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:43:14.580 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:46:09.164 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:46:14.585 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:46:15.001 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:49:15.014 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:49:15.507 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:51:09.178 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:52:15.513 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:52:15.909 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:55:15.911 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:55:16.283 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 22:56:09.188 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 22:58:16.285 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 22:58:16.634 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 23:01:09.194 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-25 23:01:16.635 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 23:01:17.020 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 23:04:16.968 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-25 23:04:17.378 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-25 23:38:20.265 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:95669}] to ************:27017
