2025-05-20 00:04:09.369 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:19, serverValue:94508}] to ************:27017
2025-05-20 00:20:51.920 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketReadException: Prematurely reached end of stream
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:112)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:579)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessage(InternalStreamConnection.java:444)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:298)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:258)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:38)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:180)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:137)
	at java.lang.Thread.run(Thread.java:750)
2025-05-20 00:54:31.955 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:117)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Connect timed out
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:126)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:514)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-05-20 01:11:11.899 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 02:15:08.403 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 02:15:09.040 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 02:30:50.833 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:23, serverValue:94528}] to ************:27017
2025-05-20 02:30:50.875 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=41323875}
2025-05-20 03:04:46.727 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:24, serverValue:94540}] to ************:27017
2025-05-20 03:37:21.754 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:25, serverValue:94546}] to ************:27017
2025-05-20 04:11:21.873 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:26, serverValue:94553}] to ************:27017
2025-05-20 04:46:32.974 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:27, serverValue:94560}] to ************:27017
2025-05-20 05:20:12.834 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:28, serverValue:94567}] to ************:27017
2025-05-20 05:53:55.856 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:29, serverValue:94573}] to ************:27017
2025-05-20 06:25:52.790 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:30, serverValue:94578}] to ************:27017
2025-05-20 06:59:01.769 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:31, serverValue:94582}] to ************:27017
2025-05-20 07:32:03.918 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:32, serverValue:94586}] to ************:27017
2025-05-20 07:54:02.674 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:33, serverValue:94590}] to ************:27017
2025-05-20 08:01:30.317 [cluster-ClusterId{value='682ae0d22033c53576484374', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:34, serverValue:94594}] to ************:27017
2025-05-20 08:05:47.701 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:05:48.048 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:07:26.954 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:08:35.990 [http-nio-8072-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:35, serverValue:94597}] to ************:27017
2025-05-20 08:08:48.047 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:08:48.573 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:10:08.806 [http-nio-8072-exec-4] INFO  c.d.oa.workflow.service.TaskService - -------------nexId:10
2025-05-20 08:10:08.806 [http-nio-8072-exec-4] INFO  c.d.oa.workflow.service.TaskService - -------------nexId:20
2025-05-20 08:10:08.806 [http-nio-8072-exec-4] INFO  c.d.oa.workflow.service.TaskService - -------------nexId:40
2025-05-20 08:10:08.806 [http-nio-8072-exec-4] INFO  c.d.oa.workflow.service.TaskService - -------------nexId:50
2025-05-20 08:10:10.501 [http-nio-8072-exec-5] INFO  c.d.oa.workflow.service.TaskService - -------------nexId:10
2025-05-20 08:10:10.501 [http-nio-8072-exec-5] INFO  c.d.oa.workflow.service.TaskService - -------------nexId:20
2025-05-20 08:10:10.501 [http-nio-8072-exec-5] INFO  c.d.oa.workflow.service.TaskService - -------------nexId:40
2025-05-20 08:10:10.502 [http-nio-8072-exec-5] INFO  c.d.oa.workflow.service.TaskService - -------------nexId:50
2025-05-20 08:11:48.572 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:11:49.247 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:12:26.952 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:14:49.249 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:14:49.606 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:17:26.952 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:17:49.604 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:17:49.965 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:20:49.967 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:20:50.322 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:22:26.953 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:23:50.325 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:23:50.690 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:25:12.779 [scheduling-14] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy261.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 22 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-20 08:25:12.779 [scheduling-14] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy261.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 22 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-20 08:25:12.817 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-20 08:25:12.871 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-20 08:26:50.691 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:26:51.060 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:27:26.953 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:29:51.062 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:29:51.411 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:32:26.952 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:32:51.411 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:32:51.769 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:35:51.771 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:35:52.433 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:37:26.954 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:38:52.436 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:38:52.799 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:41:52.862 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:41:53.218 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:42:27.018 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:44:53.222 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:44:53.607 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:47:27.023 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:47:53.611 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:47:53.965 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:50:53.969 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:50:54.335 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:52:27.028 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:53:54.339 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:53:54.856 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:56:54.877 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:56:55.205 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 08:57:27.053 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 08:59:55.210 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 08:59:55.559 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:02:27.057 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:02:55.564 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:02:55.966 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:05:55.971 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:05:56.336 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:07:27.063 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:08:56.340 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:08:56.712 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:11:56.656 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:11:57.009 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:12:27.008 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:14:57.012 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:14:57.383 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:17:27.011 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:17:57.396 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:17:57.749 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:20:57.747 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:20:58.184 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:22:27.014 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:23:58.182 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:23:58.579 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:26:58.581 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:26:58.949 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:27:27.015 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:29:59.020 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:29:59.547 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:32:27.086 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:32:59.553 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:32:59.937 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:35:59.940 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:36:00.316 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:37:27.093 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:39:00.316 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:39:00.656 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:42:00.660 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:42:01.037 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:42:27.100 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:45:01.071 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:45:02.283 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:47:27.133 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:48:02.289 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:48:02.654 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:51:02.659 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:51:03.014 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:52:27.143 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 09:54:03.016 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:54:03.387 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:57:03.393 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 09:57:03.811 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 09:57:27.151 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:00:03.824 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:00:04.259 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:02:27.168 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:03:04.264 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:03:04.675 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:06:04.682 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:06:05.399 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:07:27.176 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:09:05.403 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:09:05.920 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:12:05.926 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:12:06.867 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:12:27.180 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:15:06.872 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:15:07.404 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:17:27.187 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:18:07.419 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:18:07.849 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:21:07.856 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:21:08.319 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:22:27.194 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:24:08.325 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:24:10.869 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:27:10.875 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:27:11.385 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:27:27.201 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:30:11.409 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:30:11.959 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:32:27.236 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:33:11.972 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:33:12.383 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:36:12.389 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:36:12.797 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:37:27.242 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:39:12.805 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:39:14.865 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:42:14.873 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:42:15.344 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:42:27.248 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:45:15.345 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:45:15.811 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:47:27.247 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:48:15.817 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:48:16.403 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:51:16.410 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:51:16.878 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:52:27.256 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 10:54:16.885 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:54:17.374 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:57:17.381 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 10:57:17.895 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 10:57:27.267 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:00:17.907 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:00:18.627 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:02:27.285 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:03:18.640 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:03:19.266 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:06:19.270 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:06:19.698 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:07:27.298 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:09:19.705 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:09:20.261 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:12:20.269 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:12:20.725 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:12:27.309 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:15:20.723 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:15:21.164 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:17:27.301 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:18:21.165 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:18:21.561 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:21:21.568 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:21:21.987 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:22:27.310 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:24:21.994 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:24:22.431 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:27:22.435 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:27:22.936 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:27:27.320 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:30:22.953 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:30:23.357 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:32:27.325 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:33:23.364 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:33:23.799 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:36:23.808 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:36:24.235 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:37:27.335 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:39:24.246 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:39:24.697 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:42:24.709 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:42:25.119 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:42:27.348 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:45:25.129 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:45:25.980 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:47:27.332 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:48:25.948 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:48:26.448 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:51:26.451 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:51:26.919 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:52:27.319 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:54:26.925 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:54:27.430 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 11:57:27.332 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 11:57:27.431 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 11:57:27.943 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:00:27.952 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:00:28.634 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:02:27.341 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:03:28.643 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:03:29.106 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:06:29.117 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:06:30.159 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:07:27.347 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:09:30.166 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:09:30.922 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:12:27.356 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:12:30.922 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:12:31.786 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:15:31.796 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:15:32.273 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:17:27.393 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:18:32.321 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:18:32.988 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:21:32.992 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:21:33.393 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:22:27.406 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:24:33.402 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:24:33.789 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:27:27.413 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:27:33.800 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:27:34.337 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:30:34.344 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:30:34.797 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:32:27.427 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:33:34.802 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:33:35.203 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:36:35.205 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:36:35.675 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:37:27.439 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:39:35.684 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:39:36.356 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:42:27.450 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:42:36.359 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:42:36.796 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:45:36.808 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:45:37.209 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:47:27.464 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:48:37.237 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:48:37.620 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:51:37.628 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:51:38.021 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:52:27.494 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:54:38.024 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:54:38.415 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 12:57:27.499 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 12:57:38.427 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 12:57:38.825 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:00:38.837 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:00:39.240 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:02:27.513 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:03:39.248 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:03:39.652 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:06:39.664 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:06:40.070 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:07:27.522 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:09:40.054 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:09:40.500 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:12:27.507 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:12:40.506 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:12:40.979 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:15:40.985 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:15:41.452 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:17:27.512 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:18:41.457 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:18:41.916 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:21:41.837 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:21:42.294 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:22:27.436 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:24:42.298 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:24:43.130 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:27:27.438 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:27:43.133 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:27:43.597 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:30:43.601 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:30:44.086 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:32:27.444 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:33:44.090 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:33:44.551 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:36:44.518 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:36:45.264 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:37:27.410 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:39:45.267 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:39:45.845 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:42:27.412 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:42:45.849 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:42:46.273 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:45:46.272 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:45:46.685 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:47:27.415 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:48:46.688 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:48:47.146 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:51:47.098 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:51:47.529 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:52:27.366 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:54:47.530 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:54:47.962 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 13:57:27.365 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 13:57:47.963 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 13:57:48.546 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:00:48.548 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:00:48.982 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:02:27.365 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:03:48.984 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:03:49.478 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:06:49.405 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:06:50.492 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:07:27.292 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:09:50.493 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:09:50.897 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:12:27.288 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:12:50.895 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:12:51.314 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:15:51.313 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:15:51.709 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:17:27.282 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:18:51.708 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:18:52.115 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:21:52.091 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:21:52.515 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:22:27.248 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:24:52.511 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:24:52.879 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:27:27.244 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:27:52.873 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:27:53.243 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:30:53.241 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:30:53.629 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:32:27.237 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:33:53.628 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:33:54.098 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:36:54.093 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:36:54.498 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:37:27.351 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:39:54.616 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:39:54.999 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:42:27.352 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:42:55.001 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:42:55.448 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:45:55.450 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:45:55.801 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:47:27.355 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:48:55.804 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:48:56.288 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:51:56.290 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:51:56.697 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:52:27.354 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:54:56.792 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:54:57.229 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 14:57:27.450 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 14:57:57.234 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 14:57:57.624 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:00:57.629 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:00:58.234 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:02:27.456 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:03:58.239 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:03:58.693 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:06:58.695 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:06:59.112 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:07:27.460 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:09:59.141 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:09:59.521 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:12:27.489 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:12:59.526 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:12:59.933 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:15:59.937 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:16:00.351 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:17:27.494 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:19:00.357 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:19:00.792 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:22:00.797 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:22:01.408 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:22:27.499 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:25:01.463 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:25:02.181 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:27:27.558 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:28:02.188 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:28:02.998 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:34:23.289 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:34:24.469 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:35:47.851 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:37:24.474 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:37:24.872 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:40:24.878 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:40:27.536 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:40:47.858 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:43:27.544 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:43:30.040 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:45:47.856 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:46:30.035 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:46:32.801 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:49:32.806 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:49:34.467 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:50:47.864 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:52:34.474 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:52:34.956 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:53:28.377 [scheduling-20] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
### The error may exist in com/dlcg/oa/mapper/ProcessInstanceMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: DELETE FROM process_instance WHERE id IN ( )
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:303)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:68)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy140.deleteBatchIds(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.removeByIds(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.ProcessInstanceServiceImpl$$EnhancerBySpringCGLIB$$f55b82d.removeByIds(<generated>)
	at com.dlcg.oa.scheduled.DelRefuseProcessScheduled.delRefuseProcess(DelRefuseProcessScheduled.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at sun.reflect.GeneratedMethodAccessor179.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy227.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy225.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy260.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 25 common frames omitted
2025-05-20 15:53:28.377 [scheduling-20] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
### The error may exist in com/dlcg/oa/mapper/ProcessInstanceMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: DELETE FROM process_instance WHERE id IN ( )
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:303)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:68)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy140.deleteBatchIds(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.removeByIds(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.ProcessInstanceServiceImpl$$EnhancerBySpringCGLIB$$f55b82d.removeByIds(<generated>)
	at com.dlcg.oa.scheduled.DelRefuseProcessScheduled.delRefuseProcess(DelRefuseProcessScheduled.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at sun.reflect.GeneratedMethodAccessor179.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy227.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy225.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy260.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 25 common frames omitted
2025-05-20 15:55:34.961 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:55:35.523 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 15:55:47.870 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 15:58:35.527 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 15:58:35.997 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:00:47.892 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:01:36.019 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:01:38.362 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:04:38.369 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:04:38.764 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:05:47.898 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:07:38.771 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:07:39.664 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:10:39.671 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:10:40.176 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:10:47.904 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:11:55.055 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747728715055, current=DOWN, previous=UP]
2025-05-20 16:11:55.056 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.9:dlcg-oa:8072: registering service...
2025-05-20 16:11:55.062 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.9:dlcg-oa:8072 - registration status: 204
2025-05-20 16:11:55.189 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-20 16:11:55.315 [SpringContextShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:35, serverValue:94597}] to ************:27017 because the pool has been closed.
2025-05-20 16:11:55.316 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-20 16:11:58.320 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-20 16:11:58.326 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-05-20 16:11:58.326 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-20 16:11:58.327 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-05-20 16:11:58.328 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-20 16:11:58.328 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.9:dlcg-oa:8072 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-20 16:11:58.338 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-20 16:12:06.789 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-20 16:12:09.582 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-20 16:12:10.308 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.oa.mapper.*]' package. Please check your configuration.
2025-05-20 16:12:11.065 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8072"]
2025-05-20 16:12:11.065 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-20 16:12:11.066 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-20 16:12:11.307 [restartedMain] INFO  o.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-05-20 16:12:11.311 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-20 16:12:11.448 [restartedMain] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-20 16:12:11.566 [cluster-ClusterId{value='682c395b5911d025284aab6a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:94622}] to ************:27017
2025-05-20 16:12:11.606 [cluster-ClusterId{value='682c395b5911d025284aab6a', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=37827333}
2025-05-20 16:12:12.000 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-20 16:12:15.329 [restartedMain] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-20 16:12:17.568 [restartedMain] INFO  c.d.o.c.AliyunOSSAutoConfiguration - load aliyun-oss..
2025-05-20 16:12:17.671 [scheduling-1] INFO  c.d.o.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-20 16:12:17.940 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-20 16:12:17.940 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-20 16:12:17.950 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-20 16:12:17.951 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-20 16:12:18.870 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-20 16:12:18.923 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-20 16:12:18.923 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-20 16:12:19.049 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-20 16:12:19.050 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-20 16:12:19.287 [restartedMain] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:12:19.311 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-20 16:12:19.312 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-20 16:12:19.312 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-20 16:12:19.312 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-20 16:12:19.312 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-20 16:12:19.312 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-20 16:12:19.312 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-20 16:12:19.476 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-20 16:12:19.479 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-20 16:12:19.481 [restartedMain] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-20 16:12:19.484 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1747728739483 with initial instances count: 0
2025-05-20 16:12:19.491 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747728739491, current=UP, previous=STARTING]
2025-05-20 16:12:19.494 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8072"]
2025-05-20 16:12:19.495 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.9:dlcg-oa:8072: registering service...
2025-05-20 16:12:19.573 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.9:dlcg-oa:8072 - registration status: 204
2025-05-20 16:12:19.580 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:12:19.583 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 13.25 seconds (JVM running for 13.782)
2025-05-20 16:12:20.206 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:12:49.485 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-20 16:12:49.486 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-20 16:12:49.486 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-20 16:12:49.486 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-20 16:12:49.486 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-20 16:12:49.486 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: false
2025-05-20 16:12:49.486 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-20 16:12:49.517 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-20 16:15:19.133 [http-nio-8072-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-20 16:15:20.220 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:15:20.730 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:17:19.339 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:22:42.423 [cluster-ClusterId{value='682c395b5911d025284aab6a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:94628}] to ************:27017
2025-05-20 16:23:29.986 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:23:30.883 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:26:30.888 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:26:31.817 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:26:57.169 [http-nio-8072-exec-8] ERROR c.d.o.e.GlobalDefultExceptionHandler - APIException 异常信息:【code=00004,message=用户未登录！】 请求地址:【/sysUser/getUserInfo】  请求参数:【{}】
2025-05-20 16:26:57.202 [http-nio-8072-exec-6] ERROR c.d.o.e.GlobalDefultExceptionHandler - APIException 异常信息:【code=00004,message=用户未登录！】 请求地址:【/sysUser/getUserInfo】  请求参数:【{}】
2025-05-20 16:26:57.202 [http-nio-8072-exec-7] ERROR c.d.o.e.GlobalDefultExceptionHandler - APIException 异常信息:【code=00004,message=用户未登录！】 请求地址:【/sysUser/getUserMenu】  请求参数:【{}】
2025-05-20 16:27:25.544 [http-nio-8072-exec-3] ERROR c.d.o.e.GlobalDefultExceptionHandler - APIException 异常信息:【code=00004,message=用户未登录！】 请求地址:【/sysUser/getUserMenu】  请求参数:【{}】
2025-05-20 16:27:25.583 [http-nio-8072-exec-1] ERROR c.d.o.e.GlobalDefultExceptionHandler - APIException 异常信息:【code=00004,message=用户未登录！】 请求地址:【/sysUser/getUserInfo】  请求参数:【{}】
2025-05-20 16:27:28.546 [http-nio-8072-exec-5] INFO  c.d.o.s.impl.SysMenuServiceImpl - 处理菜单耗时：547
2025-05-20 16:27:28.585 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:27:29.110 [http-nio-8072-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:94633}] to ************:27017
2025-05-20 16:29:31.820 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:29:32.214 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:32:28.531 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:32:32.162 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:32:32.657 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:35:32.661 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:35:33.033 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:37:28.535 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:38:33.038 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:38:33.435 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:41:33.439 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:41:33.823 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:42:28.540 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:42:29.321 [http-nio-8072-exec-9] INFO  com.dlcg.oa.base.BaseController - 数量4
2025-05-20 16:46:30.963 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:46:31.325 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:49:25.682 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:49:31.330 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:49:32.030 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:52:32.037 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:52:32.407 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:54:25.688 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 16:55:32.414 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:55:32.848 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:58:32.857 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 16:58:33.223 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 16:59:25.699 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:01:33.245 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:01:36.727 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:04:25.720 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:04:36.736 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:04:37.220 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:07:37.223 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:07:37.577 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:09:25.728 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:10:37.584 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:10:37.972 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:13:37.979 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:13:38.360 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:14:25.737 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:16:38.405 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:16:38.788 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:19:25.781 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:19:38.795 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:19:39.224 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:22:39.230 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:22:42.755 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:24:25.788 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:25:42.759 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:25:44.235 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:28:44.243 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:28:44.690 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:29:25.797 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:31:44.720 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:31:45.103 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:34:25.830 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:34:45.111 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:34:45.461 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:37:45.469 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:37:45.842 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:39:25.841 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:40:45.851 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:40:46.322 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:43:46.330 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:43:46.729 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:44:05.763 [http-nio-8072-exec-9] INFO  com.dlcg.oa.base.BaseController - 数量4
2025-05-20 17:44:25.850 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:46:46.722 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:46:47.107 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:49:25.845 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:49:47.115 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:49:47.614 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:52:47.622 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:52:48.107 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:54:25.855 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 17:55:48.111 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:55:48.567 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:58:48.575 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 17:58:48.957 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 17:59:25.866 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:01:48.932 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:01:49.311 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:04:25.840 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:04:49.318 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:04:49.700 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:07:49.705 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:07:50.111 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:09:25.847 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:13:14.488 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:13:15.490 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:16:15.496 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:16:15.897 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:16:50.226 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:19:15.902 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:19:16.965 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:21:50.236 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:22:16.977 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:22:17.395 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:25:17.406 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:25:17.800 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:26:50.245 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:28:17.811 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:28:18.245 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:31:18.256 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:31:18.633 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:31:50.253 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:34:18.644 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:34:19.028 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:36:50.265 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:37:19.039 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:37:19.412 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:40:19.416 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:40:19.793 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:41:50.183 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:43:19.717 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:43:20.092 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:46:20.101 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:46:20.487 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:46:50.184 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:49:20.496 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:49:20.856 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:51:50.191 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:52:20.865 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:52:21.324 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:55:21.333 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:55:21.752 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 18:56:50.246 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 18:58:21.810 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 18:58:22.212 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:01:22.222 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:01:22.600 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:01:50.258 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:04:22.607 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:04:22.998 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:06:50.266 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:07:23.008 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:07:23.396 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:10:23.406 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:10:23.800 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:11:50.261 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:13:23.794 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:13:24.159 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:16:24.161 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:16:24.545 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:16:50.269 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:19:24.553 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:19:24.925 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:21:50.278 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:22:24.934 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:22:25.315 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:25:25.323 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:25:25.725 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:26:50.285 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:28:25.705 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:28:26.151 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:31:26.154 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:31:26.536 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:31:50.267 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:34:26.542 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:34:26.937 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:36:50.276 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:37:26.941 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:37:27.304 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:40:27.309 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:40:27.708 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:41:50.282 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:43:27.738 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:43:28.129 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:46:28.137 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:46:28.524 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:46:50.316 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:49:28.525 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:49:29.003 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:51:50.325 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:52:29.005 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:52:30.338 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:55:30.340 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:55:30.723 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 19:56:50.333 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 19:58:30.731 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 19:58:31.170 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:01:31.172 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:01:31.556 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:01:50.343 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:04:31.566 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:04:31.998 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:06:50.349 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:07:32.008 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:07:32.422 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:10:32.429 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:10:32.857 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:11:50.354 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:13:32.891 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:13:33.907 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:16:33.914 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:16:34.285 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:16:50.382 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:19:34.296 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:19:34.656 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:21:50.391 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:22:34.661 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:22:35.512 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:25:35.519 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:25:35.886 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:26:50.401 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:28:35.894 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:28:36.314 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:31:36.317 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:31:36.703 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:31:50.410 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:34:36.707 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:34:37.070 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:36:50.419 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:37:37.080 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:37:37.700 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:40:37.706 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:40:38.062 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:41:50.422 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:43:38.041 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:43:38.457 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:46:38.459 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:46:38.838 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:46:50.397 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:49:38.841 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:49:39.221 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:51:50.403 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:52:39.229 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:52:39.702 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:55:39.712 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:55:40.111 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 20:56:50.405 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 20:58:40.036 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 20:58:40.410 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:01:40.409 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:01:40.804 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:01:50.330 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:04:40.811 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:04:41.210 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:06:50.330 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:07:41.217 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:07:41.579 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:10:41.587 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:10:41.979 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:11:50.334 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:13:41.984 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:13:42.381 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:16:42.399 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:16:42.785 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:16:50.353 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:19:29.434 [cluster-ClusterId{value='682c395b5911d025284aab6a', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:129)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:502)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-05-20 21:19:42.790 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:19:46.588 [cluster-ClusterId{value='682c395b5911d025284aab6a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:94653}] to ************:27017
2025-05-20 21:19:46.620 [cluster-ClusterId{value='682c395b5911d025284aab6a', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=30968458}
2025-05-20 21:19:47.719 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:21:50.359 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:22:47.721 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:22:48.074 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:24:58.116 [cluster-ClusterId{value='682c395b5911d025284aab6a', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:94657}] to ************:27017
2025-05-20 21:25:48.078 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:25:49.365 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:26:50.364 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:28:49.368 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:28:49.750 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:31:49.752 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:31:50.117 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:31:50.372 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:34:50.123 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:34:50.482 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:36:50.373 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:37:50.486 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:37:50.849 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:40:50.854 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:40:51.216 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:41:50.378 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:43:51.220 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:43:51.574 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:46:50.385 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:46:51.575 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:46:51.938 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:49:51.938 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:49:52.294 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:51:50.391 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:52:52.302 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:52:52.679 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:55:52.684 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:55:53.030 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 21:56:50.390 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 21:58:53.038 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 21:58:53.407 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:00:00.023 [scheduling-4] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy261.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 22 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-20 22:00:00.023 [scheduling-4] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy261.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 22 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-20 22:00:00.142 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-20 22:00:00.218 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-20 22:01:50.473 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:01:53.492 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:01:53.852 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:04:53.862 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:04:54.234 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:06:50.484 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:07:54.239 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:07:54.635 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:10:54.643 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:10:55.021 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:11:50.488 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:13:55.030 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:13:55.397 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:16:50.530 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:16:55.441 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:16:55.804 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:19:55.812 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:19:56.195 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:21:50.540 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:22:56.203 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:22:56.608 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:25:56.617 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:25:56.986 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:26:50.552 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:28:56.997 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:28:57.336 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:31:50.562 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:31:57.347 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:31:57.722 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:34:57.733 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:34:58.094 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:36:50.573 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:37:58.104 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:37:58.476 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:40:58.487 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:40:58.846 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:41:50.579 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:43:58.854 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:43:59.217 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:46:50.591 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:46:59.139 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:46:59.507 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:49:59.515 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:49:59.889 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:51:50.506 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:52:59.896 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:53:00.280 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:56:00.288 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:56:00.667 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 22:56:50.509 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 22:59:00.671 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 22:59:01.111 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:01:50.518 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:02:01.115 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:02:01.468 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:05:01.480 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:05:01.847 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:06:50.527 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:08:01.847 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:08:02.213 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:11:02.222 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:11:02.605 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:11:50.534 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:14:02.613 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:14:03.007 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:16:50.540 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:17:03.016 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:17:03.397 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:20:03.507 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:20:03.933 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:21:50.652 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:23:03.945 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:23:04.305 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:26:04.314 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:26:05.247 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:26:50.664 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:29:05.259 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:29:05.625 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:31:50.676 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:32:05.627 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:32:06.001 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:35:06.029 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:35:06.401 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:36:50.706 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:38:06.406 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:38:06.815 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:41:06.826 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:41:07.242 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:41:50.719 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:44:07.251 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:44:07.614 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:46:50.732 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:47:07.626 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:47:07.995 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:50:08.007 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:50:08.371 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:51:50.740 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:53:08.376 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:53:08.736 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:56:08.742 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:56:09.253 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-20 23:56:50.751 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-20 23:59:09.256 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-20 23:59:09.618 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
