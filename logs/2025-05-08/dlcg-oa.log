2025-05-08 00:01:40.160 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:01:40.378 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:02:11.025 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:04:40.386 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:04:40.596 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:07:11.020 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:07:40.590 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:07:40.805 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:10:40.809 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:10:41.011 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:12:11.028 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:13:41.020 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:13:41.232 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:16:41.234 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:16:41.427 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:17:11.037 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:19:41.432 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:19:41.620 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:22:11.040 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:22:41.628 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:22:41.818 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:25:41.828 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:25:42.024 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:27:11.050 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:28:42.027 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:28:42.241 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:31:42.241 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:31:42.434 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:32:11.058 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:34:42.443 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:34:42.638 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:37:11.061 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:37:42.639 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:37:42.848 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:40:42.857 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:40:43.058 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:42:11.066 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:43:43.058 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:43:43.266 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:46:43.273 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:46:43.495 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:47:11.076 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:49:43.503 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:49:43.692 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:52:11.085 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:52:43.696 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:52:43.889 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:55:43.888 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:55:44.098 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 00:57:11.088 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 00:58:44.100 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 00:58:44.308 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:01:44.318 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:01:44.520 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:02:11.090 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:04:44.528 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:04:44.909 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:07:11.098 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:07:44.908 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:07:45.107 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:10:45.104 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:10:45.281 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:12:11.083 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:13:45.284 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:13:45.452 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:15:33.327 [scheduling-15] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
### The error may exist in com/dlcg/oa/mapper/ProcessInstanceMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: DELETE FROM process_instance WHERE id IN ( )
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:303)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:68)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy140.deleteBatchIds(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.removeByIds(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.ProcessInstanceServiceImpl$$EnhancerBySpringCGLIB$$43fcb84e.removeByIds(<generated>)
	at com.dlcg.oa.scheduled.DelRefuseProcessScheduled.delRefuseProcess(DelRefuseProcessScheduled.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at sun.reflect.GeneratedMethodAccessor178.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy225.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy223.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy258.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 25 common frames omitted
2025-05-08 01:15:33.327 [scheduling-15] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
### The error may exist in com/dlcg/oa/mapper/ProcessInstanceMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: DELETE FROM process_instance WHERE id IN ( )
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:303)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:68)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy140.deleteBatchIds(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.removeByIds(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.ProcessInstanceServiceImpl$$EnhancerBySpringCGLIB$$43fcb84e.removeByIds(<generated>)
	at com.dlcg.oa.scheduled.DelRefuseProcessScheduled.delRefuseProcess(DelRefuseProcessScheduled.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at sun.reflect.GeneratedMethodAccessor178.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy225.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy223.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy258.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 25 common frames omitted
2025-05-08 01:16:45.452 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:16:45.643 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:17:11.092 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:19:45.646 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:19:45.852 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:22:11.094 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:22:45.858 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:22:46.037 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:25:46.044 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:25:46.254 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:27:11.098 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:28:46.256 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:28:46.444 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:31:46.449 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:31:46.635 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:32:11.107 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:34:46.641 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:34:46.837 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:37:11.115 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:37:46.845 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:37:47.042 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:40:47.050 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:40:47.247 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:42:11.118 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:43:47.255 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:43:47.445 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:46:47.446 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:46:47.633 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:47:11.126 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:49:47.639 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:49:47.839 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:52:11.132 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:52:47.848 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:52:48.036 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:55:48.036 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:55:48.233 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 01:57:11.139 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 01:58:48.239 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 01:58:48.434 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:01:48.434 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:01:48.629 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:02:11.140 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:04:48.633 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:04:48.832 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:07:11.140 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:07:48.841 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:07:49.035 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:10:49.042 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:10:49.252 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:12:11.150 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:13:49.261 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:13:49.455 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:16:49.461 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:16:49.643 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:17:11.154 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:19:49.650 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:19:49.843 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:22:11.161 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:22:49.844 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:22:50.036 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:25:50.103 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:25:50.300 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:27:11.226 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:28:50.309 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:28:50.546 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:31:50.553 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:31:50.755 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:32:11.234 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:34:50.766 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:34:50.975 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:37:11.236 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:37:50.984 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:37:51.189 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:40:51.209 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:40:51.402 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:42:11.256 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:43:51.411 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:43:51.609 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:46:51.618 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:46:51.812 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:47:11.268 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:49:51.817 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:49:52.010 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:52:11.275 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:52:52.021 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:52:52.228 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:55:52.242 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:55:52.433 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 02:57:11.284 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 02:58:52.436 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 02:58:52.639 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:01:52.645 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:01:52.845 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:02:11.293 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:04:52.850 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:04:53.265 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:07:11.301 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:07:53.272 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:07:53.476 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:10:53.485 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:10:53.698 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:12:11.313 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:13:53.709 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:13:53.934 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:16:53.937 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:16:54.162 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:17:11.320 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:19:54.167 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:19:54.374 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:22:11.329 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:22:54.380 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:22:54.591 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:25:54.600 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:25:54.822 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:27:11.331 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:28:54.826 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:28:55.056 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:31:55.062 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:31:55.307 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:32:11.335 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:34:55.311 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:34:55.530 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:37:11.346 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:37:55.538 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:37:55.740 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:40:55.751 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:40:55.984 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:42:11.352 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:43:55.993 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:43:56.221 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:46:56.231 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:46:56.446 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:47:11.361 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:49:56.455 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:49:56.670 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:52:11.373 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:52:56.671 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:52:56.901 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:55:56.907 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:55:57.125 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 03:57:11.383 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 03:58:57.129 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 03:58:57.342 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:01:57.344 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:01:57.575 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:02:11.387 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:04:57.580 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:04:57.816 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:07:11.396 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:07:57.819 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:07:58.050 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:10:58.060 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:10:58.282 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:12:11.398 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:13:58.256 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:13:58.482 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:16:58.486 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:16:58.700 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:17:11.382 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:19:58.708 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:19:58.909 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:22:11.392 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:22:58.913 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:22:59.138 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:25:59.145 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:25:59.349 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:27:11.402 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:28:59.359 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:28:59.556 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:31:59.560 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:31:59.786 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:32:11.415 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:34:59.795 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:35:00.018 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:37:11.421 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:38:00.027 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:38:00.237 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:41:00.241 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:41:00.459 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:42:11.428 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:44:00.454 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:44:00.665 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:47:00.674 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:47:00.891 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:47:11.415 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:50:00.899 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:50:01.127 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:52:11.423 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:53:01.135 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:53:01.333 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:56:01.334 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:56:01.544 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 04:57:11.434 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 04:59:01.560 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 04:59:01.773 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:02:01.780 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:02:02.026 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:02:11.452 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:05:02.036 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:05:02.388 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:07:11.461 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:08:02.396 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:08:02.593 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:11:02.601 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:11:02.806 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:12:11.472 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:14:02.811 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:14:03.022 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:17:03.026 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:17:03.212 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:17:11.483 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:20:03.219 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:20:03.416 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:22:11.490 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:23:03.425 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:23:03.616 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:26:03.623 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:26:03.796 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:27:11.492 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:29:03.805 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:29:04.006 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:32:04.013 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:32:04.222 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:32:11.494 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:35:04.231 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:35:04.430 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:37:11.499 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:38:04.437 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:38:04.630 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:41:04.636 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:41:04.811 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:42:11.505 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:44:04.827 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:44:05.018 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:47:05.025 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:47:05.202 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:47:11.530 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:50:05.212 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:50:05.386 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:52:11.539 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:53:05.396 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:53:05.587 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:56:05.597 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:56:05.767 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 05:57:11.547 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 05:59:05.772 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 05:59:05.967 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:02:05.971 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:02:06.155 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:02:11.555 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:05:06.159 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:05:06.357 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:07:11.561 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:08:06.364 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:08:06.541 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:11:06.547 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:11:06.745 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:12:11.567 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:14:06.746 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:14:06.937 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:17:06.948 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:17:07.137 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:17:11.576 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:20:07.147 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:20:07.343 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:22:11.585 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:23:07.344 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:23:07.614 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:26:07.617 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:26:07.812 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:27:11.596 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:29:07.819 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:29:07.986 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:32:07.998 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:32:08.172 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:32:11.615 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:35:08.177 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:35:08.372 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:37:11.618 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:38:08.380 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:38:08.571 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:41:08.581 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:41:08.764 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:42:11.625 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:44:08.775 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:44:08.980 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:47:08.993 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:47:09.186 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:47:11.631 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:50:09.187 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:50:09.379 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:52:11.639 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:53:09.389 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:53:09.578 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:56:09.589 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:56:09.812 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 06:57:11.645 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 06:59:09.813 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 06:59:09.987 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:02:09.996 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:02:10.187 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:02:11.653 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:05:10.188 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:05:10.519 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:07:11.656 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:08:10.529 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:08:10.727 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:11:10.736 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:11:10.936 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:12:11.663 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:14:10.941 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:14:11.140 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:17:11.085 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:17:11.276 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:17:11.616 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:20:11.275 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:20:11.447 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:22:11.623 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:23:11.451 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:23:11.615 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:26:11.617 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:26:11.809 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:27:11.632 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:29:11.808 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:29:12.000 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:32:11.650 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:32:12.021 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:32:12.208 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:35:12.209 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:35:12.401 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:37:11.652 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:38:12.408 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:38:12.602 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:41:12.607 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:41:12.805 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:42:11.658 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:44:12.812 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:44:13.007 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:47:11.652 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:47:12.997 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:47:13.191 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:50:13.193 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:50:13.367 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:52:11.651 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:53:13.369 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:53:13.555 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:56:13.563 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:56:13.749 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 07:57:11.653 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 07:59:13.755 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 07:59:13.943 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:02:11.652 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:02:13.947 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:02:14.121 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:05:14.129 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:05:14.310 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:07:11.660 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:08:14.316 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:08:14.500 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:11:14.508 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:11:14.690 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:12:11.663 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:14:14.689 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:14:14.872 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:17:11.660 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:17:14.872 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:17:15.045 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:20:15.052 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:20:15.241 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:22:11.668 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:23:15.240 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:23:15.441 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:26:15.446 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:26:15.643 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:27:11.668 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:29:15.646 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:29:15.822 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:32:11.703 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:32:15.848 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:32:16.035 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:35:16.036 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:35:16.198 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:37:11.708 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:38:16.207 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:38:16.400 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:41:16.406 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:41:16.588 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:42:11.713 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:44:16.597 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:44:16.784 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:47:11.734 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:47:16.805 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:47:17.006 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:50:17.010 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:50:17.180 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:52:11.743 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:53:17.184 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:53:17.373 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:56:17.378 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:56:17.564 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 08:57:11.752 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 08:59:17.573 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 08:59:17.738 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:02:11.753 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:02:17.747 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:02:17.931 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:05:17.940 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:05:18.307 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:07:11.760 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:08:18.313 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:08:18.524 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:11:18.534 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:11:18.720 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:12:11.766 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:14:18.727 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:14:18.933 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:17:11.784 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:17:18.950 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:17:19.150 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:20:19.157 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:20:19.365 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:22:11.797 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:23:19.379 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:23:19.571 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:26:19.580 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:26:19.764 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:27:11.809 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:29:19.769 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:29:19.982 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:32:11.827 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:32:19.993 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:32:20.201 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:35:20.204 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:35:20.387 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:37:11.839 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:38:20.394 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:38:20.601 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:41:20.611 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:41:20.816 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:42:11.844 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:44:20.820 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:44:21.026 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:47:11.855 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:47:21.036 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:47:21.239 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:50:21.215 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:50:21.407 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:52:11.834 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:53:21.409 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:53:21.604 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:56:21.607 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:56:21.819 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 09:57:11.836 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 09:59:21.824 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 09:59:22.024 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:02:11.838 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:02:22.027 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:02:22.243 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:05:22.244 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:05:22.450 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:07:11.842 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:08:22.451 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:08:22.655 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:11:22.660 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:11:22.874 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:12:11.849 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:14:22.876 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:14:23.073 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:14:27.829 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1746670467829, current=DOWN, previous=UP]
2025-05-08 10:14:27.830 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/10.0.0.15:dlcg-oa:8072: registering service...
2025-05-08 10:14:27.853 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/10.0.0.15:dlcg-oa:8072 - registration status: 204
2025-05-08 10:14:28.013 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-08 10:14:28.137 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-08 10:14:31.143 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-08 10:14:31.153 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-05-08 10:14:31.153 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-08 10:14:31.155 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-05-08 10:14:31.155 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-08 10:14:31.155 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/10.0.0.15:dlcg-oa:8072 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-08 10:14:31.172 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-08 10:14:34.211 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-08 10:14:37.104 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-08 10:14:40.790 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.oa.mapper.*]' package. Please check your configuration.
2025-05-08 10:14:44.037 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8072"]
2025-05-08 10:14:44.038 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-08 10:14:44.038 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-08 10:14:44.864 [restartedMain] INFO  o.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-05-08 10:14:44.867 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-08 10:14:45.247 [restartedMain] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-08 10:14:45.499 [cluster-ClusterId{value='681c1395c33b3053bfd54e93', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:91375}] to ************:27017
2025-05-08 10:14:45.518 [cluster-ClusterId{value='681c1395c33b3053bfd54e93', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=16570459}
2025-05-08 10:14:47.257 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-08 10:14:49.596 [restartedMain] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-08 10:14:54.313 [restartedMain] INFO  c.d.o.c.AliyunOSSAutoConfiguration - load aliyun-oss..
2025-05-08 10:14:54.489 [scheduling-1] INFO  c.d.o.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-08 10:14:54.771 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-08 10:14:54.771 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-08 10:14:54.777 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-08 10:14:54.777 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-08 10:14:56.487 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-08 10:14:56.532 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-08 10:14:56.532 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-08 10:14:56.860 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-08 10:14:56.860 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-08 10:14:57.229 [restartedMain] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:14:57.255 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-08 10:14:57.255 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-08 10:14:57.255 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-08 10:14:57.255 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-08 10:14:57.255 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-08 10:14:57.255 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-08 10:14:57.255 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-08 10:14:57.530 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-08 10:14:57.532 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-08 10:14:57.534 [restartedMain] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-08 10:14:57.536 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1746670497536 with initial instances count: 0
2025-05-08 10:14:57.547 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1746670497547, current=UP, previous=STARTING]
2025-05-08 10:14:57.551 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/10.0.0.8:dlcg-oa:8072: registering service...
2025-05-08 10:14:57.551 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8072"]
2025-05-08 10:14:57.593 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/10.0.0.8:dlcg-oa:8072 - registration status: 204
2025-05-08 10:14:57.622 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:14:57.626 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 23.735 seconds (JVM running for 24.541)
2025-05-08 10:14:57.963 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:15:01.127 [http-nio-8072-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-08 10:15:27.534 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-08 10:15:27.536 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-08 10:15:27.536 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-08 10:15:27.536 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-08 10:15:27.536 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-08 10:15:27.536 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: false
2025-05-08 10:15:27.536 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-08 10:15:27.575 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-08 10:17:57.968 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:17:58.198 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:19:57.262 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:20:58.199 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:20:58.426 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:23:58.431 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:23:58.637 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:24:57.268 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:26:58.642 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:26:58.936 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:29:57.274 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:29:58.939 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:29:59.146 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:32:59.147 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:32:59.344 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:34:57.280 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:35:59.349 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:35:59.546 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:38:59.551 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:38:59.748 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:39:57.287 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:41:59.753 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:41:59.960 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:44:57.293 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:44:59.965 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:45:00.175 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:48:00.180 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:48:00.382 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:49:57.284 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:51:00.371 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:51:00.589 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:54:00.592 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:54:00.799 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:54:57.288 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 10:57:00.803 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 10:57:01.019 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 10:59:57.293 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:00:01.024 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:00:01.226 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:03:01.231 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:03:01.424 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:04:57.300 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:06:01.428 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:06:01.627 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:09:01.631 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:09:01.838 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:09:57.307 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:12:01.843 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:12:02.027 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:14:57.311 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:15:02.031 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:15:02.254 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:18:02.256 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:18:02.452 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:19:57.316 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:21:02.452 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:21:02.648 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:24:02.650 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:24:02.865 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:24:57.317 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:27:02.870 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:27:03.103 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:29:57.317 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:30:03.108 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:30:03.320 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:33:03.322 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:33:03.559 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:34:57.322 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:36:03.591 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:36:03.786 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:39:03.794 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:39:03.989 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:39:57.361 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:42:03.994 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:42:04.194 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:44:57.365 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:45:04.196 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:45:04.400 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:48:04.405 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:48:04.611 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:49:57.373 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:51:04.621 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:51:04.834 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:54:04.852 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:54:05.046 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:54:57.382 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 11:57:05.052 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 11:57:05.238 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 11:59:57.388 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:00:05.244 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:00:05.459 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:03:05.468 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:03:05.672 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:04:57.395 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:06:05.681 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:06:05.879 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:09:05.890 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:09:06.068 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:09:57.402 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:12:06.073 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:12:06.273 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:14:57.406 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:15:06.276 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:15:06.631 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:18:06.641 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:18:06.842 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:19:57.411 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:21:06.845 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:21:07.033 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:24:07.069 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:24:07.275 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:24:57.450 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:27:07.284 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:27:07.497 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:29:57.463 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:30:07.507 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:30:07.698 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:33:07.703 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:33:07.905 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:34:57.473 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:36:07.911 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:36:08.108 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:39:08.109 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:39:08.306 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:39:57.481 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:42:08.317 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:42:08.514 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:44:57.490 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:45:08.523 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:45:08.712 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:48:08.723 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:48:08.918 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:49:57.496 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:51:08.926 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:51:09.116 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:54:09.137 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:54:09.332 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:54:57.517 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 12:57:09.341 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 12:57:09.531 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 12:59:57.529 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:00:09.537 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:00:09.713 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:03:09.724 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:03:09.919 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:04:57.538 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:06:09.930 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:06:10.120 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:09:10.131 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:09:10.328 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:09:57.548 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:12:10.339 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:12:10.544 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:14:57.560 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:15:10.564 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:15:10.753 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:18:10.762 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:18:10.945 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:19:57.571 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:21:10.955 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:21:11.160 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:24:11.171 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:24:11.352 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:24:57.578 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:27:11.358 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:27:11.575 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:29:57.590 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:30:11.585 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:30:11.808 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:33:11.818 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:33:12.014 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:34:57.602 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:36:12.019 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:36:12.215 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:39:12.219 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:39:12.421 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:39:57.615 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:42:12.427 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:42:12.634 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:44:57.617 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:45:12.636 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:45:12.859 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:48:12.879 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:48:13.062 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:49:57.628 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:51:13.074 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:51:13.258 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:54:13.216 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:54:13.416 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:54:57.587 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 13:57:13.422 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 13:57:13.637 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 13:59:57.630 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:00:13.641 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:00:13.827 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:03:13.830 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:03:14.114 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:04:57.638 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:06:14.118 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:06:14.359 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:09:14.311 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:09:14.508 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:09:57.590 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:12:14.512 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:12:14.747 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:14:57.592 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:15:14.751 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:15:15.175 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:18:15.177 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:18:15.361 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:19:57.594 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:21:15.365 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:21:15.606 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:22:19.047 [http-nio-8072-exec-10] INFO  com.dlcg.oa.base.BaseController - 数量4
2025-05-08 14:22:19.236 [http-nio-8072-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:91391}] to ************:27017
2025-05-08 14:24:15.609 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:24:15.800 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:24:57.595 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:27:15.827 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:27:16.052 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:29:57.628 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:30:16.053 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:30:16.272 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:33:16.282 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:33:16.498 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:34:57.633 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:36:16.508 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:36:16.751 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:39:16.761 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:39:16.989 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:39:57.637 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:42:16.982 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:42:17.208 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:44:57.629 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:45:17.211 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:45:17.440 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:48:17.444 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:48:17.682 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:49:57.628 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:51:17.687 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:51:17.905 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:54:17.913 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:54:18.122 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:54:57.632 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 14:57:18.130 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 14:57:18.348 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 14:59:57.638 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:00:18.358 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:00:18.571 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:03:18.579 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:03:18.775 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:04:57.647 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:06:18.778 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:06:18.973 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:09:18.981 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:09:19.190 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:09:57.648 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:12:19.198 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:12:19.422 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:14:57.663 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:15:19.425 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:15:19.635 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:18:19.637 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:18:19.872 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:19:57.662 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:21:19.876 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:21:20.085 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:24:20.088 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:24:20.284 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:24:57.665 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:27:20.288 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:27:20.516 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:29:57.666 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:30:20.517 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:30:20.746 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:33:20.750 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:33:20.942 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:34:57.671 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:36:20.951 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:36:21.150 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:39:21.159 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:39:21.371 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:39:57.679 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:42:21.419 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:42:21.638 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:44:57.736 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:45:21.644 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:45:21.881 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:48:21.889 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:48:22.113 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:49:57.747 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:51:22.118 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:51:22.331 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:54:22.334 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:54:22.531 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:54:57.753 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 15:57:22.538 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 15:57:22.747 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 15:59:57.756 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:00:22.752 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:00:22.963 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:03:22.967 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:03:23.168 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:04:57.760 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:06:23.169 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:06:23.364 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:09:23.371 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:09:23.567 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:09:57.771 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:12:23.603 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:12:23.837 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:14:57.803 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:15:23.843 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:15:24.239 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:18:24.249 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:18:24.478 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:19:57.814 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:21:24.486 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:21:24.692 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:24:24.698 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:24:24.904 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:24:57.823 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:27:24.898 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:27:25.105 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:29:57.814 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:30:25.108 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:30:25.300 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:33:25.311 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:33:25.509 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:34:57.821 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:36:25.511 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:36:25.715 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:39:25.721 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:39:25.925 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:39:57.833 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:42:25.936 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:42:26.131 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:44:57.845 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:45:26.136 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:45:26.330 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:48:26.341 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:48:26.547 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:49:57.856 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:51:26.558 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:51:26.765 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:54:26.776 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:54:26.967 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:54:57.868 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 16:57:26.982 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 16:57:27.176 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 16:59:57.895 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:00:27.194 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:00:27.389 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:03:27.391 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:03:27.601 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:04:57.906 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:06:27.607 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:06:27.810 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:09:27.819 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:09:28.019 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:09:57.917 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:12:28.030 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:12:28.231 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:14:57.935 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:15:28.248 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:15:28.460 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:18:28.471 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:18:28.669 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:19:57.949 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:21:28.680 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:21:29.156 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:24:29.161 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:24:29.371 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:24:57.957 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:27:29.378 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:27:29.564 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:29:57.966 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:30:29.572 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:30:29.770 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:33:29.773 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:33:29.980 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:34:57.977 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:36:29.983 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:36:30.181 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:39:30.185 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:39:30.369 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:39:57.984 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:42:30.381 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:42:30.598 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:44:57.964 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:45:30.572 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:45:30.773 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:48:30.783 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:48:30.988 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:49:57.970 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:51:30.998 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:51:31.192 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:54:31.201 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:54:31.811 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:54:57.982 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 17:57:31.821 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 17:57:32.050 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 17:59:57.993 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:00:32.057 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:00:32.267 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:03:32.269 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:03:32.492 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:04:58.001 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:06:32.495 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:06:32.701 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:09:32.708 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:09:32.895 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:09:58.012 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:12:32.904 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:12:33.117 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:14:58.022 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:15:33.133 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:15:33.482 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:18:33.495 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:18:33.685 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:19:58.043 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:21:33.692 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:21:33.884 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:24:33.892 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:24:34.094 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:24:58.053 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:27:34.103 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:27:34.291 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:29:58.060 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:30:34.294 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:30:34.497 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:33:34.476 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:33:34.667 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:34:58.038 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:36:34.673 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:36:34.864 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:39:34.871 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:39:35.067 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:39:58.047 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:42:35.075 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:42:35.256 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:44:58.049 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:45:35.266 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:45:35.441 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:48:35.450 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:48:35.634 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:49:58.058 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:51:35.643 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:51:35.824 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:54:35.833 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:54:36.021 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:54:58.064 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 18:57:36.027 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 18:57:36.212 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 18:59:58.073 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:00:36.218 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:00:36.429 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:03:36.419 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:03:36.612 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:04:58.064 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:06:36.619 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:06:36.795 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:09:36.798 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:09:36.981 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:09:58.069 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:12:36.990 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:12:37.186 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:14:58.069 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:15:37.194 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:15:37.389 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:18:37.392 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:18:37.586 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:19:58.076 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:21:37.593 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:21:37.786 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:24:37.795 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:24:37.990 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:24:58.077 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:27:37.996 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:27:38.175 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:29:58.084 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:30:38.179 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:30:38.371 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:33:38.380 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:33:38.556 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:34:58.088 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:36:38.557 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:36:38.744 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:39:38.747 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:39:38.928 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:39:58.096 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:42:38.935 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:42:39.134 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:44:58.105 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:45:39.138 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:45:39.338 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:48:39.346 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:48:39.539 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:49:58.108 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:51:39.548 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:51:39.753 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:54:39.753 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:54:39.942 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:54:58.115 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 19:57:39.950 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 19:57:40.135 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 19:59:58.122 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:00:40.146 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:00:40.333 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:03:40.337 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:03:40.527 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:04:58.129 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:06:40.535 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:06:40.723 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:09:40.732 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:09:40.920 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:09:58.136 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:12:40.925 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:12:41.114 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:14:58.135 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:15:41.123 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:15:41.461 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:18:41.467 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:18:41.659 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:19:58.143 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:21:41.666 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:21:41.853 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:24:41.859 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:24:42.014 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:24:58.146 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:27:42.020 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:27:42.190 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:29:58.152 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:30:42.199 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:30:42.371 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:33:42.373 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:33:42.842 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:34:58.143 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:36:42.841 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:36:43.014 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:39:43.020 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:39:43.199 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:39:58.146 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:42:43.208 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:42:43.359 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:44:58.155 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:45:43.364 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:45:43.536 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:48:43.537 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:48:43.702 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:49:58.160 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:51:43.706 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:51:43.880 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:54:43.881 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:54:44.071 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:54:58.167 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 20:57:44.071 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 20:57:44.252 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 20:59:58.176 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:00:44.257 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:00:44.442 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:03:44.452 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:03:44.639 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:04:58.193 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:06:44.658 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:06:44.849 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:09:44.858 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:09:45.047 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:09:58.202 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:12:45.051 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:12:45.239 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:14:58.204 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:15:45.239 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:15:45.420 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:18:45.425 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:18:45.615 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:19:58.213 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:21:45.621 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:21:45.807 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:24:45.816 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:24:45.996 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:24:58.214 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:27:46.005 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:27:46.202 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:29:58.217 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:30:46.205 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:30:46.398 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:33:46.402 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:33:46.590 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:34:58.222 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:36:46.595 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:36:46.785 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:39:46.791 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:39:46.964 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:39:58.230 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:42:46.976 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:42:47.150 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:44:58.237 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:45:47.160 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:45:47.353 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:48:47.358 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:48:47.542 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:49:58.201 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:51:47.487 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:51:48.143 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:54:48.150 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:54:48.346 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:54:58.206 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 21:57:48.351 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 21:57:48.537 [scheduling-22] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 21:59:58.210 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:00:48.542 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:00:48.731 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:03:48.735 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:03:48.922 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:04:58.257 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:06:48.963 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:06:49.143 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:09:49.145 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:09:49.604 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:09:58.262 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:12:49.607 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:12:49.799 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:14:58.261 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:15:49.808 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:15:50.223 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:18:50.223 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:18:50.427 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:19:58.269 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:21:50.428 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:21:50.681 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:24:50.688 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:24:50.893 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:24:58.278 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:27:50.902 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:27:51.121 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:29:58.284 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:30:51.129 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:30:51.347 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:33:51.356 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:33:51.596 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:34:58.292 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:36:51.596 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:36:51.799 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:39:51.808 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:39:52.035 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:39:58.292 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:42:52.037 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:42:52.275 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:44:58.299 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:45:52.284 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:45:52.510 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:48:52.515 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:48:52.741 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:49:58.298 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:51:52.750 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:51:52.951 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:54:52.956 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:54:53.188 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:54:58.308 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 22:57:53.248 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 22:57:53.482 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 22:59:58.371 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:00:53.488 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:00:53.716 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:03:53.727 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:03:53.953 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:04:58.375 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:06:53.962 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:06:54.195 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:09:54.206 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:09:54.450 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:09:58.385 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:12:54.459 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:12:54.687 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:14:58.389 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:15:54.697 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:15:54.931 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:18:54.935 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:18:55.162 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:19:58.394 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:21:55.164 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:21:55.405 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:24:55.415 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:24:55.631 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:24:58.404 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:27:55.636 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:27:55.853 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:29:58.406 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:30:55.862 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:30:56.096 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:33:56.117 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:33:56.347 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:34:58.417 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:36:56.356 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:36:56.594 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:39:56.605 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:39:56.839 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:39:58.427 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:42:56.826 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:42:57.057 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:44:58.412 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:45:57.067 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:45:57.294 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:48:57.304 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:48:57.537 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:49:58.420 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:51:57.547 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:51:57.778 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:54:57.788 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:54:58.016 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:54:58.429 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-08 23:57:58.026 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-08 23:57:58.234 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-08 23:59:58.430 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
