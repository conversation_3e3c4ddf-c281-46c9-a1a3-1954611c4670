2025-05-26 00:09:44.736 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:95677}] to ************:27017
2025-05-26 00:43:33.780 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:95683}] to ************:27017
2025-05-26 01:05:53.804 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:95687}] to ************:27017
2025-05-26 01:23:55.335 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:95694}] to ************:27017
2025-05-26 01:58:40.920 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:95700}] to ************:27017
2025-05-26 02:31:51.453 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:95711}] to ************:27017
2025-05-26 02:31:52.009 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 03:07:39.036 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:95715}] to ************:27017
2025-05-26 03:41:08.807 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:10, serverValue:95719}] to ************:27017
2025-05-26 04:15:19.700 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:11, serverValue:95727}] to ************:27017
2025-05-26 04:49:59.774 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:12, serverValue:95732}] to ************:27017
2025-05-26 05:03:37.624 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:13, serverValue:95734}] to ************:27017
2025-05-26 05:38:14.853 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:14, serverValue:95739}] to ************:27017
2025-05-26 05:55:07.738 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 05:55:08.355 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 06:11:59.836 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:15, serverValue:95745}] to ************:27017
2025-05-26 06:45:26.820 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:16, serverValue:95752}] to ************:27017
2025-05-26 07:04:32.726 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:17, serverValue:95758}] to ************:27017
2025-05-26 07:14:23.439 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:18, serverValue:95763}] to ************:27017
2025-05-26 07:48:44.780 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:19, serverValue:95770}] to ************:27017
2025-05-26 07:58:35.833 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:20, serverValue:95774}] to ************:27017
2025-05-26 08:00:58.820 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:04:09.247 [scheduling-7] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$InternalServerError: [500 ] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [{"resultCode":"99999","resultMsg":"服务器异常！请稍后再试"}]
	at feign.FeignException.serverErrorStatus(FeignException.java:231)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor153.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 08:04:09.247 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:04:20.293 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 08:07:09.259 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:07:09.702 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:09:20.304 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 08:10:09.703 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:10:10.095 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:13:10.097 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:13:10.544 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:14:20.309 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 08:16:10.545 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:16:10.950 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:19:10.951 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:19:11.514 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:19:20.314 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 08:22:11.496 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:22:11.902 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:41:23.367 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:21, serverValue:95778}] to ************:27017
2025-05-26 08:42:21.026 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 08:43:12.632 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:43:13.058 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:46:13.061 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:46:13.477 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:47:21.032 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 08:49:13.478 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:49:13.982 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:52:13.983 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:52:14.589 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:52:21.035 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 08:55:14.593 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:55:15.393 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 08:57:21.037 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 08:58:15.395 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 08:58:16.068 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:01:16.072 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:01:24.558 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:02:21.041 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:06:28.512 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:06:28.949 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:09:24.999 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:09:28.948 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:09:29.724 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:12:29.725 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:12:30.281 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:14:25.003 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:15:30.280 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:15:31.226 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:18:31.227 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:18:31.729 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:19:25.003 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:21:31.730 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:21:32.303 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:24:25.003 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:24:32.305 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:24:32.858 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:27:32.860 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:27:33.645 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:29:25.006 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:30:33.650 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:30:34.923 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:33:34.959 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:33:35.517 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:34:25.047 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:36:35.520 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:36:36.188 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:39:25.050 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:39:36.191 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:39:36.944 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:42:36.945 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:42:37.473 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:44:25.051 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:45:37.476 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:45:38.108 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:48:38.113 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:48:38.595 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:49:25.078 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:51:38.615 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:51:39.174 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:54:25.084 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 09:54:39.182 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:54:39.724 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:57:39.725 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 09:57:40.310 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 09:59:25.086 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:01:46.432 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:01:46.918 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:04:46.922 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:04:47.369 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:05:31.218 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:07:47.369 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:07:47.786 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:10:31.223 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:10:47.788 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:10:48.369 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:17:02.314 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:17:05.571 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:18:45.171 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:20:05.574 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:20:15.759 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:20:47.590 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:562)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessage(InternalStreamConnection.java:447)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:298)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:258)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:33)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initializeConnectionDescription(InternalStreamConnectionInitializer.java:103)
	at com.mongodb.internal.connection.InternalStreamConnectionInitializer.initialize(InternalStreamConnectionInitializer.java:60)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:128)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:579)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessage(InternalStreamConnection.java:444)
	... 9 common frames omitted
2025-05-26 10:21:03.561 [scheduling-6] INFO  c.d.tms.scheduled.SyncDataScheduled - =======================================
2025-05-26 10:21:03.562 [scheduling-6] INFO  c.d.tms.scheduled.SyncDataScheduled - 【开始同步数据到台账表】
2025-05-26 10:21:03.562 [scheduling-6] INFO  c.d.tms.scheduled.SyncDataScheduled - =======================================
2025-05-26 10:21:09.638 [scheduling-6] WARN  c.a.druid.pool.DruidDataSource - get connection timeout retry : 1
2025-05-26 10:21:14.652 [scheduling-6] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
### The error may exist in com/dlcg/tms/mapper/ShippingDemandMapper.java (best guess)
### The error may involve com.dlcg.tms.mapper.ShippingDemandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy114.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:158)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:76)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy265.selectList(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.list(ServiceImpl.java:271)
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:247)
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.tms.service.impl.ShippingDemandServiceImpl$$EnhancerBySpringCGLIB$$88ff4057.list(<generated>)
	at com.dlcg.tms.scheduled.SyncDataScheduled.syncData(SyncDataScheduled.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
### The error may exist in com/dlcg/tms/mapper/ShippingDemandMapper.java (best guess)
### The error may involve com.dlcg.tms.mapper.ShippingDemandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor156.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 27 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:336)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:93)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:66)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:324)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:108)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy347.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	... 32 common frames omitted
Caused by: com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1510)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1253)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4619)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:680)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4615)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1231)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1223)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:90)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	... 44 common frames omitted
2025-05-26 10:21:14.652 [scheduling-6] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
### The error may exist in com/dlcg/tms/mapper/ShippingDemandMapper.java (best guess)
### The error may involve com.dlcg.tms.mapper.ShippingDemandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy114.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:158)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:76)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy265.selectList(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.list(ServiceImpl.java:271)
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:247)
	at com.baomidou.mybatisplus.extension.service.IService$$FastClassBySpringCGLIB$$f8525d18.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.tms.service.impl.ShippingDemandServiceImpl$$EnhancerBySpringCGLIB$$88ff4057.list(<generated>)
	at com.dlcg.tms.scheduled.SyncDataScheduled.syncData(SyncDataScheduled.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
### The error may exist in com/dlcg/tms/mapper/ShippingDemandMapper.java (best guess)
### The error may involve com.dlcg.tms.mapper.ShippingDemandMapper.selectList
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor156.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 27 common frames omitted
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:336)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:93)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:66)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:324)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:108)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy347.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	... 32 common frames omitted
Caused by: com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 0, maxActive 10, creating 1
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1510)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1253)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4619)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:680)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4615)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1231)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1223)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:90)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	... 44 common frames omitted
2025-05-26 10:21:54.820 [Druid-ConnectionPool-Create-2079332573] ERROR c.a.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:mysql://************:3306/wuliu_gangc?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true, errorCode 0, state 08S01
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:836)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:456)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:246)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:197)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:149)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:218)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:143)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1515)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1578)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2466)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.negotiateSSLConnection(NativeProtocol.java:334)
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.connect(NativeAuthenticationProvider.java:164)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1342)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:157)
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:956)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:826)
	... 9 common frames omitted
Caused by: javax.net.ssl.SSLHandshakeException: Remote host terminated the handshake
	at sun.security.ssl.SSLSocketImpl.handleEOF(SSLSocketImpl.java:1579)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1409)
	at sun.security.ssl.SSLSocketImpl.readHandshakeRecord(SSLSocketImpl.java:1309)
	at sun.security.ssl.SSLSocketImpl.startHandshake(SSLSocketImpl.java:440)
	at com.mysql.cj.protocol.ExportControlled.performTlsHandshake(ExportControlled.java:336)
	at com.mysql.cj.protocol.StandardSocketFactory.performTlsHandshake(StandardSocketFactory.java:188)
	at com.mysql.cj.protocol.a.NativeSocketConnection.performTlsHandshake(NativeSocketConnection.java:99)
	at com.mysql.cj.protocol.a.NativeProtocol.negotiateSSLConnection(NativeProtocol.java:325)
	... 14 common frames omitted
Caused by: java.io.EOFException: SSL peer shut down incorrectly
	at sun.security.ssl.SSLSocketInputRecord.decode(SSLSocketInputRecord.java:167)
	at sun.security.ssl.SSLTransport.decode(SSLTransport.java:109)
	at sun.security.ssl.SSLSocketImpl.decode(SSLSocketImpl.java:1401)
	... 20 common frames omitted
2025-05-26 10:21:59.168 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:26, serverValue:95798}] to ************:27017
2025-05-26 10:21:59.314 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=144819792}
2025-05-26 10:23:15.764 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:23:17.337 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:23:45.177 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:26:17.338 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:26:17.959 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:28:45.179 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:29:17.960 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:29:18.610 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:32:18.600 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:32:21.888 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:33:45.168 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:35:21.888 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:35:22.688 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:38:22.689 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:38:23.291 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:38:45.172 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:41:23.294 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:41:23.863 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:43:45.175 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:44:23.865 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:44:24.335 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:47:24.336 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:47:25.325 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:48:45.180 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:50:25.329 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:50:26.513 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:53:26.515 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:53:27.614 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:53:45.185 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:56:27.613 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:56:28.181 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 10:58:45.188 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 10:59:28.182 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 10:59:28.722 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:02:28.742 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:02:29.639 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:03:45.217 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:05:29.644 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:05:30.139 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:08:30.140 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:08:34.070 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:08:45.220 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:11:34.072 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:11:36.239 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:13:45.226 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:14:36.240 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:14:37.195 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:17:37.210 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:17:37.845 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:18:45.245 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:20:37.851 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:20:38.466 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:24:48.233 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:24:48.825 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:24:55.016 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:27:48.833 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:27:49.761 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:29:55.026 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:30:49.762 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:30:50.251 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:33:50.253 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:33:50.700 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:34:55.033 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:36:50.702 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:36:51.408 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:39:51.413 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:39:51.835 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:39:55.044 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:42:51.844 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:42:52.338 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:44:55.047 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:45:52.346 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:45:52.765 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:48:52.772 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:48:53.241 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:49:55.057 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:51:53.245 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:51:53.687 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:54:53.664 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:54:54.084 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:54:55.037 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 11:57:54.086 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 11:57:54.523 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 11:59:55.045 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:00:54.523 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:00:54.899 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:03:54.899 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:03:55.269 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:04:55.053 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:06:55.270 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:06:55.663 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:09:54.905 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:09:55.505 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:09:55.880 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:12:55.882 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:12:56.258 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:14:54.904 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:15:56.259 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:15:56.626 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:18:56.624 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:18:56.992 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:19:54.904 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:21:56.990 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:21:57.365 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:24:54.901 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:24:57.361 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:24:57.751 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:27:57.747 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:27:58.113 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:29:54.899 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:30:58.109 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:30:58.538 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:33:58.534 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:33:58.910 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:34:54.901 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:36:58.912 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:36:59.268 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:39:54.972 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:39:59.333 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:39:59.686 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:42:59.690 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:43:00.183 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:44:54.978 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:46:00.182 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:46:00.705 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:49:00.709 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:49:01.107 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:49:54.982 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:52:01.109 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:52:01.605 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:54:55.031 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 12:55:01.648 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:55:02.022 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:58:02.021 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 12:58:02.401 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 12:59:55.032 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:01:02.405 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:01:02.794 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:04:02.799 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:04:03.666 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:04:55.052 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:07:03.683 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:07:04.120 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:09:55.056 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:10:04.122 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:10:04.577 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:13:04.579 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:13:05.117 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:14:55.057 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:16:05.119 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:16:05.682 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:19:05.684 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:19:06.210 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:19:55.073 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:22:06.260 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:22:06.746 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:23:56.358 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-26 13:24:00.081 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-26 13:24:04.021 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-05-26 13:24:06.189 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-05-26 13:24:06.190 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-26 13:24:06.191 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-26 13:24:06.329 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-26 13:24:06.761 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-26 13:24:11.355 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-26 13:24:13.158 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-26 13:24:13.472 [cluster-ClusterId{value='6833fafdbe5b5003854acf39', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:95806}] to ************:27017
2025-05-26 13:24:13.521 [cluster-ClusterId{value='6833fafdbe5b5003854acf39', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=46469250}
2025-05-26 13:24:15.267 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-26 13:24:15.280 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-26 13:24:15.280 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-26 13:24:15.287 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-26 13:24:15.287 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-26 13:24:16.706 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-26 13:24:16.757 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-26 13:24:16.757 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-26 13:24:16.987 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-26 13:24:16.987 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-26 13:24:17.303 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:24:17.318 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-26 13:24:17.319 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-26 13:24:17.319 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-26 13:24:17.319 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-26 13:24:17.319 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-26 13:24:17.319 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-26 13:24:17.319 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-26 13:24:17.500 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-26 13:24:17.503 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-26 13:24:17.506 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-26 13:24:17.513 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1748237057512 with initial instances count: 5
2025-05-26 13:24:17.519 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748237057519, current=UP, previous=STARTING]
2025-05-26 13:24:17.522 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073: registering service...
2025-05-26 13:24:17.523 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-05-26 13:24:17.570 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - registration status: 204
2025-05-26 13:24:17.657 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-26 13:24:17.658 [main] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-26 13:24:20.665 [main] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-26 13:24:20.671 [main] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - deregister  status: 200
2025-05-26 13:24:20.684 [main] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-26 13:24:20.685 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8073"]
2025-05-26 13:24:20.685 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-26 13:24:20.689 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8073"]
2025-05-26 13:24:20.689 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8073"]
2025-05-26 13:24:20.696 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8073 was already in use.

Action:

Identify and stop the process that's listening on port 8073 or configure this application to listen on another port.

2025-05-26 13:24:20.696 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8073 was already in use.

Action:

Identify and stop the process that's listening on port 8073 or configure this application to listen on another port.

2025-05-26 13:24:40.957 [DiscoveryClient-HeartbeatExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - Re-registering apps/DLCG-TMS
2025-05-26 13:24:40.962 [DiscoveryClient-HeartbeatExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073: registering service...
2025-05-26 13:24:40.977 [DiscoveryClient-HeartbeatExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - registration status: 204
2025-05-26 13:24:55.114 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:25:06.747 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:25:06.754 [scheduling-18] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:90)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor153.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at com.netflix.loadbalancer.LoadBalancerContext.getServerFromLoadBalancer(LoadBalancerContext.java:483)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:184)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:180)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:94)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:42)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber$1.call(OperatorRetryWithPredicate.java:127)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.enqueue(TrampolineScheduler.java:73)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.schedule(TrampolineScheduler.java:52)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:79)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:45)
	at rx.internal.util.ScalarSynchronousObservable$WeakSingleProducer.request(ScalarSynchronousObservable.java:276)
	at rx.Subscriber.setProducer(Subscriber.java:209)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:138)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:129)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.Observable.subscribe(Observable.java:10423)
	at rx.Observable.subscribe(Observable.java:10390)
	at rx.observables.BlockingObservable.blockForSingle(BlockingObservable.java:443)
	at rx.observables.BlockingObservable.single(BlockingObservable.java:340)
	at com.netflix.client.AbstractLoadBalancerAwareClient.executeWithLoadBalancer(AbstractLoadBalancerAwareClient.java:112)
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:83)
	... 18 common frames omitted
2025-05-26 13:25:06.757 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:25:52.724 [cluster-ClusterId{value='6832b279b705bf19f142d65b', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Can't connect to SOCKS proxy:Connection refused (Connection refused)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:428)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-05-26 13:25:58.467 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-05-26 13:25:58.594 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748237158594, current=DOWN, previous=UP]
2025-05-26 13:25:58.598 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073: registering service...
2025-05-26 13:25:58.627 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - registration status: 204
2025-05-26 13:25:59.266 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-26 13:25:59.267 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-26 13:25:59.324 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-26 13:25:59.327 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-26 13:28:17.030 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-26 13:28:18.396 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-26 13:28:20.734 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-05-26 13:28:22.556 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-05-26 13:28:22.556 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-26 13:28:22.556 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-26 13:28:22.632 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-26 13:28:23.176 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-26 13:28:26.793 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-26 13:28:32.266 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-26 13:28:32.509 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:95811}] to ************:27017
2025-05-26 13:28:32.544 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=32981042}
2025-05-26 13:28:36.118 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-26 13:28:36.127 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-26 13:28:36.127 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-26 13:28:36.132 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-26 13:28:36.132 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-26 13:28:36.991 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-26 13:28:37.042 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-26 13:28:37.043 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-26 13:28:37.166 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-26 13:28:37.166 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-26 13:28:37.357 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:28:37.371 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-26 13:28:37.371 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-26 13:28:37.371 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-26 13:28:37.371 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-26 13:28:37.372 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-26 13:28:37.372 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-26 13:28:37.372 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-26 13:28:37.506 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-26 13:28:37.507 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-26 13:28:37.509 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-26 13:28:37.512 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1748237317511 with initial instances count: 0
2025-05-26 13:28:37.519 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748237317519, current=UP, previous=STARTING]
2025-05-26 13:28:37.522 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-05-26 13:28:37.526 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073: registering service...
2025-05-26 13:28:37.595 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - registration status: 204
2025-05-26 13:28:37.605 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:28:37.609 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 20.964 seconds (JVM running for 21.805)
2025-05-26 13:28:37.733 [scheduling-2] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-26 13:28:37.746 [scheduling-2] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook installed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-26 13:28:37.747 [scheduling-2] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: dlcg-oa instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-05-26 13:28:37.754 [scheduling-2] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-05-26 13:28:37.757 [scheduling-2] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client dlcg-oa initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:org.springframework.cloud.netflix.ribbon.eureka.DomainExtractingServerList@4536a8ef
2025-05-26 13:28:37.818 [scheduling-2] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:90)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at com.netflix.loadbalancer.LoadBalancerContext.getServerFromLoadBalancer(LoadBalancerContext.java:483)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:184)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:180)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:94)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:42)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber$1.call(OperatorRetryWithPredicate.java:127)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.enqueue(TrampolineScheduler.java:73)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.schedule(TrampolineScheduler.java:52)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:79)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:45)
	at rx.internal.util.ScalarSynchronousObservable$WeakSingleProducer.request(ScalarSynchronousObservable.java:276)
	at rx.Subscriber.setProducer(Subscriber.java:209)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:138)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:129)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.Observable.subscribe(Observable.java:10423)
	at rx.Observable.subscribe(Observable.java:10390)
	at rx.observables.BlockingObservable.blockForSingle(BlockingObservable.java:443)
	at rx.observables.BlockingObservable.single(BlockingObservable.java:340)
	at com.netflix.client.AbstractLoadBalancerAwareClient.executeWithLoadBalancer(AbstractLoadBalancerAwareClient.java:112)
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:83)
	... 19 common frames omitted
2025-05-26 13:28:37.818 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:29:07.513 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-26 13:29:07.514 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-26 13:29:07.514 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-26 13:29:07.514 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-26 13:29:07.514 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-26 13:29:07.514 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: false
2025-05-26 13:29:07.514 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-26 13:29:07.537 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-26 13:29:08.793 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-26 13:31:37.818 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:31:38.463 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:33:37.375 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:34:38.462 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:34:38.970 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:37:38.970 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:37:39.397 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:38:37.379 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:40:39.398 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:40:39.757 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:43:37.402 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:43:39.778 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:43:41.399 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:46:41.400 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:46:42.049 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:48:37.406 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:49:42.050 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:49:42.472 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:52:42.471 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:52:42.905 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:53:37.410 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:55:42.904 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:55:43.318 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 13:58:37.463 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 13:58:40.740 [http-nio-8073-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-26 13:58:43.366 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 13:58:43.748 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:01:43.751 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:01:44.267 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:03:37.466 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:04:44.268 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:04:44.627 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:07:44.631 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:07:44.972 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:08:37.468 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:10:44.972 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:10:45.348 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:13:37.421 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:13:45.302 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:13:45.799 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:16:45.798 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:16:46.202 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:18:37.425 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:19:46.203 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:19:46.587 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:22:46.587 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:22:46.930 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:23:37.428 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:25:46.930 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:25:47.264 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:28:37.433 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:28:47.266 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:28:47.634 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:40:37.366 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:95832}] to ************:27017
2025-05-26 14:42:19.357 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:42:19.874 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:44:09.159 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:45:19.874 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:45:20.350 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:48:20.350 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:48:20.811 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:49:09.160 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:51:20.814 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:51:21.519 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:54:09.163 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 14:54:21.520 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:54:22.126 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:57:22.129 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 14:57:22.753 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 14:59:09.180 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:00:22.766 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:00:23.259 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:03:23.261 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:03:23.745 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:04:09.186 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:06:23.746 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:06:24.213 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:09:09.191 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:09:24.214 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:09:24.719 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:12:24.721 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:12:25.311 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:14:09.166 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:15:25.285 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:15:25.719 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:18:25.722 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:18:26.245 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:19:09.168 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:21:26.244 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:21:27.027 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:24:09.169 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:24:27.028 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:24:27.664 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:27:27.664 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:27:28.135 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:29:09.161 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:30:28.122 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:30:46.221 [scheduling-21] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$InternalServerError: [500 ] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [{"resultCode":"99999","resultMsg":"服务器异常！请稍后再试"}]
	at feign.FeignException.serverErrorStatus(FeignException.java:231)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 15:30:46.221 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:33:46.222 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:33:46.873 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:34:09.160 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:36:46.873 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:36:47.285 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:39:09.164 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:39:47.286 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:39:52.069 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:42:52.070 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:42:56.284 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:44:09.206 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:45:56.326 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:45:56.963 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:48:56.963 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:48:57.637 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:49:09.209 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:51:57.638 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:51:58.210 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:54:09.214 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 15:54:58.210 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:54:58.698 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:57:58.699 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 15:57:59.391 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 15:59:09.265 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:00:59.440 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:01:00.064 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:04:00.068 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:04:00.548 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:04:09.274 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:07:00.550 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:07:01.065 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:09:09.280 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:10:01.069 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:10:01.605 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:13:01.608 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:13:02.156 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:14:09.281 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:16:02.151 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:16:13.202 [scheduling-27] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$InternalServerError: [500 ] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [{"resultCode":"99999","resultMsg":"服务器异常！请稍后再试"}]
	at feign.FeignException.serverErrorStatus(FeignException.java:231)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:16:13.203 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:16:37.009 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:95852}] to ************:27017
2025-05-26 16:16:47.012 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketReadTimeoutException: Timeout while receiving message
	at com.mongodb.internal.connection.InternalStreamConnection.translateReadException(InternalStreamConnection.java:562)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessage(InternalStreamConnection.java:447)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveCommandMessageResponse(InternalStreamConnection.java:298)
	at com.mongodb.internal.connection.InternalStreamConnection.sendAndReceive(InternalStreamConnection.java:258)
	at com.mongodb.internal.connection.CommandHelper.sendAndReceive(CommandHelper.java:83)
	at com.mongodb.internal.connection.CommandHelper.executeCommand(CommandHelper.java:38)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.lookupServerDescription(DefaultServerMonitor.java:180)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:137)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at com.mongodb.internal.connection.SocketStream.read(SocketStream.java:109)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveResponseBuffers(InternalStreamConnection.java:579)
	at com.mongodb.internal.connection.InternalStreamConnection.receiveMessage(InternalStreamConnection.java:444)
	... 7 common frames omitted
2025-05-26 16:17:57.328 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:95873}] to ************:27017
2025-05-26 16:17:57.409 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=79255250}
2025-05-26 16:19:09.286 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:19:13.205 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:19:23.232 [scheduling-19] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:19:23.233 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:22:23.236 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:22:33.254 [scheduling-16] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:22:33.255 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:24:09.293 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:25:33.256 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:25:43.275 [scheduling-15] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:25:43.276 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:28:43.287 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:28:53.307 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:28:53.308 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:29:09.317 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:31:53.317 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:32:03.330 [scheduling-17] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:32:03.331 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:34:09.325 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:35:03.333 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:35:13.349 [scheduling-30] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:35:13.350 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:38:13.352 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:38:23.369 [scheduling-25] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:38:23.370 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:39:09.331 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:41:23.372 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:41:33.391 [scheduling-26] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:41:33.392 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:44:09.351 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:44:33.408 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:44:43.424 [scheduling-19] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:44:43.425 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:47:43.427 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:47:53.443 [scheduling-21] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:47:53.443 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:49:09.359 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:50:53.447 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:51:03.463 [scheduling-17] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:51:03.464 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:54:03.470 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:54:09.368 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 16:54:13.488 [scheduling-9] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:54:13.488 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:57:13.491 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 16:57:23.508 [scheduling-7] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 16:57:23.508 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 16:59:09.383 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:00:23.519 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:00:33.536 [scheduling-20] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:00:33.537 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:03:33.540 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:03:43.559 [scheduling-19] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:03:43.560 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:04:09.395 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:06:43.564 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:06:53.581 [scheduling-17] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:06:53.582 [scheduling-17] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:09:09.404 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:09:53.586 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:10:03.602 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:10:03.603 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:13:03.610 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:13:13.629 [scheduling-5] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:13:13.629 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:14:09.392 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:16:13.606 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:16:23.626 [scheduling-18] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:16:23.627 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:19:09.392 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:19:23.632 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:19:33.657 [scheduling-6] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:19:33.657 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:22:33.659 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:22:43.675 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:22:43.676 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:24:09.406 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:25:43.681 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:25:53.696 [scheduling-3] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:25:53.697 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:28:53.699 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:29:03.719 [scheduling-7] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:29:03.720 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:29:09.418 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:32:03.728 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:32:13.741 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:32:13.742 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:34:09.427 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:35:13.745 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:35:23.761 [scheduling-24] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:35:23.762 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:38:23.764 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:38:33.780 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:38:33.780 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:39:09.435 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:41:33.784 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:41:43.806 [scheduling-19] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:41:43.806 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:44:09.441 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:44:43.749 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:44:53.768 [scheduling-27] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:44:53.769 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:47:53.770 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:48:03.789 [scheduling-23] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:48:03.790 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 17:49:09.387 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 17:51:34.675 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 17:51:35.708 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:95882}] to ************:27017
2025-05-26 17:51:35.711 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9735 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 17:51:35.712 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 18:07:17.068 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:95888}] to ************:27017
2025-05-26 18:42:13.899 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:10, serverValue:95894}] to ************:27017
2025-05-26 19:15:48.344 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:11, serverValue:95904}] to ************:27017
2025-05-26 19:33:02.154 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:12, serverValue:95911}] to ************:27017
2025-05-26 19:51:51.662 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:13, serverValue:95917}] to ************:27017
2025-05-26 20:09:00.275 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:14, serverValue:95923}] to ************:27017
2025-05-26 20:35:03.669 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:15, serverValue:95929}] to ************:27017
2025-05-26 20:37:20.651 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 20:37:25.209 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 20:37:30.671 [scheduling-12] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 20:37:30.672 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 20:40:30.676 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 20:40:40.706 [scheduling-30] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9729 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor151.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 20:40:40.707 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 20:41:13.781 [cluster-ClusterId{value='6833fc000c24c31ab6a06405', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Can't connect to SOCKS proxy:Connection refused (Connection refused)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:428)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-05-26 20:42:23.287 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-05-26 20:42:23.439 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748263343439, current=DOWN, previous=UP]
2025-05-26 20:42:23.442 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073: registering service...
2025-05-26 20:42:23.453 [DiscoveryClient-InstanceInfoReplicator-0] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.register(AbstractJerseyEurekaHttpClient.java:57)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 29 common frames omitted
2025-05-26 20:42:23.454 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-26 20:42:23.457 [DiscoveryClient-InstanceInfoReplicator-0] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.post(WebResource.java:570)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.register(AbstractJerseyEurekaHttpClient.java:57)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 30 common frames omitted
2025-05-26 20:42:23.458 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-26 20:42:23.458 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/***********:dlcg-tms:8073 - registration failed Cannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 20:42:23.459 [DiscoveryClient-InstanceInfoReplicator-0] WARN  c.n.d.InstanceInfoReplicator - There was a problem with the instance info replicator
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$1.execute(EurekaHttpClientDecorator.java:59)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.register(EurekaHttpClientDecorator.java:56)
	at com.netflix.discovery.DiscoveryClient.register(DiscoveryClient.java:873)
	at com.netflix.discovery.InstanceInfoReplicator.run(InstanceInfoReplicator.java:121)
	at com.netflix.discovery.InstanceInfoReplicator$1.run(InstanceInfoReplicator.java:101)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-26 20:42:23.872 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-26 20:42:23.873 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-26 20:42:23.965 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-26 20:42:23.969 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-26 20:42:25.211 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:07:13.151 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-26 21:07:15.847 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-26 21:07:18.436 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-05-26 21:07:20.147 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-05-26 21:07:20.147 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-26 21:07:20.147 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-26 21:07:20.252 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-26 21:07:20.623 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-26 21:07:23.333 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-26 21:07:25.833 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-26 21:07:25.945 [cluster-ClusterId{value='6834678d3a25691289dba801', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:95939}] to ************:27017
2025-05-26 21:07:25.980 [cluster-ClusterId{value='6834678d3a25691289dba801', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=32594833}
2025-05-26 21:07:27.853 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-26 21:07:27.870 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-26 21:07:27.870 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-26 21:07:27.878 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-26 21:07:27.879 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-26 21:07:28.611 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-26 21:07:28.641 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-26 21:07:28.641 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-26 21:07:28.737 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-26 21:07:28.737 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-26 21:07:28.893 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:07:28.906 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-26 21:07:28.906 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-26 21:07:28.906 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-26 21:07:28.906 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-26 21:07:28.906 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-26 21:07:28.906 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-26 21:07:28.906 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-26 21:07:29.020 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-26 21:07:29.021 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-26 21:07:29.025 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-26 21:07:29.028 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1748264849027 with initial instances count: 0
2025-05-26 21:07:29.030 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748264849030, current=UP, previous=STARTING]
2025-05-26 21:07:29.032 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073: registering service...
2025-05-26 21:07:29.033 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-05-26 21:07:29.065 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073 - registration status: 204
2025-05-26 21:07:29.077 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:07:29.078 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 16.15 seconds (JVM running for 16.624)
2025-05-26 21:07:29.158 [scheduling-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-26 21:07:29.169 [scheduling-1] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook installed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-26 21:07:29.169 [scheduling-1] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: dlcg-oa instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-05-26 21:07:29.173 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-05-26 21:07:29.175 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client dlcg-oa initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:org.springframework.cloud.netflix.ribbon.eureka.DomainExtractingServerList@510d8def
2025-05-26 21:07:29.206 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:90)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at com.netflix.loadbalancer.LoadBalancerContext.getServerFromLoadBalancer(LoadBalancerContext.java:483)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:184)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:180)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:94)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:42)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber$1.call(OperatorRetryWithPredicate.java:127)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.enqueue(TrampolineScheduler.java:73)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.schedule(TrampolineScheduler.java:52)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:79)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:45)
	at rx.internal.util.ScalarSynchronousObservable$WeakSingleProducer.request(ScalarSynchronousObservable.java:276)
	at rx.Subscriber.setProducer(Subscriber.java:209)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:138)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:129)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.Observable.subscribe(Observable.java:10423)
	at rx.Observable.subscribe(Observable.java:10390)
	at rx.observables.BlockingObservable.blockForSingle(BlockingObservable.java:443)
	at rx.observables.BlockingObservable.single(BlockingObservable.java:340)
	at com.netflix.client.AbstractLoadBalancerAwareClient.executeWithLoadBalancer(AbstractLoadBalancerAwareClient.java:112)
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:83)
	... 19 common frames omitted
2025-05-26 21:07:29.206 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:07:36.446 [http-nio-8073-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-26 21:07:59.027 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-26 21:07:59.027 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-26 21:07:59.028 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-26 21:07:59.028 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-26 21:07:59.028 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-26 21:07:59.028 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: false
2025-05-26 21:07:59.028 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-26 21:07:59.055 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-26 21:08:00.192 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-26 21:10:29.206 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:10:29.741 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:11:36.956 [cluster-ClusterId{value='6834678d3a25691289dba801', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:95944}] to ************:27017
2025-05-26 21:12:28.912 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:13:29.742 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:13:30.092 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:16:30.094 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:16:30.463 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:17:28.916 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:19:30.466 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:19:30.831 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:22:28.960 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:22:30.873 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:22:31.240 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:25:31.242 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:25:31.608 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:27:28.967 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:28:31.613 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:28:32.011 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:31:32.014 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:31:32.381 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:32:28.974 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:34:32.385 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:34:32.794 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:37:29.006 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:37:32.822 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:37:33.169 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:40:33.171 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:40:33.544 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:42:29.014 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:43:33.547 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:43:33.912 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:46:33.915 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:46:34.257 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:47:29.023 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:49:34.260 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:49:34.618 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:52:29.009 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-26 21:52:34.603 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-26 21:52:34.977 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-26 21:54:51.214 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-05-26 21:54:51.219 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748267691219, current=DOWN, previous=UP]
2025-05-26 21:54:51.220 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073: registering service...
2025-05-26 21:54:51.231 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073 - registration status: 204
2025-05-26 21:54:51.331 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-26 21:54:51.332 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-26 21:54:51.341 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-26 21:54:51.342 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-26 21:54:54.345 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-26 21:54:54.349 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-05-26 21:54:54.350 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-26 21:54:54.351 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-05-26 21:54:54.352 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-26 21:54:54.352 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/************:dlcg-tms:8073 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-26 21:54:54.364 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
