2025-05-29 00:30:43.231 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:28, serverValue:96282}] to ************:27017
2025-05-29 01:02:30.737 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:29, serverValue:96288}] to ************:27017
2025-05-29 01:03:03.199 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 01:03:03.211 [lettuce-nioEventLoop-4-11] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 01:20:16.810 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 01:37:54.304 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:30, serverValue:96298}] to ************:27017
2025-05-29 01:56:03.669 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:31, serverValue:96305}] to ************:27017
2025-05-29 01:56:05.724 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 01:56:05.734 [lettuce-nioEventLoop-4-1] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 01:56:22.876 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 01:56:23.308 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 02:29:25.731 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:32, serverValue:96319}] to ************:27017
2025-05-29 03:00:34.282 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:33, serverValue:96326}] to ************:27017
2025-05-29 03:00:56.082 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 03:00:56.088 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 03:33:01.063 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:34, serverValue:96332}] to ************:27017
2025-05-29 04:05:25.513 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:35, serverValue:96341}] to ************:27017
2025-05-29 04:37:53.056 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:36, serverValue:96346}] to ************:27017
2025-05-29 05:01:31.625 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:37, serverValue:96353}] to ************:27017
2025-05-29 05:01:32.911 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 05:01:32.915 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 05:20:01.940 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:38, serverValue:96358}] to ************:27017
2025-05-29 05:52:30.524 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:39, serverValue:96368}] to ************:27017
2025-05-29 05:52:31.565 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 05:52:31.570 [lettuce-nioEventLoop-4-4] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 06:02:33.336 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 06:02:33.340 [lettuce-nioEventLoop-4-5] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 06:02:39.353 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:40, serverValue:96373}] to ************:27017
2025-05-29 06:02:47.256 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 06:02:47.672 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 06:19:16.630 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:41, serverValue:96378}] to ************:27017
2025-05-29 06:19:18.462 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 06:19:18.467 [lettuce-nioEventLoop-4-6] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 06:53:36.151 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:42, serverValue:96387}] to ************:27017
2025-05-29 07:03:36.972 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:43, serverValue:96392}] to ************:27017
2025-05-29 07:03:40.613 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 07:21:28.718 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 07:21:28.724 [lettuce-nioEventLoop-4-7] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 07:38:43.939 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:44, serverValue:96401}] to ************:27017
2025-05-29 07:56:37.660 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:45, serverValue:96408}] to ************:27017
2025-05-29 08:04:31.531 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:46, serverValue:96410}] to ************:27017
2025-05-29 08:04:52.968 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 08:04:52.974 [lettuce-nioEventLoop-4-8] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:21:34.323 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:21:34.749 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:21:37.508 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:47, serverValue:96420}] to ************:27017
2025-05-29 08:34:12.944 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:48, serverValue:96424}] to ************:27017
2025-05-29 08:34:21.394 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 08:34:21.400 [lettuce-nioEventLoop-4-9] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:14.687 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:129)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:502)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-05-29 08:35:14.693 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 08:35:14.707 [lettuce-nioEventLoop-4-10] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:14.799 [lettuce-nioEventLoop-4-11] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:14.893 [lettuce-nioEventLoop-4-1] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:14.995 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.093 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.198 [lettuce-nioEventLoop-4-4] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.293 [lettuce-nioEventLoop-4-5] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.398 [lettuce-nioEventLoop-4-6] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.499 [lettuce-nioEventLoop-4-7] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.598 [lettuce-nioEventLoop-4-8] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.698 [lettuce-nioEventLoop-4-9] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.794 [lettuce-nioEventLoop-4-10] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:15.901 [lettuce-nioEventLoop-4-11] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.005 [lettuce-nioEventLoop-4-1] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.094 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.202 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.297 [lettuce-nioEventLoop-4-4] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.394 [lettuce-nioEventLoop-4-5] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.498 [lettuce-nioEventLoop-4-6] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.599 [lettuce-nioEventLoop-4-7] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.701 [lettuce-nioEventLoop-4-8] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.798 [lettuce-nioEventLoop-4-9] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.899 [lettuce-nioEventLoop-4-10] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:16.999 [lettuce-nioEventLoop-4-11] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.100 [lettuce-nioEventLoop-4-1] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.197 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.301 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.394 [lettuce-nioEventLoop-4-4] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.496 [lettuce-nioEventLoop-4-5] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.598 [lettuce-nioEventLoop-4-6] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.697 [lettuce-nioEventLoop-4-7] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.798 [lettuce-nioEventLoop-4-8] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.898 [lettuce-nioEventLoop-4-9] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:17.995 [lettuce-nioEventLoop-4-10] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.099 [lettuce-nioEventLoop-4-11] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.197 [lettuce-nioEventLoop-4-1] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.295 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.397 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.498 [lettuce-nioEventLoop-4-4] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.596 [lettuce-nioEventLoop-4-5] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.701 [lettuce-nioEventLoop-4-6] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.798 [lettuce-nioEventLoop-4-7] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.900 [lettuce-nioEventLoop-4-8] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:18.998 [lettuce-nioEventLoop-4-9] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.100 [lettuce-nioEventLoop-4-10] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.195 [lettuce-nioEventLoop-4-11] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.299 [lettuce-nioEventLoop-4-1] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.399 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.499 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.593 [lettuce-nioEventLoop-4-4] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.694 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 08:35:19.696 [lettuce-nioEventLoop-4-5] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.799 [lettuce-nioEventLoop-4-6] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.894 [lettuce-nioEventLoop-4-7] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:19.996 [lettuce-nioEventLoop-4-8] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.098 [lettuce-nioEventLoop-4-9] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.195 [lettuce-nioEventLoop-4-10] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.297 [lettuce-nioEventLoop-4-11] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.398 [lettuce-nioEventLoop-4-1] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.497 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.594 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.698 [lettuce-nioEventLoop-4-4] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.798 [lettuce-nioEventLoop-4-5] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.894 [lettuce-nioEventLoop-4-6] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:20.997 [lettuce-nioEventLoop-4-7] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:21.098 [lettuce-nioEventLoop-4-8] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:21.198 [lettuce-nioEventLoop-4-9] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:21.296 [lettuce-nioEventLoop-4-10] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:21.397 [lettuce-nioEventLoop-4-11] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 08:35:24.773 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:50, serverValue:96433}] to ************:27017
2025-05-29 08:35:24.803 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=28536417}
2025-05-29 08:36:42.299 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:36:46.866 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:36:47.721 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:39:47.745 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:39:48.162 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:41:42.323 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:42:48.163 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:42:48.599 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:45:48.600 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:45:49.004 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:46:42.330 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:48:49.009 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:48:49.634 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:51:42.334 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:51:49.638 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:51:50.243 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:54:50.264 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:54:50.610 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:56:42.351 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:57:50.615 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:57:51.093 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:00:51.098 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:00:51.483 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:01:42.356 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:03:51.493 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:03:51.929 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:06:42.363 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:06:51.933 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:06:52.298 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:09:52.251 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:09:52.632 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:11:42.313 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:12:52.635 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:12:53.073 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:15:53.074 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:15:53.474 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:16:42.316 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:18:53.476 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:18:53.899 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:21:42.317 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:21:53.903 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:21:54.283 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:24:54.285 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:24:54.683 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:26:42.323 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:27:54.686 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:27:55.062 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:30:55.062 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:30:55.480 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:31:42.325 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:33:55.482 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:33:55.861 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:36:42.327 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:36:55.865 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:36:56.275 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:39:56.318 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:39:56.704 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:41:42.375 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:42:56.708 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:42:58.221 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:45:58.225 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:45:58.569 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:46:42.383 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:48:58.574 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:48:58.957 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:51:42.386 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:51:58.961 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:51:59.330 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:54:59.356 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:54:59.724 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:56:42.419 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:57:59.733 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:58:00.130 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:01:00.134 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:01:00.563 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:01:42.426 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:04:00.568 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:04:01.133 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:06:42.431 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:07:01.136 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:07:01.532 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:10:01.558 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:10:01.954 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:11:42.460 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:13:01.958 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:13:02.661 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:16:02.670 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:16:03.111 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:16:42.471 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:19:03.125 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:19:03.843 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:21:42.491 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:22:03.854 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:22:04.731 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:25:04.737 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:25:05.322 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:26:42.498 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:28:05.325 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:28:05.845 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:31:05.849 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:31:06.218 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:31:42.502 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:34:06.231 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:34:07.100 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:36:42.517 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:37:07.108 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:37:08.507 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:40:08.511 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:40:09.294 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:41:42.528 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:43:09.295 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:43:09.672 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:46:09.678 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:46:10.717 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:46:42.536 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:49:10.662 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:49:12.210 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:51:42.486 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:52:12.214 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:52:12.690 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:55:12.690 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:55:14.654 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:56:42.488 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:58:14.654 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:58:15.353 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:01:15.355 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:01:15.796 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:01:42.492 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:04:15.784 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:04:16.408 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:06:42.481 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:07:16.409 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:07:16.976 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:10:16.977 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:10:18.445 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:11:42.484 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:13:18.452 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:13:23.948 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:16:23.953 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:16:24.453 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:16:42.487 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:19:24.453 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:19:24.926 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:21:42.490 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:22:24.929 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:22:25.351 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:25:25.356 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:25:25.795 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:26:42.493 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:28:25.797 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:28:26.270 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:31:26.275 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:31:26.697 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:31:42.495 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:34:26.700 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:34:27.565 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:36:42.463 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:37:27.535 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:37:27.982 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:40:27.991 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:40:28.410 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:41:42.471 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:43:28.418 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:43:28.838 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:46:28.847 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:46:29.302 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:46:42.474 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:49:29.309 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:49:29.772 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:51:42.512 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:52:29.822 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:52:30.373 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:55:30.375 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:55:30.920 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:56:42.522 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:58:30.921 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:58:31.547 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:00:18.037 [scheduling-1] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Can't assign requested address
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.GeneratedMethodAccessor261.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Can't assign requested address
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy258.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 21 common frames omitted
Caused by: java.io.IOException: Can't assign requested address
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-29 12:00:18.037 [scheduling-1] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Can't assign requested address
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.GeneratedMethodAccessor261.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Can't assign requested address
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy258.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 21 common frames omitted
Caused by: java.io.IOException: Can't assign requested address
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-29 12:00:18.068 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-29 12:00:18.123 [lettuce-nioEventLoop-4-1] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-29 12:01:31.547 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:01:31.980 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:01:42.528 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:04:31.988 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:04:32.376 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:06:42.530 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:07:32.381 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:07:32.790 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:10:32.796 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:10:33.214 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:11:42.537 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:13:33.215 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:13:33.645 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:16:33.650 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:16:34.309 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:16:42.544 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:19:34.312 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:19:34.715 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:21:42.588 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:22:34.761 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:22:35.151 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:25:35.162 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:25:35.580 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:26:42.597 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:28:35.586 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:28:35.996 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:31:36.004 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:31:36.411 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:31:42.606 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:34:36.413 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:34:36.829 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:36:42.633 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:37:36.857 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:37:37.662 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:40:37.671 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:40:38.233 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:41:42.645 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:43:38.244 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:43:38.643 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:46:38.649 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:46:39.041 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:46:42.652 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:49:39.050 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:49:39.419 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:51:42.675 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:52:39.436 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:52:39.857 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:55:39.864 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:55:40.307 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:56:42.690 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:58:40.316 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:58:40.720 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:01:40.725 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:01:41.119 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:01:42.694 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:04:41.128 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:04:41.510 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:06:42.714 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:07:41.524 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:07:41.916 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:10:41.925 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:10:42.689 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:11:42.730 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:13:42.701 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:13:43.150 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:16:42.746 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:16:43.160 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:16:44.186 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:19:44.190 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:19:44.881 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:21:42.754 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:22:44.883 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:22:47.595 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:25:47.597 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:25:49.429 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:26:42.762 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:28:49.433 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:28:49.857 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:31:42.743 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:31:49.836 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:31:51.172 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:34:51.178 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:34:51.563 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:35:59.250 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748496959250, current=DOWN, previous=UP]
2025-05-29 13:35:59.251 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.28:dlcg-oa:8072: registering service...
2025-05-29 13:35:59.270 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.28:dlcg-oa:8072 - registration status: 204
2025-05-29 13:35:59.460 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-29 13:35:59.605 [SpringContextShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:3, serverValue:96027}] to ************:27017 because there was a socket exception raised on another connection from this pool.
2025-05-29 13:35:59.607 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-29 13:36:02.611 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-29 13:36:02.622 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-05-29 13:36:02.623 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-29 13:36:02.624 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-05-29 13:36:02.624 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-29 13:36:02.625 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.28:dlcg-oa:8072 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-29 13:36:02.632 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-29 13:36:07.522 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-29 13:36:08.867 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-29 13:36:10.591 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.oa.mapper.*]' package. Please check your configuration.
2025-05-29 13:36:12.323 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8072"]
2025-05-29 13:36:12.323 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-29 13:36:12.323 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-29 13:36:12.663 [restartedMain] INFO  o.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-05-29 13:36:12.667 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-29 13:36:12.897 [restartedMain] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-29 13:36:13.062 [cluster-ClusterId{value='6837f24cd3a851609a595d4d', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:96457}] to ************:27017
2025-05-29 13:36:13.100 [cluster-ClusterId{value='6837f24cd3a851609a595d4d', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=36108542}
2025-05-29 13:36:13.758 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-29 13:36:16.670 [restartedMain] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-29 13:36:20.120 [restartedMain] INFO  c.d.o.c.AliyunOSSAutoConfiguration - load aliyun-oss..
2025-05-29 13:36:20.283 [scheduling-1] INFO  c.d.o.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-29 13:36:20.583 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-29 13:36:20.583 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-29 13:36:20.590 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-29 13:36:20.590 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-29 13:36:22.048 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-29 13:36:22.154 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-29 13:36:22.154 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-29 13:36:22.374 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-29 13:36:22.374 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-29 13:36:22.792 [restartedMain] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:36:22.823 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-29 13:36:22.824 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-29 13:36:22.824 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-29 13:36:22.824 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-29 13:36:22.824 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-29 13:36:22.824 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-29 13:36:22.824 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-29 13:36:23.174 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-29 13:36:23.175 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-29 13:36:23.178 [restartedMain] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-29 13:36:23.187 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1748496983186 with initial instances count: 0
2025-05-29 13:36:23.199 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748496983199, current=UP, previous=STARTING]
2025-05-29 13:36:23.202 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8072"]
2025-05-29 13:36:23.203 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.109:dlcg-oa:8072: registering service...
2025-05-29 13:36:23.257 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/192.168.1.109:dlcg-oa:8072 - registration status: 204
2025-05-29 13:36:23.377 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:36:23.386 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 16.202 seconds (JVM running for 16.684)
2025-05-29 13:36:23.923 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:36:53.189 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-29 13:36:53.194 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-29 13:36:53.194 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-29 13:36:53.194 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-29 13:36:53.194 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-29 13:36:53.194 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: false
2025-05-29 13:36:53.194 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-29 13:36:53.221 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-29 13:39:23.952 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:39:24.528 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:39:25.879 [http-nio-8072-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 13:41:22.831 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:42:24.534 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:42:25.144 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:45:25.160 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:45:25.769 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:46:22.852 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:48:25.777 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:48:26.212 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:51:22.857 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:51:26.216 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:51:26.969 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:54:26.976 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:54:27.481 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:56:22.863 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:57:27.485 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:57:28.011 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:00:28.018 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:00:28.585 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:01:22.874 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:03:28.594 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:03:29.313 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:06:22.883 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:06:29.317 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:06:30.043 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:09:30.049 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:09:30.428 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:11:22.893 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:12:30.430 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:12:30.841 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:15:30.849 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:15:31.452 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:16:22.902 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:18:31.458 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:18:32.014 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:21:22.911 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:21:32.026 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:21:32.445 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:24:32.450 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:24:32.950 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:26:22.917 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:27:32.953 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:27:33.566 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:30:33.574 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:30:34.298 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:31:22.926 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
