2025-05-29 00:30:43.967 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:25, serverValue:96283}] to ************:27017
2025-05-29 01:02:31.465 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:26, serverValue:96289}] to ************:27017
2025-05-29 01:37:52.843 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 01:37:54.953 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:27, serverValue:96299}] to ************:27017
2025-05-29 01:38:08.021 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 01:38:08.831 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 01:56:04.340 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:28, serverValue:96306}] to ************:27017
2025-05-29 02:29:26.299 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:29, serverValue:96320}] to ************:27017
2025-05-29 03:00:34.845 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:30, serverValue:96327}] to ************:27017
2025-05-29 03:33:01.646 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:31, serverValue:96333}] to ************:27017
2025-05-29 04:05:26.081 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:32, serverValue:96342}] to ************:27017
2025-05-29 04:37:53.641 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:33, serverValue:96347}] to ************:27017
2025-05-29 05:01:32.211 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:34, serverValue:96354}] to ************:27017
2025-05-29 05:02:04.768 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 05:02:05.599 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 05:20:02.509 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:35, serverValue:96359}] to ************:27017
2025-05-29 05:52:31.097 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:36, serverValue:96369}] to ************:27017
2025-05-29 06:02:39.927 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:37, serverValue:96374}] to ************:27017
2025-05-29 06:19:17.178 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:38, serverValue:96379}] to ************:27017
2025-05-29 06:53:36.702 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:39, serverValue:96388}] to ************:27017
2025-05-29 07:03:37.525 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:40, serverValue:96393}] to ************:27017
2025-05-29 07:03:43.099 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 07:21:28.388 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:41, serverValue:96397}] to ************:27017
2025-05-29 07:56:29.359 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:42, serverValue:96402}] to ************:27017
2025-05-29 08:04:33.230 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:43, serverValue:96411}] to ************:27017
2025-05-29 08:04:33.757 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:04:34.736 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:21:29.404 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:44, serverValue:96416}] to ************:27017
2025-05-29 08:34:13.273 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:45, serverValue:96425}] to ************:27017
2025-05-29 08:35:14.688 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server ************:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:70)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:127)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:131)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.net.SocketException: Malformed reply from SOCKS server
	at java.net.SocksSocketImpl.readSocksReply(SocksSocketImpl.java:129)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:502)
	at java.net.Socket.connect(Socket.java:607)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:63)
	at com.mongodb.internal.connection.SocketStream.initializeSocket(SocketStream.java:79)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:65)
	... 3 common frames omitted
2025-05-29 08:35:24.775 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:47, serverValue:96434}] to ************:27017
2025-05-29 08:35:24.805 [cluster-ClusterId{value='6835094fa9e7425e6cb42fd5', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=29443500}
2025-05-29 08:36:02.467 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:36:12.493 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 08:36:12.494 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:36:44.787 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:39:12.516 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:39:22.544 [scheduling-29] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 08:39:22.546 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:41:44.811 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:42:22.546 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:42:32.568 [scheduling-7] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 08:42:32.569 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:45:32.571 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:45:42.594 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 08:45:42.595 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:46:44.818 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:48:42.597 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:48:52.620 [scheduling-13] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 08:48:52.622 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:51:44.822 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:51:52.626 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:52:02.660 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 08:52:02.661 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:55:02.678 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:55:12.702 [scheduling-26] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 08:55:12.704 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 08:56:44.841 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 08:58:12.704 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 08:58:22.729 [scheduling-27] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 08:58:22.730 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:01:22.731 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:01:32.754 [scheduling-16] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:01:32.755 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:01:44.848 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:04:32.757 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:04:42.781 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:04:42.784 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:06:44.854 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:07:42.785 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:07:52.817 [scheduling-21] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:07:52.818 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:10:52.767 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:11:02.797 [scheduling-8] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:11:02.799 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:11:44.807 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:14:02.802 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:14:12.829 [scheduling-21] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:14:12.830 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:16:44.808 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:17:12.831 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:17:22.859 [scheduling-19] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:17:22.861 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:20:22.863 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:20:32.892 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:20:32.895 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:21:44.813 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:23:32.894 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:23:42.921 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:23:42.922 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:26:42.922 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:26:44.820 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:26:52.946 [scheduling-25] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:26:52.948 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:29:52.946 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:30:02.971 [scheduling-20] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:30:02.972 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:31:44.820 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:33:02.972 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:33:12.997 [scheduling-14] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:33:12.997 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:36:12.997 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:36:23.022 [scheduling-26] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:36:23.024 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:36:44.821 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:39:23.066 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:39:33.088 [scheduling-14] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:39:33.091 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:41:44.870 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:42:33.093 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:42:43.118 [scheduling-19] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:42:43.121 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:45:43.125 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:45:53.147 [scheduling-5] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:45:53.150 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:46:44.877 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:48:53.151 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:49:03.176 [scheduling-3] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:49:03.177 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:51:44.883 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:52:03.177 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:52:13.201 [scheduling-28] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:52:13.203 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:55:13.230 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:55:23.249 [scheduling-2] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:55:23.251 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 09:56:44.916 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 09:58:23.253 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 09:58:33.282 [scheduling-12] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 09:58:33.283 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:01:33.284 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:01:43.304 [scheduling-23] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:01:43.306 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:01:44.924 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:04:43.308 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:04:53.339 [scheduling-18] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:04:53.340 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:06:44.931 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:07:53.341 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:08:03.366 [scheduling-25] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:08:03.367 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:11:03.393 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:11:13.407 [scheduling-4] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:11:13.408 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:11:44.960 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:14:13.417 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:14:23.442 [scheduling-2] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:14:23.444 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:16:44.974 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:17:23.446 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:17:33.468 [scheduling-14] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:17:33.469 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:20:33.478 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:20:43.498 [scheduling-30] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:20:43.500 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:21:44.993 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:23:43.503 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:23:53.527 [scheduling-27] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:23:53.528 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:26:45.003 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:26:53.529 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:27:03.559 [scheduling-23] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:27:03.564 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:30:03.568 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:30:13.592 [scheduling-5] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:30:13.593 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:31:45.008 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:33:13.598 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:33:23.619 [scheduling-25] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:33:23.620 [scheduling-25] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:36:23.630 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:36:33.654 [scheduling-24] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:36:33.655 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:36:45.021 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:39:33.658 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:39:43.688 [scheduling-18] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:39:43.690 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:41:45.026 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:42:43.692 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:42:53.732 [scheduling-19] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:42:53.734 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:45:53.736 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:46:03.760 [scheduling-27] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:46:03.761 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:46:45.032 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:49:03.704 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:49:13.727 [scheduling-3] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:49:13.730 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:51:44.980 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:52:13.732 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:52:23.755 [scheduling-23] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:52:23.757 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:55:23.760 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:55:33.787 [scheduling-30] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:55:33.789 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 10:56:44.982 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 10:58:33.789 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 10:58:43.819 [scheduling-2] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 10:58:43.821 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:01:43.822 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:01:44.987 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:01:53.853 [scheduling-23] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:01:53.853 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:04:53.836 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:05:03.866 [scheduling-10] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:05:03.869 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:06:44.973 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:08:03.871 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:08:13.903 [scheduling-18] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:08:13.906 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:11:13.906 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:11:23.929 [scheduling-14] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:11:23.931 [scheduling-14] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:11:44.977 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:14:23.930 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:14:33.956 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:14:33.959 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:16:44.981 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:17:33.958 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:17:39.053 [scheduling-30] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9736 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:17:39.055 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:20:39.057 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:20:40.085 [scheduling-21] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9736 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:20:40.086 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:21:44.989 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:23:40.086 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:23:40.604 [scheduling-2] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9736 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:23:40.606 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:26:40.605 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:26:41.073 [scheduling-8] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9736 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:26:41.075 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:26:44.990 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:29:41.074 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:29:51.100 [scheduling-15] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:29:51.103 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:31:44.992 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:32:51.103 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:33:01.129 [scheduling-21] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:33:01.131 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:36:01.099 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:36:11.124 [scheduling-23] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:36:11.125 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:36:44.963 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:39:11.126 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:39:21.146 [scheduling-24] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:39:21.150 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:41:44.972 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:42:21.149 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:42:31.171 [scheduling-7] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:42:31.173 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:45:31.172 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:45:41.197 [scheduling-20] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:45:41.199 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:46:44.977 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:48:41.199 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:48:51.224 [scheduling-16] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:48:51.226 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:51:45.016 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:51:51.266 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:52:01.289 [scheduling-18] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:52:01.290 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:55:01.292 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:55:11.306 [scheduling-4] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:55:11.308 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 11:56:45.019 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 11:58:11.307 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 11:58:21.329 [scheduling-3] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 11:58:21.330 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:01:21.329 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:01:31.348 [scheduling-29] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:01:31.350 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:01:45.020 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:04:31.353 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:04:41.373 [scheduling-15] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:04:41.374 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:06:45.030 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:07:41.373 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:07:51.388 [scheduling-19] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:07:51.390 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:10:51.391 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:11:01.406 [scheduling-21] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:11:01.407 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:11:45.038 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:14:01.406 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:14:11.422 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:14:11.422 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:16:45.048 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:17:11.422 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:17:21.437 [scheduling-30] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:17:21.438 [scheduling-30] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:20:21.438 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:20:31.453 [scheduling-16] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:20:31.455 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:21:45.095 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:23:31.498 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:23:41.520 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:23:41.521 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:26:41.522 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:26:45.101 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:26:51.540 [scheduling-8] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:26:51.541 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:29:51.543 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:30:01.560 [scheduling-21] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:30:01.560 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:31:45.109 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:33:01.563 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:33:11.584 [scheduling-13] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:33:11.585 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:36:11.595 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:36:21.616 [scheduling-15] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:36:21.617 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:36:45.133 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:39:21.626 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:39:31.648 [scheduling-6] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:39:31.650 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:41:45.139 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:42:31.651 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:42:41.671 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:42:41.674 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:45:41.676 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:45:51.689 [scheduling-2] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:45:51.691 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:46:45.144 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:48:51.696 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:49:01.715 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:49:01.717 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:51:45.166 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:52:01.733 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:52:11.751 [scheduling-6] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:52:11.753 [scheduling-6] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:55:11.757 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:55:21.778 [scheduling-9] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:55:21.778 [scheduling-9] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 12:56:45.172 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 12:58:21.780 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 12:58:31.797 [scheduling-8] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 12:58:31.799 [scheduling-8] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:01:31.800 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:01:41.817 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:01:41.819 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:01:45.178 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:04:41.821 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:04:51.836 [scheduling-3] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:04:51.837 [scheduling-3] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:06:45.197 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:07:51.850 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:08:01.872 [scheduling-22] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:08:01.873 [scheduling-22] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:11:01.875 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:11:11.889 [scheduling-20] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:11:11.889 [scheduling-20] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:11:45.210 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:14:11.891 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:14:21.942 [scheduling-29] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:14:21.943 [scheduling-29] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:16:45.225 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:17:21.946 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:17:31.965 [scheduling-16] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:17:31.966 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:20:31.968 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:20:35.048 [scheduling-11] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9736 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:20:35.048 [scheduling-11] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:21:45.234 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:23:35.052 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:23:45.072 [scheduling-24] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:23:45.073 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:26:45.076 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:26:45.240 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:26:55.096 [scheduling-15] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:26:55.098 [scheduling-15] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:29:55.087 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:30:05.109 [scheduling-23] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:30:05.110 [scheduling-23] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:31:45.219 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:33:05.106 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:33:15.130 [scheduling-12] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
feign.FeignException$ServiceUnavailable: [503 Service Unavailable] during [GET] to [http://dlcg-oa/client/sys-dictionary/getDictionaryAllList] [SysDictionaryClient#getDictionaryAllList()]: [<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=... (9730 bytes)]
	at feign.FeignException.serverErrorStatus(FeignException.java:237)
	at feign.FeignException.errorStatus(FeignException.java:180)
	at feign.FeignException.errorStatus(FeignException.java:169)
	at feign.codec.ErrorDecoder$Default.decode(ErrorDecoder.java:92)
	at feign.AsyncResponseHandler.handleResponse(AsyncResponseHandler.java:96)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:138)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.GeneratedMethodAccessor267.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-05-29 13:33:15.132 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:35:59.222 [Thread-10] INFO  c.n.l.PollingServerListUpdater - Shutting down the Executor Pool for PollingServerListUpdater
2025-05-29 13:35:59.282 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748496959282, current=DOWN, previous=UP]
2025-05-29 13:35:59.286 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/192.168.1.28:dlcg-tms:8073: registering service...
2025-05-29 13:35:59.312 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/192.168.1.28:dlcg-tms:8073 - registration status: 204
2025-05-29 13:35:59.597 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook removed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-29 13:35:59.597 [SpringContextShutdownHook] INFO  c.n.u.c.ShutdownEnabledTimer - Exception caught (might be ok if at shutdown)
java.lang.IllegalStateException: Shutdown in progress
	at java.lang.ApplicationShutdownHooks.remove(ApplicationShutdownHooks.java:82)
	at java.lang.Runtime.removeShutdownHook(Runtime.java:231)
	at com.netflix.util.concurrent.ShutdownEnabledTimer.cancel(ShutdownEnabledTimer.java:70)
	at com.netflix.loadbalancer.BaseLoadBalancer.cancelPingTask(BaseLoadBalancer.java:632)
	at com.netflix.loadbalancer.BaseLoadBalancer.shutdown(BaseLoadBalancer.java:883)
	at com.netflix.loadbalancer.DynamicServerListLoadBalancer.shutdown(DynamicServerListLoadBalancer.java:285)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.context.support.AbstractApplicationContext.close(AbstractApplicationContext.java:979)
	at org.springframework.cloud.context.named.NamedContextFactory.destroy(NamedContextFactory.java:92)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-29 13:35:59.634 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-29 13:35:59.635 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-29 13:36:02.647 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-29 13:36:02.657 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-05-29 13:36:02.659 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-29 13:36:02.663 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-05-29 13:36:02.664 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-29 13:36:02.664 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/192.168.1.28:dlcg-tms:8073 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-29 13:36:02.682 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-29 13:36:05.222 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-29 13:36:07.164 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-29 13:36:09.314 [main] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.tms.mapper.*]' package. Please check your configuration.
2025-05-29 13:36:10.701 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8073"]
2025-05-29 13:36:10.702 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-29 13:36:10.702 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-29 13:36:10.961 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-29 13:36:11.294 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-29 13:36:14.539 [main] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-29 13:36:18.667 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-29 13:36:19.314 [cluster-ClusterId{value='6837f25262c6717a3cba4dc0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:96460}] to ************:27017
2025-05-29 13:36:19.348 [cluster-ClusterId{value='6837f25262c6717a3cba4dc0', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=31532750}
2025-05-29 13:36:23.412 [taskExecutor-1] INFO  c.d.t.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-29 13:36:23.436 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-29 13:36:23.436 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-29 13:36:23.452 [main] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-29 13:36:23.453 [main] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-29 13:36:24.823 [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-29 13:36:24.869 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-29 13:36:24.869 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-29 13:36:25.041 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-29 13:36:25.041 [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-29 13:36:25.294 [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:36:25.317 [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-29 13:36:25.317 [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-29 13:36:25.318 [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-29 13:36:25.318 [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-29 13:36:25.318 [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-29 13:36:25.318 [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-29 13:36:25.318 [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-29 13:36:25.504 [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-29 13:36:25.505 [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-29 13:36:25.508 [main] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-29 13:36:25.511 [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1748496985510 with initial instances count: 0
2025-05-29 13:36:25.516 [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1748496985516, current=UP, previous=STARTING]
2025-05-29 13:36:25.518 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/*************:dlcg-tms:8073: registering service...
2025-05-29 13:36:25.519 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8073"]
2025-05-29 13:36:25.574 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-TMS/*************:dlcg-tms:8073 - registration status: 204
2025-05-29 13:36:25.589 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:36:25.592 [main] INFO  c.dlcg.tms.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 20.617 seconds (JVM running for 21.141)
2025-05-29 13:36:25.702 [scheduling-1] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-29 13:36:25.716 [scheduling-1] INFO  c.n.u.c.ShutdownEnabledTimer - Shutdown hook installed for: NFLoadBalancer-PingTimer-dlcg-oa
2025-05-29 13:36:25.717 [scheduling-1] INFO  c.n.loadbalancer.BaseLoadBalancer - Client: dlcg-oa instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-05-29 13:36:25.721 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - Using serverListUpdater PollingServerListUpdater
2025-05-29 13:36:25.724 [scheduling-1] INFO  c.n.l.DynamicServerListLoadBalancer - DynamicServerListLoadBalancer for client dlcg-oa initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=dlcg-oa,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:org.springframework.cloud.netflix.ribbon.eureka.DomainExtractingServerList@510d8def
2025-05-29 13:36:25.774 [scheduling-1] ERROR c.d.oa.service.SysDictionaryService - 查询字典数据异常
java.lang.RuntimeException: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:90)
	at feign.SynchronousMethodHandler.executeAndDecode(SynchronousMethodHandler.java:119)
	at feign.SynchronousMethodHandler.invoke(SynchronousMethodHandler.java:89)
	at feign.ReflectiveFeign$FeignInvocationHandler.invoke(ReflectiveFeign.java:100)
	at com.sun.proxy.$Proxy150.getDictionaryAllList(Unknown Source)
	at com.dlcg.oa.service.SysDictionaryService.refCache(SysDictionaryService.java:25)
	at com.dlcg.tms.scheduled.SysDictionaryCacheScheduled.syncData(SysDictionaryCacheScheduled.java:23)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.runAndReset(FutureTask.java:308)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$301(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:294)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: com.netflix.client.ClientException: Load balancer does not have available server for client: dlcg-oa
	at com.netflix.loadbalancer.LoadBalancerContext.getServerFromLoadBalancer(LoadBalancerContext.java:483)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:184)
	at com.netflix.loadbalancer.reactive.LoadBalancerCommand$1.call(LoadBalancerCommand.java:180)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:94)
	at rx.internal.operators.OnSubscribeConcatMap.call(OnSubscribeConcatMap.java:42)
	at rx.Observable.unsafeSubscribe(Observable.java:10327)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber$1.call(OperatorRetryWithPredicate.java:127)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.enqueue(TrampolineScheduler.java:73)
	at rx.internal.schedulers.TrampolineScheduler$InnerCurrentThreadScheduler.schedule(TrampolineScheduler.java:52)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:79)
	at rx.internal.operators.OperatorRetryWithPredicate$SourceSubscriber.onNext(OperatorRetryWithPredicate.java:45)
	at rx.internal.util.ScalarSynchronousObservable$WeakSingleProducer.request(ScalarSynchronousObservable.java:276)
	at rx.Subscriber.setProducer(Subscriber.java:209)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:138)
	at rx.internal.util.ScalarSynchronousObservable$JustOnSubscribe.call(ScalarSynchronousObservable.java:129)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:48)
	at rx.internal.operators.OnSubscribeLift.call(OnSubscribeLift.java:30)
	at rx.Observable.subscribe(Observable.java:10423)
	at rx.Observable.subscribe(Observable.java:10390)
	at rx.observables.BlockingObservable.blockForSingle(BlockingObservable.java:443)
	at rx.observables.BlockingObservable.single(BlockingObservable.java:340)
	at com.netflix.client.AbstractLoadBalancerAwareClient.executeWithLoadBalancer(AbstractLoadBalancerAwareClient.java:112)
	at org.springframework.cloud.openfeign.ribbon.LoadBalancerFeignClient.execute(LoadBalancerFeignClient.java:83)
	... 19 common frames omitted
2025-05-29 13:36:25.775 [scheduling-1] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:36:55.517 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-29 13:36:55.524 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-29 13:36:55.524 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-29 13:36:55.524 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-29 13:36:55.524 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-29 13:36:55.525 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: false
2025-05-29 13:36:55.525 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-29 13:36:55.559 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-29 13:36:56.912 [PollingServerListUpdater-0] INFO  c.n.config.ChainedDynamicProperty - Flipping property: dlcg-oa.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-05-29 13:39:25.781 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:39:26.272 [scheduling-26] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:41:25.326 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:42:26.276 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:42:26.664 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:45:26.671 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:45:27.224 [scheduling-24] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:46:25.342 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:48:27.227 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:48:27.597 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:51:25.349 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:51:27.599 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:51:28.249 [scheduling-27] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:54:28.251 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:54:28.731 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 13:56:25.358 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 13:57:28.732 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 13:57:29.419 [scheduling-18] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:00:29.422 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:00:29.880 [scheduling-5] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:01:25.370 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:03:29.887 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:03:30.561 [scheduling-21] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:06:25.379 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:06:30.196 [http-nio-8073-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-29 14:06:30.564 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:06:31.067 [scheduling-28] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:09:31.069 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:09:31.468 [scheduling-2] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:11:25.387 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:12:31.470 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:12:32.115 [scheduling-19] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:15:32.118 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:15:32.625 [scheduling-13] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:16:25.397 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:18:32.627 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:18:33.197 [scheduling-10] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:21:25.405 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:21:33.206 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:21:33.787 [scheduling-7] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:24:33.789 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:24:34.296 [scheduling-12] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:26:25.412 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-29 14:27:34.298 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:27:34.997 [scheduling-4] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:30:35.000 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-29 14:30:35.479 [scheduling-16] INFO  c.d.t.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-29 14:31:25.420 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
