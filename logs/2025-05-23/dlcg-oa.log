2025-05-23 00:00:50.163 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:02:59.524 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:02:59.917 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:05:50.173 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:05:59.920 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:06:00.270 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:09:00.275 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:09:00.657 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:10:50.176 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:12:00.666 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:12:01.051 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:15:01.067 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:15:01.490 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:15:50.209 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:18:01.513 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:18:01.932 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:20:50.214 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:21:01.933 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:21:02.365 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:24:02.368 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:24:02.748 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:25:50.226 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:27:02.758 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:27:03.159 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:30:03.168 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:30:03.526 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:30:50.212 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:33:03.512 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:33:05.736 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:35:50.219 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:36:05.738 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:36:06.160 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:39:06.164 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:39:06.537 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:40:50.229 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:42:06.537 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:42:06.908 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:45:06.912 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:45:07.316 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:45:50.223 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:48:07.314 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:48:07.696 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:50:50.230 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:51:07.701 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:51:08.039 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:54:08.048 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:54:08.399 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 00:55:50.230 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 00:57:08.405 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 00:57:08.779 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:00:08.784 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:00:09.149 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:00:50.149 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:01:49.898 [scheduling-27] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
### The error may exist in com/dlcg/oa/mapper/ProcessInstanceMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: DELETE FROM process_instance WHERE id IN ( )
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:303)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:68)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy140.deleteBatchIds(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.removeByIds(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.ProcessInstanceServiceImpl$$EnhancerBySpringCGLIB$$bc7bcb07.removeByIds(<generated>)
	at com.dlcg.oa.scheduled.DelRefuseProcessScheduled.delRefuseProcess(DelRefuseProcessScheduled.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy225.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy257.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 25 common frames omitted
2025-05-23 01:01:49.898 [scheduling-27] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
### The error may exist in com/dlcg/oa/mapper/ProcessInstanceMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: DELETE FROM process_instance WHERE id IN ( )
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:303)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:68)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy140.deleteBatchIds(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.removeByIds(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.ProcessInstanceServiceImpl$$EnhancerBySpringCGLIB$$bc7bcb07.removeByIds(<generated>)
	at com.dlcg.oa.scheduled.DelRefuseProcessScheduled.delRefuseProcess(DelRefuseProcessScheduled.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy225.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy257.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 25 common frames omitted
2025-05-23 01:03:09.064 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:03:09.409 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:05:50.147 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:06:09.413 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:06:09.769 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:09:09.766 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:09:10.116 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:10:50.152 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:12:10.119 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:12:10.470 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:15:10.469 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:15:10.806 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:15:50.157 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:18:10.810 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:18:11.167 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:20:50.155 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:21:11.168 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:21:11.549 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:24:11.555 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:24:11.925 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:25:50.158 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:27:11.931 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:27:12.284 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:30:12.282 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:30:12.628 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:30:50.160 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:33:12.712 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:33:13.112 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:35:50.242 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:36:13.119 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:36:13.472 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:39:13.476 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:39:13.864 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:40:50.250 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:42:13.873 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:42:14.941 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:45:14.949 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:45:15.338 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:45:50.258 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:48:15.392 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:48:15.779 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:50:50.304 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:51:15.787 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:51:16.179 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:54:16.180 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:54:16.583 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 01:55:50.312 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 01:57:16.593 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 01:57:16.962 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:00:16.972 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:00:17.361 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:00:50.321 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:03:17.341 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:03:17.751 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:05:50.300 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:06:17.757 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:06:18.121 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:09:18.130 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:09:18.490 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:10:50.307 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:12:18.498 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:12:18.881 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:15:18.891 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:15:19.274 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:15:50.317 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:18:19.185 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:18:19.568 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:20:50.223 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:21:19.570 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:21:19.959 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:24:19.965 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:24:20.330 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:25:50.227 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:27:20.335 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:27:20.745 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:30:20.746 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:30:21.136 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:30:50.230 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:33:21.175 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:33:21.572 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:35:50.273 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:36:21.579 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:36:21.963 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:39:21.970 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:39:22.342 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:40:50.277 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:42:22.349 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:42:22.736 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:45:22.744 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:45:23.120 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:45:50.279 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:48:23.157 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:48:23.517 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:50:50.317 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:51:23.520 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:51:24.025 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:54:24.032 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:54:24.430 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 02:55:50.319 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 02:57:24.438 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 02:57:24.807 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:00:24.810 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:00:25.230 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:00:50.326 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:03:25.180 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:03:25.549 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:05:50.275 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:06:25.554 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:06:25.952 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:09:25.958 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:09:26.329 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:10:50.272 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:12:26.336 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:12:26.741 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:15:26.748 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:15:27.112 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:15:50.275 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:18:27.118 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:18:27.485 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:20:50.272 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:21:27.486 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:21:27.844 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:24:27.846 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:24:28.213 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:25:50.279 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:27:28.213 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:27:28.587 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:30:28.594 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:30:28.965 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:30:50.278 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:33:28.972 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:33:29.341 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:35:50.383 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:36:29.446 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:36:29.803 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:39:29.806 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:39:30.168 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:40:50.385 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:42:30.169 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:42:31.081 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:45:31.084 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:45:31.433 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:45:50.390 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:48:31.443 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:48:31.795 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:50:50.400 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:51:31.802 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:51:32.316 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:54:32.322 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:54:32.657 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 03:55:50.407 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 03:57:32.658 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 03:57:32.994 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:00:33.000 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:00:33.332 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:00:50.414 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:03:33.340 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:03:33.681 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:05:50.421 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:06:33.756 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:06:34.111 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:09:34.124 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:09:34.652 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:10:50.506 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:12:34.663 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:12:35.021 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:15:35.028 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:15:35.383 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:15:50.514 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:18:35.393 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:18:35.735 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:20:50.526 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:21:35.638 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:21:35.987 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:24:35.996 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:24:36.351 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:25:50.428 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:27:36.353 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:27:36.685 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:30:36.685 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:30:37.039 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:30:50.434 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:33:37.038 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:33:37.378 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:35:50.438 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:36:37.305 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:36:37.674 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:39:37.674 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:39:38.062 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:40:50.362 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:42:38.064 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:42:38.417 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:45:38.421 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:45:38.760 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:45:50.365 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:48:38.761 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:48:39.126 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:50:50.366 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:51:39.128 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:51:39.482 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:54:39.555 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:54:39.917 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 04:55:50.439 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 04:57:39.916 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 04:57:40.260 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:00:40.261 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:00:40.614 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:00:50.448 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:03:40.615 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:03:40.962 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:05:50.452 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:06:40.970 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:06:41.346 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:09:41.354 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:09:41.721 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:10:50.457 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:12:41.722 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:12:42.076 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:15:42.085 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:15:42.458 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:15:50.463 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:18:42.464 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:18:42.853 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:20:50.466 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:21:42.855 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:21:43.233 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:24:43.330 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:24:43.701 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:25:50.569 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:27:43.712 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:27:44.073 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:30:44.082 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:30:44.446 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:30:50.574 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:33:44.457 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:33:44.825 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:35:50.578 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:36:44.833 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:36:45.184 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:39:45.115 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:39:45.477 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:40:50.511 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:42:45.481 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:42:46.454 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:45:46.462 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:45:46.814 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:45:50.517 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:48:46.822 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:48:47.164 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:50:50.525 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:51:47.168 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:51:47.519 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:54:47.523 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:54:47.854 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 05:55:50.604 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 05:57:47.930 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 05:57:48.299 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:00:48.306 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:00:48.636 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:00:50.613 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:03:48.639 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:03:49.005 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:05:50.618 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:06:49.016 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:06:49.375 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:09:49.379 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:09:49.725 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:10:50.629 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:12:49.731 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:12:50.148 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:15:50.158 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:15:50.542 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:15:50.638 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:18:50.546 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:18:50.941 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:20:50.649 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:21:50.942 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:21:51.344 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:24:51.353 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:24:51.717 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:25:50.661 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:27:51.727 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:27:52.083 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:30:50.672 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:30:52.096 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:30:52.447 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:33:52.453 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:33:52.795 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:35:50.680 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:36:52.803 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:36:53.162 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:39:53.170 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:39:53.517 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:40:50.684 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:42:53.522 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:42:53.902 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:45:50.696 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:45:53.904 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:45:54.263 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:48:54.274 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:48:54.653 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:50:50.711 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:51:54.657 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:51:55.009 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:54:55.019 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:54:55.397 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 06:55:50.717 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 06:57:55.287 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 06:57:55.635 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:00:50.606 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:00:55.637 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:00:56.012 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:03:56.016 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:03:56.384 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:05:50.606 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:06:56.388 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:06:56.744 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:09:56.751 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:09:57.112 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:10:50.613 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:12:57.113 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:12:57.541 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:15:50.617 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:15:57.541 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:15:57.934 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:18:57.942 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:18:58.339 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:20:50.624 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:21:58.339 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:21:58.693 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:24:58.694 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:24:59.115 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:25:50.624 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:27:59.248 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:27:59.617 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:30:50.760 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:30:59.626 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:31:00.026 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:34:00.033 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:34:00.391 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:35:50.774 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:37:00.399 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:37:00.799 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:40:00.804 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:40:01.179 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:40:50.786 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:43:01.102 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:43:02.052 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:45:50.717 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:46:02.059 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:46:02.413 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:49:02.418 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:49:02.770 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:50:50.724 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:52:02.778 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:52:03.161 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:55:03.171 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:55:03.524 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 07:55:50.728 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 07:58:03.539 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 07:58:03.910 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:00:50.749 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:01:03.927 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:01:04.282 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:04:04.291 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:04:04.653 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:05:50.752 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:07:04.661 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:07:05.037 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:10:05.039 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:10:05.389 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:10:50.755 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:13:05.394 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:13:05.778 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:15:50.782 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:16:05.800 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:16:06.177 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:19:06.179 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:19:06.568 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:20:50.793 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:22:06.578 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:22:06.949 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:25:06.954 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:25:07.330 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:25:50.803 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:28:07.337 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:28:07.707 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:30:50.884 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:31:07.786 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:31:08.140 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:34:08.142 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:34:08.515 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:35:50.907 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:37:08.537 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:37:08.927 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:40:08.934 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:40:09.299 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:40:50.912 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:43:09.307 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:43:09.698 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:45:50.920 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:46:09.705 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:46:10.149 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:49:10.156 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:49:10.946 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:50:50.902 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:52:10.924 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:52:11.401 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:55:11.406 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:55:11.973 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 08:55:50.909 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 08:58:11.975 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 08:58:12.394 [scheduling-28] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:00:50.916 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:01:12.398 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:01:13.038 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:04:13.043 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:04:13.524 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:04:22.948 [SpringContextShutdownHook] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747962262947, current=DOWN, previous=UP]
2025-05-23 09:04:22.954 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072: registering service...
2025-05-23 09:04:22.980 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - registration status: 204
2025-05-23 09:04:23.128 [SpringContextShutdownHook] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-23 09:04:23.261 [SpringContextShutdownHook] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:2, serverValue:95007}] to ************:27017 because the pool has been closed.
2025-05-23 09:04:23.262 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-23 09:04:26.267 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-23 09:04:26.271 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:89)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 42 common frames omitted
2025-05-23 09:04:26.271 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-23 09:04:26.272 [SpringContextShutdownHook] ERROR c.n.d.s.t.d.RedirectingEurekaHttpClient - Request execution error. endpoint=DefaultEndpoint{ serviceUrl='***********************************************/eureka/}
com.sun.jersey.api.client.ClientHandlerException: java.net.ConnectException: Connection refused (Connection refused)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:187)
	at com.sun.jersey.api.client.filter.GZIPContentEncodingFilter.handle(GZIPContentEncodingFilter.java:123)
	at com.netflix.discovery.EurekaIdentityHeaderFilter.handle(EurekaIdentityHeaderFilter.java:27)
	at com.sun.jersey.api.client.Client.handle(Client.java:652)
	at com.sun.jersey.api.client.WebResource.handle(WebResource.java:682)
	at com.sun.jersey.api.client.WebResource.access$200(WebResource.java:74)
	at com.sun.jersey.api.client.WebResource$Builder.delete(WebResource.java:591)
	at com.netflix.discovery.shared.transport.jersey.AbstractJerseyEurekaHttpClient.cancel(AbstractJerseyEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.MetricsCollectingEurekaHttpClient.execute(MetricsCollectingEurekaHttpClient.java:73)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.executeOnNewServer(RedirectingEurekaHttpClient.java:118)
	at com.netflix.discovery.shared.transport.decorator.RedirectingEurekaHttpClient.execute(RedirectingEurekaHttpClient.java:79)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:120)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
Caused by: java.net.ConnectException: Connection refused (Connection refused)
	at java.net.PlainSocketImpl.socketConnect(Native Method)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:607)
	at org.apache.http.conn.scheme.PlainSocketFactory.connectSocket(PlainSocketFactory.java:121)
	at org.apache.http.impl.conn.DefaultClientConnectionOperator.openConnection(DefaultClientConnectionOperator.java:180)
	at org.apache.http.impl.conn.AbstractPoolEntry.open(AbstractPoolEntry.java:144)
	at org.apache.http.impl.conn.AbstractPooledConnAdapter.open(AbstractPooledConnAdapter.java:134)
	at org.apache.http.impl.client.DefaultRequestDirector.tryConnect(DefaultRequestDirector.java:605)
	at org.apache.http.impl.client.DefaultRequestDirector.execute(DefaultRequestDirector.java:440)
	at org.apache.http.impl.client.AbstractHttpClient.doExecute(AbstractHttpClient.java:835)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:118)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at com.sun.jersey.client.apache4.ApacheHttpClient4Handler.handle(ApacheHttpClient4Handler.java:173)
	... 43 common frames omitted
2025-05-23 09:04:26.272 [SpringContextShutdownHook] WARN  c.n.d.s.t.d.RetryableEurekaHttpClient - Request execution failed with message: java.net.ConnectException: Connection refused (Connection refused)
2025-05-23 09:04:26.272 [SpringContextShutdownHook] ERROR c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - de-registration failedCannot execute request on any known server
com.netflix.discovery.shared.transport.TransportException: Cannot execute request on any known server
	at com.netflix.discovery.shared.transport.decorator.RetryableEurekaHttpClient.execute(RetryableEurekaHttpClient.java:112)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator$2.execute(EurekaHttpClientDecorator.java:74)
	at com.netflix.discovery.shared.transport.decorator.SessionedEurekaHttpClient.execute(SessionedEurekaHttpClient.java:77)
	at com.netflix.discovery.shared.transport.decorator.EurekaHttpClientDecorator.cancel(EurekaHttpClientDecorator.java:71)
	at com.netflix.discovery.DiscoveryClient.unregister(DiscoveryClient.java:969)
	at com.netflix.discovery.DiscoveryClient.shutdown(DiscoveryClient.java:945)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleElement.invoke(InitDestroyAnnotationBeanPostProcessor.java:389)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeDestroyMethods(InitDestroyAnnotationBeanPostProcessor.java:347)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeDestruction(InitDestroyAnnotationBeanPostProcessor.java:177)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:242)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.run(DisposableBeanAdapter.java:235)
	at org.springframework.cloud.context.scope.GenericScope$BeanLifecycleWrapper.destroy(GenericScope.java:403)
	at org.springframework.cloud.context.scope.GenericScope.destroy(GenericScope.java:142)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:258)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:587)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:559)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:520)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.doClose(ServletWebServerApplicationContext.java:170)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-05-23 09:04:26.278 [SpringContextShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-23 09:04:27.983 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.5.Final
2025-05-23 09:04:30.334 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-23 09:04:31.796 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.oa.mapper.*]' package. Please check your configuration.
2025-05-23 09:04:33.616 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8072"]
2025-05-23 09:04:33.616 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-23 09:04:33.617 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-23 09:04:34.145 [restartedMain] INFO  o.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-05-23 09:04:34.148 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-23 09:04:34.455 [restartedMain] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-23 09:04:34.652 [cluster-ClusterId{value='682fc9a2a5cc8b4f5e996fee', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:95066}] to ************:27017
2025-05-23 09:04:34.686 [cluster-ClusterId{value='682fc9a2a5cc8b4f5e996fee', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=32832458}
2025-05-23 09:04:35.891 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-23 09:04:41.745 [restartedMain] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-23 09:04:46.593 [restartedMain] INFO  c.d.o.c.AliyunOSSAutoConfiguration - load aliyun-oss..
2025-05-23 09:04:46.812 [scheduling-1] INFO  c.d.o.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-23 09:04:47.192 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-23 09:04:47.192 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-23 09:04:47.199 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-23 09:04:47.199 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-23 09:04:48.973 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-23 09:04:49.059 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-23 09:04:49.059 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-23 09:04:49.407 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-23 09:04:49.407 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-23 09:04:49.843 [restartedMain] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:04:49.858 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-23 09:04:49.858 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-23 09:04:49.858 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-23 09:04:49.858 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-23 09:04:49.858 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-23 09:04:49.858 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-23 09:04:49.858 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-23 09:04:50.187 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-23 09:04:50.190 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-23 09:04:50.193 [restartedMain] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-23 09:04:50.197 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1747962290195 with initial instances count: 0
2025-05-23 09:04:50.206 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747962290206, current=UP, previous=STARTING]
2025-05-23 09:04:50.208 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072: registering service...
2025-05-23 09:04:50.212 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8072"]
2025-05-23 09:04:50.265 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - registration status: 204
2025-05-23 09:04:50.346 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:04:50.349 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 22.619 seconds (JVM running for 23.24)
2025-05-23 09:04:50.847 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:05:20.189 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-23 09:05:20.190 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-23 09:05:20.191 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-23 09:05:20.191 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-23 09:05:20.191 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-23 09:05:20.191 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: false
2025-05-23 09:05:20.191 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-23 09:05:20.237 [DiscoveryClient-CacheRefreshExecutor-0] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-23 09:05:48.759 [http-nio-8072-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 09:07:50.842 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:07:51.290 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:09:49.875 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:10:51.297 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:10:51.826 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:13:51.829 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:13:52.334 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:14:49.885 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:16:52.337 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:16:52.729 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:19:49.948 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:19:52.735 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:19:53.134 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:22:53.132 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:22:53.541 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:24:49.945 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:25:53.548 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:25:53.938 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:28:53.944 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:28:54.341 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:29:49.952 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:31:54.347 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:31:54.723 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:34:49.959 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:34:54.730 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:34:55.110 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:37:55.038 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:37:55.457 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:39:49.891 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:40:55.461 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:40:55.898 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:43:55.901 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:43:56.268 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:44:49.896 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:46:56.272 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:46:56.654 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:49:49.900 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:49:56.658 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:49:57.095 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:52:57.088 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:52:57.459 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:54:49.896 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 09:55:57.462 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:55:57.847 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:56:39.292 [http-nio-8072-exec-1] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 09:56:39.571 [http-nio-8072-exec-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:95081}] to ************:27017
2025-05-23 09:58:57.851 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 09:58:58.287 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 09:59:49.899 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:10:49.732 [cluster-ClusterId{value='682fc9a2a5cc8b4f5e996fee', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:95089}] to ************:27017
2025-05-23 10:12:24.153 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:12:24.538 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:15:15.766 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:15:24.672 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:15:25.093 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:18:25.101 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:18:25.851 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:20:15.912 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:21:25.857 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:21:26.236 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:24:26.239 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:24:26.820 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:25:15.920 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:27:26.839 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:27:27.661 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:30:15.930 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:30:27.681 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:30:28.243 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:33:28.271 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:33:28.726 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:35:15.983 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:36:28.732 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:36:29.137 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:39:29.145 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:39:29.529 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:40:14.304 [http-nio-8072-exec-3] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 10:40:14.315 [http-nio-8072-exec-3] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:2, serverValue:95081}] to ************:27017 because there was a socket exception raised on another connection from this pool.
2025-05-23 10:40:14.530 [http-nio-8072-exec-3] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:95097}] to ************:27017
2025-05-23 10:40:15.994 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:42:29.535 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:42:30.009 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:45:16.002 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:45:30.014 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:45:30.479 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:48:30.484 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:48:31.220 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:50:16.013 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:51:31.228 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:51:31.608 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:54:31.613 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:54:32.000 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 10:55:10.580 [http-nio-8072-exec-6] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 10:55:16.024 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 10:57:32.006 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 10:57:32.431 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:00:16.034 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 11:00:32.428 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:00:32.827 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:03:32.819 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:03:33.238 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:05:16.021 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 11:05:28.398 [http-nio-8072-exec-10] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 11:06:33.245 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:06:33.661 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:09:33.669 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:09:34.127 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:10:16.029 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 11:10:26.122 [scheduling-28] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy258.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 22 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-23 11:10:26.122 [scheduling-28] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy258.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 22 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-23 11:10:26.234 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-23 11:10:26.301 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-23 11:12:34.135 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:12:34.609 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:15:16.038 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 11:15:34.616 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:15:35.056 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:22:12.721 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:22:13.225 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:23:53.712 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 11:25:13.235 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:25:13.639 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:28:13.644 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 11:28:14.071 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 11:28:53.723 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 12:02:59.957 [cluster-ClusterId{value='682fc9a2a5cc8b4f5e996fee', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:95107}] to ************:27017
2025-05-23 12:35:25.710 [cluster-ClusterId{value='682fc9a2a5cc8b4f5e996fee', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:95111}] to ************:27017
2025-05-23 12:36:02.248 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 12:36:02.649 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 12:53:38.498 [cluster-ClusterId{value='682fc9a2a5cc8b4f5e996fee', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:95115}] to ************:27017
2025-05-23 13:12:09.107 [cluster-ClusterId{value='682fc9a2a5cc8b4f5e996fee', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:95123}] to ************:27017
2025-05-23 13:24:44.349 [cluster-ClusterId{value='682fc9a2a5cc8b4f5e996fee', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:95126}] to ************:27017
2025-05-23 13:25:49.157 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 13:26:09.905 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:26:10.848 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:29:10.851 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:29:11.249 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:30:49.161 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 13:32:11.251 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:32:11.667 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:35:11.670 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:35:12.094 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:35:49.166 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 13:38:12.097 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:38:12.507 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:40:49.165 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 13:41:12.503 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:41:13.243 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:44:13.247 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:44:13.628 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:45:49.166 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 13:47:13.632 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:47:14.052 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:47:29.416 [http-nio-8072-exec-9] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:47:29.420 [http-nio-8072-exec-9] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:4, serverValue:95097}] to ************:27017 because there was a socket exception raised on another connection from this pool.
2025-05-23 13:47:29.654 [http-nio-8072-exec-9] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:10, serverValue:95137}] to ************:27017
2025-05-23 13:48:11.994 [http-nio-8072-exec-7] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:48:39.703 [http-nio-8072-exec-4] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:49:11.582 [http-nio-8072-exec-1] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:50:14.055 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:50:14.555 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:50:49.170 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 13:51:18.646 [http-nio-8072-exec-1] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:51:35.311 [http-nio-8072-exec-5] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:52:59.430 [http-nio-8072-exec-2] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:53:14.555 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:53:15.175 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:54:36.731 [http-nio-8072-exec-10] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:55:33.150 [scheduling-21] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy258.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 22 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-23 13:55:33.150 [scheduling-21] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.data.redis.RedisSystemException: Redis exception; nested exception is io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:74)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:273)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.convertLettuceAccessException(LettuceSetCommands.java:520)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:245)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.sMembers(DefaultedRedisConnection.java:791)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.sMembers(DefaultStringRedisConnection.java:1090)
	at org.springframework.data.redis.core.DefaultSetOperations.lambda$members$10(DefaultSetOperations.java:214)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultSetOperations.members(DefaultSetOperations.java:214)
	at com.dlcg.oa.scheduled.UpdateRedisMsgResCodeScheduled.updateData(UpdateRedisMsgResCodeScheduled.java:46)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: io.lettuce.core.RedisException: java.io.IOException: Connection reset by peer
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:134)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy258.smembers(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceSetCommands.sMembers(LettuceSetCommands.java:243)
	... 22 common frames omitted
Caused by: java.io.IOException: Connection reset by peer
	at sun.nio.ch.FileDispatcherImpl.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:39)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1133)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:714)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:576)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	... 1 common frames omitted
2025-05-23 13:55:33.218 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-23 13:55:33.268 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-23 13:55:49.188 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 13:56:02.663 [http-nio-8072-exec-7] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:56:15.204 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:56:15.632 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:59:08.765 [http-nio-8072-exec-2] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 13:59:15.636 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 13:59:16.461 [scheduling-23] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 13:59:30.314 [http-nio-8072-exec-7] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:00:49.196 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:02:16.461 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:02:16.982 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:05:16.987 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:05:17.402 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:05:49.200 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:08:17.406 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:08:17.771 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:10:49.210 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:11:17.785 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:11:18.186 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:14:18.188 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:14:18.577 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:15:49.217 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:17:18.582 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:17:18.961 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:20:18.966 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:20:19.424 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:20:49.223 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:21:38.403 [http-nio-8072-exec-9] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:23:19.425 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:23:20.164 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:25:00.685 [http-nio-8072-exec-8] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:25:02.998 [http-nio-8072-exec-1] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:25:05.483 [http-nio-8072-exec-10] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:25:45.069 [http-nio-8072-exec-5] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:25:49.242 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:26:20.188 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:26:20.600 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:26:56.614 [http-nio-8072-exec-3] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:29:20.602 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:29:20.997 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:30:04.926 [http-nio-8072-exec-9] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:30:49.255 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:32:04.777 [http-nio-8072-exec-6] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:32:21.001 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:32:21.407 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:34:26.145 [http-nio-8072-exec-6] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:35:21.411 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:35:21.830 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:35:47.176 [http-nio-8072-exec-5] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:35:49.261 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:37:14.918 [http-nio-8072-exec-8] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:38:04.660 [http-nio-8072-exec-4] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:38:18.328 [http-nio-8072-exec-7] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:38:21.836 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:38:22.207 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:38:36.287 [http-nio-8072-exec-6] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:40:49.267 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:41:22.203 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:41:22.821 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:42:04.203 [http-nio-8072-exec-2] INFO  com.dlcg.oa.base.BaseController - 数量0
2025-05-23 14:44:22.823 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:44:23.284 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:45:49.259 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:47:23.285 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:47:23.713 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:50:23.715 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:50:25.682 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:50:49.266 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:53:25.688 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:53:26.222 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:55:49.269 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 14:56:26.228 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:56:26.626 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 14:59:26.631 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 14:59:27.042 [scheduling-27] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:00:49.278 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:02:27.047 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:02:27.647 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:05:27.653 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:05:28.074 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:05:49.281 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:08:28.080 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:08:28.508 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:10:49.287 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:11:28.511 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:11:28.929 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:14:28.920 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:14:29.338 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:15:49.279 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:17:29.339 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:17:29.738 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:20:29.743 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:20:30.137 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:20:49.285 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:23:30.145 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:23:31.741 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:25:49.288 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:26:31.746 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:26:32.228 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:29:32.228 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:29:35.528 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:30:49.288 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:32:35.529 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:32:36.039 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:35:36.044 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:35:36.879 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:35:49.295 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:38:36.880 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:38:37.359 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:40:49.301 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:41:37.364 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:41:38.531 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:44:38.538 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:44:39.212 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:45:49.306 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:47:39.216 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:47:39.667 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:50:39.668 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:50:40.233 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:50:49.310 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:53:40.234 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:53:41.076 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:55:49.317 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:56:41.081 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:56:41.636 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:56:42.666 [Thread-7] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747987002666, current=DOWN, previous=UP]
2025-05-23 15:56:42.666 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072: registering service...
2025-05-23 15:56:42.673 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - registration status: 204
2025-05-23 15:56:42.808 [Thread-7] INFO  c.a.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-23 15:56:42.937 [Thread-7] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:10, serverValue:95137}] to ************:27017 because the pool has been closed.
2025-05-23 15:56:42.939 [Thread-7] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-23 15:56:45.945 [Thread-7] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-23 15:56:45.954 [Thread-7] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - deregister  status: 200
2025-05-23 15:56:45.970 [Thread-7] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-23 15:56:46.931 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-23 15:56:47.387 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.oa.mapper.*]' package. Please check your configuration.
2025-05-23 15:56:47.549 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8072"]
2025-05-23 15:56:47.549 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-23 15:56:47.549 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-23 15:56:47.619 [restartedMain] INFO  o.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-05-23 15:56:47.620 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-23 15:56:47.654 [restartedMain] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-23 15:56:47.751 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-23 15:56:47.778 [cluster-ClusterId{value='68302a3fa5cc8b4f5e996fef', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:11, serverValue:95152}] to ************:27017
2025-05-23 15:56:47.835 [cluster-ClusterId{value='68302a3fa5cc8b4f5e996fef', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=55411292}
2025-05-23 15:56:50.586 [restartedMain] INFO  c.a.druid.pool.DruidDataSource - {dataSource-2} inited
2025-05-23 15:56:51.202 [restartedMain] INFO  c.d.o.c.AliyunOSSAutoConfiguration - load aliyun-oss..
2025-05-23 15:56:51.256 [scheduling-1] INFO  c.d.o.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-23 15:56:51.302 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-23 15:56:51.302 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-23 15:56:51.440 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-23 15:56:51.440 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-23 15:56:51.440 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-23 15:56:51.440 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-23 15:56:51.440 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-23 15:56:51.462 [restartedMain] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 15:56:51.463 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-23 15:56:51.463 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-23 15:56:51.463 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-23 15:56:51.463 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-23 15:56:51.463 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-23 15:56:51.463 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-23 15:56:51.463 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-23 15:56:51.528 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-23 15:56:51.529 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-23 15:56:51.529 [restartedMain] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-23 15:56:51.529 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1747987011529 with initial instances count: 5
2025-05-23 15:56:51.530 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747987011530, current=UP, previous=STARTING]
2025-05-23 15:56:51.530 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072: registering service...
2025-05-23 15:56:51.531 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8072"]
2025-05-23 15:56:51.537 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:56:51.538 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 5.321 seconds (JVM running for 17185.411)
2025-05-23 15:56:51.538 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - registration status: 204
2025-05-23 15:56:51.952 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 15:56:57.068 [http-nio-8072-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 15:59:51.995 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 15:59:52.648 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:01:51.506 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:02:52.654 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:02:53.195 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:05:53.201 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:05:53.742 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:06:51.513 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:08:53.746 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:08:54.421 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:11:51.523 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:11:54.427 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:11:54.933 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:14:54.957 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:14:55.388 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:16:51.552 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:17:55.395 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:17:55.923 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:20:55.929 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:20:56.562 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:21:51.558 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:23:56.569 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:23:57.199 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:26:51.566 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:26:57.203 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:26:57.977 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:29:57.963 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:29:58.592 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:31:51.553 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:32:58.598 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:32:59.304 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:35:59.310 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:36:00.117 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:36:51.571 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:39:00.123 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:39:00.694 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:41:51.577 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:41:59.074 [Thread-13] WARN  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747989719074, current=DOWN, previous=UP]
2025-05-23 16:41:59.075 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072: registering service...
2025-05-23 16:41:59.082 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - registration status: 204
2025-05-23 16:41:59.231 [Thread-13] INFO  c.a.druid.pool.DruidDataSource - {dataSource-2} closed
2025-05-23 16:41:59.351 [Thread-13] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-05-23 16:42:02.363 [Thread-13] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-05-23 16:42:02.373 [Thread-13] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - deregister  status: 200
2025-05-23 16:42:02.381 [Thread-13] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-05-23 16:42:03.310 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - No active profile set, falling back to default profiles: default
2025-05-23 16:42:03.651 [restartedMain] WARN  o.m.s.mapper.ClassPathMapperScanner - No MyBatis mapper was found in '[com.dlcg.oa.mapper.*]' package. Please check your configuration.
2025-05-23 16:42:03.765 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8072"]
2025-05-23 16:42:03.765 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-23 16:42:03.765 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.38]
2025-05-23 16:42:03.849 [restartedMain] INFO  o.apache.jasper.servlet.TldScanner - At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.
2025-05-23 16:42:03.849 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-23 16:42:03.867 [restartedMain] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[************:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-05-23 16:42:03.950 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-23 16:42:03.956 [cluster-ClusterId{value='683034dba5cc8b4f5e996ff0', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:12, serverValue:95174}] to ************:27017
2025-05-23 16:42:03.988 [cluster-ClusterId{value='683034dba5cc8b4f5e996ff0', description='null'}-************:27017] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=************:27017, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=8, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=30, roundTripTimeNanos=30890125}
2025-05-23 16:42:06.973 [restartedMain] INFO  c.a.druid.pool.DruidDataSource - {dataSource-3} inited
2025-05-23 16:42:07.435 [restartedMain] INFO  c.d.o.c.AliyunOSSAutoConfiguration - load aliyun-oss..
2025-05-23 16:42:07.464 [scheduling-1] INFO  c.d.o.e.l.SystemStartEventLisener - 监听到系统启动事件！
2025-05-23 16:42:07.505 [restartedMain] WARN  c.n.c.s.URLConfigurationSource - No URLs will be polled as dynamic configuration sources.
2025-05-23 16:42:07.505 [restartedMain] INFO  c.n.c.s.URLConfigurationSource - To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-05-23 16:42:07.617 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-05-23 16:42:07.617 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-05-23 16:42:07.617 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-05-23 16:42:07.617 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-05-23 16:42:07.617 [restartedMain] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-05-23 16:42:07.635 [restartedMain] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:42:07.636 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-05-23 16:42:07.636 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-05-23 16:42:07.636 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-05-23 16:42:07.636 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-05-23 16:42:07.636 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-05-23 16:42:07.636 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-05-23 16:42:07.636 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-05-23 16:42:07.698 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-05-23 16:42:07.699 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-05-23 16:42:07.699 [restartedMain] INFO  c.n.d.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-05-23 16:42:07.699 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1747989727699 with initial instances count: 4
2025-05-23 16:42:07.701 [restartedMain] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1747989727701, current=UP, previous=STARTING]
2025-05-23 16:42:07.701 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072: registering service...
2025-05-23 16:42:07.701 [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8072"]
2025-05-23 16:42:07.705 [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_DLCG-OA/***********:dlcg-oa:8072 - registration status: 204
2025-05-23 16:42:07.706 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:42:07.706 [restartedMain] INFO  c.dlcg.oa.DlcgLogisticsApplication - Started DlcgLogisticsApplication in 4.985 seconds (JVM running for 19901.535)
2025-05-23 16:42:08.286 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:45:08.305 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:45:08.756 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:45:13.397 [http-nio-8072-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-23 16:47:07.659 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:48:08.763 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:48:09.147 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:51:09.154 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:51:10.012 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:52:07.668 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:54:10.016 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:54:10.444 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 16:57:07.677 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 16:57:10.448 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 16:57:10.904 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:00:10.917 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:00:11.305 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:02:07.693 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:03:11.311 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:03:11.714 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:06:11.720 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:06:12.469 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:07:07.699 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:09:12.473 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:09:12.892 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:12:07.706 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:12:12.902 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:12:13.312 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:15:13.298 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:15:13.701 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:17:07.689 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:18:13.704 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:18:14.121 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:21:14.131 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:21:14.536 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:22:07.701 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:24:14.545 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:24:14.964 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:27:07.707 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:27:14.975 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:27:15.376 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:30:15.342 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:30:15.739 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:32:07.672 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:33:15.742 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:33:16.126 [scheduling-7] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:36:16.129 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:36:16.497 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:37:07.676 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:39:16.501 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:39:16.874 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:42:07.681 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:42:16.875 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:42:17.254 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:45:17.260 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:45:17.681 [scheduling-4] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:47:07.687 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:48:17.686 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:48:18.095 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:51:18.100 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:51:18.463 [scheduling-13] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:52:07.690 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:54:18.489 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:54:18.863 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 17:57:07.723 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 17:57:18.871 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 17:57:19.239 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:00:19.250 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:00:19.617 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:02:07.729 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:03:19.627 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:03:20.011 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:06:20.021 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:06:20.397 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:07:07.735 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:09:20.420 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:09:20.795 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:12:07.764 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:12:20.805 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:12:21.189 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:15:21.195 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:15:21.549 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:17:07.776 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:18:21.562 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:18:21.914 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:21:21.923 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:21:22.318 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:22:07.788 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:24:22.340 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:24:22.692 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:27:07.807 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:27:22.705 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:27:23.094 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:30:23.099 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:30:23.473 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:32:07.815 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:33:23.485 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:33:23.866 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:36:23.876 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:36:24.247 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:37:07.819 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:39:24.250 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:39:24.633 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:42:07.828 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:42:24.640 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:42:25.392 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:45:25.399 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:45:25.751 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:47:07.836 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:48:25.761 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:48:26.109 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:51:26.116 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:51:26.466 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:52:07.841 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:54:26.482 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:54:26.820 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 18:57:07.853 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 18:57:26.828 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 18:57:27.167 [scheduling-1] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:00:27.173 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:00:27.507 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:02:07.865 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:03:27.517 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:03:27.869 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:06:27.874 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:06:28.216 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:07:07.874 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:09:28.229 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:09:28.590 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:12:07.881 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:12:28.597 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:12:28.955 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:15:28.961 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:15:29.316 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:17:07.894 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:18:29.322 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:18:29.674 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:21:29.681 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:21:30.033 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:22:07.902 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:24:30.042 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:24:30.404 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:27:07.908 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:27:30.412 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:27:30.748 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:30:30.753 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:30:31.088 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:32:07.918 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:33:31.099 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:33:31.424 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:36:31.436 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:36:31.779 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:37:07.931 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:39:31.787 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:39:32.122 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:42:07.943 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:42:32.129 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:42:32.488 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:45:32.499 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:45:32.882 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:47:07.950 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:48:32.891 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:48:33.250 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:51:33.258 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:51:33.642 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:52:07.963 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:54:33.652 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:54:34.031 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 19:57:07.962 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 19:57:34.038 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 19:57:34.398 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:00:34.402 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:00:34.755 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:02:07.975 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:03:34.766 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:03:35.116 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:06:35.123 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:06:35.476 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:07:07.981 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:09:35.486 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:09:35.834 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:12:07.988 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:12:35.842 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:12:36.211 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:15:36.222 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:15:36.573 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:17:07.994 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:18:36.583 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:18:36.938 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:21:36.942 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:21:37.286 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:22:08.004 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:24:37.289 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:24:37.638 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:27:07.983 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:27:37.618 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:27:37.983 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:30:37.992 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:30:38.351 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:32:07.987 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:33:38.360 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:33:38.747 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:36:38.749 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:36:39.114 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:37:07.997 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:39:39.125 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:39:39.507 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:42:08.000 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:42:39.490 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:42:40.226 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:45:40.233 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:45:40.604 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:47:07.983 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:48:40.611 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:48:40.954 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:51:40.960 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:51:41.315 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:52:07.988 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:54:41.320 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:54:41.689 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 20:57:07.993 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 20:57:41.694 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 20:57:42.040 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:00:42.044 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:00:42.406 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:02:08.000 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:03:42.414 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:03:42.788 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:06:42.793 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:06:43.414 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:07:08.001 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:09:43.423 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:09:43.763 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:12:08.009 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:12:43.773 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:12:45.579 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:15:45.584 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:15:45.948 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:17:08.009 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:18:45.954 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:18:46.313 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:21:46.315 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:21:46.687 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:22:08.014 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:24:46.688 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:24:47.049 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:27:08.024 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:27:47.050 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:27:47.391 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:30:47.416 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:30:47.763 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:32:08.053 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:33:47.769 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:33:48.134 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:36:48.135 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:36:48.492 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:37:08.062 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:39:48.496 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:39:48.865 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:42:08.069 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:42:48.873 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:42:49.229 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:45:49.247 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:45:49.600 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:47:08.088 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:48:49.605 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:48:49.963 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:51:49.973 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:51:50.341 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:52:08.092 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:54:50.343 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:54:50.698 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 21:57:08.102 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 21:57:50.709 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 21:57:51.053 [scheduling-29] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:00:51.034 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:00:51.385 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:02:08.087 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:03:51.385 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:03:51.737 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:06:51.746 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:06:52.095 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:07:08.092 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:09:52.104 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:09:52.612 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:12:08.099 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:12:52.618 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:12:52.951 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:15:52.926 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:15:53.275 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:17:08.053 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:18:53.260 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:18:53.606 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:21:53.615 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:21:54.032 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:22:08.056 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:24:54.031 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:24:54.367 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:27:08.060 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:27:54.370 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:27:54.715 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:30:54.724 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:30:55.066 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:32:08.044 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:33:55.039 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:33:55.387 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:36:55.387 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:36:55.731 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:37:08.051 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:39:55.739 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:39:56.084 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:42:08.049 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:42:56.088 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:42:56.800 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:45:56.806 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:45:57.152 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:47:08.035 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:48:57.146 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:48:57.558 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:51:57.560 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:51:57.907 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:52:08.040 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:54:57.909 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:54:58.257 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 22:57:08.045 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 22:57:58.257 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 22:57:58.611 [scheduling-9] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:00:58.613 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:00:59.057 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:02:08.046 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:03:59.052 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:03:59.403 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:06:59.409 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:06:59.779 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:07:08.046 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:09:59.786 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:10:00.123 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:12:08.050 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:13:00.121 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:13:00.452 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:16:00.452 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:16:00.782 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:17:08.048 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:19:00.787 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:19:01.120 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:22:01.127 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:22:01.450 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:22:08.049 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:25:01.456 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:25:01.802 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:27:08.049 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:28:01.808 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:28:02.139 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:31:02.143 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:31:02.487 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:32:08.057 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:34:02.528 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:34:02.874 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:37:02.879 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:37:03.214 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:37:08.094 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:40:03.217 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:40:03.552 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:42:08.100 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:43:03.557 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:43:03.895 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:46:03.902 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:46:04.249 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:47:08.100 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:49:04.302 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:49:04.625 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:52:04.629 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:52:04.977 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:52:08.158 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:55:04.979 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:55:05.333 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-23 23:57:08.167 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-23 23:58:05.341 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-23 23:58:05.691 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
