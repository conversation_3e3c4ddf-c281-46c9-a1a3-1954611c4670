2025-05-28 00:00:14.172 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:00:14.524 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:02:39.653 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:03:14.534 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:03:14.888 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:06:14.890 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:06:15.238 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:07:39.651 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:09:15.247 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:09:15.608 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:12:15.610 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:12:15.962 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:12:39.658 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:15:15.967 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:15:16.328 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:17:39.668 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:18:16.337 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:18:16.704 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:21:16.704 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:21:17.059 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:22:39.639 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:24:17.044 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:24:17.410 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:27:17.417 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:27:17.787 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:27:39.645 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:30:17.795 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:30:18.142 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:32:39.644 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:33:18.150 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:33:18.508 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:36:18.512 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:36:18.865 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:37:39.659 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:39:18.875 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:39:19.222 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:42:19.231 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:42:19.605 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:42:39.666 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:45:19.608 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:45:19.956 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:47:39.672 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:48:19.963 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:48:20.320 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:51:20.322 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:51:20.677 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:52:39.650 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 00:54:20.657 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:54:21.556 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:57:21.564 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 00:57:21.909 [scheduling-3] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 00:57:39.649 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:00:21.916 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:00:22.257 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:02:39.648 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:03:22.265 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:03:22.592 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:05:00.069 [scheduling-30] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
### The error may exist in com/dlcg/oa/mapper/ProcessInstanceMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: DELETE FROM process_instance WHERE id IN ( )
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:303)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:68)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy140.deleteBatchIds(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.removeByIds(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.ProcessInstanceServiceImpl$$EnhancerBySpringCGLIB$$e7899cd8.removeByIds(<generated>)
	at com.dlcg.oa.scheduled.DelRefuseProcessScheduled.delRefuseProcess(DelRefuseProcessScheduled.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy225.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy257.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 25 common frames omitted
2025-05-28 01:05:00.069 [scheduling-30] ERROR o.s.s.s.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
### The error may exist in com/dlcg/oa/mapper/ProcessInstanceMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: DELETE FROM process_instance WHERE id IN ( )
### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
; bad SQL grammar []; nested exception is java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:88)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.delete(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.delete(SqlSessionTemplate.java:303)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:68)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy140.deleteBatchIds(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.removeByIds(ServiceImpl.java:202)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.ProcessInstanceServiceImpl$$EnhancerBySpringCGLIB$$e7899cd8.removeByIds(<generated>)
	at com.dlcg.oa.scheduled.DelRefuseProcessScheduled.delRefuseProcess(DelRefuseProcessScheduled.java:45)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:93)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near ')' at line 1
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:97)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:953)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:370)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3051)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:440)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3049)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:167)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:498)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:47)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:74)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy225.update(Unknown Source)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doUpdate(MybatisSimpleExecutor.java:54)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:63)
	at com.sun.proxy.$Proxy257.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.delete(DefaultSqlSession.java:212)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 25 common frames omitted
2025-05-28 01:06:22.598 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:06:22.987 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:07:39.637 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:09:22.975 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:09:23.299 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:12:23.297 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:12:23.697 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:12:39.643 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:15:23.698 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:15:24.029 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:17:39.646 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:18:24.036 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:18:24.443 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:21:24.445 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:21:24.781 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:22:39.641 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:24:24.769 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:24:25.153 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:27:25.154 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:27:25.481 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:27:39.639 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:30:25.482 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:30:25.827 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:32:39.644 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:33:25.833 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:33:26.165 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:36:26.165 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:36:26.509 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:37:39.659 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:39:26.533 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:39:26.873 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:42:26.880 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:42:27.463 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:42:39.678 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:45:27.471 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:45:27.822 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:47:39.683 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:48:27.824 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:48:28.159 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:51:28.167 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:51:28.492 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:52:39.682 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 01:54:28.500 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:54:28.850 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:57:28.856 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 01:57:29.209 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 01:57:39.681 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:00:29.215 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:00:29.567 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:02:39.689 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:03:29.573 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:03:29.903 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:06:29.908 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:06:30.232 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:07:39.694 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:09:30.310 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:09:30.661 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:12:30.665 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:12:30.981 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:12:39.771 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:15:30.982 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:15:31.313 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:17:39.774 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:18:31.315 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:18:31.634 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:21:31.635 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:21:31.966 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:22:39.782 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:24:31.978 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:24:32.334 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:27:32.340 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:27:32.682 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:27:39.796 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:30:32.683 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:30:33.033 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:32:39.807 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:33:33.044 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:33:33.371 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:36:33.379 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:36:33.729 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:37:39.809 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:39:33.743 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:39:34.128 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:42:34.133 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:42:34.472 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:42:39.825 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:45:34.479 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:45:34.819 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:47:39.833 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:48:34.823 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:48:35.173 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:51:35.175 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:51:35.502 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:52:39.842 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 02:54:35.531 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:54:36.463 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:57:36.482 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 02:57:36.826 [scheduling-16] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 02:57:39.891 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:00:36.837 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:00:37.180 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:02:39.896 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:03:37.189 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:03:37.526 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:06:37.549 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:06:37.895 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:07:39.905 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:09:37.909 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:09:38.252 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:12:38.276 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:12:38.615 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:12:39.934 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:15:38.623 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:15:39.163 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:17:39.943 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:18:39.175 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:18:39.517 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:21:39.529 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:21:39.884 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:22:39.953 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:24:39.887 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:24:40.237 [scheduling-19] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:27:39.948 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:27:40.226 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:27:40.570 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:30:40.582 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:30:40.931 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:32:39.961 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:33:40.935 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:33:41.261 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:36:41.271 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:36:41.618 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:37:39.971 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:39:41.627 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:39:41.954 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:42:39.969 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:42:41.946 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:42:42.284 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:45:42.294 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:45:42.626 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:47:39.981 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:48:42.638 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:48:42.990 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:51:42.997 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:51:43.369 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:52:39.995 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:54:43.374 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:54:43.716 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 03:57:40.019 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 03:57:43.737 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 03:57:44.094 [scheduling-8] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:00:44.098 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:00:44.434 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:02:40.029 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:03:44.445 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:03:44.825 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:06:44.835 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:06:45.190 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:07:40.035 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:09:45.201 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:09:45.546 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:12:40.031 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:12:45.534 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:12:45.891 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:15:45.893 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:15:46.225 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:17:40.041 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:18:46.236 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:18:46.593 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:21:46.606 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:21:46.942 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:22:40.054 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:24:46.946 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:24:47.296 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:27:40.044 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:27:47.288 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:27:47.621 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:30:47.625 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:30:47.972 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:32:40.057 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:33:47.982 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:33:48.318 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:36:48.328 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:36:48.672 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:37:40.060 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:39:48.674 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:39:49.030 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:42:40.069 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:42:49.039 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:42:49.441 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:45:49.452 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:45:49.797 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:47:40.080 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:48:49.807 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:48:50.141 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:51:50.151 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:51:50.502 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:52:40.083 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:54:50.512 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:54:51.432 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 04:57:40.082 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 04:57:51.431 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 04:57:51.784 [scheduling-10] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:00:51.789 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:00:52.154 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:02:40.089 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:03:52.156 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:03:52.514 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:06:52.519 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:06:52.878 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:07:40.091 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:09:52.884 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:09:53.230 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:12:40.119 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:12:53.259 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:12:53.608 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:15:53.619 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:15:53.959 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:17:40.127 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:18:53.970 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:18:54.324 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:21:54.335 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:21:54.687 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:22:40.140 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:24:54.692 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:24:55.041 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:27:40.131 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:27:55.028 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:27:55.394 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:30:55.400 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:30:55.764 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:32:40.135 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:33:55.774 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:33:56.157 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:36:56.168 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:36:56.534 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:37:40.139 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:39:56.544 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:39:56.906 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:42:40.144 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:42:56.914 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:42:57.289 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:45:57.280 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:45:57.646 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:47:40.144 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:48:57.656 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:48:58.016 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:51:58.026 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:51:58.390 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:52:40.155 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:54:58.401 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:54:58.761 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 05:57:40.159 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 05:57:58.769 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 05:57:59.142 [scheduling-11] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:00:59.165 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:00:59.511 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:02:40.188 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:03:59.516 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:03:59.859 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:06:59.866 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:07:00.294 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:07:40.195 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:10:00.305 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:10:00.670 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:12:40.200 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:13:00.681 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:13:01.033 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:16:01.052 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:16:01.393 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:17:40.222 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:19:01.399 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:19:01.733 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:22:01.740 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:22:02.092 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:22:40.230 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:25:02.094 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:25:02.452 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:27:40.240 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:28:02.455 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:28:02.823 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:31:02.814 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:31:03.177 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:32:40.232 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:34:03.188 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:34:03.547 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:37:03.550 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:37:03.906 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:37:40.237 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:40:03.914 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:40:04.318 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:42:40.242 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:43:04.329 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:43:04.679 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:46:04.668 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:46:05.019 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:47:40.235 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:49:05.029 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:49:05.379 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:52:05.388 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:52:05.731 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:52:40.241 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:55:05.741 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:55:06.644 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 06:57:40.247 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 06:58:06.654 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 06:58:07.003 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:01:06.999 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:01:07.397 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:02:40.243 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:04:07.403 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:04:07.750 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:07:07.760 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:07:08.111 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:07:40.247 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:10:08.121 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:10:08.480 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:12:40.257 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:13:08.490 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:13:08.849 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:16:08.871 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:16:09.224 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:17:40.281 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:19:09.230 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:19:09.575 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:22:09.582 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:22:09.952 [scheduling-14] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:22:40.288 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:25:09.962 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:25:10.344 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:27:40.299 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:28:10.353 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:28:10.698 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:31:10.723 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:31:11.073 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:32:40.325 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:34:11.083 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:34:11.446 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:37:11.456 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:37:11.845 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:37:40.334 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:40:11.854 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:40:12.223 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:42:40.341 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:43:12.233 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:43:12.594 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:46:12.617 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:46:12.955 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:47:40.368 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:49:12.964 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:49:13.303 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:52:13.314 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:52:13.678 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:52:40.376 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:55:13.688 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:55:14.046 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 07:57:40.379 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 07:58:14.056 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 07:58:14.412 [scheduling-6] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:01:14.420 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:01:14.784 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:02:40.391 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:04:14.788 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:04:15.132 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:07:15.135 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:07:15.474 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:07:40.400 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:10:15.483 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:10:15.843 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:12:40.405 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:13:15.849 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:13:16.210 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:16:16.224 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:16:16.583 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:17:40.418 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:19:16.587 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:19:16.938 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:22:16.948 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:22:17.291 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:22:40.422 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:25:17.297 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:25:17.642 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:27:40.427 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:28:17.648 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:28:18.001 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:31:18.002 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:31:18.365 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:32:40.433 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:34:18.376 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:34:18.780 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:37:18.785 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:37:19.168 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:37:40.438 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:40:19.179 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:40:19.545 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:42:40.443 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:43:19.549 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:43:19.980 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:46:19.983 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:46:20.395 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:47:40.452 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:49:20.400 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:49:20.783 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:52:20.789 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:52:21.134 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:52:40.461 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:55:21.136 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:55:22.066 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 08:57:40.467 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 08:58:22.071 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 08:58:22.443 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:01:22.449 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:01:22.822 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:02:40.476 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:04:22.824 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:04:23.167 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:07:23.170 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:07:23.684 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:07:40.483 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:10:23.686 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:10:24.055 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:12:40.486 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:13:23.987 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:13:24.381 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:16:24.382 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:16:24.778 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:17:40.416 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:19:24.779 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:19:25.176 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:22:25.177 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:22:25.709 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:22:40.420 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:25:25.710 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:25:26.090 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:27:40.424 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:28:26.103 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:28:26.569 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:31:26.575 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:31:26.974 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:32:40.444 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:34:26.977 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:34:27.460 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:37:27.464 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:37:27.845 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:37:40.451 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:40:27.846 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:40:28.220 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:42:40.451 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:43:28.143 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:43:28.522 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:46:28.521 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:46:28.870 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:47:40.372 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:49:28.871 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:49:29.274 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:52:29.276 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:52:29.777 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:52:40.373 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:55:29.778 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:55:30.146 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 09:57:40.373 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 09:58:30.149 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 09:58:30.547 [scheduling-2] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:01:30.571 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:01:30.957 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:02:40.398 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:04:30.959 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:04:31.367 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:07:31.366 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:07:31.836 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:07:40.400 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:10:31.839 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:10:32.225 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:12:40.401 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:13:32.227 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:13:32.581 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:16:32.593 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:16:32.992 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:17:40.416 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:19:32.992 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:19:33.373 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:22:33.376 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:22:33.733 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:22:40.418 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:25:33.736 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:25:34.115 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:27:40.417 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:28:34.114 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:28:34.512 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:31:34.516 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:31:34.942 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:32:40.416 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:34:34.942 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:34:35.322 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:37:35.320 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:37:35.806 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:37:40.420 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:40:35.809 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:40:36.351 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:42:40.422 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:43:36.354 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:43:36.728 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:46:36.797 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:46:37.267 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:47:40.491 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:49:37.269 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:49:37.719 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:52:37.722 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:52:38.239 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:52:40.495 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:55:38.239 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:55:39.236 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 10:57:40.498 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 10:58:39.240 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 10:58:39.696 [scheduling-18] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:01:39.700 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:01:40.169 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:02:40.503 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:04:40.171 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:04:40.538 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:07:40.509 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:07:40.540 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:07:40.976 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:10:40.981 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:10:41.991 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:12:40.513 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:13:41.993 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:13:42.361 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:16:42.364 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:16:42.801 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:17:40.517 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:19:42.805 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:19:43.203 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:22:40.519 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:22:43.205 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:22:43.614 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:25:43.616 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:25:44.053 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:27:40.523 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:28:44.057 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:28:44.557 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:31:44.561 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:31:44.947 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:32:40.557 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:34:44.981 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:34:45.973 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:37:40.564 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:37:45.979 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:37:46.455 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:40:46.465 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:40:46.902 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:42:40.577 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:43:46.907 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:43:47.386 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:46:47.394 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:46:47.754 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:47:40.588 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:49:47.734 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:49:49.263 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:52:40.540 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:52:49.248 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:52:49.692 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:55:49.699 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:55:50.089 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 11:57:40.547 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 11:58:50.098 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 11:58:50.559 [scheduling-15] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:01:50.558 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:01:51.902 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:02:40.552 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:04:51.908 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:04:52.257 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:07:40.557 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:07:52.266 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:07:53.162 [scheduling-30] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:10:53.170 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:10:53.592 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:12:40.563 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:13:53.594 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:13:53.952 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:16:53.962 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:16:54.305 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:17:40.570 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:19:54.310 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:19:54.651 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:22:40.573 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:22:54.658 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:22:55.021 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:25:55.026 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:25:55.380 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:27:40.577 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:28:55.389 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:28:55.755 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:31:55.764 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:31:56.101 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:32:40.585 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:34:56.119 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:34:56.465 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:37:40.604 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:37:56.465 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:37:56.846 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:40:56.854 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:40:57.196 [scheduling-12] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:42:40.605 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:43:57.203 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:43:57.633 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:46:57.639 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:46:58.011 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:47:40.615 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:49:58.018 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:49:58.362 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:52:40.628 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:52:58.373 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:52:58.722 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:55:58.731 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:56:00.025 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 12:57:40.630 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 12:59:00.030 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 12:59:00.387 [scheduling-26] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:02:00.400 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:02:00.757 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:02:40.639 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:05:00.766 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:05:01.138 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:07:40.648 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:08:01.143 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:08:01.488 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:11:01.466 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:11:01.913 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:12:40.630 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:14:01.916 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:14:02.312 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:17:02.315 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:17:02.743 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:17:40.633 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:20:02.746 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:20:03.134 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:22:40.616 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:23:03.116 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:23:03.588 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:26:03.592 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:26:03.992 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:27:40.619 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:29:03.994 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:29:04.397 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:32:04.411 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:32:04.804 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:32:40.616 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:35:04.806 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:35:05.283 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:37:40.657 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:38:05.321 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:38:05.700 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:41:05.701 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:41:11.775 [scheduling-5] WARN  c.a.druid.pool.DruidDataSource - get connection timeout retry : 1
2025-05-28 13:41:12.097 [http-nio-8072-exec-5] WARN  c.a.druid.pool.DruidDataSource - get connection timeout retry : 1
2025-05-28 13:41:17.147 [http-nio-8072-exec-5] ERROR c.d.o.e.GlobalDefultExceptionHandler - Exception 请求地址:【/client/sys-dictionary/getDictionaryAllList】  请求参数:【{}】
2025-05-28 13:41:17.147 [http-nio-8072-exec-5] ERROR c.d.o.e.GlobalDefultExceptionHandler - Exception 堆栈信息：org.mybatis.spring.MyBatisSystemException: nested exception is org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 1, maxActive 10, creating 1, runningSqlCount 1 : SELECT  id,dictionary_key,update_date,code,spare4,spare3,spare6,spare5,del_flag,spare2,spare1,create_by,update_by,value,remarks,create_date  FROM sys_dictionary 
 
 WHERE (del_flag = ?) ORDER BY dictionary_key ASC , spare6 ASC , code ASC LIMIT ? 
### The error may exist in com/dlcg/oa/mapper/SysDictionaryMapper.java (best guess)
### The error may involve com.dlcg.oa.mapper.SysDictionaryMapper.selectList_COUNT
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 1, maxActive 10, creating 1, runningSqlCount 1 : SELECT  id,dictionary_key,update_date,code,spare4,spare3,spare6,spare5,del_flag,spare2,spare1,create_by,update_by,value,remarks,create_date  FROM sys_dictionary 
 
 WHERE (del_flag = ?) ORDER BY dictionary_key ASC , spare6 ASC , code ASC LIMIT ? 
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:440)
	at com.sun.proxy.$Proxy124.selectList(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:223)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:158)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:76)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:61)
	at com.sun.proxy.$Proxy206.selectList(Unknown Source)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.list(ServiceImpl.java:271)
	at com.baomidou.mybatisplus.extension.service.impl.ServiceImpl$$FastClassBySpringCGLIB$$76535273.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:687)
	at com.dlcg.oa.service.impl.SysDictionaryServiceImpl$$EnhancerBySpringCGLIB$$a9f36e7b.list(<generated>)
	at com.dlcg.oa.provider.SysDictionaryProvider.lambda$getDictionaryAllList$0(SysDictionaryProvider.java:80)
	at com.github.pagehelper.Page.doSelectPage(Page.java:348)
	at com.dlcg.oa.provider.SysDictionaryProvider.getDictionaryAllList(SysDictionaryProvider.java:79)
	at sun.reflect.GeneratedMethodAccessor259.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:105)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:878)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:792)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1040)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:943)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:626)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:733)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:123)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:143)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:374)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:868)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1590)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
Caused by: org.apache.ibatis.exceptions.PersistenceException: 
### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 1, maxActive 10, creating 1, runningSqlCount 1 : SELECT  id,dictionary_key,update_date,code,spare4,spare3,spare6,spare5,del_flag,spare2,spare1,create_by,update_by,value,remarks,create_date  FROM sys_dictionary 
 
 WHERE (del_flag = ?) ORDER BY dictionary_key ASC , spare6 ASC , code ASC LIMIT ? 
### The error may exist in com/dlcg/oa/mapper/SysDictionaryMapper.java (best guess)
### The error may involve com.dlcg.oa.mapper.SysDictionaryMapper.selectList_COUNT
### The error occurred while executing a query
### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 1, maxActive 10, creating 1, runningSqlCount 1 : SELECT  id,dictionary_key,update_date,code,spare4,spare3,spare6,spare5,del_flag,spare2,spare1,create_by,update_by,value,remarks,create_date  FROM sys_dictionary 
 
 WHERE (del_flag = ?) ORDER BY dictionary_key ASC , spare6 ASC , code ASC LIMIT ? 
	at org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:149)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at sun.reflect.GeneratedMethodAccessor173.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:426)
	... 66 more
Caused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection; nested exception is com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 1, maxActive 10, creating 1, runningSqlCount 1 : SELECT  id,dictionary_key,update_date,code,spare4,spare3,spare6,spare5,del_flag,spare2,spare1,create_by,update_by,value,remarks,create_date  FROM sys_dictionary 
 
 WHERE (del_flag = ?) ORDER BY dictionary_key ASC , spare6 ASC , code ASC LIMIT ? 
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:82)
	at org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)
	at org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:336)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.prepareStatement(MybatisSimpleExecutor.java:93)
	at com.baomidou.mybatisplus.core.executor.MybatisSimpleExecutor.doQuery(MybatisSimpleExecutor.java:66)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:324)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.util.ExecutorUtil.executeAutoCount(ExecutorUtil.java:138)
	at com.github.pagehelper.PageInterceptor.count(PageInterceptor.java:150)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:97)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at com.sun.proxy.$Proxy257.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)
	... 71 more
Caused by: com.alibaba.druid.pool.GetConnectionTimeoutException: wait millis 5004, active 1, maxActive 10, creating 1, runningSqlCount 1 : SELECT  id,dictionary_key,update_date,code,spare4,spare3,spare6,spare5,del_flag,spare2,spare1,create_by,update_by,value,remarks,create_date  FROM sys_dictionary 
 
 WHERE (del_flag = ?) ORDER BY dictionary_key ASC , spare6 ASC , code ASC LIMIT ? 
	at com.alibaba.druid.pool.DruidDataSource.getConnectionInternal(DruidDataSource.java:1510)
	at com.alibaba.druid.pool.DruidDataSource.getConnectionDirect(DruidDataSource.java:1253)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4619)
	at com.alibaba.druid.filter.stat.StatFilter.dataSource_getConnection(StatFilter.java:680)
	at com.alibaba.druid.filter.FilterChainImpl.dataSource_connect(FilterChainImpl.java:4615)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1231)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1223)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:90)
	at org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:158)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:116)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:79)
	... 85 more

2025-05-28 13:41:23.941 [Druid-ConnectionPool-Create-2089232412] ERROR c.a.druid.pool.DruidDataSource - create connection holder error
com.mysql.cj.jdbc.exceptions.CommunicationsException: Communications link failure

The last packet successfully received from the server was 1,008 milliseconds ago. The last packet sent successfully to the server was 1,008 milliseconds ago.
	at com.mysql.cj.jdbc.exceptions.SQLError.createCommunicationsException(SQLError.java:174)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:64)
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1414)
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1399)
	at com.alibaba.druid.filter.FilterChainImpl.connection_isReadOnly(FilterChainImpl.java:399)
	at com.alibaba.druid.filter.FilterAdapter.connection_isReadOnly(FilterAdapter.java:893)
	at com.alibaba.druid.filter.FilterChainImpl.connection_isReadOnly(FilterChainImpl.java:396)
	at com.alibaba.druid.proxy.jdbc.ConnectionProxyImpl.isReadOnly(ConnectionProxyImpl.java:292)
	at com.alibaba.druid.pool.DruidConnectionHolder.<init>(DruidConnectionHolder.java:135)
	at com.alibaba.druid.pool.DruidConnectionHolder.<init>(DruidConnectionHolder.java:75)
	at com.alibaba.druid.pool.DruidDataSource.put(DruidDataSource.java:2189)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2509)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet successfully received from the server was 1,008 milliseconds ago. The last packet sent successfully to the server was 1,008 milliseconds ago.
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:151)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:167)
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:807)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:622)
	at com.mysql.cj.protocol.a.NativeProtocol.sendCommand(NativeProtocol.java:128)
	at com.mysql.cj.NativeSession.sendCommand(NativeSession.java:311)
	at com.mysql.cj.NativeSession.queryServerVariable(NativeSession.java:1008)
	at com.mysql.cj.jdbc.ConnectionImpl.isReadOnly(ConnectionImpl.java:1406)
	... 9 common frames omitted
Caused by: java.io.IOException: Socket is closed
	at com.mysql.cj.protocol.AbstractSocketConnection.getMysqlInput(AbstractSocketConnection.java:72)
	at com.mysql.cj.protocol.a.NativeProtocol.clearInputStream(NativeProtocol.java:803)
	... 14 common frames omitted
2025-05-28 13:41:23.942 [Druid-ConnectionPool-Create-2089232412] INFO  c.a.druid.pool.DruidDataSource - put physical connection to pool failed.
2025-05-28 13:42:07.526 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:42:40.661 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:45:07.530 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:45:10.040 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:47:40.663 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:48:10.041 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:48:14.622 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:51:14.624 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:51:18.052 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:52:40.655 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 13:54:18.043 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:54:20.654 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:57:20.658 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 13:57:23.792 [scheduling-5] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 13:57:40.658 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:00:23.796 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:00:27.373 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:02:40.658 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:03:27.376 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:03:31.990 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:06:31.992 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:06:35.258 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:07:40.725 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:09:35.263 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:09:38.754 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:12:38.757 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:12:40.732 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:12:40.777 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:15:40.783 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:15:43.458 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:17:40.735 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:18:43.460 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:18:46.687 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:21:46.693 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:21:51.676 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:22:40.741 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:24:51.681 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:24:53.389 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:27:40.745 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:27:53.395 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:27:59.589 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:30:59.594 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:31:04.280 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:32:40.750 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:34:04.286 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:34:08.744 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:37:08.779 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:37:10.567 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:37:40.794 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:40:10.585 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:40:14.431 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:42:40.804 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:43:14.435 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:43:17.899 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:46:17.904 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:46:19.815 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:47:40.810 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:49:19.820 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:49:35.896 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:52:35.901 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:52:38.706 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:52:40.816 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:55:38.705 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:55:44.156 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 14:57:40.816 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 14:58:44.160 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 14:58:46.550 [scheduling-21] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:01:46.557 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:01:50.772 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:02:40.826 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:04:50.777 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:04:55.184 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:07:40.831 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:07:55.189 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:07:59.094 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:10:59.129 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:11:03.146 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:12:40.869 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:14:03.153 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:14:05.392 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:17:05.400 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:17:08.587 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:17:40.881 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:20:08.592 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:20:11.522 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:22:40.885 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:23:11.533 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:23:14.926 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:26:14.949 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:26:17.193 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:27:40.910 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:29:17.204 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:29:19.401 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:32:19.414 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:32:24.090 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:32:40.923 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:35:24.102 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:35:26.022 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:40:22.112 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:41:07.210 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:41:10.336 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:44:10.342 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:44:10.980 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:45:22.117 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:47:10.986 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:47:12.397 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:50:12.398 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:50:12.771 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:50:22.123 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:53:12.760 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:53:13.130 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:55:22.116 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 15:56:13.135 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:56:13.540 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 15:59:13.541 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 15:59:14.033 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:00:22.123 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:02:14.038 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:02:15.762 [scheduling-20] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:08:41.355 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:96112}] to ************:27017
2025-05-28 16:10:22.378 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:96118}] to ************:27017
2025-05-28 16:10:56.842 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:10:57.329 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:11:03.214 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:13:57.330 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:13:57.843 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:16:03.216 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:16:57.846 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:16:58.255 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:19:58.222 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:19:58.785 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:21:03.185 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:22:58.790 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:22:59.237 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:25:59.242 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:25:59.650 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:26:03.186 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:34:12.031 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:96123}] to ************:27017
2025-05-28 16:34:25.391 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 16:34:25.416 [lettuce-nioEventLoop-4-2] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 16:36:39.203 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:36:39.605 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:38:42.740 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:39:39.610 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:39:40.026 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:42:40.043 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:42:40.825 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:43:42.757 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:45:40.828 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:45:41.257 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:48:41.258 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:48:41.797 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:48:42.763 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:51:41.802 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:51:42.293 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:53:42.770 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 16:54:42.297 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:54:44.439 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:57:44.440 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 16:57:44.879 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 16:58:42.775 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 17:09:22.778 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:96134}] to ************:27017
2025-05-28 17:09:28.259 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 17:09:28.269 [lettuce-nioEventLoop-4-3] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 17:09:46.440 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 17:09:46.848 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 17:30:29.571 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:96141}] to ************:27017
2025-05-28 17:48:21.430 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 17:48:21.440 [lettuce-nioEventLoop-4-4] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 17:48:26.407 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:96150}] to ************:27017
2025-05-28 18:06:21.639 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:10, serverValue:96158}] to ************:27017
2025-05-28 18:21:54.878 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:11, serverValue:96165}] to ************:27017
2025-05-28 18:37:46.404 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 18:37:46.417 [lettuce-nioEventLoop-4-5] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 18:55:14.901 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:12, serverValue:96173}] to ************:27017
2025-05-28 19:01:20.766 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:13, serverValue:96178}] to ************:27017
2025-05-28 19:18:29.085 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:14, serverValue:96184}] to ************:27017
2025-05-28 19:33:59.198 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 19:33:59.206 [lettuce-nioEventLoop-4-6] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 19:51:04.661 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:15, serverValue:96193}] to ************:27017
2025-05-28 20:02:18.622 [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-05-28 20:02:19.473 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:16, serverValue:96198}] to ************:27017
2025-05-28 20:02:21.140 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 20:02:21.526 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 20:17:32.481 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:17, serverValue:96205}] to ************:27017
2025-05-28 20:50:49.869 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:18, serverValue:96213}] to ************:27017
2025-05-28 21:03:21.728 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:19, serverValue:96222}] to ************:27017
2025-05-28 21:03:35.533 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 21:03:35.543 [lettuce-nioEventLoop-4-7] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 21:21:05.990 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:20, serverValue:96226}] to ************:27017
2025-05-28 21:38:23.750 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:21, serverValue:96233}] to ************:27017
2025-05-28 21:55:22.001 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 21:55:22.009 [lettuce-nioEventLoop-4-8] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 22:04:16.701 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:22, serverValue:96239}] to ************:27017
2025-05-28 22:20:31.957 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:23, serverValue:96247}] to ************:27017
2025-05-28 22:51:18.430 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:24, serverValue:96254}] to ************:27017
2025-05-28 22:51:41.481 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 22:51:41.489 [lettuce-nioEventLoop-4-9] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 23:05:20.737 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:25, serverValue:96259}] to ************:27017
2025-05-28 23:05:30.890 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 开始更新字典表缓存缓存....
2025-05-28 23:05:31.258 [scheduling-17] INFO  c.d.o.s.SysDictionaryCacheScheduled - 更新字典表缓存完成！
2025-05-28 23:22:35.841 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:26, serverValue:96267}] to ************:27017
2025-05-28 23:38:34.608 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.protocol.ConnectionWatchdog - Reconnecting, last destination was /************:6379
2025-05-28 23:38:34.615 [lettuce-nioEventLoop-4-10] INFO  i.l.c.protocol.ReconnectionHandler - Reconnected to ************:6379
2025-05-28 23:56:37.782 [cluster-ClusterId{value='6835094891f59a10a8335ffd', description='null'}-************:27017] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:27, serverValue:96275}] to ************:27017
