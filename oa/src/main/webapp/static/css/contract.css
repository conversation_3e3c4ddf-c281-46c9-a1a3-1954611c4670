body{
    font-family: simsun;
}
tr, th, td{
    border: 1px solid gray;
    /*align: center;*/
}
table{
    border-collapse:collapse;
    border: 1px solid black;
    /*width:" px";*/
    /*height:" px";*/
}
td{
    text-align: center;
    min-width: 70px;
}
.minTD{
    min-width: 25px;
    width: 25px;
}
.dataDiv{
    display: inline-block;
    min-width: 20px;
}
.dataDivs{
    display: inline-block;
    min-width: 20px;
}
.dataMapDiv{
    display: inline-block;
    min-width: 25px;
}
.hideDiv{
    visibility: hidden;
    display: inline-block;
}
.textTR{
    border-bottom: 0px ;
    border-top: 0px;
}
.textTD{
    text-align: left;
    border-bottom: 0px ;
    border-top: 0px;
}
#buttons{
    margin-top: 40px;
    text-align:center;
}
#savebutton{
    width: 150px;
    padding:8px;
    background-color: #428bca;
    border-color: #357ebd;
    color: #fff;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px; /* future proofing */
    -khtml-border-radius: 10px; /* for old Konqueror browsers */
    text-align: center;
    vertical-align: middle;
    border: 1px solid transparent;
    font-weight: 900;
    font-size:125%;
    cursor: pointer;
}
#savebutton:hover{

    background-color: rgba(135, 135, 135, 0.15);
    border-color: rgba(135, 135, 135, 0.15);
    color: #428bca;
}
