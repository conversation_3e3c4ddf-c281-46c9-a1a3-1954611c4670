package com.dlcg.oa.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ProcessInstanceVo implements Serializable {
    private static final long serialVersionUID = -6215126241131027778L;
    private String flowName;
    private String processId;
    private String sponsorName;
    private String briefContent;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date sponsorTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date finishTime;
    private String operName;
    private String status;
    private String showStatus;
    private Integer statusId;
    private String sponsorAvatar;
    private String reason;
    private String flowCode;
    private String param1;
    private String isNoPageId;
    private Integer nodeId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date selfAuditSucceedTime;  //当前登陆人审核时间

    /* 抄送人已读状态，0:未读，1：已读 */
    private Integer ccsReadState;

}
