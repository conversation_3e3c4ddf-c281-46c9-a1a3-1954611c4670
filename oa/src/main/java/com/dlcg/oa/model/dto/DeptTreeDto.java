package com.dlcg.oa.model.dto;

import com.dlcg.oa.model.tree.TreeNode;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * @Desc :
 * @Create : zhaoey ~ 2020/06/29
 */
public class DeptTreeDto extends TreeNode<DeptTreeDto> {

    /**
     * 节点类型：0：公司，1：部门，2：员工
     */
    private Integer nodeType;
    /**
     * 公司名称
     */
    private String name;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 企业secret
     */
    private String corpSecret;

    /**
     * 排序
     */
    private Integer sort;

    private String businessId;
    private String avatar;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除 0：已删除，1：正常
     */
    private Boolean delFlag;

    private Boolean editEnable;
    private Boolean addEnable;
    private Boolean syncEnable;

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getBusinessId() {
        return businessId;
    }

    public void setBusinessId(String businessId) {
        this.businessId = businessId;
    }

    public Integer getNodeType() {
        return nodeType;
    }

    public void setNodeType(Integer nodeType) {
        this.nodeType = nodeType;
    }

    public Boolean getEditEnable() {
        return editEnable;
    }

    public void setEditEnable(Boolean editEnable) {
        this.editEnable = editEnable;
    }

    public Boolean getAddEnable() {
        return addEnable;
    }

    public void setAddEnable(Boolean addEnable) {
        this.addEnable = addEnable;
    }

    public Boolean getSyncEnable() {
        return syncEnable;
    }

    public void setSyncEnable(Boolean syncEnable) {
        this.syncEnable = syncEnable;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getCorpSecret() {
        return corpSecret;
    }

    public void setCorpSecret(String corpSecret) {
        this.corpSecret = corpSecret;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }
}
