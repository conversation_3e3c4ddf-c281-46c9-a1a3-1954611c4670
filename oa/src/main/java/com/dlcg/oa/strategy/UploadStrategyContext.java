package com.dlcg.oa.strategy;


import com.dlcg.oa.config.properties.UploadProperties;
import com.dlcg.oa.constants.OaConstants;
import com.dlcg.oa.model.pojo.UploadResult;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Component
@AutoConfigureAfter(value = {LocalUploadStrategy.class,AliyunOSSUploadStrategy.class})
public class UploadStrategyContext {

    private IUploadStrategy uploadStrategy;

    public UploadStrategyContext(UploadProperties uploadProperties,
                                 AliyunOSSUploadStrategy aliyunOSSUploadStrategy,
                                 LocalUploadStrategy localUploadStrategy){
        if(OaConstants.UPLOAD_STRATEGY_ALIYUN.equals(uploadProperties.getStrategy())){
            // 阿里云
            this.uploadStrategy = aliyunOSSUploadStrategy;
        }else{
            // 本地
            this.uploadStrategy = localUploadStrategy;
        }
    }

    public void setUploadStrategy(IUploadStrategy uploadStrategy) {
        this.uploadStrategy = uploadStrategy;
    }

    public UploadResult upload(MultipartFile file){
        return this.uploadStrategy.upload(file);
    }

}
