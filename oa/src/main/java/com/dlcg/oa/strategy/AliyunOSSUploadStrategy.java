package com.dlcg.oa.strategy;


import com.dlcg.oa.model.pojo.UploadResult;
import com.zthzinfo.aliyun.oss.OSSObject;
import com.zthzinfo.aliyun.oss.ResultFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

@Component
public class AliyunOSSUploadStrategy implements IUploadStrategy {

    private static final Logger logger = LoggerFactory.getLogger(AliyunOSSUploadStrategy.class);

    @Resource
    private OSSObject ossObject;

    @Override
    public UploadResult upload(MultipartFile file) {
        ResultFile resultFile = null;
        try {
            resultFile = ossObject.uploadFileByMultipartFile(null, file, null);
        } catch (IOException e) {
            e.printStackTrace();
        }

        logger.info("上传文件到云服务器！url = " + resultFile.getUrl());
        UploadResult uploadResult = new UploadResult();
        uploadResult.setUrl(resultFile.getUrlHttps());
        return uploadResult;
    }
}
