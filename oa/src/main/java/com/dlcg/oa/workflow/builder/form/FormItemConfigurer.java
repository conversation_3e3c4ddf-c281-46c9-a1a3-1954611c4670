package com.dlcg.oa.workflow.builder.form;

import com.dlcg.oa.workflow.builder.AbstractConfigureAdapter;
import com.dlcg.oa.workflow.builder.IBuilder;
import com.dlcg.oa.workflow.dynamicform.FormComponent;
import com.dlcg.oa.workflow.dynamicform.FormItem;
import com.dlcg.oa.workflow.dynamicform.FormSchema;

import java.util.function.Supplier;

public final class FormItemConfigurer extends AbstractConfigureAdapter<FormItemConfigurer> implements IBuilder<FormSchema> {

    private FormSchema formSchema;

    public FormItemConfigurer(FormSchema formSchema) {
        if (null == formSchema){
            throw new IllegalArgumentException("请传入formSchema");
        }
        this.formSchema = formSchema;
    }

    /**
     * 表单项构建
     * @param itemName 属性值，对应表单组件的 "name" 属性
     */
    public FormItemPropConfigurer item(String itemName, String labelName, String componentName){
        FormItem item = new FormItem();
        FormComponent component = new FormComponent();
        component.setName(componentName);
        item.setLabel(labelName);
        item.setComponent(component);
        formSchema.getProperties().put(itemName,item);

        FormItemPropConfigurer itemProp = new FormItemPropConfigurer(item);
        itemProp.setBuilder(this);
        return itemProp;
    }

    /**
     * 表单项构建
     * @param itemName 属性值，对应表单组件的 "name" 属性
     * @param sup 自定义表单项
     * @return
     */
    public FormItemPropConfigurer item(String itemName,Supplier<FormItem> sup){
        FormItem item = sup.get();
        if (null == item){
            throw new IllegalArgumentException("item cannot be null");
        }
        formSchema.getProperties().put(itemName,item);

        FormItemPropConfigurer itemProp = new FormItemPropConfigurer(item);
        itemProp.setBuilder(this);
        return itemProp;
    }

    @Override
    public FormSchema build() {
        return this.formSchema;
    }
}
