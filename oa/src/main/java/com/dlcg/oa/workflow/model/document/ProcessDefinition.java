package com.dlcg.oa.workflow.model.document;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.List;

@Document(collection = "process")
@Data
public class ProcessDefinition implements Serializable {

    @Id
    private String id;
    private String name;
    private String code;
    /** 抄送人列表 */
    private List<String> cc;
    /** 抄送人角色列表 */
    private List<String> ccRule;
    /** 后置处理，每个节点完成后都会调用该方法 */
    private String afterEveryNode;
    /** 流程实例列表 */
    private List<ProcessNode> process;
    /** 表单结构 */
    private String formSchema;
    /** 显示在摘要中的字段 */
    private List<String> briefFields;

    /** 是否使用视图组件 */
    private Boolean useViewComponent;
    /** 自定义内容显示组件 */
    private String viewComponent;
    /** 是否禁用流程流转时通知发起人：如果true不进行通知,null和false进行通知 */
    private Boolean disableProcessChangeNoticeToSponsor;

    private List<ProcessNode.DynamicNext> ccOperIdGetterDynamic;

    private static final long serialVersionUID = -2563208199593444531L;
}
