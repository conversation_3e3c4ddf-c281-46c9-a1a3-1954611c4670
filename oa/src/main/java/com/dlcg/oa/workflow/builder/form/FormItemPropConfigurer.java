package com.dlcg.oa.workflow.builder.form;

import com.dlcg.oa.workflow.builder.AbstractConfigureAdapter;
import com.dlcg.oa.workflow.builder.IBuilder;
import com.dlcg.oa.workflow.dynamicform.FormComponentProps;
import com.dlcg.oa.workflow.dynamicform.FormComponentRules;
import com.dlcg.oa.workflow.dynamicform.FormItem;
import com.dlcg.oa.workflow.dynamicform.FormSchema;

import java.util.function.Consumer;

public class FormItemPropConfigurer extends AbstractConfigureAdapter<FormItemConfigurer> implements IBuilder<FormSchema> {

    private FormItem formItem;

    public FormItemPropConfigurer(FormItem formItem) {
        this.formItem = formItem;
    }

    /**
     * 表单项的组件配置
     */
    public FormItemPropConfigurer props(Consumer<FormComponentProps> con){
        this.beforeProp();
        con.accept(formItem.getComponent().getProps());
        return this;
    }

    /**
     * 表单项的类型配置 对应组件的 "type" 属性
     * @param type
     * @return
     */
    public FormItemPropConfigurer type(String type){
        this.beforeProp();
        formItem.getComponent().getProps().setType(type);
        return this;
    }

    /**
     * 对FormItem进行设置
     */
    public FormItemPropConfigurer optItem(Consumer<FormItem> con){
        con.accept(formItem);
        return this;
    }

    /**
     * 表单校验
     */
    public FormItemPropConfigurer rules(Consumer<FormComponentRules> con){
        if (null == formItem.getRules()){
            formItem.setRules(new FormComponentRules());
        }
        con.accept(formItem.getRules());
        return this;
    }

    @Override
    public FormSchema build() {
        return super.getBuilder().build();
    }


    private void beforeProp(){
        if (null == formItem.getComponent().getProps()){
            formItem.getComponent().setProps(new FormComponentProps());
        }
    }
}
