package com.dlcg.oa.util;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.dlcg.oa.entity.SysUser;

/**
 * <AUTHOR>
 * @since 2022/6/8
 */
public class WorkFlowUserThreadLocal {
    private static final ThreadLocal<SysUser> THREAD_LOCAL = new TransmittableThreadLocal<>();


    /**
     * 取得线程变量值
     */
    public static SysUser getValue() {
        return THREAD_LOCAL.get();
    }

    /**
     * 设置线程变量值
     */
    public static void setValue(SysUser value) {
        THREAD_LOCAL.set(value);
    }

}
