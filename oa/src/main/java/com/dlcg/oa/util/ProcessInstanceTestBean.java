package com.dlcg.oa.util;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class ProcessInstanceTestBean {

    @JsonProperty("RECORDS")
    private List<RECORDSDTO> records;

    @NoArgsConstructor
    @Data
    public static class RECORDSDTO {
        @JsonProperty("id")
        private String id;
        @JsonProperty("global_param2")
        private List<GlobalParam2DTO> globalParam2;

        @NoArgsConstructor
        @Data
        public static class GlobalParam2DTO {
            @JsonProperty("id")
            private String id;
            @JsonProperty("type")
            private Integer type;
            @JsonProperty("costType")
            private Integer costType;
            @JsonProperty("costPrice")
            private Double costPrice;
            @JsonProperty("costPriceno")
            private Double costPriceno;
            @JsonProperty("costTax")
            private Integer costTax;
            @JsonProperty("sysSupplierId")
            private String sysSupplierId;
            @JsonProperty("remark")
            private Object remark;
            @JsonProperty("shipLineId")
            private String shipLineId;
            @JsonProperty("createBy")
            private String createBy;
            @JsonProperty("createDate")
            private String createDate;
            @JsonProperty("updateBy")
            private Object updateBy;
            @JsonProperty("updateDate")
            private Object updateDate;
            @JsonProperty("delFlag")
            private String delFlag;
            @JsonProperty("contractCompany")
            private String contractCompany;
            @JsonProperty("customerSupplier")
            private Integer customerSupplier;
            @JsonProperty("shipOrGoods")
            private Integer shipOrGoods;
            @JsonProperty("money")
            private Double money;
            @JsonProperty("tonnage")
            private Double tonnage;
            @JsonProperty("shipId")
            private String shipId;
            @JsonProperty("yewuId")
            private String yewuId;
            @JsonProperty("guazhang")
            private Integer guazhang;
            @JsonProperty("epartureTimeDate")
            private String epartureTimeDate;
            @JsonProperty("onAccountNum")
            private Object onAccountNum;
            @JsonProperty("guaTime")
            private Object guaTime;
            @JsonProperty("loadWharf")
            private String loadWharf;
            @JsonProperty("finalWharf")
            private String finalWharf;
            @JsonProperty("endLocal")
            private Object endLocal;
            @JsonProperty("oneCustomerName")
            private Object oneCustomerName;
            @JsonProperty("secondCustomerName")
            private String secondCustomerName;
            @JsonProperty("sysSupplierName")
            private Object sysSupplierName;
            @JsonProperty("loadWharfName")
            private String loadWharfName;
            @JsonProperty("unloadWharfName")
            private String unloadWharfName;
            @JsonProperty("shipName")
            private String shipName;
        }
    }
}
