package com.dlcg.oa.config;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dlcg.oa.bean.WxUserBean;
import com.dlcg.oa.entity.SysUser;
import com.dlcg.oa.entity.WxDepartUser;
import com.dlcg.oa.entity.WxDepartment;
import com.dlcg.oa.entity.WxUser;
import com.dlcg.oa.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@EnableScheduling
@Slf4j
public class TimingTask {
    
    @Autowired
    QyWxUtilService qyWxUtilService;
    @Autowired
    IWxDepartmentService iWxDepartmentService;
    @Autowired
    IWxUserService iWxUserService;
    @Autowired
    ISysUserService iSysUserService;
    @Autowired
    IWxDepartUserService iWxDepartUserService;
    
//    @Scheduled(cron="0 0 0/2 * * ?")
    public void updateToken() {
        log.info("开始定时更新token...");
        String token = qyWxUtilService.getTokenByWxService();
        log.info("更新token结果：token="+token);
    }
    
//    @Scheduled(cron="0 0 12,20 * * ?")
    public void saveOrUpdateWxUser(){
        List<WxDepartment> dept = iWxDepartmentService.getDepartment();
        List<WxDepartment> deptList = iWxDepartmentService.list();
        List<Integer> arrList = new ArrayList<>();
        List<WxDepartment> list = new ArrayList<>();
        for(WxDepartment wxDepartment:deptList){
            arrList.add(wxDepartment.getDepartmentId());
        }
        for(WxDepartment wxDepartment:dept){
            if(!arrList.contains(wxDepartment.getDepartmentId())){
                list.add(wxDepartment);
            }
        }
        iWxDepartmentService.saveBatch(list);
        List<WxUser> list1 = new ArrayList<>();
        List<String> ids = new ArrayList<>();
        for(int i=0;i<list.size();i++){
            List<WxUserBean> beanlist = iWxUserService.getUserList(list.get(i).getDepartmentId());
            List<WxUser> list2 = new ArrayList<>();
            for(int j=0;j<beanlist.size();j++){
                WxDepartUser wxDepartUser = new WxDepartUser();
                wxDepartUser.setId(IdUtil.simpleUUID());
                wxDepartUser.setDepartmentId(list.get(i).getId());
                wxDepartUser.setUserId(beanlist.get(j).getId());
                wxDepartUser.setUserIsLeader(beanlist.get(j).getIsLeader());
                String regEx="[\\[\\],]";
                String aa = "";
                Pattern p = Pattern.compile(regEx);
                Matcher m1 = p.matcher(beanlist.get(j).getDepart());
                Matcher m2 = p.matcher(beanlist.get(j).getUserOrder());
                String depart = m1.replaceAll(aa);
                String userorder = m2.replaceAll(aa);
                char[] arr =depart.toCharArray();
                char[] arr2 =userorder.toCharArray();
                int order = 0;
                for(int a=0;a<arr.length;a++){
                    if(list.get(i).getDepartmentId() == Integer.parseInt(String.valueOf(arr[a]))){
                        order = Integer.parseInt(String.valueOf(arr2[a]));
                    }
                }
                wxDepartUser.setUserOrder(order);
                wxDepartUser.setCreateDate(new Date());
                iWxDepartUserService.save(wxDepartUser);
                WxUser wxUser = new WxUser();
                BeanUtils.copyProperties(beanlist.get(j),wxUser);
                list2.add(wxUser);
            }
            for(int k=0;k<list2.size();k++){
                if(!ids.contains(list2.get(k).getUserId())){
                    ids.add(list2.get(k).getUserId());
                    list1.add(list2.get(k));
                }
            }
        }
        List<WxUser> newlist = new ArrayList<>();
        for(int i =0;i<list1.size();i++){
            QueryWrapper<WxUser> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id",list1.get(i).getUserId());
            queryWrapper.eq("del_flag",0);
            WxUser olduser = iWxUserService.getOne(queryWrapper);
            if(olduser != null){
                WxUser wxUser = new WxUser();
                BeanUtils.copyProperties(olduser,wxUser);
                wxUser.setStatus(list1.get(i).getStatus());
                wxUser.setPosition(list1.get(i).getPosition());
                wxUser.setUpdateDate(new Date());
                iWxUserService.updateById(wxUser);
            } else {
                newlist.add(list1.get(i));
            }
        }
        List<SysUser> userList = new ArrayList<>();
        for(int i=0;i<newlist.size();i++){
            WxUser wxUser = newlist.get(i);
            SysUser sysUser  = new SysUser();
            sysUser.setId(IdUtil.simpleUUID());
            sysUser.setCreateDate(new Date());
            sysUser.setName(wxUser.getName());
            sysUser.setLoginName(wxUser.getUserId());
            sysUser.setEmail(wxUser.getEmail());
            sysUser.setHeader(wxUser.getThumbAvatar());
            sysUser.setSex(wxUser.getGender());
            sysUser.setPhone(StringUtils.isNotBlank(wxUser.getMobile())?wxUser.getMobile():wxUser.getTelephone());
            userList.add(sysUser);
            wxUser.setSysUserId(sysUser.getId());
        }
        iWxUserService.saveBatch(newlist);
        iSysUserService.saveBatch(userList);
    }
}
