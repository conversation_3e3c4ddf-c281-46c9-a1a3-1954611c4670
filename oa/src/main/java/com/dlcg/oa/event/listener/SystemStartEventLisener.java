package com.dlcg.oa.event.listener;


import com.dlcg.oa.event.SystemStartEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@EnableAsync
public class SystemStartEventLisener implements ApplicationListener<SystemStartEvent> {


    @Override
    @Async
    public void onApplicationEvent(SystemStartEvent info) {

        log.info("监听到系统启动事件！");
    }


}
