package com.dlcg.oa.mapper;

import com.dlcg.oa.entity.ReceiveBillOutApply;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
public interface ReceiveBillOutApplyMapper extends BaseMapper<ReceiveBillOutApply> {
    public String selectOutBillCompany(@Param("voyageNumber") String voyageNumber, @Param("sys_cus_sup_id") String sys_cus_sup_id);
}
