package com.dlcg.oa.mapper;

import com.dlcg.oa.entity.SysRole;
import com.dlcg.oa.entity.SysUserRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-19
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

	@Select("select b.* from sys_user_role a inner join sys_role b on a.role_id = b.id and a.del_flag = 0 and b.del_flag = 0 and a.user_id = #{userId}")
	List<SysRole> getRolesByUserId(String userId);


	@Select("select distinct b.enname from sys_user_role a inner join sys_role b on a.role_id = b.id and a.del_flag = 0 and b.del_flag = 0 and a.user_id = #{userId}")
	List<String> getRoleCodesByUserId(String userId);


	@Select("select distinct b.wx_user_id from sys_user_role a inner join sys_user b  on a.user_id = b.id  inner join sys_role c on c.id = a.role_id where c.enname = #{roleCode} ")
	List<String> getWxUserIdByRoleCode(String roleCode);
}
