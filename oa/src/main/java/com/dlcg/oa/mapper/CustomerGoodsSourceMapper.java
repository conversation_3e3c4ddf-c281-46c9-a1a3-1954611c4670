package com.dlcg.oa.mapper;

import com.dlcg.oa.entity.CustomerGoodsSource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 货源表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-21
 */
public interface CustomerGoodsSourceMapper extends BaseMapper<CustomerGoodsSource> {
    CustomerGoodsSource selectCustomerGoods(@Param("shipLineID") String shipLineID,@Param("customerID") String customerID);
    List<String> selectCustomerList(@Param("shipLineID")String shipLineID);
}
