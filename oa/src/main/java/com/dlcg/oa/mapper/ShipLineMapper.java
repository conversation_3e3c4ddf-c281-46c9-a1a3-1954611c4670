package com.dlcg.oa.mapper;

import com.dlcg.oa.bean.GoodsInGcContractBean;
import com.dlcg.oa.entity.ShipLine;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 船期表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-18
 */
public interface ShipLineMapper extends BaseMapper<ShipLine> {
    List<Map<String, Object>> selectShipOwnerByShiplineid(@Param("shipLineId") String shipLineId);
    Map<String, Object> selectContractDataZcGc(@Param("shipLineId") String shipLineId);
    List<GoodsInGcContractBean> selectContractGoodsZcGc(@Param("shipLineId") String shipLineId);
    List<Map<String, Object>> selectShipownerBankAccount(@Param("shipownerID") String shipownerID);
    Map<String, Object> selectContractDataPhGc(@Param("shipLineId") String shipLineId);
    Map<String, Object> selectCompanyInfoById(@Param("goodssourceid") String goodssourceid,@Param("costid") String costid);
    List<Map<String, Object>> selectCompanyBankAccountById(@Param("goodssourceid") String goodssourceid);
    List<GoodsInGcContractBean> selectContractGoodsPhGc(@Param("shipLineId") String shipLineId, @Param("customerid") String customerid);
    Map<String, Object> selectContractDataZcMt(@Param("shipLineId") String shipLineId);
    Map<String, Object> selectContractDataPhMt(@Param("shipLineId") String shipLineId);
    List<GoodsInGcContractBean> selectContractGoodsPhGcByCustomerId(@Param("listss") List<String> listss);

    List<ShipLine> selectStartPortsByShipLineId(@Param("shipLineIds") String shipLineIds);
}
