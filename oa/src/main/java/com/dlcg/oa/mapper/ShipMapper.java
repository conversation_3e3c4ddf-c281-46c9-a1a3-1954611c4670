package com.dlcg.oa.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dlcg.oa.entity.Ship;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dlcg.oa.entity.Shipinlist;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-21
 */
public interface ShipMapper extends BaseMapper<Ship> {
    IPage<Shipinlist> selectShipsInfo(Page<String> page, @Param("followerID")String followerID);
    List<Shipinlist> selectShipsInfo(@Param("followerID")String followerID);
    Shipinlist selectMapShipInfo(@Param("mmsi") String mmsi);
}
