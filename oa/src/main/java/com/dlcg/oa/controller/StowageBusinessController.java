package com.dlcg.oa.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dlcg.oa.base.BaseController;
import com.dlcg.oa.bean.StowageDataBean;
import com.dlcg.oa.entity.StowageData;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.oa.service.IStowageDataService;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import com.zthzinfo.common.ResponseMapBuilder;
import com.zthzinfo.libs.dictionary.mongo.MongoDictionaryService;
import com.zthzinfo.libs.dictionary.mongo.entity.MongoDictionary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.web.bind.annotation.*;

import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/stowageBusiness")
public class StowageBusinessController extends BaseController {
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    IStowageDataService stowageDataService;

    @Autowired
    MongoDictionaryService mongoDictionaryService;

    String STOWAGE_DATA_NAME="stowage_data";
    // 根据id 查询合计列表
    @GetMapping("/sumList")
    public Map<String, Object> sumList(@RequestParam(value = "ids",required = false)String ids,@RequestParam(value = "pageNum",required = false)Integer pageNum,@RequestParam(value = "pageSize",required = false)Integer pageSize){
//        if(StrUtil.isBlank(ids)){
//            return  ResponseMapBuilder.newBuilder().putSuccess()
//                    .getResult();
//        }

        Query query=new Query();
        if(StrUtil.isNotBlank(ids)){
            query.addCriteria(Criteria.where("_id").in(Arrays.stream(ids.split(",")).collect(Collectors.toList())));
        }else if(pageNum!=null && pageSize!=null){
            long skip = (long) (pageNum - 1) * pageSize;
            query.limit(pageSize);
            query.skip(skip);
        }
        query.with(Sort.by(Sort.Order.desc("orderby")));

//                Criteria.where("_id").in(Arrays.stream(ids.split(",")).collect(Collectors.toList()));


//        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "orderby")));
//        query.with(Sort.by(Sort.Order.desc("orderby")));

        List<StowageDataBean> list =  mongoTemplate.find(query, StowageDataBean.class,STOWAGE_DATA_NAME);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",list)
                .getResult();
    }
    // 保存合计
    @PostMapping("/saveSum")
    public Map<String, Object> saveSum(@RequestBody StowageDataBean stowageDataBean){

        StowageDataBean s1 =   mongoTemplate.insert(stowageDataBean,STOWAGE_DATA_NAME);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",s1)
                .getResult();
    }
    @PostMapping("/updateSum")
    public Map<String, Object> updateSum(@RequestBody StowageDataBean stowageDataBean){
        Criteria criteria=  Criteria.where("_id").is(stowageDataBean.getId());
        Query query=new Query(criteria);
        Update update=new Update();

        update.set("zzg",stowageDataBean.getZzg());
        update.set("shipName",stowageDataBean.getShipName());
        update.set("orderby",stowageDataBean.getOrderby());
        UpdateResult updateResult= mongoTemplate.updateFirst(query,update,STOWAGE_DATA_NAME);
        if(updateResult.getMatchedCount()==0){
           return saveSum(stowageDataBean);
        }
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",updateResult.getModifiedCount())
                .getResult();
    }
    @GetMapping("/removeSum")
    public Map<String, Object> removeSum(@RequestParam("id") String id){
        Query query=new Query(new Criteria("id").is(id));
        DeleteResult deleteResult= mongoTemplate.remove(query,STOWAGE_DATA_NAME);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",deleteResult.getDeletedCount())
                .getResult();
    }


    @GetMapping("/getHistoryList")
    public  Map<String, Object> getHistoryList(){
        QueryWrapper<StowageData> queryWrapper=new QueryWrapper<>();
//        queryWrapper.select(StowageData::getFlowDirection);
        queryWrapper.select("DISTINCT flow_direction").lambda().isNotNull(StowageData::getFlowDirection);
        List<Object> list= stowageDataService.listObjs(queryWrapper);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",list)
                .getResult();
    }
    @GetMapping("/sortList")
    public synchronized Map<String, Object> sortList(
            @RequestParam("id") String id,@RequestParam("mode") String mode,@RequestParam("direction") String direction,@RequestParam("num") Integer num
    ){
        if(StrUtil.isBlank(id) || num==null || num==0){
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .put("data",num)
                    .getResult();
        }
        // 排序  根据 id 查找排序值， 判断新增类型，船期为空行 货源 为船期 船名有值，新增数据 并更新所有排序值比id排序值大的数据
        StowageData stowageData=stowageDataService.getById(id);
        Long orderDef= stowageData.getOrderby();
        Criteria queryCriteria;
        // 根据数量修改排序 num=3,list: 10 9 [8] 7 6  ,direction down  10+3 9+3 [8+3](10 9 8) 7 6 direction up 10+3 9+3 (11 10 9)[8] 7 6
        LambdaUpdateWrapper<StowageData> upSort=new LambdaUpdateWrapper<>();
        if(direction!=null && direction.equals("down")){
            upSort.ge(StowageData::getOrderby,stowageData.getOrderby());
            queryCriteria = Criteria.where("orderby").gte(stowageData.getOrderby());
        }else{
            orderDef=orderDef+1;
            upSort.gt(StowageData::getOrderby,stowageData.getOrderby());
            queryCriteria = Criteria.where("orderby").gt(stowageData.getOrderby());
        }
        upSort.eq(StowageData::getDelFlag,0);
        upSort.setSql("orderby = orderby+"+num);
        stowageDataService.update(upSort);
        // 更新 合计 orderby 排序
        Query queryStowageSum=new Query(queryCriteria);
        Update updateStowageSum=new Update();
        updateStowageSum.inc("orderby",num);
        mongoTemplate.updateMulti(queryStowageSum,updateStowageSum,STOWAGE_DATA_NAME);


        List<StowageData> addList = new ArrayList<>();

        for(int i=0;i<num;i++){
            StowageData item=new StowageData();
            item.setId(IdUtil.fastSimpleUUID());
            if(mode!=null && mode.equals("ship")){
                item.setZzg(stowageData.getZzg());
                item.setSailingSchedule(stowageData.getSailingSchedule());
                item.setShipName(stowageData.getShipName());
                item.setStatus(stowageData.getStatus());
                item.setShipLineName(stowageData.getShipLineName());
            }
            // 空白不添加内容
            item.setOrderby(orderDef);
            item.setCreateId(CurrentUserInfoAdmin.getAdminUserValue().getId());
            item.setCreatetime(new Date());
            addList.add(item);
            orderDef= orderDef+1;
        }
        stowageDataService.saveBatch(addList);

        if(mode!=null && mode.equals("ship") && direction!=null && direction.equals("down")){
            // 如果向下新增 传递合计id
            String newSumId = addList.get(addList.size()-1).getId();
            oldUpNewId(id,newSumId);
        }

        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",num)
                .getResult();
    }
    private String getIfNull(String key){
        return String.format("ifnull(%s,'')", key);
    }
    private String concatKeyFmt(String ...key){
        if(key!=null && key.length>0){
            StringBuffer sb=new StringBuffer();

            for(int i=0;i< key.length;i++){
                if(sb.length()==0){
                    sb.append("concat(");
                }else{
                    sb.append(",");
                }
                sb.append(getIfNull(key[i]));
            }
            sb.append(")");
            return sb.toString();
        }
        return null;
    }

    @GetMapping("/list")
    public Map<String,Object> list(@RequestParam(value = "keyword",required = false) String keyword){
        startPage();
        LambdaQueryWrapper<StowageData> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(StowageData::getDelFlag,0);
        if(StrUtil.isNotBlank(keyword)){
            queryWrapper.apply(concatKeyFmt("zzg","sailing_schedule","ship_name","status","ship_line_name","xhg_finish_time","bship_name",
                    "customer_name","goods_type","tonnage","flow_direction","insurance","xhg","price","total_price","total_price_name","imprest_fund","remark")+" like concat('%',{0},'%')",keyword);
        }
        queryWrapper.orderByDesc(StowageData::getOrderby);
        List<StowageData> list= stowageDataService.list(queryWrapper);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",getDataTable(list))
                .getResult();

//        Query query=new Query();
////        query.with(new Sort(new Sort.Order(Sort.Direction.DESC, "orderby")));
//        query.with(Sort.by(Sort.Order.desc("orderby")));
//        List<StowageData> list =  mongoTemplate.find(query,StowageData.class);
//        return ResponseMapBuilder.newBuilder().putSuccess()
//                .put("data",list)
//                .getResult();
    }
    @PostMapping("/save")
    public Map<String, Object> save(@RequestBody List<StowageData> list){
        if(list==null || list.size()==0){
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .getResult();
        }
        // 获取集合数 排序
//        final long[] count = {mongoTemplate.count(new Query(), StowageData.class)};
//        final long[] count = {stowageDataService.count()};
//        count[0] = count[0] +list.size();
        List<StowageData> saveList =new ArrayList<>();
        List<String> saveIds = new ArrayList<>();
        list.forEach(stowageData -> {
            if(stowageData!=null && StrUtil.isNotBlank(stowageData.getId())){
                stowageData.setUpdateId(CurrentUserInfoAdmin.getAdminUserValue().getId());
                stowageData.setUpdatetime(new Date());
                stowageData.setOrderby(null);
                saveIds.add(stowageData.getId());
                saveList.add(stowageData);
            }
        });
      stowageDataService.updateBatchById(saveList);
//        boolean boo= stowageDataService.updateById(stowageData);


//        list.forEach(item->{
//            if(StrUtil.isBlank(item.getId())){
//                item.setId(IdUtil.fastSimpleUUID());
//                item.setCreateId(CurrentUserInfoAdmin.getAdminUserValue().getId());
//            }else{
//                item.setUpdateId(CurrentUserInfoAdmin.getAdminUserValue().getId());
//            }
//            saveIds.add(item.getId());
////            item.setOrderby(count[0]);
////            count[0] = count[0] -1;
//        });
//        stowageDataService.batchSaveOrUpdateList(list);
        return ResponseMapBuilder.newBuilder().putSuccess().put("data",saveIds)
                .getResult();
//        List<Pair<Query, Update>> upquery = new ArrayList<>(list.size());
//
//        list.forEach(item->{
//            if(StrUtil.isBlank(item.getId())){
//                item.setId(IdUtil.fastSimpleUUID());
//            }
//            Query query=new Query(new Criteria("id").is(item.getId()));
//            Update update=stowageUpdate(item, count[0]--);
//            Pair<Query, Update> updatePair = Pair.of(query, update);
//            upquery.add(updatePair);
//        });
//        BulkOperations bulkOperations= mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED,StowageData.class).upsert(upquery);
//        BulkWriteResult bulkWriteResult= bulkOperations.execute();
//        return ResponseMapBuilder.newBuilder().putSuccess()
//                .put("data",bulkWriteResult)
//                .getResult();
    }
    private void oldUpNewId(String oldId,String newSumId){
        if(StrUtil.isBlank(oldId) || StrUtil.isBlank(newSumId)){
            return;
        }
        Criteria criteria=  Criteria.where("_id").is(newSumId);
        Query query=new Query(criteria);
        List<StowageDataBean> isNewList= mongoTemplate.find(query,StowageDataBean.class,STOWAGE_DATA_NAME);
        if(isNewList.size()==0){
            Criteria qnew=  Criteria.where("_id").is(oldId);
            Query queryUp=new Query(qnew);
            StowageDataBean oldBean =  mongoTemplate.findOne(queryUp,StowageDataBean.class,STOWAGE_DATA_NAME);
            if(oldBean!=null){
                oldBean.setId(newSumId);
                mongoTemplate.insert(oldBean,STOWAGE_DATA_NAME);
                // 删除就数据
                mongoTemplate.remove(queryUp,STOWAGE_DATA_NAME);
            }

//                Update update=new Update();
//                update.set("_id",newSumId);
//                mongoTemplate.updateFirst(queryUp,update,STOWAGE_DATA_NAME);
        }
    }
    @PostMapping("/dellist")
    public Map<String, Object> dellist(@RequestBody Map<String,String> map){
        if(map==null || !map.containsKey("ids")){
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .getResult();
        }
        List<String> ids = Arrays.stream(map.get("ids").split(",")).collect(Collectors.toList());
        if(map.containsKey("newSumId") && StrUtil.isNotBlank( map.getOrDefault("newSumId",null))){
            // 修改新的id
            String newSumId = map.get("newSumId");
            String oldId = ids.get(ids.size()-1);
            // 判断是否存在 不存在 替换
            oldUpNewId(oldId,newSumId);
//            Criteria criteria=  Criteria.where("_id").is(newSumId);
//            Query query=new Query(criteria);
//            List<StowageDataBean> isNewList= mongoTemplate.find(query,StowageDataBean.class,STOWAGE_DATA_NAME);
//            if(isNewList.size()==0){
//                String oldId = ids.get(ids.size()-1);
//                Criteria qnew=  Criteria.where("_id").is(oldId);
//                Query queryUp=new Query(qnew);
//               StowageDataBean oldBean =  mongoTemplate.findOne(queryUp,StowageDataBean.class,STOWAGE_DATA_NAME);
//                oldBean.setId(newSumId);
//                mongoTemplate.insert(oldBean,STOWAGE_DATA_NAME);
//                // 删除就数据
//                mongoTemplate.remove(queryUp,STOWAGE_DATA_NAME);
////                Update update=new Update();
////                update.set("_id",newSumId);
////                mongoTemplate.updateFirst(queryUp,update,STOWAGE_DATA_NAME);
//            }
        }
//        Query query=new Query(new Criteria("id").in(Arrays.stream(map.get("ids").split(",")).collect(Collectors.toList())));
//        DeleteResult deleteResult= mongoTemplate.remove(query,StowageData.class);
        LambdaUpdateWrapper<StowageData> stowageDataLambdaUpdateWrapper=new LambdaUpdateWrapper<>();
        stowageDataLambdaUpdateWrapper.in(StowageData::getId,ids);
        stowageDataLambdaUpdateWrapper.set(StowageData::getDelFlag,1);
        boolean upresult = stowageDataService.update(stowageDataLambdaUpdateWrapper);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",upresult)
                .getResult();
    }

    private Update stowageUpdate(StowageData stowageData,long order){
        Update update=new Update();
//        Field[] fields= StowageData.class.getFields();
        PropertyDescriptor [] propertyDescriptors= BeanUtil.getPropertyDescriptors(StowageData.class);
        for(PropertyDescriptor f:propertyDescriptors){
            String name = f.getName();
            if(Objects.equals("id",name)){
                continue;
            }
            Object value = BeanUtil.getFieldValue(stowageData,name);
//            System.out.println("name:"+name+",value:"+value);
            update.set(name,value);
        }
        update.set("orderby",order);
        return update;
    }

    @GetMapping("/colWidths")
    public Map<String,Object> colWidths(){
      MongoDictionary mongoDictionary= mongoDictionaryService.queryDictionary("stowage_data_col_width");
    return ResponseMapBuilder.newBuilder().putSuccess()
            .put("data",mongoDictionary.getStr1())
            .getResult();
    }
    @GetMapping("/saveColWidth")
    public Map<String,Object> saveColWidth(@RequestParam(value = "width",required = false) String width,@RequestParam("colKey")String colKey){
        Query query=new Query(new Criteria("key").is("stowage_data_col_width"));
        Update update=new Update();
//        update.set("str1"+"."+colKey,width);
        // 根据colKey 修改 str1 json中的值
        update.set("str1."+colKey,width);
        UpdateResult updateResult= mongoTemplate.updateFirst(query,update,"dictionary");
        return ResponseMapBuilder.newBuilder()
                .put("data",updateResult.getMatchedCount())
                .putSuccess()
                .getResult();
    }
}
