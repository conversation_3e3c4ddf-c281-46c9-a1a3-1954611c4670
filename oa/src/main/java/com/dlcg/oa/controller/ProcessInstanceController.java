package com.dlcg.oa.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.oa.bean.ProcessInstanceBean;
import com.dlcg.oa.entity.ProcessInstance;
import com.dlcg.oa.service.IProcessInstanceService;
import com.dlcg.oa.workflow.enums.ProcessStatusEnum;
import com.zthzinfo.common.ResponseMapBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/processInstance")
public class ProcessInstanceController {
    @Autowired
    IProcessInstanceService processInstanceService;

    @RequestMapping("/getProcessInstanceOne")
    public Map<String,Object> getProcessInstanceOne(@RequestParam(value = "flowCode",required = false) String flowCode,
                                                    @RequestParam(value = "status",required = false) String status,
                                                    @RequestParam(value = "param2",required = false) String param2) {

        ProcessInstanceBean processInstanceBean = new ProcessInstanceBean();
        processInstanceBean.setFlowCode(flowCode);
        processInstanceBean.setStatus(status);
        processInstanceBean.setGlobalParam2(param2);
        ProcessInstanceBean pib= processInstanceService.getProcessInsOne(processInstanceBean);

        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",pib)
                .getResult();
    }
}
