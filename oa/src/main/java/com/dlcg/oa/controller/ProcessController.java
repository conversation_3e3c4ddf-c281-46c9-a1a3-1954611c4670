package com.dlcg.oa.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dlcg.bms.client.AdvanceReceiveClient;
import com.dlcg.bms.entity.ReceiveWater;
import com.dlcg.mobile.client.MessageClient;
import com.dlcg.mobile.enums.MessageTypeEnums;
import com.dlcg.mobile.model.ResultBody;
import com.dlcg.mobile.model.dto.MessageDto;
import com.dlcg.mobile.model.dto.MessageInfoDto;
import com.dlcg.oa.base.BaseController;
import com.dlcg.oa.bean.AdminUser;
import com.dlcg.oa.config.properties.OaProperties;
import com.dlcg.oa.entity.ProcessInstance;
import com.dlcg.oa.entity.SysRole;
import com.dlcg.oa.entity.SysUser;
import com.dlcg.oa.entity.WxUserJob;
import com.dlcg.oa.exception.AlertException;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.oa.model.page.TableDataInfo;
import com.dlcg.oa.model.po.ProcessSubmitPo;
import com.dlcg.oa.model.vo.ProcessInstanceVo;
import com.dlcg.oa.service.*;
import com.dlcg.oa.service.client.ProcessInstanceClient;
import com.dlcg.oa.util.ProcessCodeUtils;
import com.dlcg.oa.workflow.engine.ProcessEngine;
import com.dlcg.oa.workflow.enums.NodeTypeEnum;
import com.dlcg.oa.workflow.enums.ProcessStatusEnum;
import com.dlcg.oa.workflow.model.document.ProcessDefinition;
import com.dlcg.oa.workflow.model.document.ProcessNode;
import com.dlcg.oa.workflow.model.entity.ProcessApproveLog;
import com.dlcg.oa.workflow.model.entity.ProcessApproveLogBean;
import com.dlcg.oa.workflow.processor.IPostProcessor;
import com.dlcg.oa.workflow.service.RepositoryService;
import com.dlcg.tms.client.CarPaymentClient;
import com.dlcg.tms.client.InvoiceClient;
import com.dlcg.tms.client.PaymentClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageInfo;
import com.google.gson.JsonObject;
import com.zthzinfo.common.BusinessException;
import com.zthzinfo.common.IErrorInfo;
import com.zthzinfo.common.ResponseMapBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RequestMapping("/process")
@RestController
public class ProcessController extends BaseController {

    @Resource
    private ProcessEngine processEngine;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private IWxUserJobService wxUserJobService;
    @Resource
    private OaProperties oaProperties;
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private MessageClient messageClient;
    @Resource
    ISysRoleService sysRoleService;
    @Autowired
    ProcessInstanceClient processInstanceClient;
    @Autowired
    PaymentClient paymentClient;
    @Autowired
    CarPaymentClient carPaymentClient;
    @Resource
    InvoiceClient invoiceClient;
    @Autowired
    AdvanceReceiveClient advanceReceiveClient;
    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    IProcessApproveLogService iProcessApproveLogService;
    @Autowired
    ISysUserService iSysUserService;

    /**
     * 获取审批表
     * @param code 流程code
     */
    @GetMapping("/start/{code}")
    public Map<String,Object> startGet(@PathVariable("code") String code){
        ProcessDefinition processDefinition = processEngine.getRepositoryService().getProcessDefinitionByCode(code);
        if (null == processDefinition){
            throw new AlertException("code有误");
        }

        Map<String,Object> map = new HashMap<>();
        // 表单
        map.put("formSchema",processDefinition.getFormSchema());

        List<ProcessNode> process = processDefinition.getProcess();


		// 审批人
		List<String> spIds = processDefinition.getProcess().stream()
				.map(ProcessNode::getOperId)
				.filter(id -> StrUtil.isNotBlank(id))
				.distinct()
				.collect(Collectors.toList());

		// 审批角色code
		List<String> spRuleCodes = processDefinition.getProcess().stream()
				.map(ProcessNode::getRule)
				.filter(rule -> StrUtil.isNotBlank(rule))
				.distinct()
				.collect(Collectors.toList());

        // 审批id动态获取
        List<String> spIdGetter = processDefinition.getProcess().stream()
            .map(ProcessNode::getSpOperIdGetter)
            .filter(idGetter -> StrUtil.isNotBlank(idGetter))
            .distinct()
            .collect(Collectors.toList());
        // 审批角色code动态获取
        List<String> spRoleGetter = processDefinition.getProcess().stream()
            .map(ProcessNode::getSpRuleGetter)
            .filter(idGetter -> StrUtil.isNotBlank(idGetter))
            .distinct()
            .collect(Collectors.toList());

		List<SysUser> users = new ArrayList<>();
		if (spIds != null && spIds.size() > 0) {
			users = sysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getId, spIds));
            if (users == null) {
                users = new ArrayList<>();
            }
		}
		List<SysRole> rules = new ArrayList<>();
		if (spRuleCodes != null || spRuleCodes.size() > 0) {
			rules = sysRoleService.getRolesByCodes(spRuleCodes);
		}

		if (CollectionUtil.isNotEmpty(spIds) || CollectionUtil.isNotEmpty(spRuleCodes) || CollectionUtil.isNotEmpty(spIdGetter) || CollectionUtil.isNotEmpty(spRoleGetter)) {

			List<Map<String, Object>> spItem = new ArrayList<>();
			List<ProcessNode> collect = process.stream().sorted(Comparator.comparing(item -> item.getId())).collect(Collectors.toList());
			for (ProcessNode item : collect) {
				if (NodeTypeEnum.START.getKey().equals(item.getType())){
					continue;
				}
				if (NodeTypeEnum.END.getKey().equals(item.getType())) {
					continue;
				}
				Map<String, Object> s = new HashMap<>();
				s.put("id", item.getId());
				s.put("name",item.getName());
				// sp_type: 0头像、1角色名
				if (StrUtil.isNotBlank(item.getRule())) {
					s.put("sp_type", "1");
					List<SysRole> roles = rules.stream().filter(r -> Objects.equals(r.getEnname(), item.getRule())).collect(Collectors.toList());
					if (roles != null && roles.size() > 0) {
						s.put("avatar", roles.get(0).getName());
					} else {
						s.put("avatar", null);
					}
				} else if (StrUtil.isNotBlank(item.getSpOperIdGetter()) || StrUtil.isNotBlank(item.getSpRuleGetter())) {
					s.put("sp_type", "1");
					s.put("avatar", item.getName());
				} else {
					s.put("sp_type", "0");
					List<String> headers = users.stream().filter(u -> Objects.equals(u.getId(), item.getOperId())).map(SysUser::getHeader).collect(Collectors.toList());
					if (headers != null && headers.size() > 0) {
						s.put("avatar", headers.get(0));
					} else {
						s.put("avatar", null);
					}

				}
				spItem.add(s);
			}
			map.put("sp",spItem);
		}

		// 抄送人
        List<String> cc = processDefinition.getCc();
        List<String> roles = processDefinition.getCcRule();
        Set<String> ccs = new HashSet<>();
        if (CollectionUtil.isNotEmpty(cc)){
            ccs.addAll(cc);
        }
        if (CollectionUtil.isNotEmpty(roles)){
            List<String> ids = wxUserJobService.list(new LambdaQueryWrapper<WxUserJob>().in(WxUserJob::getJobId, roles))
                    .stream().map(WxUserJob::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ids)){
                ccs.addAll(ids);
            }
        }
        if (CollectionUtil.isNotEmpty(ccs)){
            List<Map<String, Object>> list = sysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getId, ccs))
                    .stream()
                    .map(item -> {
                        Map<String, Object> s = new HashMap<>();
                        s.put("id", item.getId());
                        s.put("name",item.getName());
                        s.put("avatar",item.getHeader());
                        return s;
                    }).collect(Collectors.toList());
            map.put("cc",list);
        }

        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",map)
                .getResult();
    }

    /**
     * 发起审批
     */
    @PostMapping("/start/{code}")
    public Map<String,Object> startProcess(
            @PathVariable("code") String code,
            @RequestBody Map<String,Object> valueMap
            ) throws Exception {


        Map<String,Object> formValue = (Map)valueMap.get("value");
        Map<String,Object> globalParams = (Map)valueMap.get("global");
        List<String> ccs = (List)valueMap.get("ccs");
        List<String> roles = (List)valueMap.get("roles");
        String briefContent = valueMap.containsKey("briefContent") ? (String)valueMap.get("briefContent") : null;

        // 判断是否到款核销 code receiveApprove，判断是否已核销、正在审批中 resultMsg
        // is_deduct 0 未抵扣 apply_status 未审批or审批结束
        if(StrUtil.isNotBlank(code) && globalParams!=null){
            // 付款核销 receiveApprove
            if(code.equals("receiveApprove")){
                // 根据收款账户判断
                String receiveWaterId = (String) globalParams.getOrDefault("params2","");
                if(StrUtil.isNotBlank(receiveWaterId)){
                    ReceiveWater receiveWater= advanceReceiveClient.getReceiveWaterById(receiveWaterId);
                    if(receiveWater!=null){
                        // 审批中
                        if(receiveWater.getIsDeduct().equals(0) && receiveWater.getApplyStatus().equals(0)){
                            // 正常
                        }else{
                            // 不正常
                            return  ResponseMapBuilder.newBuilder()
                                    .putBusinessException(new BusinessException(new IErrorInfo(){
                                        @Override
                                        public String getCode() {
                                            return "-2222";
                                        }

                                        @Override
                                        public String getDesc() {
                                            return "费用已核销或审批中";
                                        }
                                    }))
                                    .getResult();
                        }
                    }
                }
            }
            //如果是改账 判断费用是不是已经在审核了
            if(code.equals("updateStowage")){
                String idStrs = (String) globalParams.getOrDefault("params7","");
                if(!StringUtils.isEmpty(idStrs)){
                   String[] ids =  idStrs.split(",");
                    for (String id: ids) {
                        Integer num = iProcessInstanceService.lambdaQuery().eq(ProcessInstance::getStatus,1)
                                .eq(ProcessInstance::getFlowCode,"updateStowage")
                                .like(ProcessInstance::getGlobalParam7,id)
                                .count();
                        if(num != null && num > 0){
                            return  ResponseMapBuilder.newBuilder()
                                    .putBusinessException(new BusinessException(new IErrorInfo(){
                                        @Override
                                        public String getCode() {
                                            return "-2222";
                                        }

                                        @Override
                                        public String getDesc() {
                                            return "该费用已在审批中了，请刷新后重试";
                                        }
                                    }))
                                    .getResult();
                        }
                    }
                }
            }
        }

        String id = processEngine.getRuntimeService().startProcessInstance(code, ccs, globalParams, formValue, roles,briefContent);

        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",id)
                .getResult();
    }
    
    /**
     * 发起审批,跳到无纸化办公系统审批
     */
    @PostMapping("/startToNopage/{code}")
    public Map<String,Object> startProcessToNopage(
            @PathVariable("code") String code,
            @RequestBody Map<String,Object> valueMap
    ) throws Exception {
        String conditions = JSON.toJSONString(valueMap.get("value"));
        String processName = (String) valueMap.get("processName");
        SysUser user = iSysUserService.getById(CurrentUserInfoAdmin.getAdminUserValue().getId());
        //  抄送人 转换
        List<String> ccs = (List)valueMap.get("ccs");
        if(ccs!=null && ccs.size()>0){
            LambdaQueryWrapper<SysUser> queryWrapper=new LambdaQueryWrapper<>();
            queryWrapper.in(SysUser::getId,ccs);
            List<SysUser> ccUserList= iSysUserService.list(queryWrapper);
            List<String> wxUserIds= ccUserList.stream().map(SysUser::getWxUserId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
            valueMap.put("ccsWxUserIds",wxUserIds);
        }

        ResponseEntity<Object> map = processInstanceClient
                .startProcess(code, valueMap, user.getLoginName(),conditions, processName);
        
        LinkedHashMap resMap = (LinkedHashMap)map.getBody();
        String nopageId = (String)resMap.get("data");
        Map<String,Object> formValue = (Map)valueMap.get("value");
        Map<String,Object> globalParams = (Map)valueMap.get("global");
//        List<String> ccs = (List)valueMap.get("ccs");
        List<String> roles = (List)valueMap.get("roles");
        String briefContent = valueMap.containsKey("briefContent") ? (String)valueMap.get("briefContent") : null;
        
        String id = processEngine.getRuntimeService().saveProcessInstance(code, processName, ccs, globalParams, formValue, roles,briefContent,nopageId);
        
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",id)
                .put("nopage",nopageId)
                .getResult();
    }
    @PostMapping("/saveProcess/{code}")
    public Map<String,Object> saveProcess(
            @PathVariable("code") String code,
            @RequestBody Map<String,Object> valueMap
    ) throws Exception {
        String nopageId = (String) valueMap.get("id");
        String processName = (String) valueMap.get("processName");
        //  抄送人 转换
        List<String> ccs = (List)valueMap.get("ccs");
        Map<String,Object> formValue = (Map)valueMap.get("value");
        Map<String,Object> globalParams = (Map)valueMap.get("global");
        List<String> roles = (List)valueMap.get("roles");
        String briefContent = valueMap.containsKey("briefContent") ? (String)valueMap.get("briefContent") : null;

        String id = processEngine.getRuntimeService().saveProcessInstance(code, processName, ccs, globalParams, formValue, roles,briefContent,nopageId);

        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("processId",id)
                .put("nopageId",nopageId)
                .getResult();
    }

    /**
     * 获取审批任务列表
     */
    @GetMapping("/task/list")
    public Map<String,Object> taskList(
            @RequestParam("type") Integer type,
            @RequestParam(required = false,value = "keyword") String keyword,
            @RequestParam(required = false,value = "status") Integer status,
            @RequestParam(required = false,value = "code") String code,
            @RequestParam(required = false,value = "content") String content,
            @RequestParam(required = false,value = "ccsReadState") Integer ccsReadState

    ){
        List<ProcessInstance> personalTask = new ArrayList<>();
        LambdaQueryWrapper<ProcessInstance> newqueryWrapper  = processEngine.getTaskService().findPersonalTask(type,status,keyword,code,content, ccsReadState);
        if(newqueryWrapper != null){
            startPage();
            personalTask = iProcessInstanceService.list(newqueryWrapper);
        }
        List<String> insIds = personalTask.stream().map(ProcessInstance::getId).collect(Collectors.toList());
        if(insIds == null || insIds.size()<=0){
            TableDataInfo rspData = new TableDataInfo();
            rspData.setCode(HttpStatus.HTTP_OK);
            rspData.setRows(null);
            rspData.setTotal(0);
            return ResponseMapBuilder.newBuilder().putSuccess()
                    .put("data",rspData)
                    .getResult();
        }
        LambdaQueryWrapper<ProcessApproveLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProcessApproveLog::getProcessId,insIds);
        queryWrapper.eq(ProcessApproveLog::getApproveResult,ProcessStatusEnum.CANCEL.getKey());
        List<ProcessApproveLog> logs = iProcessApproveLogService.list(queryWrapper);
        Map<String, List<ProcessApproveLog>> map = logs.stream().collect(Collectors.groupingBy(ProcessApproveLog::getProcessId));
        // 发起人,审批人,抄送人
        List<String> uids = personalTask.stream()
                .map(ProcessInstance::getSpId)
                .distinct()
                .collect(Collectors.toList());
        List<String> suids = personalTask.stream()
                .map(ProcessInstance::getSponsorId)
                .distinct()
                .collect(Collectors.toList());
        List<String> cuids = personalTask.stream()
                .map(ProcessInstance::getCcs)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .map(item -> new ArrayList<String>(Arrays.asList(StringUtils.delimitedListToStringArray(item, ","))))
                .reduce((x, y) -> {
                    x.addAll(y);
                    return x;
                }).orElse(new ArrayList<>());
        uids.addAll(cuids);
        uids.addAll(suids);
        uids = uids.stream().distinct().collect(Collectors.toList());
        Map<String,SysUser> userCache = new HashMap<>();
        if (CollectionUtil.isNotEmpty(uids)){
            sysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getId,uids))
                    .forEach(item -> userCache.put(item.getId(),item));
        }

		List<String> roleCodes = personalTask.stream().map(ProcessInstance::getSpRoleId).filter(r -> StrUtil.isNotBlank(r)).distinct().collect(Collectors.toList());
		List<SysRole> rolesByCodes = sysRoleService.getRolesByCodes(roleCodes);

        List<String> noPageProcessIds = personalTask.stream().filter(t -> Objects.equals(t.getStatus(),ProcessStatusEnum.SPING.getKey()+"") &&
            StrUtil.isNotBlank(t.getIsNoPageId())).map(ProcessInstance::getIsNoPageId).collect(Collectors.toList());
        ResponseEntity<Object> processByIds = null;
        if(noPageProcessIds.size()>0){
            processByIds = processInstanceClient.getProcessByIds(noPageProcessIds);
        }
//        Map<String, String> processOnNoPage = (Map<String, String>)Optional.ofNullable(processByIds).map(ResponseEntity::getBody).map(body -> ((Map) body).get("data")).orElse(null);
        Map<String, ProcessInstance> caiWuProcessMap = new HashMap<>();
        Optional.ofNullable(processByIds).map(ResponseEntity::getBody)
                .map(body -> JSONObject.parseObject(JSONArray.toJSONString(body)))
                .map(item -> {
                    JSONArray data = item.getJSONArray("data");
                    if (data == null) {
                        return new ArrayList<ProcessInstance>();
                    }
                    return data.toJavaList(ProcessInstance.class);
                })
                .orElse(new ArrayList<>())
                .forEach(item -> caiWuProcessMap.put(item.getId(), item));


        AdminUser adminUserValue = CurrentUserInfoAdmin.getAdminUserValue();
        List<ProcessInstanceVo> list = personalTask.stream()
                .map(item -> {
                    SysUser sponsoruc = Optional.ofNullable(userCache.get(item.getSponsorId())).orElse(new SysUser());
                    SysUser spuc = Optional.ofNullable(userCache.get(item.getSpId())).orElse(new SysUser());
                    ProcessInstanceVo vo = new ProcessInstanceVo();
                    vo.setFlowName(item.getFlowName());
                    vo.setFlowCode(item.getFlowCode());
                    vo.setParam1(item.getGlobalParam1());
                    vo.setProcessId(item.getId());
                    vo.setSponsorTime(item.getSponsorTime());
                    vo.setFinishTime(item.getFinishTime());
                    vo.setIsNoPageId(item.getIsNoPageId());
                    vo.setSponsorName(sponsoruc.getName());
                    if(Objects.equals(Integer.parseInt(item.getStatus()),ProcessStatusEnum.CANCEL.getKey())){
                        List<ProcessApproveLog> logList = map.get(item.getId());
                        if(logList != null &&logList.size()>0){
                            ProcessApproveLog processApproveLog = logList.get(0);
                            vo.setReason(processApproveLog.getApproveComment());
                        }
                    }
                    if (StrUtil.isNotBlank(item.getSpRoleId())) {
						List<String> roleNames = rolesByCodes.stream().filter(r -> Objects.equals(r.getEnname(), item.getSpRoleId())).map(SysRole::getName).collect(Collectors.toList());
						if (roleNames != null && roleNames.size() > 0) {
							vo.setOperName(roleNames.get(0));
						}

					} else {
						vo.setOperName(spuc.getName());
					}
                    vo.setSponsorAvatar(sponsoruc.getHeader());
                    vo.setBriefContent(item.getBriefContent());
                    vo.setStatus(ProcessStatusEnum.getDescByKey(Integer.parseInt(item.getStatus())));
                    vo.setStatusId(Integer.parseInt(item.getStatus()));
                    vo.setNodeId(item.getStatusId());
                    if (ProcessStatusEnum.SPING.getKey() == Integer.parseInt(item.getStatus())){
                        // 判断节点 是否 出纳 会计
                        if(item.getStatusId()!=null && item.getStatusId()/10*10==80){
                            vo.setShowStatus(vo.getOperName() + " 会计做账中");
                        }else{
                            vo.setShowStatus(vo.getOperName() +" "+ ProcessStatusEnum.getDescByKey(Integer.parseInt(item.getStatus())));

                        }
                    }else{
                        vo.setShowStatus(ProcessStatusEnum.getDescByKey(Integer.parseInt(item.getStatus())));
                    }

                    if (Objects.equals(item.getStatus(),ProcessStatusEnum.SPING.getKey()+"") && StrUtil.isNotBlank(item.getIsNoPageId())) {
                        ProcessInstance caiWuProcessInstance = caiWuProcessMap.get(item.getIsNoPageId());
                        if (caiWuProcessInstance != null) {
                            vo.setShowStatus(caiWuProcessInstance.getStatus());
                            if (caiWuProcessInstance.getFinishTime() != null) {
                                vo.setFinishTime(caiWuProcessInstance.getFinishTime());
                            }
                        }
                    }
                    //我已处理的时候，要显示自己处理的时间 lijl
                    if(type == 2){
                        List<ProcessApproveLog> process_approve_log_list = iProcessApproveLogService.lambdaQuery()
                                .eq(ProcessApproveLog::getSpId, adminUserValue.getId())
                                .eq(ProcessApproveLog::getProcessId, vo.getProcessId())
                                .ne(ProcessApproveLog::getNodeId, NodeTypeEnum.START.getCode())
                               // .eq(ProcessApproveLog::getApproveResult, ProcessStatusEnum.AGREE.getKey())
                                .list();
                        if(process_approve_log_list != null && process_approve_log_list.size() > 0){
                            ProcessApproveLog processApproveLog = process_approve_log_list.get(0);
                            vo.setSelfAuditSucceedTime(processApproveLog.getApproveTime());
                        }
                    }
                    //设置抄送人审批已读状态
                    if (4 == type ) {
                        if (StrUtil.isBlank(item.getCcsReadState())) {
                            vo.setCcsReadState(0);
                        } else if ("1".equals(item.getCcsReadState())) {
                            vo.setCcsReadState(1);
                        } else {
                            JSONObject crsJo = JSONObject.parseObject(item.getCcsReadState());
                            /* 判断已读 */
                            if (crsJo.containsKey(CurrentUserInfoAdmin.getAdminUserValue().getId()) && 1 == crsJo.getIntValue(CurrentUserInfoAdmin.getAdminUserValue().getId())) {
                                vo.setCcsReadState(1);
                            } else {
                                vo.setCcsReadState(0);
                            }
                        }
                    } else {
                        vo.setCcsReadState(1);
                    }

                    return vo;
                })
                .collect(Collectors.toList());

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(personalTask).getTotal());
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",rspData)
                .getResult();
    }


    /**
     * 获取审批任务
     */
    @GetMapping("/task/{id}")
    public Map<String,Object> taskGet(@PathVariable("id") String id) throws JsonProcessingException {
        Map<String, Object> map = processEngine.getTaskService().findTask(id);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",map)
                .getResult();
    }
    
    @RequestMapping("/getNoPageTaskById")
    public Map<String,Object> getNoPageTaskById(@RequestParam(value = "processId", required = false) String processId){
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        SysUser user = sysUserService.getById(adminUser.getId());
        ResponseEntity<Object> map = processInstanceClient.taskGet(processId,user.getLoginName());
        LinkedHashMap mapBody = (LinkedHashMap) map.getBody();
        Map data = (Map) mapBody.get("data");
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",data)
                .getResult();
    }
    

    /**
     * 处理审批任务
     */
    @PostMapping("/task/process")
    public Map<String,Object> taskProcess(
            @RequestBody ProcessSubmitPo po
    ) throws Exception {
    	if (po.getNodeId() == null) {
			throw new AlertException("缺少nodeid参数");
		}
        int type = po.getType();
        if(type == 2){ //审批时 如果通过  不能 有不符合的字样
            String str = "无法核对,不符,有误,不对,不清楚,不知道,不可";
            if(!StringUtils.isEmpty(po.getComment())){
                String[] split = str.split(",");
                String comment = po.getComment();
                for (String s : split) {
                    if(comment.equals(s)){
                        throw new AlertException("审批意见中不能是 "+comment);
                    }
                }
            }
        }
        processEngine.getTaskService().complete(po);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .getResult();
    }

    @GetMapping("/task/point")
    public Map<String,Object> taskPoint(){
        String id = CurrentUserInfoAdmin.getAdminUserValue().getId();
        Map<String,Integer> map  = processEngine.getTaskService().getPointCount(id);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",map)
                .getResult();
    }

    @GetMapping("/select")
    public Map<String,Object> select(){
        return ResponseMapBuilder.newBuilder().putSuccess()
                .put("data",processEngine.getRepositoryService().getProcessSelectCode())
                .getResult();
    }
    @Autowired
    IProcessInstanceService iProcessInstanceService;
    //发送回执单提交消息
    @RequestMapping("/sendMessage")
    public Map<String,Object> sendMessage(@RequestParam(value = "processId", required = false) String processId){
        ProcessInstance instance = iProcessInstanceService.getById(processId);
        ProcessDefinition definition = repositoryService.getProcessDefinitionByCode(instance.getFlowCode());
        if (null == instance){
            log.warn("实例为空");
        }
        List<String> spIds = definition.getProcess().stream()
                .map(ProcessNode::getOperId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        spIds.add(instance.getSponsorId());
        Map<String, SysUser> userCache = new HashMap<>();
        sysUserService.listByIds(spIds)
                .forEach(item -> userCache.put(item.getId(),item));

        if(!userCache.containsKey(instance.getSponsorId())){
            log.warn("找不到发起者：{}",instance.getSponsorId());
        }
        if(!userCache.containsKey(instance.getSpId())){
            log.warn("找不到审批者：{}",instance.getSpId());
        }

        if (StrUtil.isBlank(userCache.get(instance.getSpId()).getWxUserId())){
            log.warn("审批者没有userid,{}",instance.getSpId());
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分");
        String time = simpleDateFormat.format(instance.getSponsorTime());

        List<String> wxUserIds = new ArrayList<>();
        for (String id : spIds) {
            wxUserIds.add(userCache.get(id).getWxUserId());
        }
        MessageDto messageDto = new MessageDto();
        messageDto.setMessageType(MessageTypeEnums.TASKCARD.getCode());
        messageDto.setToUser(wxUserIds);
        messageDto.setTitle(String.format("%s%s提交的%s,已上传回执单",userCache.get(instance.getSponsorId()).getName(),time,Optional.ofNullable(definition).map(ProcessDefinition::getName).orElse("审批")));
        messageDto.setDescription(StrUtil.isBlank(instance.getBriefContent()) ? "--" : instance.getBriefContent());
//        messageDto.setUrl(String.format("%s/approval-detail?processId=%s",oaProperties.getMobileUrl(),instance.getId()));
        // 为避免TaskId重复，参数格式：processId + "_" + 当前时间戳
        messageDto.setTaskId(instance.getId() + "_" + System.currentTimeMillis());

        try {
            ResultBody resultBody = messageClient.send(messageDto);
            if(!resultBody.isSuccess()){
                System.out.println(resultBody.getMsg());
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return ResponseMapBuilder.newBuilder().putSuccess()
                .getResult();
    }



    @RequestMapping("/getSpPerspnNoPage")
    public Map<String,Object> getSpPerspnNoPage(@RequestParam(value = "noPageProcessId", required = false) String noPageProcessId){
        ResponseEntity<Object> spPerspn = processInstanceClient.getSpPerspn(noPageProcessId);
        return (Map<String, Object>)Optional.ofNullable(spPerspn).map(ResponseEntity::getBody).map(body -> ((Map) body)).orElse(null);
    }

    @RequestMapping("/getSpPerspn")
    public Map<String,Object> getSpPerspn(@RequestParam(value = "processId", required = false) String processId){
        List<ProcessApproveLogBean> newlist = iProcessApproveLogService.getSpPerspnListByProcessId(processId);
//        LambdaQueryWrapper<ProcessApproveLog> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(ProcessApproveLog::getProcessId,processId);
//        queryWrapper.orderByAsc(ProcessApproveLog::getNodeId);
//        List<ProcessApproveLog> list = iProcessApproveLogService.list(queryWrapper);
//        List<ProcessApproveLogBean> newlist = new ArrayList<>();
//        if(list != null && list.size()>0){
//            List<String> ids = list.stream().map(ProcessApproveLog::getSpId).collect(Collectors.toList());
//            LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
//            wrapper.in(SysUser::getId,ids);
//            List<SysUser> userList = iSysUserService.list(wrapper);
//            Map<String,SysUser> map = new HashMap<>();
//            for(SysUser sysUser:userList){
//                map.put(sysUser.getId(),sysUser);
//            }
//
//            for(ProcessApproveLog processApproveLog:list){
//                ProcessApproveLogBean processApproveLogBean = new ProcessApproveLogBean();
//                BeanUtils.copyProperties(processApproveLog,processApproveLogBean);
//                processApproveLogBean.setUserName(map.get(processApproveLogBean.getSpId()).getName());
//                newlist.add(processApproveLogBean);
//            }
//        }
        return ResponseMapBuilder
                .newBuilder()
                .putSuccess()
                .put("list",newlist)
                .getResult();
    }

    @Autowired
    private Map<String, IPostProcessor> processorMap;

    @RequestMapping("/cancelApply")
    public Map<String,Object> cancelApply(
            @RequestParam(value = "processId", required = false) String processId,
            @RequestParam(value = "reason", required = false) String reason
    ){

        ProcessInstance processInstance = iProcessInstanceService.getById(processId);
        // 审批中流程 不能撤回
//        QueryWrapper<ProcessApproveLog> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("process_id",processId);
//        long count = iProcessApproveLogService.count(queryWrapper);
//        if(count > 0){
//            return ResultBody.failed().msg("审批中流程 不能撤回");
//        }
//        ProcessDefinition definition = repositoryService.getProcessDefinitionByCode(processInstance.getFlowCode());
//        SysUser user = sysUserService.getById(processInstance.getSponsorId());
//
//        String spId = processInstance.getSpId();
//        String spRole = processInstance.getSpRoleId();
//        if(StrUtil.isNotBlank(spId)){
//            SysUser sysUser = sysUserService.getById(spId);
//            MessageDto messageDto = new MessageDto();
//            messageDto.setMessageType(MessageTypeEnums.TEXT.getCode());
//            messageDto.setToUser(Arrays.asList(sysUser.getWxUserId()));
//            messageDto.setContent(String.format("%s提交的%s已被撤回", user.getName(),Optional.ofNullable(definition).map(ProcessDefinition::getName).orElse("审批")));
////            messageDto.setDescription(StrUtil.isBlank(processInstance.getBriefContent()) ? "--" : processInstance.getBriefContent());
////            messageDto.setUrl(String.format("%s/approval-detail?processId=%s",oaProperties.getMobileUrl(),processInstance.getId()));
//            try {
////                ResultBody resultBody = messageClient.send(messageDto);
////                if(!resultBody.isSuccess()){
////                    System.out.println(resultBody.getMsg());
////                }
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//        }
//        if(StrUtil.isNotBlank(spRole)){
//            List<SysUser> list = iSysUserService.selectByRoleId(spRole);
//            List<String> ids = list.stream().map(SysUser::getWxUserId).collect(Collectors.toList());
//            MessageDto messageDto = new MessageDto();
//            messageDto.setMessageType(MessageTypeEnums.TEXT.getCode());
//            messageDto.setToUser(ids);
//            messageDto.setContent(String.format("%s提交的%s已被撤回", user.getName(),Optional.ofNullable(definition).map(ProcessDefinition::getName).orElse("审批")));
//            //            messageDto.setDescription(StrUtil.isBlank(processInstance.getBriefContent()) ? "--" : processInstance.getBriefContent());
//            //            messageDto.setUrl(String.format("%s/approval-detail?processId=%s",oaProperties.getMobileUrl(),processInstance.getId()));
//            try {
////                ResultBody resultBody = messageClient.send(messageDto);
////                if(!resultBody.isSuccess()){
////                    System.out.println(resultBody.getMsg());
////                }
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//        }
    
        ProcessApproveLog approveLog = new ProcessApproveLog();
        approveLog.setProcessId(processId);
        approveLog.setApproveComment(reason);
        approveLog.setApproveTime(new Date());
        approveLog.setApproveResult(ProcessStatusEnum.CANCEL.getKey());
        approveLog.setSpId(processInstance.getSponsorId());
        approveLog.setNodeId(NodeTypeEnum.START.getCode());
        iProcessApproveLogService.save(approveLog);
        
        LambdaUpdateWrapper<ProcessInstance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProcessInstance::getId,processId).set(ProcessInstance::getStatus,ProcessStatusEnum.CANCEL.getKey());
        iProcessInstanceService.update(updateWrapper);
        
        //挂账审批手动撤回时，更改船期挂账状态
//        if("onAccount".equals(processInstance.getFlowCode())){
//            if(processInstance.getGlobalParam1().equals("onAccountAccepCost")){
//                Map<String, Object> result = paymentClient.onAccountAccepCost(processInstance.getGlobalParam2(),"reject",null);
//            }else{
//                Map<String, Object> result = paymentClient.onAccount(processInstance.getGlobalParam1(),processInstance.getGlobalParam2(),"reject");
//            }
//        }
        //汽运挂账审批手动撤回时，更改汽运挂账状态
//        if("carOnAccount".equals(processInstance.getFlowCode())){
//            Map<String, Object> result = carPaymentClient.onCarAccount(processInstance.getGlobalParam1(),processInstance.getGlobalParam2(),"reject");
//        }
        //其他费用挂账审批手动撤回时，更改船期挂账状态
//        if("onAccountOtherCost".equals(processInstance.getFlowCode())){
//            Map<String, Object> result = paymentClient.onAccountOtherCost(processInstance.getGlobalParam2(),"reject",null);
//        }
        // 进票申请手动撤回时，更改进票状态
//        if ("ticketApprove".equals(processInstance.getFlowCode())) {
//            invoiceClient.renewInvoiceCost(processInstance.getGlobalParam3());
//            paymentClient.updateTicketCost(processInstance.getGlobalParam1(),"4");
//        }
        // 预付款撤回 更改账户审批状态
//        if("paymentApprove".equals(processInstance.getFlowCode()) && StrUtil.isNotBlank(processInstance.getGlobalParam7()) && ProcessCodeUtils.ADVANCE_PREPAYMENT_CODE.equals(processInstance.getGlobalParam7())){
//            // 有充值记录 修改充值状态
//            // 无充值记录 修改账户状态
//            //  同步的财务系统
//            if(StrUtil.isNotBlank(processInstance.getIsNoPageId())){
//                processInstanceClient.cancelApply(processInstance.getIsNoPageId(),reason);
//            }
//        }
        // 全部撤回[sungf 2022-05-17]
        if(StrUtil.isNotBlank(processInstance.getIsNoPageId())){
            processInstanceClient.cancelApply(processInstance.getIsNoPageId(),reason);
        }
        processInstance.setStatus(ProcessStatusEnum.CANCEL.getKey()+"");

        /* 因为上面已经有两个流程（挂账、其他费用挂账）手动处理完成的业务了，
           这里就不敢让全部流程都执行finishHandle了，容易出现重复处理的情况，
           所以未来根据需求自行添加需要处理的业务流程编号在这个数组里就可以了

           流程：（每加一个要完善注释）
           1. receiveApprove: 收款核销流程
           2. billOutApprove: 开票申请
         */
//        String[] otherProcesses = new String[]{ "receiveApprove", "billOutApprove", "updateStowage" };
//        if (ArrayUtil.contains(otherProcesses, processInstance.getFlowCode())) {
        if(StrUtil.isNotBlank(processInstance.getFlowCode())){
            log.info("流程撤回处理完成，开始执行finishHandle");
            ProcessDefinition processDefinition = repositoryService.getProcessDefinitionByCode(processInstance.getFlowCode());
            IPostProcessor postProcessor = processorMap.get(processDefinition.getAfterEveryNode());
            if (postProcessor != null) {
                postProcessor.finishHandle(processInstance);
            }
        }
        // 撤回消息推送
        List<MessageInfoDto> messages = Optional.ofNullable(stringRedisTemplate.opsForSet().members("sendMessageOverDue"))
                .orElse(new HashSet<>()).stream()
                .map(item -> JSON.parseObject(item, MessageInfoDto.class))
                .filter(item -> StrUtil.equals(item.getProcessId(), processInstance.getId()))
                .collect(Collectors.toList());
        for (MessageInfoDto item : messages) {
            // 撤回已发消息
            messageClient.recallMsg(item);
            // 删除redis当前code
            String messageStr = JSON.toJSONString(item);
            stringRedisTemplate.opsForSet().remove("sendMessageOverDue",messageStr);
        }
    
        return ResponseMapBuilder.newBuilder().putSuccess()
                .getResult();
    }

    /**
     * 流程催办
     * @return
     */
    @PostMapping("/reminder")
    public ResultBody reminder(String processId,boolean isNoPage) {
        if (isNoPage) {
            // 综合管控催办
            processInstanceClient.reminder(processId);
            return ResultBody.ok();
        }
        processEngine.getTaskService().reminder(processId);
        return ResultBody.ok();
    }
    @GetMapping("/getSpLogList")
    public Map<String,Object> getSpLogList(@RequestParam(required = false,value = "processId") String processId){
        List<ProcessApproveLogBean> list = iProcessApproveLogService.getSpPerspnListByProcessId(processId);
        return ResponseMapBuilder.newBuilder()
                .put("data",list)
                .putSuccess()
                .getResult();
    }
    // 查询结束时间
    @GetMapping("/getFinishTime")
    public Map<String,Object> getFinishTime(@RequestParam(required = false,value = "processId") String processId){
        ProcessInstance processInstance = iProcessInstanceService.getById(processId);
        return ResponseMapBuilder.newBuilder()
                .put("data",processInstance.getFinishTime())
                .putSuccess()
                .getResult();
    }

    /**
     * 设置抄送人已读审批流程
     * @param processId
     * @return
     */
    @GetMapping("/ccsRead")
    public Map<String,Object> ccsRead(@RequestParam(required = false,value = "processId") String processId){
        iProcessInstanceService.ccsRead(processId);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }
}
