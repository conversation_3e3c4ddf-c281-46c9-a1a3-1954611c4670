package com.dlcg.oa.controller;


import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dlcg.oa.bean.AdminUser;
import com.dlcg.oa.bean.OAException;
import com.dlcg.oa.bean.RoleMenusBean;
import com.dlcg.oa.bean.SysMenuBean;
import com.dlcg.oa.bean.SysUserBean;
import com.dlcg.oa.config.properties.OaProperties;
import com.dlcg.oa.constants.OaConstants;
import com.dlcg.oa.entity.*;
import com.dlcg.oa.exception.AlertException;
import com.dlcg.oa.interceptor.CurrentUserInfoAdmin;
import com.dlcg.oa.interceptor.UserMenuPermission;
import com.dlcg.oa.service.*;
import com.dlcg.oa.wechat.cp.config.WxCpConfiguration;
import com.zthzinfo.common.ResponseMapBuilder;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxError;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import org.apache.catalina.User;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
@Slf4j
@RestController
@RequestMapping("/sysUser")
public class SysUserController {


    @Autowired
    ISysUserService iSysUserService;

    @Value("${user.login.expire:5}")
    private Long loginExpire;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    ISysMenuService iSysMenuService;

    @Autowired
    ISysRoleService sysRoleService;

    @Autowired
    ISysUserRoleService sysUserRoleService;
    @Autowired
    ISysRoleMenuService iSysRoleMenuService;

    @Resource
    private OaProperties oaProperties;
    @Resource
    private IDlcgCompanyService dlcgCompanyService;
    @Resource
    private IQywxApplicationService qywxApplicationService;

    @RequestMapping("/getUserMenu")
    public Map<String, Object> getUserMenu() {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();

        List<SysMenuBean> beans = iSysMenuService.getMenusByUserId(adminUser.getId());

        return ResponseMapBuilder.newBuilder()
                .put("beans",beans)
                .putSuccess()
                .getResult();
    }


    @PostMapping("/getUserListByIds")
    public Map<String, Object> getUserListByIds(@RequestParam(value = "ids") List<String> ids) {
        if (ids == null || ids.size() == 0) {
            return ResponseMapBuilder.newBuilder().putSuccess().getResult();
        }
        List<SysUser> list = iSysUserService.list(new LambdaQueryWrapper<SysUser>().in(SysUser::getId, ids));
        return ResponseMapBuilder.newBuilder().putSuccess().put("list", list).getResult();
    }


    @PostMapping("/getUserListByRoleCodes")
    public Map<String, Object> getUserListByRoleCodes(@RequestParam(value = "roles") List<String> roles) {
        if (roles == null || roles.size() == 0) {
            return ResponseMapBuilder.newBuilder().putSuccess().getResult();
        }

        List<SysUser> list = iSysUserService.selectByRoleCodes(roles);
        return ResponseMapBuilder.newBuilder().putSuccess().put("list", list).getResult();
    }

    @RequestMapping("/getUserList")
    @UserMenuPermission("/userlist")
    public Map<String, Object> getUserList(
            @RequestParam(value = "pageNo",required = false) Integer pageNo,
            @RequestParam(value = "pageSize",required = false) Integer pageSize
    ) {

        if(pageNo==null||pageNo<1){
            pageNo = 1;
        }
        if(pageSize==null||pageSize<0){
            pageSize = 10;
        }

        IPage<SysUser> page =  iSysUserService.getUserList(pageNo,pageSize);

        return ResponseMapBuilder.newBuilder()
                .put("page",page)
                .putSuccess()
                .getResult();
    }

    @RequestMapping("/getUserInfo")
    public Map<String, Object> userinfo() {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        adminUser.setPassword(null);
        return ResponseMapBuilder.newBuilder()
                .put("user",adminUser)
                .putSuccess()
                .getResult();
    }
    
    @RequestMapping("/wxUserId")
    public Map<String, Object> wxUserId() {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        String wxId = adminUser.getWxUserId();
        return ResponseMapBuilder.newBuilder()
                .put("data",wxId)
                .putSuccess()
                .getResult();
    }
    @RequestMapping("/wxlogin")
    public Map<String, Object> wxlogin(@RequestParam(value = "loginName",required = false) String loginName,
                                       @RequestParam(value = "url",required = false) String url){
        log.info("wxlogin");
        log.info(loginName);
        log.info(url);
        if(StrUtil.isBlank(loginName) || StrUtil.isBlank(url)){
            return ResponseMapBuilder.newBuilder()
                    .put("msg", "无相关参数")
                    .putSuccess()
                    .getResult();
        }
        // 判断是否有url 权限
        LambdaQueryWrapper<SysMenu> menuLambdaQueryWrapper=new LambdaQueryWrapper<>();
        menuLambdaQueryWrapper.and(warpper->warpper.eq(SysMenu::getHref,url).or().eq(SysMenu::getHref,"/"+url));
        menuLambdaQueryWrapper.and(warper->warper.eq(SysMenu::getDelFlag,0));
        List<SysMenu> sysMenus= iSysMenuService.list(menuLambdaQueryWrapper);
        if(sysMenus==null || sysMenus.size()==0){
            return ResponseMapBuilder.newBuilder()
                    .put("msg", "无相关地址")
                    .putSuccess()
                    .getResult();
        }
        SysMenu sysMenu=sysMenus.get(0);
        LambdaQueryWrapper<SysRoleMenu> sysRoleMenuLambdaQueryWrapper=new LambdaQueryWrapper<>();
        sysRoleMenuLambdaQueryWrapper.eq(SysRoleMenu::getMenuId,sysMenu.getId());
        sysRoleMenuLambdaQueryWrapper.eq(SysRoleMenu::getDelFlag,0);
        List<SysRoleMenu> sysRoleMenus= iSysRoleMenuService.list(sysRoleMenuLambdaQueryWrapper);
        if(sysRoleMenus==null || sysMenus.size()==0){
            return ResponseMapBuilder.newBuilder()
                    .put("msg", "无相关目录")
                    .putSuccess()
                    .getResult();
        }

        LambdaQueryWrapper<SysUser> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUser::getWxUserId,loginName);
        queryWrapper.eq(SysUser::getDelFlag,0);
        List<SysUser> users= iSysUserService.list(queryWrapper);
        if(users==null || users.size()==0){
            return ResponseMapBuilder.newBuilder()
                    .put("msg", "无相关用户")
                    .putSuccess()
                    .getResult();
        }
        SysUser sysUser=users.get(0);
        String token = IdUtil.simpleUUID();

        List<String> userRole = sysRoleService.getUserRole(sysUser.getId());

        // 用户权限 比对 菜单角色
        boolean isAuth=false;
        List<SysRole> sysRoles= sysRoleService.getRolesByCodes(userRole);
        for (SysRole sysRole : sysRoles){
            if(isAuth){
                break;
            }
            for(SysRoleMenu sysRoleMenu:sysRoleMenus){
                if(Objects.equals(sysRoleMenu.getRoleId(),sysRole.getId())){
                    isAuth=true;
                    break;
                }
            }
        }
        if(!isAuth){
            return ResponseMapBuilder.newBuilder()
                    .put("msg", "无相关权限")
                    .putSuccess()
                    .getResult();
        }


        AdminUser adminUser = new AdminUser();
        BeanUtils.copyProperties(sysUser,adminUser);
        setRoleMenuIds(adminUser);
        stringRedisTemplate.opsForValue().set(AdminUser.ADMIN_PRE_USERINFO+token, JSON.toJSONString(adminUser), loginExpire, TimeUnit.DAYS);
        return ResponseMapBuilder.newBuilder()
                .put("usertoken", token)
                .put("user",userRole)
                .putSuccess()
                .getResult();
    }

    @RequestMapping("/login")
    public Map<String, Object> login(
            @RequestParam(value = "loginName",required = false) String loginName,
            @RequestParam(value = "passWord",required = false) String passWord,
            HttpServletRequest request) {


        SysUser sysUser = iSysUserService.login(loginName,passWord);

        if (sysUser == null) {
            return ResponseMapBuilder.newBuilder()
                    .put(OAException.PASSWORD_ERROR.getCode(), OAException.PASSWORD_ERROR.getDesc())
                    .putSuccess()
                    .getResult();
        }

        String token = IdUtil.simpleUUID();

        List<String> userRole = sysRoleService.getUserRole(sysUser.getId());

        AdminUser adminUser = new AdminUser();
        BeanUtils.copyProperties(sysUser,adminUser);
        setRoleMenuIds(adminUser);
        stringRedisTemplate.opsForValue().set(AdminUser.ADMIN_PRE_USERINFO+token, JSON.toJSONString(adminUser), loginExpire, TimeUnit.DAYS);


    //        String oldtoken = stringRedisTemplate.opsForValue().get(AdminUser.ADMIN_PRE_TOKEN + adminUser.getId());
    //        if(oldtoken!=null&&oldtoken.trim().length()>0){
    //            //移除上一次登录产生的缓存
    //            stringRedisTemplate.delete(AdminUser.ADMIN_PRE_USERINFO+oldtoken);
    //        }

        //记录用户对应的sid值，实现账号登录相互挤掉功能
//        stringRedisTemplate.opsForValue().set(AdminUser.ADMIN_PRE_TOKEN + adminUser.getId(),token, loginExpire, TimeUnit.DAYS);

        return ResponseMapBuilder.newBuilder()
                .put("usertoken", token)
                .put("user",userRole)
                .putSuccess()
                .getResult();
    }

    private void setRoleMenuIds(AdminUser adminUser){
        List<RoleMenusBean> list = iSysUserService.getRoleMenusByUserId(adminUser.getId());
        if(list!=null&&list.size()>0){
            Set<String> menuHrefs = new HashSet<>();
            Set<String> roleEnnames = new HashSet<>();
            Set<String> roleIds = new HashSet<>();
            for(int i=0;i<list.size();i++){
                RoleMenusBean bean = list.get(i);
                if(bean.getMenuhref()!=null&&bean.getMenuhref().trim().length()>0){
                    menuHrefs.add(bean.getMenuhref());
                }
                if(bean.getRolename()!=null&&bean.getRolename().trim().length()>0){
                    roleEnnames.add(bean.getRolename());
                }
                if(bean.getRoleid()!=null&&bean.getRoleid().trim().length()>0){
                    roleIds.add(bean.getRoleid());
                }

            }
            adminUser.setRoleIds(roleIds);
            adminUser.setMenuHrefs(menuHrefs);
            adminUser.setRoleEnnames(roleEnnames);
        }
    }



    @RequestMapping("/updateUser")
    @UserMenuPermission("/userlist")
    public Map<String, Object> updateUser(
            @ModelAttribute SysUser sysUser
    ) {
        if(sysUser.getPassword()!=null&&sysUser.getPassword().trim().length()<=0){
            sysUser.setPassword(null);
        }
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        sysUser.setUpdateBy(adminUser.getId());
        sysUser.setUpdateDate(new Date());
        iSysUserService.updateById(sysUser);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }
    @RequestMapping("/delUser")
    @UserMenuPermission("/userlist")
    public Map<String, Object> delUser(
            @RequestParam(value = "userid",required = false) String userid
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        SysUser sysUser = new SysUser();
        sysUser.setId(userid);
        sysUser.setUpdateBy(adminUser.getId());
        sysUser.setUpdateDate(new Date());
        sysUser.setDelFlag("1");
        iSysUserService.updateById(sysUser);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @RequestMapping("/getAllUser")
    public Map<String, Object> getAllUser() {
        
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag","0");
        queryWrapper.orderByDesc("create_date");
        List<SysUser> list =  iSysUserService.list(queryWrapper);
        ResponseMapBuilder responseMapBuilder = ResponseMapBuilder.newBuilder();
        // 查询单证人员
        List<SysUser> danzheng =iSysUserService.selectDanZheng();
        List<SysUser> shipDanzheng =iSysUserService.selectShipDanZheng();
        List<SysUserBean> listyewu = new ArrayList<>();
        //  查询货物业务人员
        List<SysUser> goodslistyewu= iSysUserService.selectSource();
        for(SysUser sysUser:goodslistyewu){
            if(sysUser==null){
                continue;
            }
            SysUserBean sysUserBean = new SysUserBean();
            BeanUtils.copyProperties(sysUser,sysUserBean);
            sysUserBean.setSource("goods");
            listyewu.add(sysUserBean);
        }
        // 查询船舶 业务人员
        List<SysUser> shiplistyewu= iSysUserService.selectShip();
        for(SysUser sysUser:shiplistyewu){
            if(sysUser==null){
                continue;
            }
            SysUserBean sysUserBean = new SysUserBean();
            BeanUtils.copyProperties(sysUser,sysUserBean);
            sysUserBean.setSource("ship");
            listyewu.add(sysUserBean);
        }
        //查找汽运 单证员
        List<SysUser> carlistdanzheng = iSysUserService.selectCarDanZheng();
        List<SysUser> carlistdanzheng_new = new ArrayList<>();
        if(carlistdanzheng != null && carlistdanzheng.size() > 0){
            for (SysUser sysUser : carlistdanzheng) {
                if (sysUser == null) {
                    continue;
                }
                SysUserBean sysUserBean = new SysUserBean();
                BeanUtils.copyProperties(sysUser, sysUserBean);
                sysUserBean.setSource("cardanzheng");
                carlistdanzheng_new.add(sysUserBean);
            }
        }
        //查找汽运 业务员
        List<SysUser> carlistyewu = iSysUserService.selectCarYeWu();
        List<SysUser> carlistyewu_new = new ArrayList<>();
        if(carlistyewu != null && carlistyewu.size() > 0){
            for (SysUser sysUser : carlistyewu) {
                if(sysUser == null){
                    continue;
                }
                SysUserBean sysUserBean = new SysUserBean();
                BeanUtils.copyProperties(sysUser, sysUserBean);
                sysUserBean.setSource("caryewu");
                carlistyewu_new.add(sysUserBean);
            }
        }
        return responseMapBuilder
                .put("list",list)
                .put("listyewu",listyewu)
                .put("danzhengList",danzheng)
                .put("shipDanzhengList", shipDanzheng)
                .put("carDanzhengList", carlistdanzheng_new)
                .put("carYewuList", carlistyewu_new)
                .putSuccess()
                .getResult();
    }

    @RequestMapping("/saveUser")
    @UserMenuPermission("/userlist")
    public Map<String, Object> saveUser(
            @ModelAttribute SysUser sysUser
    ) {
        AdminUser adminUser = CurrentUserInfoAdmin.getAdminUserValue();
        sysUser.setId(IdUtil.simpleUUID());
        sysUser.setCreateBy(adminUser.getId());
        sysUser.setCreateDate(new Date());
        iSysUserService.save(sysUser);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @RequestMapping("/getNewUserList")
    @UserMenuPermission("/usermanage")
    public Map<String,Object> getNewUserList(
            @RequestParam(value = "pageNo",required = false) Integer pageNo,
            @RequestParam(value = "pageSize",required = false) Integer pageSize
    ){
        if(pageNo==null||pageNo<1){
            pageNo = 1;
        }
        if(pageSize==null||pageSize<0){
            pageSize = 10;
        }
        com.github.pagehelper.Page<SysUser> page = iSysUserService.getNewUserList(pageNo,pageSize);
        IPage<SysUser> newpage = new Page<>();
        newpage.setRecords(page.getResult());
        newpage.setTotal(page.getTotal());
        return ResponseMapBuilder.newBuilder()
                .put("page",newpage)
                .putSuccess()
                .getResult();
    }

    /**
     * 企业微信扫码登录
     * @return
     */
    @RequestMapping("/signIn")
    public Map<String,Object> signIn(
            @RequestParam("code") String code
    ){
        QywxApplication app = qywxApplicationService.getOne(new LambdaQueryWrapper<QywxApplication>()
                .eq(QywxApplication::getAppKey, OaConstants.APPKEY_MAIN)
                .last("limit 1"));
        if(app == null){
            throw new AlertException("未配置企业应用");
        }
        String token = null;
        List<String> userRole = new ArrayList<>();
        try {
            WxCpService cpService = WxCpConfiguration.getCpService(app.getAgentId());
            if (cpService == null){
                throw new AlertException("系统中未加载配置["+app.getAgentId()+"]");
            }
            WxCpOauth2UserInfo userInfo = cpService.getOauth2Service().getUserInfo(code);
            if (StrUtil.isBlank(userInfo.getUserId())){
                throw new WxErrorException(WxError.builder().errorMsg("非企业员工禁止登录").build());
            }
            SysUser sysUser = iSysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getWxUserId, userInfo.getUserId()).last("limit 1"));
            userRole = sysRoleService.getUserRole(sysUser.getId());
            if (sysUser == null){
                throw new AlertException("系统中无此用户");
            }

            AdminUser adminUser = new AdminUser();
            BeanUtils.copyProperties(sysUser,adminUser);
            setRoleMenuIds(adminUser);

            token = IdUtil.simpleUUID();
            stringRedisTemplate.opsForValue().set(AdminUser.ADMIN_PRE_USERINFO+token, JSON.toJSONString(adminUser), loginExpire, TimeUnit.DAYS);

            String oldtoken = stringRedisTemplate.opsForValue().get(AdminUser.ADMIN_PRE_TOKEN + adminUser.getId());
            if(oldtoken!=null&&oldtoken.trim().length()>0){
                //移除上一次登录产生的缓存
                stringRedisTemplate.delete(AdminUser.ADMIN_PRE_USERINFO+oldtoken);
            }
            //记录用户对应的sid值，实现账号登录相互挤掉功能
            stringRedisTemplate.opsForValue().set(AdminUser.ADMIN_PRE_TOKEN + adminUser.getId(),token, loginExpire, TimeUnit.DAYS);
        } catch (WxErrorException e) {
            log.error(e.getError().getJson());
            throw new AlertException(e.getError().getErrorMsg());
        }

        return ResponseMapBuilder
                .newBuilder().putSuccess()
                .put("usertoken", token)
                .put("user",userRole)
                .getResult();
    }

    @RequestMapping("/getUserInfoById")
    public Map<String,Object> getUserInfoById(@RequestParam(value = "userid",required = false) String userid){
        SysUser sysUser = iSysUserService.getById(userid);
        return ResponseMapBuilder.newBuilder()
                .put("user",sysUser)
                .putSuccess()
                .getResult();
    }
}
