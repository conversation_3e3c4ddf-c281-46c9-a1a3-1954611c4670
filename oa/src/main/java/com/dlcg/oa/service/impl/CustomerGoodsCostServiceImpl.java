package com.dlcg.oa.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dlcg.oa.entity.CustomerGoodsSource;
import com.dlcg.oa.mapper.CustomerGoodsCostMapper;
import com.dlcg.oa.mapper.CustomerGoodsSourceMapper;
import com.dlcg.oa.service.ICustomerGoodsCostService;
import com.dlcg.oa.service.ICustomerGoodsSourceService;
import com.dlcg.tms.entity.CustomerGoodsCost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 货源表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-21
 */
@Service
public class CustomerGoodsCostServiceImpl extends ServiceImpl<CustomerGoodsCostMapper, CustomerGoodsCost> implements ICustomerGoodsCostService {

    

}
