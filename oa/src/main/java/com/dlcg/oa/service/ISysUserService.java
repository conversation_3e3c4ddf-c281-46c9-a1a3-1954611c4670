package com.dlcg.oa.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dlcg.oa.bean.RoleMenusBean;
import com.dlcg.oa.entity.SysRole;
import com.dlcg.oa.entity.SysUser;
import com.dlcg.oa.entity.SysUserRoleCode;

import java.util.List;
import java.util.Map;


/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-13
 */
public interface ISysUserService extends IService<SysUser> {

    IPage<SysUser> getUserList(Integer pageNo, Integer pageSize);
    SysUser login(String loginName,String passWord);

    List<RoleMenusBean> getRoleMenusByUserId(String uid);

    List<SysRole> getRoleByWxUserId(String wxUserId);
    
    com.github.pagehelper.Page<SysUser> getNewUserList(Integer pageNo, Integer pageSize);

    SysUser getWxUserByUserId(String userId);

    List<SysUser> selectSource();

    List<SysUser> selectShip();
    List<SysUser> selectCarDanZheng();
    List<SysUser> selectCarYeWu();

    List<SysUser> selectDanZheng();

    List<SysUser> selectShipDanZheng();
    
    List<SysUser> selectByRoleId(String roleId);

    List<SysUser> selectByRoleCodes(List<String> roleCodes);

    /**
     * 获取用户并按照角色分组，根据角色code
     * @param roleCodes
     * @return
     */
    Map<String,List<SysUser>> getUsersRoleMapByRoleCodes(List<String> roleCodes);
}
