package com.dlcg.oa.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dlcg.oa.bean.SysMenuBean;
import com.dlcg.oa.entity.SysMenu;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-19
 */
public interface ISysMenuService extends IService<SysMenu> {

    /**
     * 获得菜单列表，按照父菜单子菜单格式化后的结构
     * @return
     */
    List<SysMenuBean> getMenuBeans();

    /**
     * 根据用户id获得菜单列表
     * @param userid
     * @return
     */
    List<SysMenuBean> getMenusByUserId(String userid);


    List<SysMenu> getUserMenuList(String userid);
    List<SysMenu> getAllMenuList();

    IPage<SysMenu> getMenuList(Integer pageNo, Integer pageSize);

    List<String> getHuoWuRole();
    List<String> getShipRole();
    List<String> getJiaoJieRole();
    List<String> getShenPiRole();
    
    List<String> getZongGangCunRole();
    List<String> getChuanboXuqiuRole();
    List<String> getZhidaoJiageRole();
    List<String> getYewuPeizaiRole();
    List<String> getPeiZhaiDanRole();
}
