package com.dlcg.oa.service.impl;

import com.dlcg.oa.entity.SysCustomerSecondlevel;
import com.dlcg.oa.mapper.SysCustomerSecondlevelMapper;
import com.dlcg.oa.service.ISysCustomerSecondlevelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-22
 */
@Service
public class SysCustomerSecondlevelServiceImpl extends ServiceImpl<SysCustomerSecondlevelMapper, SysCustomerSecondlevel> implements ISysCustomerSecondlevelService {
    
    @Autowired
    SysCustomerSecondlevelMapper sysCustomerSecondlevelMapper;
    
    @Override
    public List<Map<String, Object>> selectCustomerNameList(String shipLineID) {
        List<Map<String, Object>> list = sysCustomerSecondlevelMapper.selectCustomerNameList(shipLineID).stream().distinct().collect(Collectors.toList());
        return list;
    }
}
