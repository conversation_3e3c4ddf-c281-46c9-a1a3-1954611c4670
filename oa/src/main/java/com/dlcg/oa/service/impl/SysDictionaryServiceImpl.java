package com.dlcg.oa.service.impl;

import com.dlcg.oa.entity.SysDictionary;
import com.dlcg.oa.mapper.SysDictionaryMapper;
import com.dlcg.oa.service.ISysDictionaryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@Service
public class SysDictionaryServiceImpl extends ServiceImpl<SysDictionaryMapper, SysDictionary> implements ISysDictionaryService {


    @Autowired
    SysDictionaryMapper sysDictionaryMapper;

    @Override
    public Integer getMaxCode(String key) {
        return sysDictionaryMapper.getMaxCode(key);
    }
}
