package com.dlcg.oa.service.impl;

import com.dlcg.oa.entity.ReceiveBillOutApply;
import com.dlcg.oa.mapper.ReceiveBillOutApplyMapper;
import com.dlcg.oa.service.IReceiveBillOutApplyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-24
 */
@Service
public class ReceiveBillOutApplyServiceImpl extends ServiceImpl<ReceiveBillOutApplyMapper, ReceiveBillOutApply> implements IReceiveBillOutApplyService {
    @Autowired
    ReceiveBillOutApplyMapper receiveBillOutApplyMapper;
    
    @Override
    public String selectOutBillCompany(String voyageNumber, String sys_cus_sup_id) {
        String s = receiveBillOutApplyMapper.selectOutBillCompany(voyageNumber,sys_cus_sup_id);
        String obcom = " ";
        if(s != null) {
            if (s.equals("成功")) {
                obcom = "CG";
            } else if (s.equals("和盛")) {
                obcom = "HS";
            }
        }
        return obcom;
    }
}
