package com.dlcg.oa.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dlcg.oa.entity.SysUser;
import com.dlcg.oa.mapper.ProcessApproveLogMapper;
import com.dlcg.oa.service.IProcessApproveLogService;
import com.dlcg.oa.service.IProcessInstanceService;
import com.dlcg.oa.service.ISysUserService;
import com.dlcg.oa.workflow.model.document.ProcessDefinition;
import com.dlcg.oa.workflow.model.document.ProcessNode;
import com.dlcg.oa.workflow.model.entity.ProcessApproveLog;
import com.dlcg.oa.workflow.model.entity.ProcessApproveLogBean;
import com.dlcg.oa.workflow.service.RepositoryService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ProcessApproveLogServiceImpl extends ServiceImpl<ProcessApproveLogMapper, ProcessApproveLog> implements IProcessApproveLogService {
    @Autowired
    ISysUserService iSysUserService;
    @Autowired
    IProcessInstanceService processInstanceService;
    @Autowired
    RepositoryService repositoryService;
    @Override
    public List<ProcessApproveLogBean> getSpPerspnListByProcessId(String processId) {
        // 申请人
        ProcessApproveLogBean shenQingLog = processInstanceService.getShenQingRenUserId(processId);
        if(shenQingLog==null){
            return null;
        }
        List<ProcessApproveLogBean> newlist = new ArrayList<>();
        SysUser shenQingUser = iSysUserService.getById(shenQingLog.getSpId());
        if(shenQingUser!=null){
            shenQingLog.setUserName(shenQingUser.getName());
        }
        String code = processInstanceService.getProcessInstanceCode(processId);
        Map<Integer,String> nodeMap = new HashMap<>();
        if(code!=null){
            // 获取label
            ProcessDefinition pd= repositoryService.getProcessDefinitionByCode(code);
            if(pd!=null){
                List<ProcessNode> processNodes= pd.getProcess();
                if(processNodes!=null && processNodes.size()>0){
                    for(ProcessNode processNode:processNodes){
                        if(processNode.getSignInfo()!=null && !processNode.getSignInfo().isNull("label")){
                            nodeMap.put(processNode.getId(),processNode.getSignInfo().getStr("label"));
                        }
                    }
                    // 给申请人加label
                    if(shenQingLog.getNodeId()!=null){
                        shenQingLog.setNodeLabel(nodeMap.get(shenQingLog.getNodeId()));
                    }
                }
            }
        }
        newlist.add(shenQingLog);


        LambdaQueryWrapper<ProcessApproveLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProcessApproveLog::getProcessId,processId);
//        queryWrapper.orderByAsc(ProcessApproveLog::getNodeId);
        queryWrapper.orderByAsc(ProcessApproveLog::getApproveTime);
        List<ProcessApproveLog> list = this.list(queryWrapper);
        if(list != null && list.size()>0){
            List<String> ids = list.stream().map(ProcessApproveLog::getSpId).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(SysUser::getId,ids);
            List<SysUser> userList = iSysUserService.list(wrapper);
            Map<String,SysUser> map = new HashMap<>();
            for(SysUser sysUser:userList){
                map.put(sysUser.getId(),sysUser);
            }

            for(ProcessApproveLog processApproveLog:list){
                ProcessApproveLogBean processApproveLogBean = new ProcessApproveLogBean();
                BeanUtils.copyProperties(processApproveLog,processApproveLogBean);
                if(processApproveLogBean.getNodeId()!=null){
                    processApproveLogBean.setNodeLabel(nodeMap.get(processApproveLogBean.getNodeId()));
                }
                if(map.containsKey(processApproveLogBean.getSpId())){
                    processApproveLogBean.setUserName(map.get(processApproveLogBean.getSpId()).getName());
                }
                newlist.add(processApproveLogBean);
            }
        }

        return newlist;
    }


}
