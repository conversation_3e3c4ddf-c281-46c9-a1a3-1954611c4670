package com.dlcg.oa.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dlcg.oa.entity.Ship;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dlcg.oa.entity.Shipinlist;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-21
 */
public interface IShipService extends IService<Ship> {
    IPage<Shipinlist> selectShipsInfo(Page<String> page, String followerID);
    Shipinlist selectMapShipInfo(String mmsi);
    List<Shipinlist> selectShipInfo(String followerID);
}
