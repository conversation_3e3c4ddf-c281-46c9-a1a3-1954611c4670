package com.dlcg.oa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 船期表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-18
 */
@TableName("ship_line")
public class ShipLine implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 航次号
     */
    private String voyageNumber;

    /**
     * 船期
     */
    private Date shipTime;

    /**
     * 指导价格列如，74-77
     */
    private Float freightRate;

    /**
     * 船舶id
     */
    private String shipId;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    private String delFlag;

    /**
     * 航线id
     */
    private String sysShiplineId;

    /**
     * 船期大货种，非必要字段。因为一条船拉的货种可能不是一种
     */
    private Integer goodsType;

    /**
     * 找船业务员
     */
    private String findShipUser;

    /**
     * 预配载状态：0：未配载 1：配载中 2:配载完成
     */
    private Integer preStatus;

    /**
     * 船期费用业务员是否确认：0：未确认费用 1：已确认费用
     */
    private Integer recordStatus;

    /**
     * 船舶类型：⾃有、平台、代理（一条船舶在不同船期下类型会变化）
     */
    private Integer businessType;

    /**
     * 船期起运港
     */
    private String startPort;

    /**
     * 船期目的港
     */
    private String endPort;

    /**
     * 是否是虚拟船期，1：是，2 否
     */
    private Integer isVirtual;

    /**
     * 船期排序序号，一般固定的这几个虚拟船期需要设置排序序号，在显示虚拟船期时使用
     */
    private Integer sort;

    /**
     * 船期吨位；注：这个吨位不是对应船舶的吨位，而是船期还未订船时预计要找的船的大概吨位数
     */
    private String tonnage;


    /**
     * 费用确认
     */
    private Integer queren;

    /**
     * 挂账号
     * @return
     */
    private String onAccountNum;

    /**
     * 挂账时间
     * @return
     */
    private Date guaTime;

    private Integer accountingType;

    public Integer getAccountingType() {
        return accountingType;
    }

    public void setAccountingType(Integer accountingType) {
        this.accountingType = accountingType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getVoyageNumber() {
        return voyageNumber;
    }

    public void setVoyageNumber(String voyageNumber) {
        this.voyageNumber = voyageNumber;
    }
    public Date getShipTime() {
        return shipTime;
    }

    public void setShipTime(Date shipTime) {
        this.shipTime = shipTime;
    }
    public Float getFreightRate() {
        return freightRate;
    }

    public void setFreightRate(Float freightRate) {
        this.freightRate = freightRate;
    }
    public String getShipId() {
        return shipId;
    }

    public void setShipId(String shipId) {
        this.shipId = shipId;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    public String getSysShiplineId() {
        return sysShiplineId;
    }

    public void setSysShiplineId(String sysShiplineId) {
        this.sysShiplineId = sysShiplineId;
    }
    public Integer getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }
    public String getFindShipUser() {
        return findShipUser;
    }

    public void setFindShipUser(String findShipUser) {
        this.findShipUser = findShipUser;
    }
    public Integer getPreStatus() {
        return preStatus;
    }

    public void setPreStatus(Integer preStatus) {
        this.preStatus = preStatus;
    }
    public Integer getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(Integer recordStatus) {
        this.recordStatus = recordStatus;
    }
    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }
    public String getStartPort() {
        return startPort;
    }

    public void setStartPort(String startPort) {
        this.startPort = startPort;
    }
    public String getEndPort() {
        return endPort;
    }

    public void setEndPort(String endPort) {
        this.endPort = endPort;
    }
    public Integer getIsVirtual() {
        return isVirtual;
    }

    public void setIsVirtual(Integer isVirtual) {
        this.isVirtual = isVirtual;
    }
    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public String getTonnage() {
        return tonnage;
    }

    public void setTonnage(String tonnage) {
        this.tonnage = tonnage;
    }
    public Integer getQueren() {
        return queren;
    }

    public void setQueren(Integer queren) {
        this.queren = queren;
    }

    public String getOnAccountNum() {
        return onAccountNum;
    }

    public void setOnAccountNum(String onAccountNum) {
        this.onAccountNum = onAccountNum;
    }

    public Date getGuaTime() {
        return guaTime;
    }

    public void setGuaTime(Date guaTime) {
        this.guaTime = guaTime;
    }

    @Override
    public String toString() {
        return "ShipLine{" +
            "id=" + id +
            ", voyageNumber=" + voyageNumber +
            ", shipTime=" + shipTime +
            ", freightRate=" + freightRate +
            ", shipId=" + shipId +
            ", createBy=" + createBy +
            ", createDate=" + createDate +
            ", updateBy=" + updateBy +
            ", updateDate=" + updateDate +
            ", delFlag=" + delFlag +
            ", sysShiplineId=" + sysShiplineId +
            ", goodsType=" + goodsType +
            ", findShipUser=" + findShipUser +
            ", preStatus=" + preStatus +
            ", recordStatus=" + recordStatus +
            ", businessType=" + businessType +
            ", startPort=" + startPort +
            ", endPort=" + endPort +
            ", isVirtual=" + isVirtual +
            ", sort=" + sort +
            ", tonnage=" + tonnage +
            ", queren=" + queren +
        "}";
    }
}
