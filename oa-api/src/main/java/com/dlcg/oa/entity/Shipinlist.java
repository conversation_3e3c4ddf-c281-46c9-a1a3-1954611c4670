package com.dlcg.oa.entity;

import java.util.Date;

/**
 * @Author: zhudx
 * @Date: 2020/9/7
 */
public class Shipinlist {
    private String mmsi;
    private String name;
    private Integer deadWeight;
    private Integer preStatus;
    private String endPortName;
    private String voyageNumber;
    private Float speed;
    private String status;
    private Float lat;
    private Float lng;
    private Float shipLength;
    private Float shipWidth;
    private Float draught;
    private String startPortName;
    private Date startTime;
    private String endTime;
    private String operatingLicensePic;
    private Integer customers;
    private String shipLineId;

    public String getShipLineId() {
        return shipLineId;
    }

    public void setShipLineId(String shipLineId) {
        this.shipLineId = shipLineId;
    }

    public Integer getCustomers() {
        return customers;
    }

    public void setCustomers(Integer customers) {
        this.customers = customers;
    }

    public String getOperatingLicensePic() {
        return operatingLicensePic;
    }

    public void setOperatingLicensePic(String operatingLicensePic) {
        this.operatingLicensePic = operatingLicensePic;
    }

    public String getEndTime() {
        return endTime;
    }
 
     public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getStartPortName() {
        return startPortName;
    }

    public void setStartPortName(String startPortName) {
        this.startPortName = startPortName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    public Float getShipLength() {
        return shipLength;
    }

    public void setShipLength(Float shipLength) {
        this.shipLength = shipLength;
    }

    public Float getShipWidth() {
        return shipWidth;
    }

    public void setShipWidth(Float shipWidth) {
        this.shipWidth = shipWidth;
    }

    public Float getDraught() {
        return draught;
    }

    public void setDraught(Float draught) {
        this.draught = draught;
    }

    

    public String getMmsi() {
        return mmsi;
    }

    public void setMmsi(String mmsi) {
        this.mmsi = mmsi;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDeadWeight() {
        return deadWeight;
    }

    public void setDeadWeight(Integer deadWeight) {
        this.deadWeight = deadWeight;
    }

    public Integer getPreStatus() {
        return preStatus;
    }

    public void setPreStatus(Integer preStatus) {
        this.preStatus = preStatus;
    }

    public String getEndPortName() {
        return endPortName;
    }

    public void setEndPortName(String endPortName) {
        this.endPortName = endPortName;
    }

    public String getVoyageNumber() {
        return voyageNumber;
    }

    public void setVoyageNumber(String voyageNumber) {
        this.voyageNumber = voyageNumber;
    }
    public Float getSpeed() {
        return speed;
    }

    public void setSpeed(Float speed) {
        this.speed = speed;
    }
    public String getStatus() {
        return status;
    }

    public Float getLat() {
        return lat;
    }

    public void setLat(Float lat) {
        this.lat = lat;
    }

    public Float getLng() {
        return lng;
    }

    public void setLng(Float lng) {
        this.lng = lng;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
