package com.dlcg.oa.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-27
 */
@TableName("sys_dictionary")
public class SysDictionary implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String dictionaryKey;

    private Integer code;

    private String value;

    private String createBy;

    @TableField(exist = false)
    private String feeCategoryName;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    private String updateBy;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    private String delFlag;

    private String remarks;
    
    private String spare1;
    
    private String spare2;
    
    private String spare3;
    
    private String spare4;
    
    private String spare5;
    
    private String spare6;
    
    public String getSpare1() {
        return spare1;
    }
    
    public void setSpare1(String spare1) {
        this.spare1 = spare1;
    }
    
    public String getSpare2() {
        return spare2;
    }
    
    public void setSpare2(String spare2) {
        this.spare2 = spare2;
    }
    
    public String getSpare3() {
        return spare3;
    }
    
    public void setSpare3(String spare3) {
        this.spare3 = spare3;
    }
    
    public String getSpare4() {
        return spare4;
    }
    
    public void setSpare4(String spare4) {
        this.spare4 = spare4;
    }
    
    public String getSpare5() {
        return spare5;
    }
    
    public void setSpare5(String spare5) {
        this.spare5 = spare5;
    }
    
    public String getSpare6() {
        return spare6;
    }
    
    public void setSpare6(String spare6) {
        this.spare6 = spare6;
    }
    
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDictionaryKey() {
        return dictionaryKey;
    }

    public void setDictionaryKey(String dictionaryKey) {
        this.dictionaryKey = dictionaryKey;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getFeeCategoryName() {
        return feeCategoryName;
    }

    public void setFeeCategoryName(String feeCategoryName) {
        this.feeCategoryName = feeCategoryName;
    }

    @Override
    public String toString() {
        return "SysDictionary{" +
            "id=" + id +
            ", key=" + dictionaryKey +
            ", code=" + code +
            ", value=" + value +
            ", createBy=" + createBy +
            ", createDate=" + createDate +
            ", updateBy=" + updateBy +
            ", updateDate=" + updateDate +
            ", delFlag=" + delFlag +
            ", remarks=" + remarks +
        "}";
    }
}
