package com.dlcg.oa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-03
 */
@TableName("contract_model")
public class ContractModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String contractName;

    private String contractType;
    
    private String contractTypeAbbreviation;

    private String dataCollector;

    private String webView;

    private String contractImg;

    private String createBy;

    private Date createTime;

    private String updateBy;

    private Date updateTime;

    private String isDel;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }
    public String getContractType() {
        return contractType;
    }

    public void setContractType(String contractType) {
        this.contractType = contractType;
    }

    public String getContractTypeAbbreviation() {
        return contractTypeAbbreviation;
    }

    public void setContractTypeAbbreviation(String contractTypeAbbreviation) {
        this.contractTypeAbbreviation = contractTypeAbbreviation;
    }

    public String getDataCollector() {
        return dataCollector;
    }

    public void setDataCollector(String dataCollector) {
        this.dataCollector = dataCollector;
    }
    public String getWebView() {
        return webView;
    }

    public void setWebView(String webView) {
        this.webView = webView;
    }
    public String getContractImg() {
        return contractImg;
    }

    public void setContractImg(String contractImg) {
        this.contractImg = contractImg;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public String getIsDel() {
        return isDel;
    }

    public void setIsDel(String isDel) {
        this.isDel = isDel;
    }

    @Override
    public String toString() {
        return "ContractModel{" +
            "id=" + id +
            ", contractName=" + contractName +
            ", contractType=" + contractType +
            ", dataCollector=" + dataCollector +
            ", webView=" + webView +
            ", contractImg=" + contractImg +
            ", createBy=" + createBy +
            ", createTime=" + createTime +
            ", updateBy=" + updateBy +
            ", updateTime=" + updateTime +
            ", isDel=" + isDel +
        "}";
    }
}
