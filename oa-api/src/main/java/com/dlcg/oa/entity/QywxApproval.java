package com.dlcg.oa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-28
 */
@TableName("qywx_approval")
public class QywxApproval implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 审批单编号
     */
    private String spNo;

    /**
     * 审批申请类型名称（审批模板名称）
     */
    private String spName;

    /**
     * 申请单状态：1-审批中；2-已通过；3-已驳回；4-已撤销；6-通过后撤销；7-已删除；10-已支付
     */
    private Integer spStatus;

    /**
     * 审批模板id。可在“获取审批申请详情”、“审批状态变化回调通知”中获得，也可在审批模板的模板编辑页面链接中获得。
     */
    private String templateId;

    /**
     * 审批申请提交时间,
     */
    private Date applyTime;

    /**
     * 申请人userid
     */
    private String applyerUserid;

    /**
     * 申请人所在部门id
     */
    private String applyerPartyid;

    /**
     * 审批流程信息，可能有多个审批节点。
     */
    private String spRecord;

    /**
     * 抄送信息，可能有多个抄送节点
     */
    private String notifyer;

    /**
     * 审批申请数据
     */
    private String applyData;

    /**
     * 审批申请备注信息，可能有多个备注节点
     */
    private String comments;

    private Date createDate;

    private String createBy;

    private Date updateDate;

    private String updateBy;

    private String remarks;

    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getSpNo() {
        return spNo;
    }

    public void setSpNo(String spNo) {
        this.spNo = spNo;
    }
    public String getSpName() {
        return spName;
    }

    public void setSpName(String spName) {
        this.spName = spName;
    }
    public Integer getSpStatus() {
        return spStatus;
    }

    public void setSpStatus(Integer spStatus) {
        this.spStatus = spStatus;
    }
    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }
    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }
    public String getApplyerUserid() {
        return applyerUserid;
    }

    public void setApplyerUserid(String applyerUserid) {
        this.applyerUserid = applyerUserid;
    }
    public String getApplyerPartyid() {
        return applyerPartyid;
    }

    public void setApplyerPartyid(String applyerPartyid) {
        this.applyerPartyid = applyerPartyid;
    }
    public String getSpRecord() {
        return spRecord;
    }

    public void setSpRecord(String spRecord) {
        this.spRecord = spRecord;
    }
    public String getNotifyer() {
        return notifyer;
    }

    public void setNotifyer(String notifyer) {
        this.notifyer = notifyer;
    }
    public String getApplyData() {
        return applyData;
    }

    public void setApplyData(String applyData) {
        this.applyData = applyData;
    }
    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "QywxApproval{" +
                "id=" + id +
                ", spNo=" + spNo +
                ", spName=" + spName +
                ", spStatus=" + spStatus +
                ", templateId=" + templateId +
                ", applyTime=" + applyTime +
                ", applyerUserid=" + applyerUserid +
                ", applyerPartyid=" + applyerPartyid +
                ", spRecord=" + spRecord +
                ", notifyer=" + notifyer +
                ", applyData=" + applyData +
                ", comments=" + comments +
                ", createDate=" + createDate +
                ", createBy=" + createBy +
                ", updateDate=" + updateDate +
                ", updateBy=" + updateBy +
                ", remarks=" + remarks +
                ", delFlag=" + delFlag +
                "}";
    }
}
