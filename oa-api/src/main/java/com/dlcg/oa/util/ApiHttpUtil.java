package com.dlcg.oa.util;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.zthzinfo.microservice.security.TokenUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class ApiHttpUtil {

	private static final String PARAM_TOKEN_KEY = "token";

	private static Logger logger = LoggerFactory.getLogger(ApiHttpUtil.class);
	public static String post(String url,String pk, Map<String,Object> paramMap){
		if(url==null||url.length()<=0){
			logger.error("请求url为空！");
			return null;
		}
		if(pk==null||pk.length()<=0){
			logger.error("请求加密参数pk为空！");
			return null;
		}
		if(paramMap==null){
			paramMap = new HashMap<>();
		}

		Object timestamp = paramMap.get("timestamp");
		if(timestamp==null||timestamp.toString().length()<=0){
			paramMap.put("timestamp",new Date().getTime());
		}
		String res = req(url,pk,paramMap);

		return res;
	}
	private static String req(String url,String pk, Map<String,Object> param){

		logger.info("请求时秘钥PK="+pk+"；参数列表="+ JSON.toJSONString(param));
		String token = TokenUtil.generateToken(pk, param.keySet(), new TokenUtil.ParamMap() {
			public String getValue(String var1) {
				return String.valueOf(param.get(var1));
			}
		});
		param.put(PARAM_TOKEN_KEY,token);
		return HttpUtil.post(url, param);
	}
}
