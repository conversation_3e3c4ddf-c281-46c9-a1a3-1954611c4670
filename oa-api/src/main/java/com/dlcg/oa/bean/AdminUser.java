package com.dlcg.oa.bean;

import cn.hutool.core.util.IdUtil;
import com.dlcg.oa.entity.SysUser;

import java.util.Set;

public class AdminUser extends SysUser {

    public static void main(String[] args) {
        for (int i = 0; i < 634; i++) {
            System.out.println(IdUtil.simpleUUID());
        }
    }


    private Set<String> roleEnnames;

    private Set<String> menuHrefs;

    Set<String> roleIds ;

    public static String ADMIN_PRE_USERINFO = "ADMIN_PRE_USERINFO_";
    public static String ADMIN_PRE_TOKEN = "ADMIN_PRE_TOKEN_";


    public Set<String> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(Set<String> roleIds) {
        this.roleIds = roleIds;
    }

    public Set<String> getRoleEnnames() {
        return roleEnnames;
    }

    public void setRoleEnnames(Set<String> roleEnnames) {
        this.roleEnnames = roleEnnames;
    }

    public Set<String> getMenuHrefs() {
        return menuHrefs;
    }

    public void setMenuHrefs(Set<String> menuHrefs) {
        this.menuHrefs = menuHrefs;
    }
}
