package com.dlcg.oa.client;


import com.dlcg.oa.bean.ProcessInstanceBean;
import com.dlcg.oa.entity.ProcessInstance;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

@FeignClient(value = "dlcg-oa", url = "${debug.dlcg-oa:}")
@RequestMapping(value = "/client/process")
public interface ProcessClient {

    @PostMapping("/task/process/{id}")
    Map<String,Object> taskProcess(
            @PathVariable("id") String id,
            @RequestParam("status") Integer status,
            @RequestParam("userid") String userid,
			@RequestParam("nodeid") String nodeIdStr,
            @RequestParam("dtoStr") String dtoStr
    );

    /** 更新审批状态 */
    @RequestMapping("/updateProcess")
    Map<String,Object> updateProcess(
            @RequestParam(value = "processId", required = false) String processId,
            @RequestParam(value = "param", required = false) String param,
            @RequestParam(value = "waterId", required = false) String waterId

    );

    //获取审批实例
    @RequestMapping("/getPaymentId")
    Map<String,Object> getPaymentId(@RequestParam(value = "processId", required = false) String processId);

    @RequestMapping("/getUpdateStowageById")
    Map<String,Object> getUpdateStowageById(@RequestParam(value = "shipLineId", required = false) String shipLineId);
    
    @RequestMapping("/delProcess")
    Map<String,Object> delProcess(@RequestParam(value = "processId", required = false) String processId);

    @RequestMapping("/getOnAccountProcessList")
    Map<String,Object> getOnAccountProcessList(
            @RequestParam(value = "pageNo", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
            @RequestParam(value = "shipName", required = false) String shipName,
            @RequestParam(value = "shipLiGangTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date shipLiGangTime,
            @RequestParam(value = "shipTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date shipTime
    );
    @RequestMapping("/getOnAccountProcessOnAccountNumList")
    Map<String,Object> getOnAccountProcessOnAccountNumList(
            @RequestParam(value = "pageNo", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
            @RequestParam(value = "shipName", required = false) String shipName,
            @RequestParam(value = "typeAccount", required = false) String typeAccount,
            @RequestParam(value = "shipLiGangTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date shipLiGangTime,
            @RequestParam(value = "shipTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date shipTime,
            @RequestParam(value = "onAccountNum", required = false) String onAccountNum,
            @RequestParam(value = "voyageNumber", required = false) String voyageNumber
    );
    @RequestMapping("/getOnAccountProcessOnAccountNumQYList")
    Map<String,Object> getOnAccountProcessOnAccountNumQYList(
            @RequestParam(value = "pageNo", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
            @RequestParam(value = "shipName", required = false) String shipName,
            @RequestParam(value = "typeAccount", required = false) String typeAccount,
            @RequestParam(value = "shipLiGangTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date shipLiGangTime,
            @RequestParam(value = "shipTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date shipTime,
            @RequestParam(value = "onAccountNum", required = false) String onAccountNum
    );

    @RequestMapping("/getOnAccountOtherCostProcessList")
    Map<String, Object> getOnAccountOtherCostProcessList(
        @RequestParam(value = "pageNo", required = false) Integer pageNo,
        @RequestParam(value = "pageSize", required = false) Integer pageSize,
        @RequestParam(value = "applyStatus", required = false) Integer applyStatus);

    @RequestMapping("/getOnAccountOtherCostProcessNewList")
    Map<String, Object> getOnAccountOtherCostProcessNewList(
            @RequestParam(value = "pageNo", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
            @RequestParam(value = "shipName", required = false) String shipName,
            @RequestParam(value = "onAccountNum", required = false) String onAccountNum
    );

    @RequestMapping("/getOnAccountUpdateStowageProcessList")
    Map<String, Object> getOnAccountUpdateStowageProcessList(
            @RequestParam(value = "wxUserId", required = false) String wxUserId,
            @RequestParam(value = "pageNo", required = false) Integer pageNo,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
            @RequestParam(value = "typeAccount", required = false) String typeAccount,
            @RequestParam(value = "finishTime", required = false) String finishTime,
            @RequestParam(value = "briefContent", required = false) String briefContent
    );
    
    @RequestMapping("/getOnAccountCount")
    Map<String,Object> getOnAccountCount(@RequestParam(value = "applyStatus", required = false) Integer applyStatus);

    @RequestMapping("/getOnAccountByShipLineId")
    Map<String,Object> getOnAccountByShipLineId(@RequestParam(value = "shipLineId", required = false) String shipLineId);

    @RequestMapping("/ceraeteOnAccountByShipLineId")
    Map<String,Object> ceraeteOnAccountByShipLineId(@RequestParam(value = "shipLineId", required = false) String shipLineId);
    
    @RequestMapping("/updateOnAccountProcess")
    Map<String,Object> updateOnAccountProcess(
            @RequestParam(value = "processId", required = false) String processId,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "reason", required = false) String reason
    );

    @RequestMapping("/updateOnAccountOtherCostProcess")
    Map<String,Object> updateOnAccountOtherCostProcess(
        @RequestParam(value = "processId", required = false) String processId,
        @RequestParam(value = "nodeId", required = false) Integer nodeId,
        @RequestParam(value = "status", required = false) Integer status,
        @RequestParam(value = "wxUserId", required = false) String wxUserId,
        @RequestParam(value = "reason", required = false) String reason
    ) throws JsonProcessingException;


    /**
     * 获取审批任务
     */
    @RequestMapping("/taskGet")
    Map<String,Object> taskGet(@RequestParam(value = "processId", required = false) String processId) throws JsonProcessingException;
    
    @RequestMapping("/getFormValue")
    Map<String,Object> getFormValue(@RequestParam(value = "processId", required = false) String processId) throws JsonProcessingException;
    
    @RequestMapping("/updateProcessInstance")
    Map<String,Object> updateProcessInstance(@RequestParam(value = "processInstanceStr", required = false) String processInstanceStr);
    
    @RequestMapping("/sendMsgSaveCode")
    Map<String,Object> sendMsgSaveCode(
            @RequestParam("processId") String processId,
            @RequestParam("taskId") String taskId,
            @RequestParam("resCode") String resCode);

    @RequestMapping("/finishProcess")
    Map<String, Object> finishProcess(@RequestParam("processId") String processId,
                                      @RequestParam("statusId") Integer statusId,
                                      @RequestParam("status") String status,
                                      @RequestParam("finishTime") Long finishTime);


    @RequestMapping("/getProcessInstance")
    ProcessInstance getProcessInstance(@RequestParam(value = "processId", required = false) String processId);

    /**
     * 查询流程实例，根据流程code和params1
     */
    @RequestMapping("/queryProcessByFlowCodeAndParams1s")
    List<ProcessInstance> queryProcessByFlowCodeAndParams1s(
            @RequestParam("flowCode") String code,
            @RequestParam("params1s") List<String> params1s
    );

    /**
     * 查询流程实例，根据流程code和params1 提供分页
     */
    @RequestMapping("/queryPageDataByCodeAndParams1s")
    Map<String, Object> queryPageDataByCodeAndParams1s(
            @RequestParam("pageNum")  Integer pageNum,
            @RequestParam("pageSize")   Integer pageSize,
            @RequestParam("flowCode") String code,
            @RequestParam(name = "brief_content",required = false) String brief_content,
            @RequestParam(name = "sponsor_name",required = false) String sponsor_name,
            @RequestParam(name = "customerSecondlevelId",required = false) String customerSecondlevelId
    );

    @RequestMapping("/queryProcessByFlowCodeAndParams1AndFindInParam2")
    List<ProcessInstanceBean> queryProcessByFlowCodeAndParams1AndFindInParam2(
            @RequestParam("flowCode") String code,
            @RequestParam("params1") String params1,
            @RequestParam(value="params2", required = false) String params2
    );
    @RequestMapping("/queryProcessByFlowCodeAndParams1Page")
    Map<String, Object> queryProcessByFlowCodeAndParams1Page( @RequestParam("flowCode") String code,
                                                                     @RequestParam("params1") String params1,
                                                              @RequestParam("status") String status,@RequestParam(value = "statusId", required = false)String statusId,
                                                              @RequestParam("pageNum")  Integer pageNum,
                                                              @RequestParam("pageSize")   Integer pageSize);

    @RequestMapping("/queryProcessByFlowCodeAndParams1AndShipAndOnaccountNumberPage")
    Map<String, Object> queryProcessByFlowCodeAndParams1AndShipAndOnaccountNumberPage( @RequestParam("flowCode") String code,
                                                              @RequestParam("params1") String params1,
                                                              @RequestParam("status") String status,@RequestParam(value = "statusId", required = false)String statusId,
                                                              @RequestParam("pageNum")  Integer pageNum,
                                                              @RequestParam("pageSize")   Integer pageSize,
                                                              @RequestParam(value = "shipName",required = false) String shipName,
                                                              @RequestParam(value = "onAccountNum",required = false) String onAccountNum
                                                              );

    @RequestMapping("/updateProcessValueByProcessId")
    Map<String, Object> updateProcessValueByProcessId(@RequestParam("processId") String processId,
                                                      @RequestParam("key") String key,
                                                      @RequestParam(value="value",required = false) String value);


    @RequestMapping("/sendMsgBillOutApplyApplicant")
    Map<String, Object> sendMsgBillOutApplyApplicant(@RequestParam("processId") String processId,@RequestParam("content") String content);

    @RequestMapping("/getProcessInstanceIdByBillOutApplyId")
    String getProcessInstanceIdByBillOutApplyId(@RequestParam("billOutApplyId") String billOutApplyId);
    @RequestMapping("/updateProcessParams10SponsorId")
    void updateProcessParams10SponsorId(@RequestParam("processId") String processId);
    @RequestMapping("/deleteProcessParams10SponsorId")
    void deleteProcessParams10SponsorId(@RequestParam("processId") String processId);

    @RequestMapping("/getSpPerspnListByProcessId")
    Map<String,Object> getSpPerspnListByProcessId(@RequestParam("processId") String processId);
    @RequestMapping("/updateProcessParams2")
    void updateProcessParams2(@RequestParam("processId") String processId,@RequestParam("params2") String params2);
    @RequestMapping("/updateProcessParams5")
    void updateProcessParams5(@RequestParam("processId") String processId,@RequestParam("params5") String params5);

    // 修改摘要前缀
    @RequestMapping("/updateSummaryPrefixProcessById")
    void updateSummaryPrefixProcessById(@RequestParam("processId") String processId,@RequestParam("summary") String summary);




    /**
     * 查询流程实例，根据流程code和params4 获取数据
     */
    @RequestMapping("/queryDataByCodeAndParams4")
    List<ProcessInstance> queryDataByCodeAndParams4(
            @RequestParam("flowCode") String code,
            @RequestParam(name = "params4",required = false) String params4);

    /**
     * 查询汽运流向是否审核过，根据流程code和params2 获取数据
     */
    @RequestMapping("/queryDataByCodeAndParams2")
    List<ProcessInstance> queryDataByCodeAndParams2(
            @RequestParam("flowCode") String code,
            @RequestParam(name = "params2",required = false) String params2);


    /**
     * 处理物流挂账重复的问题，如果有被拒绝和撤销的流程，就不再创建新的挂账号
     */
    @RequestMapping("/getRepetitionOnAccountNum")
    String getRepetitionOnAccountNum(
            @RequestParam("processId") String processId);

}
