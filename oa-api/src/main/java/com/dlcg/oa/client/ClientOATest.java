package com.dlcg.oa.client;


import com.dlcg.oa.entity.WxJob;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

@FeignClient(value = "dlcg-oa", url = "${debug.dlcg-oa:}")
@RequestMapping(value = "/client/oa")
public interface ClientOATest {

	@RequestMapping("/test")
	String getSystemName(@RequestParam(name = "id", required = false) String id);

	@PostMapping("/post")
	Map<String,Object> post(@RequestBody WxJob wxJob);
}
