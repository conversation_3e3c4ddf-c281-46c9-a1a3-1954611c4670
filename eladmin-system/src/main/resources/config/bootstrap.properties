#spring.cloud.config.name=el-admin
#spring.cloud.config.profile=dev
#spring.cloud.config.label=wangn
spring.cloud.config.enabled=false
spring.profiles.active=dev
#
#spring.cloud.config.uri=http://zhongtian:zhongtian<PERSON>zhi@*************:8808/
spring.application.name=caiwu
eureka.client.serviceUrl.defaultZone=***********************************************/eureka/
eureka.instance.instance-id=${spring.cloud.client.ip-address}:${server.port}
eureka.instance.prefer-ip-address=true
spring.main.allow-bean-definition-overriding=true

#ribbon\u7684\u8D85\u65F6\u65F6\u95F4
ribbon.ReadTimeout=30000
ribbon.ConnectTimeout=30000
# \u8BBE\u7F6Efeign\u8BF7\u6C42\u8D85\u65F6\u65F6\u95F4
feign.client.config.default.connectTimeout=30000
feign.client.config.default.readTimeout=30000

feign.client.config.default.connect-timeout=30000
feign.client.config.default.read-timeout=30000
