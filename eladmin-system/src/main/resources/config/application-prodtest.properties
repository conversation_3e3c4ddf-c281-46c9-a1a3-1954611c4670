file.linux.avatar=/home/<USER>/avatar/
file.linux.path=/home/<USER>/file/
file.mac.avatar=~/avatar/
file.mac.path=~/file/
file.windows.path=C:\\eladmin\\file
file.windows.avatar=C:\\eladmin\\avatar
generator.enabled=false
ip.local-parsing=false
jwt.base64-secret=ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=

spring.datasource.druid.url=*************************************************************************************************************************************************************************
spring.datasource.druid.username=root
spring.datasource.druid.password=jLp4@pMeyN98


#mongo\u914D\u7F6E
spring.data.mongodb.uri=***********************************************************************************************

# Redis数据库索引（默认为0）
spring.redis.database=2
# Redis服务器地址
spring.redis.host=**************
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=dlcg20qdjf20


oa.mobile-url=http://moa.successhetai.com


oa.schedule.syncqywxdata2sys.enable=true

spring.kafka.bootstrap-servers=127.0.0.1:9092
canalroot.autostart=false

hangci.base-url=http://localhost:28002/open-api
hangci.PK=#@!$%^&shipoper#@$%^&*