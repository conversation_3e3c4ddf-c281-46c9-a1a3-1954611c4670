<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.zhengjie.modules.business.mapper.SysExternalAccountMapper">
    <select id="listByCustomerIdAndDictId"
            resultType="me.zhengjie.modules.business.domain.SysExternalAccount">
        select sa.* from sys_external_account sa
                  LEFT join sys_external_account_plate  sp
                on sa.id=sp.account_id
        <choose>
            <when test="dictId!=null">
                and sp.dict_id=#{dictId}
                <if test="isCustomer!=null and isCustomer!=''">
                    and sp.is_customer=1
                </if>
                <if test="isSupplier!=null and isSupplier!=''">
                    and sp.is_supplier=1
                </if>
            </when>
            <otherwise>
                and sp.dict_id is null
            </otherwise>
        </choose>
        where sp.del_flag=0 and sa.del_flag=0
        <if test="customerId!=null">
            and sa.spare1=#{customerId}
        </if>
        order by
        <if test="isCustomer!=null and isCustomer!=''">
            sp.is_customer desc,
        </if>
        <if test="isSupplier!=null and isSupplier!=''">
            sp.is_supplier desc,
        </if>
            sp.create_date desc,
            sa.create_date desc
    </select>
    <select id="listByCustomerIdAndDictIdAndBankName"
            resultType="me.zhengjie.modules.business.domain.SysExternalAccount">
        select sa.* from sys_external_account sa
        LEFT join sys_external_account_plate  sp
        on sa.id=sp.account_id
        <choose>
            <when test="dictId!=null">
                and sp.dict_id=#{dictId}
            </when>
            <otherwise>
                and sp.dict_id is null
            </otherwise>
        </choose>
        WHERE sa.spare1=#{customerId}
        and sa.bank_name=#{bankName}
        and sa.del_flag=0
        and sp.del_flag=0
        order by
        <if test="isCustomer!=null and isCustomer!=''">
            sp.is_customer desc,
        </if>
        <if test="isSupplier!=null and isSupplier!=''">
            sp.is_supplier desc,
        </if>
        sp.create_date desc,
        sa.create_date desc
    </select>
</mapper>
