<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.zhengjie.modules.system.mapper.SysExternalCustomerMapper">
    <select id="getTreeList" resultType="me.zhengjie.modules.system.domain.SysExternalCustomer">
        select * from sys_external_customer
        <if test="id!=null">
            where FIND_IN_SET(id,customerFindChildren(customerFindParentId(#{id}))) and del_flag=0
        </if>
        order by create_date
    </select>
    <select id="qureyCustomerByDictId" resultType="me.zhengjie.modules.system.domain.SysExternalCustomer">
        select ec.* from sys_external_customer ec
        LEFT join sys_external_customer_plate ecp
        on ecp.customer_id=ec.id
        <choose>
        <when test="dictId!=null">
            and ecp.dict_id=#{dictId}
            <if test="isCustomer!=null and isCustomer!=''">
                and ecp.is_customer=1
            </if>
            <if test="isSupplier!=null and isSupplier!=''">
               and ecp.is_supplier=1
            </if>
        </when>
        <otherwise>
            and ecp.dict_id is null
        </otherwise>
        </choose>
        where ec.del_flag=0 and ecp.del_flag=0
        order by
        <if test="isCustomer!=null and isCustomer!=''">
            ecp.is_customer desc,
        </if>
        <if test="isSupplier!=null and isSupplier!=''">
            ecp.is_supplier desc,
        </if>
            ecp.create_date desc,
            ec.create_date desc
    </select>
    <select id="qureyCustomerAllOrDictId" resultType="me.zhengjie.modules.system.domain.vo.SysExternalCustomerVo">
        select GROUP_CONCAT(CONCAT_WS('|'
        ,IFNULL(ecp.dict_id,0)
        ,IFNULL(sd.`description`,'')
        ,IFNULL(ecp.is_customer,0)
        ,IFNULL(ecp.is_supplier,0))) as dictr,
               ec.* from sys_external_customer ec
        LEFT join sys_external_customer_plate ecp
        on ecp.customer_id=ec.id and ecp.del_flag=0
        left join sys_dict sd on sd.dict_id=ecp.dict_id
        where ec.del_flag=0
        <if test="dictId!=null and dictId!=0 ">
              and ecp.dict_id = #{dictId}
          </if>
        <if test="queryCriteria!=null and queryCriteria.likename!=null and queryCriteria.likename!=''">
            and  (ec.name like concat('%',#{queryCriteria.likename},'%') or ec.short_name like concat('%',#{queryCriteria.likename},'%'))
        </if>
        group by ec.id
        order by
        ec.create_date desc
    </select>
    <select id="qureyCustomerByDictIds" resultType="me.zhengjie.modules.system.domain.vo.SysExternalCustomerVo">
        select GROUP_CONCAT(CONCAT_WS('|'
        ,IFNULL(ecp.dict_id,0)
        ,IFNULL(sd.`description`,'')
        ,IFNULL(ecp.is_customer,0)
        ,IFNULL(ecp.is_supplier,0))) as dictr,
        max(ecp.create_date) ecpcdate,
        ec.* from sys_external_customer ec
        left join sys_external_customer_plate ecp on ecp.customer_id=ec.id and ecp.del_flag=0
        left join sys_dict sd on sd.dict_id=ecp.dict_id
        where ec.del_flag=0
          <if test="dictIds!=null">
              and ecp.dict_id in
              <foreach collection="dictIds" item="tmp" open="(" close=")" separator=",">
                  #{tmp}
              </foreach>
          </if>
        <if test="queryCriteria!=null and queryCriteria.likename!=null and queryCriteria.likename!=''">
           and  (ec.name like concat('%',#{queryCriteria.likename},'%') or ec.short_name like concat('%',#{queryCriteria.likename},'%'))
        </if>
        group by ec.id
        order by
        ecpcdate desc,
        ec.create_date desc
    </select>
    <select id="getCustomerByNameWithAccount"
            resultMap="customerAccountResult">
        SELECT
            sec.*,
            sea.id account_id,
            sea.bank_name account_bank_name,
            sea.bank_account account_bank_account
        FROM `sys_external_customer` sec
        left join sys_external_account sea on sec.id = sea.spare1
        where sec.name = #{name}
        order by sea.id asc
        limit 1
    </select>

    <resultMap id="customerAccountResult" type="me.zhengjie.modules.system.domain.vo.SysExternalCustomerAccountVo">
        <id property="id" column="id" />
        <result property="name" column="name"/>
        <result property="shortName" column="short_name"/>
        <result property="contractHeader" column="contract_header"/>
        <result property="taxIdNumber" column="tax_id_number"/>
        <result property="registeredAddress" column="registered_address"/>
        <result property="spare1" column="spare1"/>
        <result property="spare2" column="spare2"/>
        <result property="spare3" column="spare3"/>
        <result property="spare4" column="spare4"/>
        <result property="spare5" column="spare5"/>
        <association property="account" resultMap="accountResult" />
    </resultMap>
    <resultMap id="accountResult" type="me.zhengjie.modules.business.domain.SysExternalAccount">
        <id property="id" column="account_id"/>
        <result property="companyName" column="account_company_name"/>
        <result property="bankName" column="account_bank_name"/>
        <result property="bankAccount" column="account_bank_account"/>
    </resultMap>

</mapper>
