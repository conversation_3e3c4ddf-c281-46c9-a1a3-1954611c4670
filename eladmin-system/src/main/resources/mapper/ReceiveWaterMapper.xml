<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.zhengjie.modules.system.mapper.ReceiveWaterMapper">

    <select id="getReceiveWaterList" resultType="me.zhengjie.modules.system.domain.bean.ReceiveWaterBean">
        SELECT
            rw.*,fc.name as funds_class_name,fc.param3 as funds_class_path_ids
        FROM
            receive_water rw
            LEFT JOIN sys_company_plate scp ON scp.wx_department_id = rw.recevice_company
            left join funds_classification fc on fc.id = rw.funds_class_id
            WHERE rw.del_flag = 0
            <if test="ids!=null">
                AND (scp.dict_id IN
                <foreach collection="ids" item="tmp" open="(" close=")" separator=",">
                    #{tmp}
                </foreach>
                <if test="receiveCompanyTypes != null and receiveCompanyTypes.size() > 0">
                    or (
                        rw.cost_type=1 and rw.recevice_company in
                            <foreach collection="receiveCompanyTypes" item="tmp" open="(" close=")" separator=",">
                                #{tmp}
                            </foreach>
                        )
                </if>
                )
            </if>

            <if test="receiveCompany!=null and receiveCompany!=''">
                AND ( rw.recevice_company_name LIKE concat('%',#{receiveCompany},'%')
                <if test="receiveCompanyTypes != null and receiveCompanyTypes.size() > 0">
                    or (
                    rw.cost_type=1 and rw.recevice_company in
                    <foreach collection="receiveCompanyTypes" item="tmp" open="(" close=")" separator=",">
                        #{tmp}
                    </foreach>
                    )
                </if>
                )
            </if>
            <if test="customer!=null and customer!=''">
                AND rw.customer_name LIKE concat('%',#{customer},'%')
            </if>
            <if test="costType!=null">
                AND rw.cost_type = #{costType}
            </if>
            <if test="receiveStatus!=null and receiveStatus == 0 ">
                AND rw.apply_status = 0
            </if>
            <if test="receiveStatus!=null and receiveStatus !=0 ">
                AND rw.apply_status != 0
            </if>
            <if test="receiveType!=null and receiveType!=''">
                AND rw.recevice_type = #{receiveType}
            </if>
            <if test="startTime!=null and endTime!=null">
                AND rw.occur_date &gt;= #{startTime}
                AND rw.occur_date &lt;= #{endTime}
            </if>
            <if test="accid!=null and accid!=''">
                AND rw.account_id = #{accid}
            </if>
            <if test="fundsClassId!=null and fundsClassId!=''">
                AND rw.funds_class_id = #{fundsClassId}
            </if>
            ORDER BY
            rw.occur_date DESC
    </select>

    <select id="getReceiveWaterListBusiness" resultType="me.zhengjie.modules.system.domain.ReceiveWater">
        SELECT
        rw.*
        FROM
        receive_water rw
        LEFT JOIN sys_company_plate scp ON scp.wx_department_id = rw.recevice_company
        WHERE rw.del_flag = 0
            AND rw.cost_type = 1
        <if test="receiveCompany!=null and receiveCompany!=''">
            AND rw.recevice_company_name LIKE concat('%',#{receiveCompany},'%')
        </if>
        <if test="customer!=null and customer!=''">
            AND rw.customer_name LIKE concat('%',#{customer},'%')
        </if>
        <if test="receiveStatus!=null and receiveStatus == 0 ">
            AND rw.apply_status = 0
        </if>
        <if test="receiveStatus!=null and receiveStatus != 0 ">
            AND rw.apply_status != 0
        </if>
        <if test="receiveType!=null and receiveType!=''">
            AND rw.recevice_type = #{receiveType}
        </if>
        <if test="startTime!=null and endTime!=null">
            AND rw.occur_date &gt;= #{startTime}
            AND rw.occur_date &lt;= #{endTime}
        </if>
        ORDER BY
        rw.occur_date DESC
    </select>
</mapper>
