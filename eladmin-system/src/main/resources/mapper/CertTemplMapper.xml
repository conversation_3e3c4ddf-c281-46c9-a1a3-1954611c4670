<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.zhengjie.modules.system.mapper.CertTemplMapper">
    <select id="getCertTemplList" resultType="me.zhengjie.modules.system.domain.dto.CertTemplDto">
        SELECT
        ctr.*,
        ct.id as parent_id,
        ct.app_code as parent_app_code,
        ct.code as parent_code,
        ct.flow_code as parent_flow_code,
        ct.condition_str as parent_condition_str,
        ct.name as parent_name,
        ct.regular_expressions as parent_regular_expressions,
        ct.create_time as parent_create_time,
        ct.last_time as parent_last_time,
        ct.templ_data as parent_templ_data
        FROM
        cert_templ ct
        LEFT JOIN cert_templ_relation ctr ON ct.id = ctr.cert_templ_id
        where 1=1
        <if test="name!=null and name!=''">
            and ( ct.name like concat('%',#{name},'%')
                    or ctr.name like concat('%',#{name},'%')
                    or ctr.templ_abstract like concat('%',#{name},'%')
                    or ctr.field_summary like concat('%',#{name},'%')
                    or ctr.accounting_subjects like concat('%',#{name},'%')
                    or ctr.field_code like concat('%',#{name},'%')
            )
        </if>
    </select>
</mapper>
