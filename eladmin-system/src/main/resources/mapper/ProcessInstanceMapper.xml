<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.zhengjie.modules.workflow.mapper.ProcessInstanceMapper">
    <select id="getPaymentProcessListNew" resultType="me.zhengjie.modules.workflow.model.entity.ProcessInstance">
        SELECT
            pi.id,
            pi.sp_id,
            pi.sp_role_id,
            pi.flow_name,
            pi.flow_code,
            pi.sponsor_id,
            pi.sponsor_time,
            pi.finish_time,
            pi.status_id,
            pi.STATUS,
            pi.ccs,
            pi.after_every_node,
            pi.brief_content,
            pi.global_param1,
            pi.global_param2,
            pi.global_param3,
            pi.global_param4,
            pi.global_param5,
            pi.global_param6,
            pi.global_param7,
            pi.global_param8,
            pi.global_param9,
            pi.global_param10,
            pi.paid_money
        FROM
            process_instance pi
            LEFT JOIN wx_department wd ON wd.id = pi.global_param4
            LEFT JOIN sys_company_plate scp ON scp.wx_department_id = wd.dept_id
            LEFT JOIN sys_user su ON su.user_id = pi.sponsor_id
        WHERE
            scp.dict_id IN
            <foreach collection="ids" item="tmp" open="(" close=")" separator=",">
                #{tmp}
            </foreach>
            AND pi.flow_code IN
            <foreach collection="paymentCode" item="tmp" open="(" close=")" separator=",">
                #{tmp}
            </foreach>
            <if test="userId!=null and userId != '1697202609988829185'">
            AND (
            pi.sp_id = #{userId}
                <if test="roleIds != null and roleIds.size() > 0">
                    or (pi.sp_role_id IN
                    <foreach collection="roleIds" item="listItem" open="(" close=")" separator="," >
                        #{listItem}
                    </foreach> )
                </if>
                <if test="rolelistByComId != null and rolelistByComId.size() > 0">
                    or (concat(pi.sp_role_id, pi.global_param4) IN
                    <foreach collection="rolelistByComId" item="listItem" open="(" close=")" separator="," >
                        #{listItem}
                    </foreach>)
                </if>
            )
            </if>
            <if test="companyId!=null and companyId!=''">
                AND pi.global_param4 = #{companyId}
            </if>
           <if test="money!=null and money!=''">
                AND pi.global_param3 like concat('%',#{money},'%')
            </if>
            <if test="applyStatus!=null and applyStatus==1">
                AND (pi.status_id div 10 in (8, 9))  AND (pi.STATUS = 1 )
            </if>
            <if test="applyStatus!=null and applyStatus==2">
                AND ( pi.STATUS = 2 or  ( (pi.status_id div 10 > 8) AND ( pi.STATUS = 1 ) ) )
            </if>
            <if test="applyStatus!=null and applyStatus==3">
                AND pi.STATUS = 2
            </if>
            <if test="applyStatus==null">
                AND (((pi.status_id div 10 in (8, 9)) AND ( pi.STATUS = 1 )) OR pi.STATUS = 2)
            </if>
            <if test="code!=null and code!=''">
                and pi.flow_code = #{code}
            </if>
            <if test="userName!=null and userName!=''">
                and su.nick_name like concat('%',#{userName},'%')
            </if>
            <if test="startTime != null and endTime != null ">
                AND pi.sponsor_time &gt;= #{startTime}
                AND pi.sponsor_time &lt;= #{endTime}
            </if>
            ORDER BY
            pi.sponsor_time DESC,
            pi.finish_time DESC
    </select>
    <select id="getPaymentProcessList" resultType="me.zhengjie.modules.workflow.model.entity.ProcessInstance">
        SELECT
            pi.id,
            pi.sp_id,
            pi.sp_role_id,
            pi.flow_name,
            pi.flow_code,
            pi.sponsor_id,
            pi.sponsor_time,
            pi.finish_time,
            pi.status_id,
            pi.STATUS,
            pi.ccs,
            pi.after_every_node,
            pi.brief_content,
            pi.global_param1,
            pi.global_param2,
            pi.global_param3,
            pi.global_param4,
            pi.global_param5,
            pi.global_param6,
            pi.global_param7,
            pi.global_param8,
            pi.global_param9,
            pi.global_param10,
            pi.paid_money
        FROM
            process_instance pi
            LEFT JOIN wx_department wd ON wd.id = pi.global_param4
            LEFT JOIN sys_company_plate scp ON scp.wx_department_id = wd.dept_id
            LEFT JOIN sys_user su ON su.user_id = pi.sponsor_id
        WHERE
            scp.dict_id IN
            <foreach collection="ids" item="tmp" open="(" close=")" separator=",">
                #{tmp}
            </foreach>
            AND pi.flow_code IN
            <foreach collection="paymentCode" item="tmp" open="(" close=")" separator=",">
                #{tmp}
            </foreach>
            <if test="userId!=null and userId != '1697202609988829185'">
            AND (
            pi.sp_id = #{userId}
                <if test="roleIds != null and roleIds.size() > 0">
                    or (pi.sp_role_id IN
                    <foreach collection="roleIds" item="listItem" open="(" close=")" separator="," >
                        #{listItem}
                    </foreach> )
                </if>
                <if test="rolelistByComId != null and rolelistByComId.size() > 0">
                    or (concat(pi.sp_role_id, pi.global_param4) IN
                    <foreach collection="rolelistByComId" item="listItem" open="(" close=")" separator="," >
                        #{listItem}
                    </foreach>)
                </if>
            )
            </if>
            <if test="companyId!=null and companyId!=''">
                AND pi.global_param4 = #{companyId}
            </if>
           <if test="money!=null and money!=''">
                AND pi.global_param3 like concat('%',#{money},'%')
            </if>
            <if test="applyStatus!=null and applyStatus==1">
                AND (pi.status_id div 10 in (8, 9))  AND (pi.STATUS = 1 )
            </if>
            <if test="applyStatus!=null and applyStatus==2">
                AND ( pi.STATUS = 2 or  ( (pi.status_id div 10 > 8) AND ( pi.STATUS = 1 ) ) )
            </if>
            <if test="applyStatus!=null and applyStatus==3">
                AND pi.STATUS = 2
            </if>
            <if test="applyStatus==null">
                AND (((pi.status_id div 10 in (8, 9)) AND ( pi.STATUS = 1 )) OR pi.STATUS = 2)
            </if>
            <if test="code!=null and code!=''">
                and pi.flow_code = #{code}
            </if>
            <if test="userName!=null and userName!=''">
                and su.nick_name like concat('%',#{userName},'%')
            </if>
            <if test="startTime != null and endTime != null ">
                AND pi.sponsor_time &gt;= #{startTime}
                AND pi.sponsor_time &lt;= #{endTime}
            </if>
            ORDER BY
            pi.sponsor_time DESC,
            pi.finish_time DESC
    </select>

    <select id="getCount" resultType="me.zhengjie.modules.workflow.model.entity.ProcessInstance">
        SELECT
        pi.id,
        pi.sp_id,
        pi.sp_role_id,
        pi.flow_name,
        pi.flow_code,
        pi.sponsor_id,
        pi.sponsor_time,
        pi.finish_time,
        pi.status_id,
        pi.STATUS,
        pi.ccs,
        pi.after_every_node,
        pi.brief_content,
        pi.global_param1,
        pi.global_param2,
        pi.global_param3,
        pi.global_param4,
        pi.global_param5,
        pi.global_param6,
        pi.global_param7,
        pi.global_param8,
        pi.global_param9,
        pi.global_param10
        FROM
        process_instance pi
        LEFT JOIN wx_department wd ON wd.id = pi.global_param4
        LEFT JOIN sys_company_plate scp ON scp.wx_department_id = wd.dept_id
        WHERE pi.STATUS = 1 AND (pi.STATUS div 10 = 8)
        AND scp.dict_id IN
        <foreach collection="ids" item="tmp" open="(" close=")" separator=",">
            #{tmp}
        </foreach>
        AND pi.flow_code IN
        <foreach collection="paymentCode" item="tmp" open="(" close=")" separator=",">
            #{tmp}
        </foreach>
        <if test="deptId !=null and deptId!=''">
            and wd.dept_id = #{deptId}
        </if>
    </select>

    <select id="getHomeprocessing" resultType="me.zhengjie.modules.business.service.bean.ProcessForHomeBean">
        select spu.nick_name as `name`,wd.`name` as departmentname,if(wd.parent_id=1,wd.`name`,wdc.`name`) as companyname,pi.*,pi.`flow_name`as processname
        from process_instance as pi
        INNER JOIN sys_user as su on su.user_id = #{id}
        left JOIN sys_user as spu on spu.user_id  = pi.sponsor_id
<!--        left JOIN sys_process_detail as spd on spd.spare1 = pi.flow_code-->
        LEFT JOIN wx_department as wd on wd.dept_id = spu.main_department  and wd.del_flag=0
        LEFT JOIN wx_department as wdc on wd.parent_id = wdc.dept_id  and wdc.del_flag=0
        WHERE (
            pi.sp_id = #{id}
        <if test="rolelist != null and rolelist.size() > 0">
            or (pi.sp_role_id IN
            <foreach collection="rolelist" item="listItem" open="(" close=")" separator="," >
                #{listItem}
            </foreach>
            and pi.global_param4 IN
            <foreach collection="Deptids" item="dept" open="(" close=")" separator="," >
                #{dept}
            </foreach> )
        </if>
        <if test="rolelistByComId != null and rolelistByComId.size() > 0">
            or (concat(pi.sp_role_id, pi.global_param4) IN
            <foreach collection="rolelistByComId" item="listItem" open="(" close=")" separator="," >
                #{listItem}
            </foreach>)
        </if>
            )
        and pi.status = '1' and (pi.STATUS_ID div 10 not in (8, 9)) and pi.id is not null
        ORDER BY pi.sponsor_time desc
        LIMIT 7
    </select>
</mapper>
