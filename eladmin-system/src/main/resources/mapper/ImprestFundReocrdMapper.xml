<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.zhengjie.modules.system.mapper.ImprestFundReocrdMapper">
    <select id="selectIncomeMoney" resultType="java.math.BigDecimal">
        select sum(balance) from imprest_fund_reocrd where state = 2 and del_flag=0
        <if test="fundId!=null and fundId!=''">
            and imprest_fund_id = #{fundId}
        </if>
        <if test="startMonth!=null and startMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &gt;= #{startMonth}
        </if>
        <if test="endMonth!=null and endMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &lt; #{endMonth}
        </if>
    </select>
    <select id="incomeMoneyList" resultType="me.zhengjie.modules.system.domain.ImprestFundReocrd">
        select * from imprest_fund_reocrd where state = 2 and del_flag=0
        <if test="fundId!=null and fundId!=''">
            and imprest_fund_id = #{fundId}
        </if>
        <if test="startMonth!=null and startMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &gt;= #{startMonth}
        </if>
        <if test="endMonth!=null and endMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &lt; #{endMonth}
        </if>
        order by consumption_date desc
    </select>
    <select id="expenditureMoneyList" resultType="me.zhengjie.modules.system.domain.ImprestFundReocrd">
        select * from imprest_fund_reocrd where state = 1 and del_flag=0
        <if test="fundId!=null and fundId!=''">
            and imprest_fund_id = #{fundId}
        </if>
        <if test="startMonth!=null and startMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &gt;= #{startMonth}
        </if>
        <if test="endMonth!=null and endMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &lt; #{endMonth}
        </if>
        order by consumption_date desc
    </select>
    <select id="reocrdMoneyList" resultType="me.zhengjie.modules.system.domain.ImprestFundReocrd">
        select * from imprest_fund_reocrd where del_flag=0
        <if test="fundId!=null and fundId!=''">
            and imprest_fund_id = #{fundId}
        </if>
        <if test="startMonth!=null and startMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &gt;= #{startMonth}
        </if>
        <if test="endMonth!=null and endMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &lt; #{endMonth}
        </if>
        order by consumption_date desc
    </select>
    <select id="selectExpenditureMoney" resultType="java.math.BigDecimal">
        select sum(balance) from imprest_fund_reocrd where state = 1 and del_flag=0
        <if test="fundId!=null and fundId!=''">
            and imprest_fund_id = #{fundId}
        </if>
        <if test="startMonth!=null and startMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &gt;= #{startMonth}
        </if>
        <if test="endMonth!=null and endMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &lt; #{endMonth}
        </if>
    </select>
    <select id="getIncomeCount" resultType="java.lang.Integer">
        select count(*) from imprest_fund_reocrd where state = 2 and del_flag=0
        <if test="fundId!=null and fundId!=''">
            and imprest_fund_id = #{fundId}
        </if>
        <if test="startMonth!=null and startMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &gt;= #{startMonth}
        </if>
        <if test="endMonth!=null and endMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &lt; #{endMonth}
        </if>
    </select>
    <select id="getExpendCount" resultType="java.lang.Integer">
        select count(*) from imprest_fund_reocrd where state = 1 and del_flag=0
        <if test="fundId!=null and fundId!=''">
            and imprest_fund_id = #{fundId}
        </if>
        <if test="startMonth!=null and startMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &gt;= #{startMonth}
        </if>
        <if test="endMonth!=null and endMonth!=''">
            and DATE_FORMAT(consumption_date,'%Y-%m') &lt; #{endMonth}
        </if>
    </select>
    <select id="queryRecordAndWuLiu" resultType="me.zhengjie.modules.system.domain.bean.ImprestFundReocrdBean">
        select * from imprest_fund_reocrd ifr
        where
        ifr.del_flag=0
        <if test="fundId!=null and fundId!=''">
            and ifr.imprest_fund_id = #{fundId}
        </if>
        <if test="state!=null">
            and ifr.state = #{state}
        </if>
        <if test="status!=null">
            and ifr.status = #{status}
        </if>
        <if test="startMonth!=null and startMonth!=''">
            and DATE_FORMAT(ifr.consumption_date,'%Y-%m') &gt;= #{startMonth}
        </if>
        <if test="endMonth!=null and endMonth!=''">
            and DATE_FORMAT(ifr.consumption_date,'%Y-%m') &lt; #{endMonth}
        </if>
        order by ifr.consumption_date desc,ifr.create_time desc
    </select>
</mapper>
