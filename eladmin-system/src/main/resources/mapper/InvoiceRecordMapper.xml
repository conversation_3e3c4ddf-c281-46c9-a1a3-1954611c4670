<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.zhengjie.modules.system.mapper.InvoiceRecordMapper">

    <select id="getInvoiceCountByProcessIds" resultType="java.util.Map">
        SELECT
        ir.associated_process_id as processId,
        count(ir.id) as count
        FROM
        invoice_record ir
        where 1 = 1
        <if test="ids!=null">
            and ir.associated_process_id in
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        group by ir.associated_process_id
    </select>
</mapper>
