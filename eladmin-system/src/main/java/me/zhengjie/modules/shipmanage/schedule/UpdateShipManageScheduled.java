package me.zhengjie.modules.shipmanage.schedule;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dlcg.tms.bean.CostAggregationBean;
import com.dlcg.tms.client.ShipLineClient;
import lombok.extern.slf4j.Slf4j;
import me.zhengjie.modules.client.shipmanage.ShipManageClient;
import me.zhengjie.modules.system.service.PaymentWaterService;
import me.zhengjie.modules.system.service.ReceiveWaterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class UpdateShipManageScheduled {
    List<String> shipNames = Arrays.asList("和泰通12","和泰通1","和泰通7","和泰通9","和泰通11");
    @Autowired
    ShipLineClient shipLineClient;
    @Autowired
    ShipManageClient shipManageClient;
    @Autowired
    PaymentWaterService paymentWaterService;
    @Autowired
    ReceiveWaterService receiveWaterService;

//    @Scheduled(cron="0 0 0 1 * ?")
//    @Scheduled(fixedRate = 24 * 60 * 60 * 1000)
    // 船舶 挂帐 核销
    @Scheduled(fixedDelay=1000 * 60 * 30)
    public void shipAccountWriteOffScheduled(){
        String yearMonth = LocalDate.now().minusMonths(1).toString().substring(0,7);
        for(String shipName: shipNames) {
            JSONArray shipPayment = new JSONArray();
            JSONArray shipManage = new JSONArray();
            // 1. 根据船名 航次号 挂帐
            // 上个月 挂帐 gua_time
            Map<String, Object> shipMap = shipLineClient.getPaymentListByShipName(shipName, yearMonth);
//            JSONArray list = (JSONArray) shipMap.get("data");
            List<Object> list = (List<Object>) shipMap.get("data");
            // shipName 航次号voyageNumber 类型(cost_type_name) 费用（surplus）

// 修改账户 shipLineClient.updateCostAggregatePayment (array (id,money))

            // 船舶系统
            Map<String, Object> shipManagePaymentMap = shipManageClient.getPaymentListByShipName(shipName, yearMonth);
            // 运费列表 船名(shipName) 航次号（voyageNum） 类型(船运费） 费用(unreceivedAmount)
            JSONArray shipManagePaymentList = (JSONArray) shipManagePaymentMap.get("data");
            if(shipManagePaymentList==null){
                shipManagePaymentList = new JSONArray();
            }
            // 船舶 收入列表
            Map<String, Object> shipManageIncomeMap = shipManageClient.getPaymentShipCostByShipName(shipName, yearMonth);
            JSONArray shipManageCostList = (JSONArray) shipManageIncomeMap.get("data");
            if(shipManageCostList==null){
                shipManageCostList = new JSONArray();
            }
            // 匹配上后 核销
            for (int i = 0; i < list.size(); i++) {
//                JSONObject shipPriceObj = list.getJSONObject(i);
//                Object shipPriceObj = list.get(i);
                JSONObject shipPriceObj = JSONUtil.parseObj(list.get(i));
                // 航次号 类型 费用
                String voyageNumber = shipPriceObj.getStr("voyageNumber");
                String typeName = shipPriceObj.getStr("costTypeName");
                Double surplus = shipPriceObj.getDouble("surplus");
                for (int j = 0; j < shipManagePaymentList.size(); j++) {
                    JSONObject shipManagePriceObj = shipManagePaymentList.getJSONObject(j);
                    String voyageNumber1 = shipManagePriceObj.getStr("voyageNum");
                    String typeName1 = "海运费";
                    String typeYunName1 = "运费";
                    Double surplus1 = shipManagePriceObj.getDouble("unreceivedAmount");
                    // 航次号 类型 费用
//                    if (list.getJSONObject(i).getString("cost_type_name").equals(shipManagePaymentList.getJSONObject(j).getString("cost_type_name")))
//                        shipPayment.add(list.getJSONObject(i));
//                    shipManage.add(shipManagePaymentList.getJSONObject(j));
                    if (voyageNumber.equals(voyageNumber1) && (typeName.equals(typeName1) || typeName.equals(typeYunName1)) && surplus.equals(surplus1)) {
                        JSONObject jo = new JSONObject();
                        jo.set("id", shipPriceObj.getStr("id"));
                        jo.set("money", surplus);
                        shipPayment.add(jo);
                        JSONObject jo1 = new JSONObject();
                        jo1.set("id", shipManagePriceObj.getStr("id"));
                        jo1.set("type","voyageNumber");
                        jo1.set("money", surplus);
                        shipManage.add(jo1);
                    }
                }
                for(int j=0;j<shipManageCostList.size();j++){
                    JSONObject shipCostObj = shipManageCostList.getJSONObject(j);
                    String voyageNumber2 = shipCostObj.getStr("voyageNum");
                    String typeName2 = shipCostObj.getStr("typeName");
                    Double surplus2 = shipCostObj.getDouble("unpaidAmount");
                    // 航次号 类型 费用
//                    if(list.getJSONObject(i).getString("cost_type_name").equals(shipManageCostList.getJSONObject(j).getString("cost_type_name")))
//                        shipPayment.add(list.getJSONObject(i));
//                    shipManage.add(shipManageCostList.getJSONObject(j));
                    if (voyageNumber.equals(voyageNumber2) && typeName.equals(typeName2) && surplus.equals(surplus2)) {
                        JSONObject jo = new JSONObject();
                        jo.set("id", shipPriceObj.getStr("id"));
                        jo.set("money", surplus);
                        shipPayment.add(jo);
                        JSONObject jo1 = new JSONObject();
                        jo1.set("id", shipCostObj.getStr("id"));
                        jo1.set("type","shipCostRecordChild");
                        jo1.set("money", surplus);
                        shipManage.add(jo1);
                    }
                }
            }
            if(shipPayment.size()>0){
                shipLineClient.updateCostAggregatePayment(shipPayment);
                // 付款记录
                paymentWaterService.addOtherPaymentWater(shipPayment);
            }
            if(shipManage.size()>0){
                shipManageClient.updatePaymentByCostId(shipManage);
                // 收款记录
                receiveWaterService.addOtherReceiveWater(shipManage);
            }
        }
        //  月末 自动核销 业务系统成本 自动付款，船舶系统收入 自动收款
        // 业务系统条件：已挂帐 航次号 船名： 并且未支付 成本 包含滞期费等
        // 成本已支付 回写
        // 业务系统 船 ship_line（ship_id、
        // 船舶系统条件：已挂帐 航次号 船名 并且未收款 收入 包含滞期费等

        // 类型相等 成本=收入 直接设置已支付、已收款

        // 2. 生成银行流水
            // 根据 交易金额 生成其他指出、其他收入

//        月末， 统计 业务挂帐 与 船舶挂帐 ，
    //        和泰通12、1、7、9、11

//            挂帐
//        航次收入  核销金额
    //        自动生成对应记录，业务系统运费付款自动实现、船舶系统运费收款自动实现。
    //        财务系统自动生成 流水，其他支出， 其他收入

//       航次调整收入 核销金额
            // 滞期费
        // 业务系统 成本 滞期费 自动付款，船舶系统滞期费 自动收款


    }
}
