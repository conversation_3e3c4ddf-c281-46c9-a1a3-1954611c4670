package me.zhengjie.modules.operationlog.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @description /
* <AUTHOR>
* @date 2022-05-26
**/
@Data
@TableName("operation_log")
public class OperationLog implements Serializable {


    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Integer id;

    @NotNull
    @ApiModelProperty(value = "操作类型")
    private Integer type;

    @ApiModelProperty(value = "params1")
    private String params1;

    @ApiModelProperty(value = "params2")
    private String params2;

    @ApiModelProperty(value = "params3")
    private String params3;

    @ApiModelProperty(value = "params4")
    private String params4;

    @ApiModelProperty(value = "params5")
    private String params5;

    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    @ApiModelProperty(value = "创建人名称")
    @TableField(fill = FieldFill.INSERT)
    private String createByName;

    @ApiModelProperty(value = "创建时间")
    private Timestamp createTime;

    public void copy(OperationLog source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}