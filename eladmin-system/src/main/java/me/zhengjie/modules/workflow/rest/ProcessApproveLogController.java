/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.workflow.rest;

import me.zhengjie.annotation.Log;
import me.zhengjie.modules.workflow.model.entity.ProcessApproveLog;
import me.zhengjie.modules.workflow.service.ProcessApproveLogService;
import me.zhengjie.modules.workflow.service.dto.ProcessApproveLogQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2021-09-03
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "workflow管理")
@RequestMapping("/api/processApproveLog")
public class ProcessApproveLogController {

    private final ProcessApproveLogService processApproveLogService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('processApproveLog:list')")
    public void download(HttpServletResponse response, ProcessApproveLogQueryCriteria criteria) throws IOException {
        processApproveLogService.download(processApproveLogService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询workflow")
    @ApiOperation("查询workflow")
    @PreAuthorize("@el.check('processApproveLog:list')")
    public ResponseEntity<Object> query(ProcessApproveLogQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(processApproveLogService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增workflow")
    @ApiOperation("新增workflow")
    @PreAuthorize("@el.check('processApproveLog:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody ProcessApproveLog resources){
        return new ResponseEntity<>(processApproveLogService.save(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改workflow")
    @ApiOperation("修改workflow")
    @PreAuthorize("@el.check('processApproveLog:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody ProcessApproveLog resources){
        processApproveLogService.updateById(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除workflow")
    @ApiOperation("删除workflow")
    @PreAuthorize("@el.check('processApproveLog:del')")
    @DeleteMapping
    public ResponseEntity<Object> delete(@RequestBody String[] ids) {
        processApproveLogService.removeByIds(Arrays.asList(ids));
        return new ResponseEntity<>(HttpStatus.OK);
    }
}