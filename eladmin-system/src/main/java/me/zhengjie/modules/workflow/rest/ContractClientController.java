package me.zhengjie.modules.workflow.rest;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.zthzinfo.common.ResponseMapBuilder;
import lombok.extern.slf4j.Slf4j;
import me.zhengjie.annotation.AnonymousAccess;
import me.zhengjie.modules.contract.domain.Contract;
import me.zhengjie.modules.contract.enums.ContractModuleEnums;
import me.zhengjie.modules.contract.service.ContractService;
import me.zhengjie.modules.contract.service.dto.ContractQueryCriteria;
import me.zhengjie.modules.until.ParamsKeys;
import me.zhengjie.modules.workflow.enums.ProcessStatusEnum;
import me.zhengjie.modules.workflow.model.entity.ProcessInstance;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@RequestMapping("/restapi/contractclient")
@RestController
public class ContractClientController {

    @Autowired
    ContractService contractService;

    /**
     * 根据id(s)批量获取合同
     *
     * @param ids 合同id集合
     */
    @PostMapping("/queryContracts")
    @AnonymousAccess
    public List<Contract> queryContracts(@RequestParam String[] ids) {
        if (ids.length == 0) {
            return new ArrayList<>();
        }
        QueryWrapper<Contract> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", ids);
        queryWrapper.eq("del_flag", 0);
        List<Contract> list = contractService.list(queryWrapper);

        return list;
    }

    /**
     * 获取物流系统 流程审批人
     */
    @PostMapping("/getShipContractListPage")
    @AnonymousAccess
    public Map<String, Object> getShipContractListPage(
            @RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "contractNo", required = false) String contractNo,
            @RequestParam(value = "partyAName", required = false) String partyAName,
            @RequestParam(value = "partyBName", required = false) String partyBName,
            @RequestParam(value = "contractModule", required = false) Integer contractModule,
            @RequestParam(value = "signStartDate", required = false) String signStartDate,
            @RequestParam(value = "signEndDate", required = false) String signEndDate,
            @RequestParam(value = "spStatus", required = false) Integer spStatus
    ) {
        QueryWrapper<Contract> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.lambda().eq(Contract::getSource, "gangcai");
        if (contractNo != null && !"".equals(contractNo)) {
            queryWrapper.lambda().like(Contract::getContractNo, contractNo);
        }
        if (partyAName != null && !"".equals(partyAName)) {
            queryWrapper.lambda().like(Contract::getPartyAName, partyAName);
        }
        if (partyBName != null && !"".equals(partyBName)) {
            queryWrapper.lambda().like(Contract::getPartyBName, partyBName);
        }
        if (contractModule != null) {
            queryWrapper.lambda().eq(Contract::getContractModule, contractModule);
        }
        if (StringUtils.isNotBlank(signStartDate)) {
            queryWrapper.lambda().ge(Contract::getSignDate, signStartDate);
        }
        if (StringUtils.isNotBlank(signEndDate)) {
            queryWrapper.lambda().le(Contract::getSignDate, signEndDate);
        }
        if (spStatus != null) {
            queryWrapper.lambda().eq(Contract::getSpStatus, spStatus);
        }
        queryWrapper.lambda().orderByDesc(Contract::getCreateTime);
        com.github.pagehelper.Page<Contract> page = PageHelper.startPage(pageNo, pageSize)
                .doSelectPage(() ->{
                    contractService.list(queryWrapper);
                });
        for (Contract contract : page.getResult()) {
            Integer spStatust = contract.getSpStatus();
            String descByKey = ProcessStatusEnum.getDescByKey(spStatust);
            contract.setSpStatusName(descByKey);
            String descByKey1 = ContractModuleEnums.getDescByKey(contract.getContractModule());
            contract.setContractModuleName(descByKey1);
        }
        return ResponseMapBuilder.newBuilder().put("data", page.getResult()).put("total", page.getTotal())
                .putSuccess()
                .getResult();
    }


}
