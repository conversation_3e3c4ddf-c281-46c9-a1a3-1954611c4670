package me.zhengjie.modules.workflow.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dlcg.mobile.client.MessageClient;
import com.dlcg.mobile.enums.MessageTypeEnums;
import com.dlcg.mobile.model.ResultBody;
import com.dlcg.mobile.model.dto.*;
import com.dlcg.oa.client.ProcessClient;
import com.dlcg.tms.client.BaseDataClient;
import com.dlcg.tms.entity.SysSupplier;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import me.zhengjie.config.OaProperties;
import me.zhengjie.modules.business.domain.SysCompanyPlate;
import me.zhengjie.modules.business.domain.SysUser;
import me.zhengjie.modules.business.domain.SysUserPlate;
import me.zhengjie.modules.business.service.SysCompanyPlateService;
import me.zhengjie.modules.business.service.SysProcessService;
import me.zhengjie.modules.business.service.SysUserPlateService;
import me.zhengjie.modules.business.service.SysUserService;
import me.zhengjie.modules.business.service.bean.RoleByComId;
import me.zhengjie.modules.contract.service.ContractService;
import me.zhengjie.modules.exception.AlertException;
import me.zhengjie.modules.system.domain.ComeToPaymentWater;
import me.zhengjie.modules.system.domain.Role;
import me.zhengjie.modules.system.repository.UserRepository;
import me.zhengjie.modules.system.service.ComeToPaymentWaterService;
import me.zhengjie.modules.system.service.RoleService;
import me.zhengjie.modules.wechat.code.domain.WxDepartment;
import me.zhengjie.modules.wechat.code.service.WxDepartmentService;
import me.zhengjie.modules.workflow.common.PaymentConfig;
import me.zhengjie.modules.workflow.dynamicform.FormComponentProps;
import me.zhengjie.modules.workflow.dynamicform.FormItem;
import me.zhengjie.modules.workflow.dynamicform.FormSchema;
import me.zhengjie.modules.workflow.enums.NodeTypeEnum;
import me.zhengjie.modules.workflow.enums.ProcessStatusEnum;
import me.zhengjie.modules.workflow.model.document.DProcessInstance;
import me.zhengjie.modules.workflow.model.document.ProcessDefinition;
import me.zhengjie.modules.workflow.model.document.ProcessFormValue;
import me.zhengjie.modules.workflow.model.document.ProcessNode;
import me.zhengjie.modules.workflow.model.entity.ProcessApproveLog;
import me.zhengjie.modules.workflow.model.entity.ProcessInstance;
import me.zhengjie.modules.workflow.processor.IPostProcessor;
import me.zhengjie.modules.workflow.processor.predicate.IPredicate;
import me.zhengjie.modules.workflow.service.po.ProcessSubmitPo;
import me.zhengjie.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;


@Slf4j
@Service
public class TaskService {

    @Resource
    private ProcessInstanceService processInstanceService;
    @Resource
    private ProcessApproveLogService processApproveLogService;
    @Resource
    private RepositoryService repositoryService;


    @Autowired
    SysUserService sysUserService;

    @Autowired
    private Map<String, IPostProcessor> processorMap;
    @Autowired
    private Map<String, IPredicate> predicateMap;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private BaseDataClient baseDataClient;



	@Resource
    RoleService sysRoleService;
	@Autowired
    UserRepository userRepository;
    @Autowired
    ProcessClient processClient;
    @Autowired
    com.zthz.wuliu.client.ProcessClient hsProcessClient;
    @Autowired
    WxDepartmentService wxDepartmentService;
    @Autowired
    SysCompanyPlateService sysCompanyPlateService;
    @Autowired
    SysUserPlateService sysUserPlateService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    MessageClient messageClient;
    @Autowired
    ComeToPaymentWaterService comeToPaymentWaterService;
    @Autowired
    OaProperties oaProperties;

    @Autowired
    SysProcessService sysProcessService;
    @Autowired
    ContractService contractService;

    @Autowired
    PaymentConfig paymentConfig;

    /**
     * 查询个人任务
     * @param type 1：待我处理的，2：我已处理，3：我发起的，4：抄送我的
     * @return
     */
    public LambdaQueryWrapper<ProcessInstance> findPersonalTask(Integer type,Integer status,String keyword,String code,String content){

        LambdaQueryWrapper<ProcessInstance> queryWrapper = new LambdaQueryWrapper<>();

        if (StrUtil.isNotBlank(code)){
            queryWrapper.eq(ProcessInstance::getFlowCode,code);
        }
        if (status != null){
            queryWrapper.eq(ProcessInstance::getStatus,status);
        }
        if (StrUtil.isNotBlank(keyword)){
//            UserQueryCriteria userQueryCriteria = new UserQueryCriteria();
//            userQueryCriteria.setNickName(keyword);
            LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.like(SysUser::getNickName,keyword);
            wrapper.eq(SysUser::getEnabled,true);
            List<SysUser> sysUsers = sysUserService.list(wrapper);
//            List<UserDto> userDtos = sysUserService.queryAll(userQueryCriteria);
            List<String> ids = sysUsers.stream().map(SysUser::getUserId).map(String::valueOf).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(ids)){
                return null;
            }
            queryWrapper.in(ProcessInstance::getSponsorId,ids);
        }
        if(StrUtil.isNotBlank(content)){
            String s = "%"+content+"%";
            queryWrapper.like(ProcessInstance::getBriefContent,s);
        }
        switch (type){
            case 1:
				List<String> roles = sysUserService.getUserRole(String.valueOf(SecurityUtils.getCurrentUserId()));
				// 1：待我处理的
				queryWrapper.and(wrapper -> wrapper.eq(ProcessInstance::getSpId,String.valueOf(SecurityUtils.getCurrentUserId()))
											.or().in(ProcessInstance::getSpRoleId, roles ));

				queryWrapper.eq(ProcessInstance::getStatus,ProcessStatusEnum.SPING.getKey())
                        .orderByDesc(ProcessInstance::getSponsorTime)
                        .orderByDesc(ProcessInstance::getFinishTime);
                break;
            case 2:
                // 2：我已处理
                List<ProcessApproveLog> logs = processApproveLogService.list(new LambdaQueryWrapper<ProcessApproveLog>()
                        .eq(ProcessApproveLog::getSpId, String.valueOf(SecurityUtils.getCurrentUserId()))
                        .orderByDesc(ProcessApproveLog::getApproveTime)
                );
                List<String> ids = logs.stream()
                        .map(ProcessApproveLog::getProcessId)
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtil.isEmpty(ids)){
                    return null;
                }
                queryWrapper.in(ProcessInstance::getId,ids)
                        .ne(ProcessInstance::getStatus,ProcessStatusEnum.RESUBMIT.getKey())
                        .orderByDesc(ProcessInstance::getSponsorTime)
                        .orderByDesc(ProcessInstance::getFinishTime);
                break;
            case 3:
                // 3：我发起的
                queryWrapper.eq(ProcessInstance::getSponsorId,String.valueOf(SecurityUtils.getCurrentUserId()))
                        .ne(ProcessInstance::getStatus,ProcessStatusEnum.RESUBMIT.getKey())
                        .orderByDesc(ProcessInstance::getSponsorTime)
                        .orderByDesc(ProcessInstance::getFinishTime)
                ;
                break;
            case 4:
                // 4：抄送我的
                queryWrapper.like(ProcessInstance::getCcs,String.valueOf(SecurityUtils.getCurrentUserId()))
                        .ne(ProcessInstance::getStatus,ProcessStatusEnum.RESUBMIT.getKey())
                    .orderByDesc(ProcessInstance::getSponsorTime)
                    .orderByDesc(ProcessInstance::getFinishTime)
                ;
                break;
            default:
                throw new AlertException("类型有误");
        }
        return queryWrapper;
    }
    public Map<String,Object> getProcessVal(String id){
        ProcessInstance instance = processInstanceService.getById(id);
        if (null == instance || StrUtil.isBlank(instance.getFlowCode())){
            return null;
        }
        Map<String,Object> result = new HashMap<>();
        // 表单内容
        List<DProcessInstance> formValues = mongoTemplate.find(new Query(Criteria.where("_id").is(instance.getId())), DProcessInstance.class);
        if (CollectionUtil.isNotEmpty(formValues)){
            result.put("formValue",formValues.get(0));
        }
        List<String> cuids = new ArrayList<>(Arrays.asList(StringUtils.delimitedListToStringArray(instance.getCcs(), ",")));
        if(cuids!=null && cuids.size()>0){
            Map<String,SysUser> userCache = new HashMap<>();
            sysUserService.listByUserIds(cuids)
                    .forEach(item -> userCache.put(item.getUserId(),item));
            // 抄送人
            List<Map<String,Object>> m3List =new ArrayList<>();
            cuids.forEach(item -> {
                Map<String,Object> m3User = new HashMap<>();
                m3User.put("id",Optional.ofNullable(userCache.get(item)).map(SysUser::getUserId).orElse(null));
                m3User.put("name",Optional.ofNullable(userCache.get(item)).map(SysUser::getNickName).orElse(null));
                m3User.put("avatar",Optional.ofNullable(userCache.get(item)).map(SysUser::getAvatarPath).orElse(null));
                m3List.add(m3User);
            });
            result.put("ccs",m3List);
        }

        return result;
    }

    final String COLOR_SUCCESS="#67C23A";
    final String COLOR_CURRENT="#E6A23C";
    final String COLOR_REJECT="#F56C6C";
    final String COLOR_SKIP="#DFE4ED";
    /**
     * 根据实例ID获取流程
     */
    public Map<String,Object> findTask(String processId,String userName) throws JsonProcessingException {
//        StopWatch stopWatch = new StopWatch("findTask");
//        stopWatch.start("findTask");
        Map<String,Object> result = new HashMap<>();
        AtomicBoolean btnIsShow = new AtomicBoolean(false);

        ProcessInstance instance = processInstanceService.getById(processId);
        if (null == instance || StrUtil.isBlank(instance.getFlowCode())){
            throw new AlertException("请检查该实例是否存在，并且flowcode是否为null");
        }
//        stopWatch.stop();
//        stopWatch.start("getProcessDefinitionByCode");
        ProcessDefinition processDefinition = repositoryService.getProcessDefinitionByCode(instance.getFlowCode());
        Assert.notNull(processDefinition);
        result.put("useComponent",Optional.ofNullable(processDefinition.getUseViewComponent()).orElse(false));
        result.put("viewName",processDefinition.getViewComponent());
        result.put("flowName",instance.getFlowName());
        result.put("flowCode",instance.getFlowCode());
		result.put("statusId",instance.getStatus());
		result.put("nodeId",instance.getStatusId());
//        stopWatch.stop();
//        stopWatch.start("getProcessVal");
        // 表单内容
        List<DProcessInstance> formValues = mongoTemplate.find(new Query(Criteria.where("_id").is(instance.getId())), DProcessInstance.class);

        JSONObject formValue = null;
        if (CollectionUtil.isNotEmpty(formValues)) {
            formValue = formValues.get(0).getFromvalue();
        }
//        stopWatch.stop();
//        stopWatch.start("getProcessNode");
        //初始化审批流程
        processInstanceService.processInitProcess(processDefinition,instance,formValue);
//        stopWatch.stop();
//        Iterator<ProcessNode> iterator = processDefinition.getProcess().iterator();
//        while (iterator.hasNext()) {
//            ProcessNode item = iterator.next();
//            if (item.getProcessbycompany() == null) {
//                continue;
//            }
//            List<Map<String, Object>> mapList = item.getProcessbycompany();
//            for (Map<String, Object> stringObjectMap : mapList) {
//                if (!instance.getGlobalParam4().equals(stringObjectMap.get("cid"))) {
//                    continue;
//                }
//                if (stringObjectMap.get("peopleid") != null) {
//                    item.setOperId(String.valueOf(stringObjectMap.get("peopleid")));
//                    item.setRule(null);
//                } else if (stringObjectMap.get("roleid") != null) {
//                    item.setRule(String.valueOf(stringObjectMap.get("roleid")));
//                }
//                //跳过时，删除当前节点，修改上一节点的next
//                else if (stringObjectMap.get("isSkip") != null) {
//                    for (ProcessNode process : processDefinition.getProcess()) {
//                        if (Objects.equals(process.getNext(),item.getId())) {
//                            process.setNext(item.getNext());
//                        }
//                        if (process.getDynamicNexts() != null) {
//                            for (ProcessNode.DynamicNext dynamicNext : process.getDynamicNexts()) {
//                                if (Objects.equals(dynamicNext.getNext(), item.getId())) {
//                                    dynamicNext.setNext(item.getNext());
//                                }
//                            }
//                        }
//                    }
//                    iterator.remove();
//                    break;
//                }
//            }
//
//        }


//        stopWatch.start("getProcessNode319");
        List<String> uids = processDefinition.getProcess()
                .stream()
                .map(ProcessNode::getOperId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
//        stopWatch.stop();
//        stopWatch.start("getProcessNode322");
        uids.add(instance.getSponsorId());
//        stopWatch.stop();
//        stopWatch.start("getProcessNode325");
        List<String> cuids = new ArrayList<>();
        if (StrUtil.isNotBlank(instance.getCcs())){
            cuids = new ArrayList<>(Arrays.asList(StringUtils.delimitedListToStringArray(instance.getCcs(), ",")));
            uids.addAll(cuids);
        }
//        stopWatch.stop();
//        stopWatch.start("getProcessNode331");
        uids = uids.stream().distinct().collect(Collectors.toList());
//        stopWatch.stop();
//        stopWatch.start("getProcessNode332");
        Map<String,SysUser> userCache = new HashMap<>();
//        stopWatch.stop();
        if (CollectionUtil.isNotEmpty(uids)){
//            stopWatch.start("getProcessNode344");
//         List<UserDto>  userDtoList=  sysUserService.getAllByIds(uids);
            List<SysUser> userList = sysUserService.listByUserIds(uids);
//            stopWatch.stop();
//            stopWatch.start("getProcessNode347");
            userList.forEach(item -> userCache.put(String.valueOf(item.getUserId()),item));
//            userDtoList.forEach(item -> userCache.put(String.valueOf(item.getId()),item));
//            stopWatch.stop();
        }

//        stopWatch.start("getProcessNode333");
        String formSchemaStr = processDefinition.getFormSchema();
        if (processDefinition.getFormSchema() != null){
            try {
                FormSchema formSchema = objectMapper.readValue(processDefinition.getFormSchema(), FormSchema.class);

                Integer nodeId = instance.getStatusId();
                processDefinition.getProcess()
                        .stream()
                        .filter(item -> item.getId().equals( nodeId))
                        .findFirst()
                        .ifPresent(item -> {
                            // 字段可否编辑 ｜ 字段是否隐藏
                            if (null != item.getHideFields()){
                                item.getHideFields().forEach(c -> {
                                    FormItem formItem = formSchema.getProperties().get(c);
                                    if(null != formItem){
                                        formItem.setHidden(true);
                                    }
                                });
                            }
                            if(null != item.getFieldStatus()){
                                ProcessNode.FieldStatus fieldStatus = item.getFieldStatus();
                                List<String> fields = fieldStatus.getFields();
                                if(fieldStatus.getModel().equals(0)){
                                    // 部分字段可以编辑
                                    formSchema.getProperties().keySet().forEach(c -> {
                                        if (!fields.contains(c)){
                                            FormItem formItem = formSchema.getProperties().get(c);
                                            if(null != formItem){
                                                if(formItem.getComponent().getProps() == null){
                                                    formItem.getComponent().setProps(new FormComponentProps());
                                                }
                                                formItem.getComponent().getProps().setDisabled(true);
                                            }
                                        }
                                    });
                                }else if(fieldStatus.getModel().equals(1)){
                                    // 部分字段不可以编辑
                                    fields.forEach(c -> {
                                        FormItem formItem = formSchema.getProperties().get(c);
                                        if(null != formItem){
                                            if(formItem.getComponent().getProps() == null){
                                                formItem.getComponent().setProps(new FormComponentProps());
                                            }
                                            formItem.getComponent().getProps().setDisabled(true);
                                        }
                                    });
                                }
                            }else{
                                // 默认审核人员全部字段都不可以编辑
                                formSchema.getProperties().keySet().forEach(c -> {
                                    FormItem formItem = formSchema.getProperties().get(c);
                                    if(formItem.getComponent().getProps() == null){
                                        formItem.getComponent().setProps(new FormComponentProps());
                                    }
                                    formItem.getComponent().getProps().setDisabled(true);
                                });
                            }
                        });
                formSchemaStr = formSchema.toJsonString();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("解析动态表单异常：{}",e.getMessage());
            }
        }

        result.put("formSchema",formSchemaStr);
        if (CollectionUtil.isNotEmpty(formValues)){
            DProcessInstance dProcessInstance = formValues.get(0);
            //重新获取图片水印地址 lijl
//            String  param2 = dProcessInstance.getGlobalParam2();
//            JSONObject jsonObject = JSONUtil.parseObj(param2);
//            if(jsonObject.containsKey("id") && StrUtil.isNotBlank(jsonObject.getStr("id"))){
//                String contract_id = jsonObject.getStr("id");
//                Contract byId = contractService.getById(contract_id);
//                String water_mark_preview_file_url = byId.getWaterMarkPreviewFileUrl();
//                if(!StringUtils.isEmpty(water_mark_preview_file_url)){
//                    if(jsonObject.containsKey("previewFileUrl") && StrUtil.isNotBlank(jsonObject.getStr("previewFileUrl"))){
//                        jsonObject.set("previewFileUrl",water_mark_preview_file_url);
//                    }
//                    if(jsonObject.containsKey("fileUrl") && StrUtil.isNotBlank(jsonObject.getStr("fileUrl"))){
//                        jsonObject.set("fileUrl",water_mark_preview_file_url);
//                    }
//                    dProcessInstance.setGlobalParam2(jsonObject.toString());
//                }
//            }
            result.put("formValue",dProcessInstance);
        }



//        stopWatch.stop();
//        stopWatch.start("getProcessNode356");
        SysUser user = new SysUser();
        if(StrUtil.isNotBlank(userName)){
//            user = userRepository.findByUsername(userName);
           user= sysUserService.getOne(new QueryWrapper<SysUser>().eq("username",userName),false);
        }
//        stopWatch.stop();
//        stopWatch.start("getProcessNode362");
        String usreId = user!=null ? user.getUserId() : null;
        String currentUser = StrUtil.blankToDefault(SecurityUtils.getCurrentUserId(), usreId);
        Assert.notNull(currentUser,"当前登录人为空");
//        stopWatch.stop();
//        stopWatch.start("getProcessNode364");
        // 审批流程
        // 发起人 -> 审批人 -> 抄送人
        Map<String,Object> spFlow = new HashMap<>();
        // 发起人
        Map<String,Object> m1User = new HashMap<>();
        m1User.put("id",Optional.ofNullable(userCache.get(instance.getSponsorId())).map(SysUser::getUserId).orElse(null));
        m1User.put("name", StrUtil.equals(instance.getSponsorId(),currentUser) ? "我" :Optional.ofNullable(userCache.get(instance.getSponsorId())).map(SysUser::getNickName).orElse(null));
        m1User.put("avatar",Optional.ofNullable(userCache.get(instance.getSponsorId())).map(SysUser::getAvatarPath).orElse(null));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        m1User.put("sponsorTime",simpleDateFormat.format(instance.getSponsorTime()));
//        stopWatch.stop();
//        stopWatch.start("getProcessNode380");
        String sta = "";
        String reason="";
        if(Objects.equals(Integer.parseInt(instance.getStatus()), ProcessStatusEnum.CANCEL.getKey())){
            LambdaQueryWrapper<ProcessApproveLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProcessApproveLog::getProcessId,instance.getId());
            queryWrapper.eq(ProcessApproveLog::getNodeId, NodeTypeEnum.START.getCode());
            queryWrapper.eq(ProcessApproveLog::getApproveResult,ProcessStatusEnum.CANCEL.getKey());
            queryWrapper.orderByDesc(ProcessApproveLog::getApproveTime);
            ProcessApproveLog processApproveLog = processApproveLogService.getOne(queryWrapper,false);
            reason = StrUtil.isNotBlank(processApproveLog.getApproveComment())?processApproveLog.getApproveComment():"";
            sta = "(已撤回)";
            m1User.put("sponsorTime",simpleDateFormat.format(instance.getSponsorTime())+"|"+simpleDateFormat.format(processApproveLog.getApproveTime()));
        }
        Map<String,Object> m1 = new HashMap<>();
        m1.put("content","发起申请"+sta);
        m1.put("item",m1User);
        m1.put("reason",reason);
        spFlow.put("start",m1);
//        stopWatch.stop();
//        stopWatch.start("getProcessNode402");
        // 审批人
        List<Map<String,Object>> m2 = new ArrayList<>();
        AtomicBoolean sping = new AtomicBoolean(false);
        Map<Integer,Integer> spingStatusMap = new HashMap<>();
        spingStatusMap.put(0,0); // 当前
        spingStatusMap.put(1,0); // 总的
        // 审批意见
        List<ProcessApproveLog> approveLogs = processApproveLogService.list(new LambdaQueryWrapper<ProcessApproveLog>()
                .eq(ProcessApproveLog::getProcessId, processId));
        approveLogs.sort(Comparator.comparing(ProcessApproveLog::getApproveTime));
        Map<Integer,ProcessApproveLog> logMap = new HashMap<>();
        approveLogs.forEach(item -> logMap.put(item.getNodeId(),item));
//        stopWatch.stop();
//        stopWatch.start("getProcessNode417");
		// 审批角色code
		List<String> spRuleCodes = processDefinition.getProcess().stream()
				.map(ProcessNode::getRule)
				.filter(rule -> StrUtil.isNotBlank(rule))
				.distinct()
				.collect(Collectors.toList());
        if (StrUtil.isNotBlank(instance.getSpRoleId()) && !spRuleCodes.contains(instance.getSpRoleId())) {
            spRuleCodes.add(instance.getSpRoleId());
        }


		List<Role> rulesTmp = new ArrayList<>();
		if (spRuleCodes != null || spRuleCodes.size() > 0) {
			rulesTmp = sysRoleService.getRolesByCodes(spRuleCodes);
		}
		final List<Role> rules = rulesTmp;
//        stopWatch.stop();
//        stopWatch.start("getProcessNode429");
        // 排序 所有流程
		List<ProcessNode> nodesByThisProcess = getNodesByProcessInstance(processDefinition, instance, formValue);
//        stopWatch.stop();
//        stopWatch.start("getProcessNode433");
        List<String> list1;
        if (StrUtil.isNotBlank(instance.getSpRoleId())) {
            list1 = sysUserService.getRoleUserIdByCompany(instance.getSpRoleId(), instance.getGlobalParam4());
        } else {
            list1 = new ArrayList<>();
        }
//        stopWatch.stop();
//        stopWatch.start("getProcessNode440");
//		List<String> currentRoles = sysUserService.getRoleCodesByUserId(currentUser);
		LambdaQueryWrapper<SysUserPlate> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(SysUserPlate::getUserId,currentUser);
        List<SysUserPlate> UserPlates = sysUserPlateService.list(queryWrapper);
        List<Long> currentUserPlates = UserPlates.stream().map(SysUserPlate::getDictId).collect(Collectors.toList());
//        stopWatch.stop();
//        stopWatch.start("getProcessNode448");
        String finalCurrentUser = currentUser;
        List<WxDepartment> WxDepartments = wxDepartmentService.list();
        List<SysCompanyPlate> plateList = sysCompanyPlateService.list();
//        stopWatch.stop();
//        stopWatch.start("getProcessNode452");
        JSONObject finalFormValue = formValue;
        String afterEveryNode = processDefinition.getAfterEveryNode();
        IPostProcessor postProcessor = processorMap.get(afterEveryNode);
        nodesByThisProcess.forEach(item -> {
            if (NodeTypeEnum.NODE.getKey().equals(item.getType())){
				if (NodeTypeEnum.START.getKey().equals(item.getType())){
					return;
				}
                Map<String,Object> m2User = new HashMap<>();
                m2User.put("id",Optional.ofNullable(userCache.get(item.getOperId())).map(SysUser::getUserId).orElse(null));
                m2User.put("name",Optional.ofNullable(userCache.get(item.getOperId())).map(SysUser::getNickName).orElse(null));

				// sp_type: 0头像、1角色名
				if (StrUtil.isNotBlank(item.getRule()) || (Objects.equals(item.getId(), instance.getStatusId()) && StrUtil.isNotBlank(instance.getSpRoleId()))) {
					m2User.put("sp_type", "1");
                    String ruleCode = Optional.ofNullable(item.getRule()).orElse(instance.getSpRoleId());
                    List<Role> roles = rules.stream().filter(r -> Objects.equals(r.getEnname(), ruleCode)).collect(Collectors.toList());
					if (roles != null && roles.size() > 0) {
						m2User.put("avatar", roles.get(0).getName());
					} else {
						m2User.put("avatar", null);
					}

					List<String> username;
					if(item.getId()!=null && NodeTypeEnum.NODE7.getCode() == item.getId()/10*10){
					    // 80 财务出纳 增加按版块查询【sungf 2022/02/23】
                        username = sysUserService.getroleuserbycompanyAndPlate(ruleCode,instance.getGlobalParam4());
                    }else{
                        username = sysUserService.getroleuserbycompany(ruleCode,instance.getGlobalParam4());
                    }

                    m2User.put("userlist",username);
				} else if (StrUtil.isNotBlank(item.getSpOperIdGetter())) {
					m2User.put("sp_type", "1");
					m2User.put("avatar", item.getName());
                    instance.setCurrentQueryNode(item);
                    String spOperId = processInstanceService.invokeDynamicMethod(instance, postProcessor, item.getSpOperIdGetter());
                    if (StrUtil.isNotBlank(spOperId)) {
                        SysUser sysUser = sysUserService.getById(spOperId);
                        List<String> username = new ArrayList<>();
                        if(sysUser!=null){
                            username.add(sysUser.getNickName());
                        }
                        m2User.put("userlist",username);
                    }
				} else if (StrUtil.isNotBlank(item.getSpRuleGetter())) {
//                    String afterEveryNode = processDefinition.getAfterEveryNode();
//                    IPostProcessor postProcessor = processorMap.get(afterEveryNode);
                    instance.setCurrentQueryNode(item);
                    String spRule = processInstanceService.invokeDynamicMethod(instance, postProcessor, item.getSpRuleGetter());
                    m2User.put("sp_type", "1");
                    m2User.put("avatar", item.getName());
                    if (StrUtil.isNotBlank(spRule)) {
                        List<String> userNames = sysUserService.getroleuserbycompany(spRule, instance.getGlobalParam4());
                        m2User.put("userlist",userNames);
                    }
                } else {
					m2User.put("sp_type", "0");
					m2User.put("avatar", Optional.ofNullable(userCache.get(item.getOperId())).map(SysUser::getAvatarPath).orElse(null));

				}
//                if ("部门负责人审批".equals(item.getName())){
//                    LambdaQueryWrapper<SysUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//                    lambdaQueryWrapper.eq(SysUser::getUserId,instance.getSponsorId());
//                    List<SysUser> userList = sysUserService.list(lambdaQueryWrapper);
//                    LambdaQueryWrapper<SysUser> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
//                    String leader = sysUserService.getLeaderByProcessInstance(userList.get(0), instance.getId(), finalFormValue);
//                    lambdaQueryWrapper1.eq(SysUser::getWxUserId, leader);
//                    List<SysUser> users = sysUserService.list(lambdaQueryWrapper1);
//                    List<String> username = new ArrayList<>();
//                    if (users !=null && users.size() >0){
//                        for (SysUser sysUser : users) {
//                            username.add(sysUser.getNickName());
//                        }
//                    } else {
//                        username.add("管理员");
//                    }
//                    m2User.put("userlist",username);
//                }

                Map<String,Object> m22 = new HashMap<>();
                if(item.getId()!=null && (item.getId()/10*10)==NodeTypeEnum.NODE7.getCode()){
                    m22.put("content","出纳");
                    // 判断是否付款
//                    if(instance.getStatus() != null && ProcessStatusEnum.AGREE.getKey() == Integer.parseInt(instance.getStatus())){
//                        m22.put("content","出纳(已付款)");
//                    }
                }else{
                    m22.put("content","审批人");
                }

                m22.put("item",m2User);
                m22.put("status",instance.getStatus());

                spingStatusMap.put(1,spingStatusMap.get(1)+1);
                if (!sping.get()){
                    spingStatusMap.put(0,spingStatusMap.get(0)+1);
                    // 审核通过
                    m22.put("color",COLOR_SUCCESS);
                    m22.put("size","large");
                }

                // 审批意见
                if (logMap.containsKey(item.getId())){
                    ProcessApproveLog approveLog = logMap.get(item.getId());
                    if (StrUtil.isNotBlank(approveLog.getApproveComment())){
                        // 系统跳过 改变颜色
                        if (approveLog.getApproveComment().equals("系统跳过") && !sping.get()){
                            m22.put("color",COLOR_SKIP);
                            m22.put("size","normal");
                        }
                        int clen = approveLog.getApproveComment().length();
                        int crow = (int)Math.ceil(clen / 22.0);
                        StringBuilder sbf = new StringBuilder();
                        for (int i = 0; i < crow; i++) {
                            sbf.append(approveLog.getApproveComment(), i*22, Math.min((i+1)*22,clen));
                            if (i+1 < crow){
                                sbf.append("<br/>");
                            }
                        }
                        m22.put("comment",sbf.toString());
                    }
                    m22.put("commentTime", DateUtil.format(approveLog.getApproveTime(),"yyyy/MM/dd HH:mm"));
                    LambdaQueryWrapper<SysUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    userLambdaQueryWrapper.eq(SysUser :: getUserId,approveLog.getSpId());
                    List<SysUser> userList = sysUserService.list(userLambdaQueryWrapper);
                    if (userList != null && userList.size() > 0){
                        SysUser sysUser = userList.get(0);
                        Map<String,Object> userBean = (Map<String, Object>) m22.get("item");
                        userBean.put("name",sysUser.getNickName());
                        userBean.put("avatar",sysUser.getAvatarPath());
                        userBean.put("id",sysUser.getUserId());
                        userBean.put("sp_type",0);
                        m22.put("item",userBean);
                    }
                }

                if (Objects.equals(item.getId(),instance.getStatusId())){
                    if(item.getId()!=null && (item.getId()/10*10)==NodeTypeEnum.NODE7.getCode()){
                        m22.put("content","出纳(付款中)");
                    }else{
                        m22.put("content","审批人" + "("+ProcessStatusEnum.getDescByKey(Integer.parseInt(instance.getStatus()))+")");
                    }

                    if (instance.getStatus() != null && ProcessStatusEnum.SPING.getKey() == Integer.parseInt(instance.getStatus())){
                        //当前节点是否需要板块下用户审批
                        Integer plate = item.getIsPlate();

                        m22.put("color",COLOR_CURRENT);
                        m22.put("size","large");
                        sping.set(true);

                        // 审批中状态并且满足以下条件可以显示审核按钮
                        if(StrUtil.equals(instance.getSpId(), finalCurrentUser)){
                            // 当前登陆人是审批人
                            btnIsShow.set(true);
                        }else if(StrUtil.isNotBlank(instance.getSpRoleId())){
                            if(plate != null){
                                String dept = instance.getGlobalParam4();
                                if(StrUtil.isBlank(dept)){
                                    return;
                                }
                                WxDepartment wxDepartment = WxDepartments.stream().filter(a->Objects.equals(a.getId(),dept)).findFirst().orElse(new WxDepartment());
                                if(wxDepartment.getDeptId() ==null){
                                    return;
                                }
                                SysCompanyPlate sysCompanyPlate = plateList.stream()
                                        .filter(b -> Objects.equals(b.getWxDepartmentId(), String.valueOf(wxDepartment.getDeptId())))
                                        .findFirst().orElse(new SysCompanyPlate());
                                if(sysCompanyPlate.getDictId() ==null){
                                    return;
                                }
                                if(currentUserPlates.contains(sysCompanyPlate.getDictId()) && list1.contains(currentUser)){
                                    btnIsShow.set(true);
                                }
                            } else {
                                if (list1.contains(currentUser)) {
                                    btnIsShow.set(true);
                                }
                            }
                        }
                    }else if(instance.getStatus() != null && ProcessStatusEnum.AGREE.getKey() == Integer.parseInt(instance.getStatus())){
                        // 审批通过
                        m22.put("color",COLOR_SUCCESS);
                        m22.put("size","large");
                        sping.set(true);
                    }else if(instance.getStatus() != null && ProcessStatusEnum.REJECT.getKey() == Integer.parseInt(instance.getStatus())){
                        // 审批拒绝
                        m22.put("color",COLOR_REJECT);
                        m22.put("size","large");
                        sping.set(true);
                    }
                }

                m2.add(m22);
            }
        });
        spFlow.put("sp",m2);
//        stopWatch.stop();
//        stopWatch.start("spFlow695");
        // 抄送人
        List<Map<String,Object>> m3List =new ArrayList<>();
        cuids.forEach(item -> {
            Map<String,Object> m3User = new HashMap<>();
            m3User.put("id",Optional.ofNullable(userCache.get(item)).map(SysUser::getUserId).orElse(null));
            m3User.put("name",Optional.ofNullable(userCache.get(item)).map(SysUser::getNickName).orElse(null));
            m3User.put("avatar",Optional.ofNullable(userCache.get(item)).map(SysUser::getAvatarPath).orElse(null));
            m3List.add(m3User);
        });
        Map<String,Object> m3 = new HashMap<>();
        m3.put("content","抄送（"+m3List.size()+"）人");
        m3.put("items",m3List);
//        stopWatch.stop();
//        stopWatch.start("spFlow715");
        if (spingStatusMap.get(0) .equals( spingStatusMap.get(1))){
            // 审批人全部通过后，抄送人显示图标
            m3.put("color",COLOR_SUCCESS);
            m3.put("size","large");
        }

        spFlow.put("ccs",m3);


        result.put("spFlow",spFlow);
//        stopWatch.stop();
//        stopWatch.start("spFlow727");
        // 可否处理
        // 可处理： 当前审批人是自己 && 审批中的状态
//        btnIsShow.set(true); // 方便测试，测试完删除
        result.put("process",btnIsShow.get());

        // 是否显示催办按钮
        // 当前登录人=发起人 && 流程进行中
        boolean isReminderBtnShow = StrUtil.equals(instance.getSponsorId(), currentUser)
                && StrUtil.equals(ProcessStatusEnum.SPING.getKey()+"",instance.getStatus());
        result.put("reminder", isReminderBtnShow);
//        stopWatch.stop();
//        stopWatch.start("spFlow739");
        // 全局参数
        Map<String,Object> globalParams = new HashMap<>();
        globalParams.put("params1",instance.getGlobalParam1());
        //修改params2 增加乙方营业执照
        try {
            if (StrUtil.isNotBlank(instance.getGlobalParam2()) && StrUtil.isNotBlank(instance.getGlobalParam5())) {
                JSONObject p2jo = new JSONObject(instance.getGlobalParam2());
                SysSupplier sysSupplier = baseDataClient.shipOwnerSysSupplierByContractId(instance.getGlobalParam5());
                if (null != sysSupplier) {
                    p2jo.putOpt("partyBImgBiz", sysSupplier.getBusinessImageUrl());
                }
                globalParams.put("params2",p2jo.toString());
            } else {
                globalParams.put("params2",instance.getGlobalParam2());
            }
        } catch (Exception exception) {
            log.info(exception.getMessage());
            globalParams.put("params2",instance.getGlobalParam2());
        }
        globalParams.put("params3",instance.getGlobalParam3());
        globalParams.put("params4",instance.getGlobalParam4());
        globalParams.put("params5",instance.getGlobalParam5());
        globalParams.put("params6",instance.getGlobalParam6());
        globalParams.put("params7",instance.getGlobalParam7());
        globalParams.put("params8",instance.getGlobalParam8());
        globalParams.put("params9",instance.getGlobalParam9());
        globalParams.put("params10",instance.getGlobalParam10());
        result.put("globalParams",globalParams);
//        stopWatch.stop();
//        log.info("findTask:{}",stopWatch.prettyPrint());
        return result;
    }
    public Map<String,Object> MobilefindTask(String processId,String userId) throws JsonProcessingException {
        Map<String,Object> result = new HashMap<>();
        AtomicBoolean btnIsShow = new AtomicBoolean(false);

        ProcessInstance instance = processInstanceService.getById(processId);
        if (null == instance || StrUtil.isBlank(instance.getFlowCode())){
            throw new AlertException("请检查该实例是否存在，并且flowcode是否为null");
        }

        ProcessDefinition processDefinition = repositoryService.getProcessDefinitionByCode(instance.getFlowCode());
        Assert.notNull(processDefinition);
        result.put("useComponent",Optional.ofNullable(processDefinition.getUseViewComponent()).orElse(false));
        result.put("viewName",processDefinition.getViewComponent());
        result.put("flowName",instance.getFlowName());
        result.put("flowCode",instance.getFlowCode());
        result.put("statusId",instance.getStatus());
        result.put("nodeId",instance.getStatusId());
        // 表单内容
        List<DProcessInstance> formValues = mongoTemplate.find(new Query(Criteria.where("_id").is(instance.getId())), DProcessInstance.class);

        JSONObject formValue = null;
        if (CollectionUtil.isNotEmpty(formValues)) {
            formValue = formValues.get(0).getFromvalue();
        }
          //初始化审批流程
        processInstanceService.processInitProcess(processDefinition,instance,formValue);
//        Iterator<ProcessNode> iterator = processDefinition.getProcess().iterator();
//        while (iterator.hasNext()) {
//            ProcessNode item = iterator.next();
//            if (item.getProcessbycompany() == null) {
//                continue;
//            }
//            List<Map<String, Object>> mapList = item.getProcessbycompany();
//            for (Map<String, Object> stringObjectMap : mapList) {
//                if (!instance.getGlobalParam4().equals(stringObjectMap.get("cid"))) {
//                    continue;
//                }
//                if (stringObjectMap.get("peopleid") != null) {
//                    item.setOperId(String.valueOf(stringObjectMap.get("peopleid")));
//                    item.setRule(null);
//                } else if (stringObjectMap.get("roleid") != null) {
//                    item.setRule(String.valueOf(stringObjectMap.get("roleid")));
//                }
//                //跳过时，删除当前节点，修改上一节点的next
//                else if (stringObjectMap.get("isSkip") != null) {
//                    for (ProcessNode process : processDefinition.getProcess()) {
//                        if (Objects.equals(process.getNext(),item.getId())) {
//                            process.setNext(item.getNext());
//                        }
//                        if (process.getDynamicNexts() != null) {
//                            for (ProcessNode.DynamicNext dynamicNext : process.getDynamicNexts()) {
//                                if (Objects.equals(dynamicNext.getNext(), item.getId())) {
//                                    dynamicNext.setNext(item.getNext());
//                                }
//                            }
//                        }
//                    }
//                    iterator.remove();
//                    break;
//                }
//            }
//
//        }

        List<String> uids = processDefinition.getProcess()
                .stream()
                .map(ProcessNode::getOperId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        uids.add(instance.getSponsorId());
        List<String> cuids = new ArrayList<>();
        if (StrUtil.isNotBlank(instance.getCcs())){
            cuids = new ArrayList<>(Arrays.asList(StringUtils.delimitedListToStringArray(instance.getCcs(), ",")));
            uids.addAll(cuids);
        }
        uids = uids.stream().distinct().collect(Collectors.toList());
        Map<String,SysUser> userCache = new HashMap<>();
        if (CollectionUtil.isNotEmpty(uids)){
            sysUserService.listByUserIds(uids)
                    .forEach(item -> userCache.put(item.getUserId(),item));
        }


        String formSchemaStr = processDefinition.getFormSchema();
        if (processDefinition.getFormSchema() != null){
            try {
                FormSchema formSchema = objectMapper.readValue(processDefinition.getFormSchema(), FormSchema.class);

                Integer nodeId = instance.getStatusId();
                processDefinition.getProcess()
                        .stream()
                        .filter(item -> item.getId().equals( nodeId))
                        .findFirst()
                        .ifPresent(item -> {
                            // 字段可否编辑 ｜ 字段是否隐藏
                            if (null != item.getHideFields()){
                                item.getHideFields().forEach(c -> {
                                    FormItem formItem = formSchema.getProperties().get(c);
                                    if(null != formItem){
                                        formItem.setHidden(true);
                                    }
                                });
                            }
                            if(null != item.getFieldStatus()){
                                ProcessNode.FieldStatus fieldStatus = item.getFieldStatus();
                                List<String> fields = fieldStatus.getFields();
                                if(fieldStatus.getModel().equals(0)){
                                    // 部分字段可以编辑
                                    formSchema.getProperties().keySet().forEach(c -> {
                                        if (!fields.contains(c)){
                                            FormItem formItem = formSchema.getProperties().get(c);
                                            if(null != formItem){
                                                if(formItem.getComponent().getProps() == null){
                                                    formItem.getComponent().setProps(new FormComponentProps());
                                                }
                                                formItem.getComponent().getProps().setDisabled(true);
                                            }
                                        }
                                    });
                                }else if(fieldStatus.getModel().equals(1)){
                                    // 部分字段不可以编辑
                                    fields.forEach(c -> {
                                        FormItem formItem = formSchema.getProperties().get(c);
                                        if(null != formItem){
                                            if(formItem.getComponent().getProps() == null){
                                                formItem.getComponent().setProps(new FormComponentProps());
                                            }
                                            formItem.getComponent().getProps().setDisabled(true);
                                        }
                                    });
                                }
                            }else{
                                // 默认审核人员全部字段都不可以编辑
                                formSchema.getProperties().keySet().forEach(c -> {
                                    FormItem formItem = formSchema.getProperties().get(c);
                                    if(formItem.getComponent().getProps() == null){
                                        formItem.getComponent().setProps(new FormComponentProps());
                                    }
                                    formItem.getComponent().getProps().setDisabled(true);
                                });
                            }
                        });
                formSchemaStr = formSchema.toJsonString();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("解析动态表单异常：{}",e.getMessage());
            }
        }

        result.put("formSchema",formSchemaStr);
        if (CollectionUtil.isNotEmpty(formValues)){
            result.put("formValue",formValues.get(0));
        }

        // 审批流程
        // 发起人 -> 审批人 -> 抄送人
        Map<String,Object> spFlow = new HashMap<>();
        // 发起人
        Map<String,Object> m1User = new HashMap<>();
        m1User.put("id",Optional.ofNullable(userCache.get(instance.getSponsorId())).map(SysUser::getUserId).orElse(null));
        m1User.put("name",
                StrUtil.equals(instance.getSponsorId(),String.valueOf(userId)) ? "我" :Optional.ofNullable(userCache.get(instance.getSponsorId())).map(SysUser::getNickName).orElse(null));
        m1User.put("avatar",Optional.ofNullable(userCache.get(instance.getSponsorId())).map(SysUser::getAvatarPath).orElse(null));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        m1User.put("sponsorTime",simpleDateFormat.format(instance.getSponsorTime()));
        String sta = "";
        String reason="";
        if(Objects.equals(Integer.parseInt(instance.getStatus()), ProcessStatusEnum.CANCEL.getKey())){
            LambdaQueryWrapper<ProcessApproveLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProcessApproveLog::getProcessId,instance.getId());
            queryWrapper.eq(ProcessApproveLog::getNodeId, NodeTypeEnum.START.getCode());
            queryWrapper.eq(ProcessApproveLog::getApproveResult,ProcessStatusEnum.CANCEL.getKey());
            queryWrapper.orderByDesc(ProcessApproveLog::getApproveTime);
            ProcessApproveLog processApproveLog = processApproveLogService.getOne(queryWrapper,false);
            reason = StrUtil.isNotBlank(processApproveLog.getApproveComment())?processApproveLog.getApproveComment():"";
            sta = "(已撤回)";
            m1User.put("sponsorTime",simpleDateFormat.format(instance.getSponsorTime())+"|"+simpleDateFormat.format(processApproveLog.getApproveTime()));
        }
        Map<String,Object> m1 = new HashMap<>();
        m1.put("content","发起申请"+sta);
        m1.put("item",m1User);
        m1.put("reason",reason);
        spFlow.put("start",m1);

        // 审批人
        List<Map<String,Object>> m2 = new ArrayList<>();
        AtomicBoolean sping = new AtomicBoolean(false);
        Map<Integer,Integer> spingStatusMap = new HashMap<>();
        spingStatusMap.put(0,0); // 当前
        spingStatusMap.put(1,0); // 总的
        // 审批意见
        List<ProcessApproveLog> approveLogs = processApproveLogService.list(new LambdaQueryWrapper<ProcessApproveLog>()
                .eq(ProcessApproveLog::getProcessId, processId));
        approveLogs.sort(Comparator.comparing(ProcessApproveLog::getApproveTime));
        Map<Integer,ProcessApproveLog> logMap = new HashMap<>();
        approveLogs.forEach(item -> logMap.put(item.getNodeId(),item));


        // 审批角色code
        List<String> spRuleCodes = processDefinition.getProcess().stream()
                .map(ProcessNode::getRule)
                .filter(rule -> StrUtil.isNotBlank(rule))
                .distinct()
                .collect(Collectors.toList());


        List<Role> rulesTmp = new ArrayList<>();
        if (spRuleCodes != null || spRuleCodes.size() > 0) {
            rulesTmp = sysRoleService.getRolesByCodes(spRuleCodes);
        }
        final List<Role> rules = rulesTmp;


        List<WxDepartment> WxDepartments = wxDepartmentService.list();

        List<SysCompanyPlate> plateList = sysCompanyPlateService.list();


        List<String> list1;
        if (StrUtil.isNotBlank(instance.getSpRoleId())) {
            list1 = sysUserService.getRoleUserIdByCompany(instance.getSpRoleId(), instance.getGlobalParam4());
        } else {
            list1 = new ArrayList<>();
        }
//        List<String> currentRoles = sysUserService.getRoleCodesByUserId(userId);
        LambdaQueryWrapper<SysUserPlate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysUserPlate::getUserId, userId);
        List<SysUserPlate> UserPlates = sysUserPlateService.list(queryWrapper);
        List<Long> currentUserPlates = UserPlates.stream().map(SysUserPlate::getDictId).collect(Collectors.toList());

        List<ProcessNode> nodesByThisProcess = getNodesByProcessInstance(processDefinition, instance, formValue);

        List<RoleByComId> rolelistFull = sysProcessService.getmyrole(userId);
//        List<String> currentRoles = sysUserService.getRoleCodesByUserId(String.valueOf(userId));

        nodesByThisProcess.forEach(item -> {
            if (NodeTypeEnum.NODE.getKey().equals(item.getType())){
                if (NodeTypeEnum.START.getKey().equals(item.getType())){
                    return;
                }
                Map<String,Object> m2User = new HashMap<>();
                m2User.put("id",Optional.ofNullable(userCache.get(item.getOperId())).map(SysUser::getUserId).orElse(null));
                m2User.put("name",Optional.ofNullable(userCache.get(item.getOperId())).map(SysUser::getNickName).orElse(null));

                // sp_type: 0头像、1角色名
                if (StrUtil.isNotBlank(item.getRule())) {
                    m2User.put("sp_type", "1");
                    List<Role> roles = rules.stream().filter(r -> Objects.equals(r.getEnname(), item.getRule())).collect(Collectors.toList());
                    if (roles != null && roles.size() > 0) {
                        m2User.put("avatar", roles.get(0).getName());
                    } else {
                        m2User.put("avatar", null);
                    }
                } else if (StrUtil.isNotBlank(item.getSpOperIdGetter()) || StrUtil.isNotBlank(item.getSpRuleGetter())) {
                    m2User.put("sp_type", "1");
                    m2User.put("avatar", item.getName());
                } else {
                    m2User.put("sp_type", "0");
                    m2User.put("avatar", Optional.ofNullable(userCache.get(item.getOperId())).map(SysUser::getAvatarPath).orElse(null));

                }


                Map<String,Object> m22 = new HashMap<>();
                // 判断是否出纳
                if(item.getId()!=null && (item.getId()/10*10)==NodeTypeEnum.NODE7.getCode()){
                    m22.put("content","出纳");
                    // 判断是否付款
//                    if(instance.getStatus() != null && ProcessStatusEnum.AGREE.getKey() == Integer.parseInt(instance.getStatus())){
//                        m22.put("content","出纳(已付款)");
//                    }
                }else{
                    m22.put("content","审批人");
                }
                m22.put("item",m2User);
                m22.put("status",instance.getStatus());

                spingStatusMap.put(1,spingStatusMap.get(1)+1);
                if (!sping.get()){
                    spingStatusMap.put(0,spingStatusMap.get(0)+1);
                    // 审核通过
                    m22.put("color",COLOR_SUCCESS);
                    m22.put("size","large");
                }

                // 审批意见
                if (logMap.containsKey(item.getId())){
                    ProcessApproveLog approveLog = logMap.get(item.getId());
                    if (StrUtil.isNotBlank(approveLog.getApproveComment())){
                        // 系统跳过 改变颜色
                        if (approveLog.getApproveComment().equals("系统跳过") && !sping.get()){
                            m22.put("color",COLOR_SKIP);
                            m22.put("size","normal");
                        }
                        int clen = approveLog.getApproveComment().length();
                        int crow = (int)Math.ceil(clen / 22.0);
                        StringBuilder sbf = new StringBuilder();
                        for (int i = 0; i < crow; i++) {
                            sbf.append(approveLog.getApproveComment(), i*22, Math.min((i+1)*22,clen));
                            if (i+1 < crow){
                                sbf.append("<br/>");
                            }
                        }
                        m22.put("comment",sbf.toString());
                    }
                    m22.put("commentTime", DateUtil.format(approveLog.getApproveTime(),"yyyy/MM/dd HH:mm"));
                    LambdaQueryWrapper<SysUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
                    userLambdaQueryWrapper.eq(SysUser :: getUserId,approveLog.getSpId());
                    List<SysUser> userList = sysUserService.list(userLambdaQueryWrapper);
                    if (userList != null && userList.size() > 0){
                        SysUser sysUser = userList.get(0);
                        Map<String,Object> userBean = (Map<String, Object>) m22.get("item");
                        userBean.put("name",sysUser.getNickName());
                        userBean.put("avatar",sysUser.getAvatarPath());
                        userBean.put("id",sysUser.getUserId());
                        userBean.put("sp_type",0);
                        m22.put("item",userBean);
                    }
                }
                // 当前审批状态
                if (Objects.equals(item.getId(),instance.getStatusId())){
                    // 判断是否出纳
                    if(item.getId()!=null && (item.getId()/10*10)==NodeTypeEnum.NODE7.getCode()){
                        // 已付款当前状态不会是出纳
                        m22.put("content","出纳(付款中)");
                    }else{
                        m22.put("content","审批人" + "("+ProcessStatusEnum.getDescByKey(Integer.parseInt(instance.getStatus()))+")");
                    }

                    if (instance.getStatus() != null && ProcessStatusEnum.SPING.getKey() == Integer.parseInt(instance.getStatus())){
                        // 审批中
                        m22.put("color",COLOR_CURRENT);
                        m22.put("size","large");
                        sping.set(true);

                        Integer plate = item.getIsPlate();

                        // 审批中状态并且满足以下条件可以显示审核按钮
                        if(StrUtil.equals(String.valueOf(userId),instance.getSpId())){
                            // 当前登陆人是审批人
                            btnIsShow.set(true);
                        }else if(StrUtil.isNotBlank(instance.getSpRoleId())){
                            if (plate != null) {

                                String dept = instance.getGlobalParam4();
                                if (StrUtil.isBlank(dept)) {
                                    return;
                                }
                                WxDepartment wxDepartment = WxDepartments.stream().filter(a -> Objects.equals(a.getId(), dept)).findFirst().orElse(new WxDepartment());
                                if (wxDepartment.getDeptId() == null) {
                                    return;
                                }
                                SysCompanyPlate sysCompanyPlate = plateList.stream()
                                    .filter(b -> Objects.equals(b.getWxDepartmentId(), String.valueOf(wxDepartment.getDeptId())))
                                    .findFirst().orElse(new SysCompanyPlate());
                                if (sysCompanyPlate.getDictId() == null) {
                                    return;
                                }
                                if (currentUserPlates.contains(sysCompanyPlate.getDictId()) && list1.contains(userId)) {
                                    btnIsShow.set(true);
                                }
                            } else {
                                if (list1.contains(userId)) {
                                    btnIsShow.set(true);
                                }
                            }
                        }
                    }else if(instance.getStatus() != null && ProcessStatusEnum.AGREE.getKey() == Integer.parseInt(instance.getStatus())){
                        // 审批通过
                        m22.put("color",COLOR_SUCCESS);
                        m22.put("size","large");
                        sping.set(true);
                    }else if(instance.getStatus() != null && ProcessStatusEnum.REJECT.getKey() == Integer.parseInt(instance.getStatus())){
                        // 审批拒绝
                        m22.put("color",COLOR_REJECT);
                        m22.put("size","large");
                        sping.set(true);
                    }
                }

                m2.add(m22);
            }
        });
        spFlow.put("sp",m2);

        // 抄送人
        List<Map<String,Object>> m3List =new ArrayList<>();
        cuids.forEach(item -> {
            Map<String,Object> m3User = new HashMap<>();
            m3User.put("id",Optional.ofNullable(userCache.get(item)).map(SysUser::getUserId).orElse(null));
            m3User.put("name",Optional.ofNullable(userCache.get(item)).map(SysUser::getNickName).orElse(null));
            m3User.put("avatar",Optional.ofNullable(userCache.get(item)).map(SysUser::getAvatarPath).orElse(null));
            m3List.add(m3User);
        });
        Map<String,Object> m3 = new HashMap<>();
        m3.put("content","抄送（"+m3List.size()+"）人");
        m3.put("items",m3List);

        if (spingStatusMap.get(0).equals( spingStatusMap.get(1))){
            // 审批人全部通过后，抄送人显示图标
            m3.put("color",COLOR_SUCCESS);
            m3.put("size","large");
        }

        spFlow.put("ccs",m3);

        result.put("spFlow",spFlow);
        // 可否处理
        // 可处理： 当前审批人是自己 && 审批中的状态
//        btnIsShow.set(true); // 方便测试，测试完删除
        result.put("process",btnIsShow.get());

        // 全局参数
        Map<String,Object> globalParams = new HashMap<>();
        globalParams.put("params1",instance.getGlobalParam1());
        //修改params2 增加乙方营业执照
        try {
            if (StrUtil.isNotBlank(instance.getGlobalParam2()) && StrUtil.isNotBlank(instance.getGlobalParam5())) {
                JSONObject p2jo = new JSONObject(instance.getGlobalParam2());
                SysSupplier sysSupplier = baseDataClient.shipOwnerSysSupplierByContractId(instance.getGlobalParam5());
                if (null != sysSupplier) {
                    p2jo.putOpt("partyBImgBiz", sysSupplier.getBusinessImageUrl());
                }
                globalParams.put("params2",p2jo.toString());
            } else {
                globalParams.put("params2",instance.getGlobalParam2());
            }
        } catch (Exception exception) {
            log.info(exception.getMessage());
            globalParams.put("params2",instance.getGlobalParam2());
        }
        globalParams.put("params3",instance.getGlobalParam3());
        globalParams.put("params4",instance.getGlobalParam4());
        globalParams.put("params5",instance.getGlobalParam5());
        globalParams.put("params6",instance.getGlobalParam6());
        globalParams.put("params7",instance.getGlobalParam7());
        globalParams.put("params8",instance.getGlobalParam8());
        globalParams.put("params9",instance.getGlobalParam9());
        globalParams.put("params10",instance.getGlobalParam10());
        result.put("globalParams",globalParams);
        return result;
    }


    private ProcessNode findNodeById(List<ProcessNode> list, Integer id) {
		return list.stream().filter(n -> Objects.equals(id, n.getId())).findFirst().orElse(null);
	}

	private List<ProcessNode> getNodesByProcessInstance(ProcessDefinition processDefinition, ProcessInstance instance, Map<String, Object> formValue){
		List<ProcessNode> nodesByThisProcess = new ArrayList<>();
		ProcessNode startNode = processDefinition.getProcess().stream().filter(n -> Objects.equals(NodeTypeEnum.START.getKey(), n.getType())).findFirst().orElse(null);
		Integer nextId = processInstanceService.getNextId(processDefinition, instance, startNode.getId(), formValue);
		ProcessNode nextNode = null;
		while (nextId != null && !Objects.equals(NodeTypeEnum.END.getCode(), nextId)) {
			nextNode = findNodeById(processDefinition.getProcess(), nextId);
			if (nextNode != null && !Objects.equals(NodeTypeEnum.END.getCode(), nextNode.getId()) ) {
				nodesByThisProcess.add(nextNode);
			}
			nextId = processInstanceService.getNextId(processDefinition, instance, nextNode.getId(), formValue);
		}
		return nodesByThisProcess;
	}

    /**
     * 完成任务
     */
//    @RequestShared
//    @RequestTokenShared
    @Transactional(rollbackFor = Exception.class)
    public void complete(ProcessSubmitPo po,Boolean Skip) throws Exception {
        ProcessInstance instance = processInstanceService.getById(po.getProcessId());
		if (po.getNodeId() == null) {
			throw new AlertException("缺少nodeId");
		}
		if (!Objects.equals(instance.getStatusId(), po.getNodeId())) {
			throw new AlertException("本次审批已通过其他方式或其他人完成！");
		}

		IPostProcessor postProcessor = processorMap.get(instance.getAfterEveryNode());
        if (null == instance){
            throw new AlertException("未找到任务");
        }
        if (!StrUtil.equals(ProcessStatusEnum.SPING.getKey()+"", instance.getStatus())){
            throw new AlertException("该任务已完成,请勿重复审批");

        }
        log.info("处理审批...");
        String operUserId = null;
        if (SecurityUtils.getCurrentUser() != null && StrUtil.isBlank(instance.getSpId())){
            operUserId = String.valueOf(SecurityUtils.getCurrentUserId());
        } else {
            operUserId = instance.getSpId();
        }
        // TODO 企业微信用户id
        if(StrUtil.isNotBlank(po.getUserid())){
            SysUser user = sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getWxUserId, po.getUserid()).last("limit 1"));
            operUserId = String.valueOf(user.getUserId());
        }
        if(StrUtil.isBlank(operUserId)){
            throw new AlertException("当前操作人异常");
        }
        if(po.getWaterType() != null){
            LambdaUpdateWrapper<ProcessInstance> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ProcessInstance::getId,po.getProcessId()).set(ProcessInstance::getGlobalParam10,po.getWaterType());
            processInstanceService.update(updateWrapper);
        }
        // 修改数据
        Map<String,Object> updateValueMap = new HashMap<>();
        if (null != po.getFormValue()){
            List<ProcessFormValue> formValues = mongoTemplate.find(new Query(Criteria.where("processorId").is(po.getProcessId())), ProcessFormValue.class);
            if(CollectionUtil.isNotEmpty(formValues)){
                ProcessFormValue formValue = formValues.get(0);
                try{
                    Map map = objectMapper.readValue(formValue.getFormValue(), Map.class);
                    boolean isUpdate = false;
                    for (String key : po.getFormValue().keySet()) {
                        Object originValue = map.get(key);
                        Object newValue = po.getFormValue().get(key);

                        boolean isReplace = false;
                        if (originValue != null){
                            if (newValue == null){
                                continue;
                            }

                            if (originValue instanceof String){
                                if (!StrUtil.equals((String)newValue,(String)originValue)){
                                    isReplace = true;
                                }
                            }else if(originValue instanceof Boolean){
                                if ((Boolean) originValue != (Boolean)newValue){
                                    isReplace = true;
                                }
                            }else if (originValue instanceof ArrayList){
                                List<Object> l1 = (ArrayList) originValue;
                                List<Object> l2 = (ArrayList) newValue;

                                if (CollectionUtil.isNotEmpty(l1) && CollectionUtil.isNotEmpty(l2)){
                                    if (l1.get(0) instanceof String){
                                        List<String> list1 = l1.stream().map(String::valueOf).collect(Collectors.toList());
                                        List<String> list2 = l2.stream().map(String::valueOf).collect(Collectors.toList());
                                        list1.sort(Comparator.comparing(String::hashCode));
                                        list2.sort(Comparator.comparing(String::hashCode));
                                        if (!list1.toString().equals(list2.toString())){
                                            isReplace = true;
                                        }
                                    }
                                }
                            }
                        }else if(newValue != null){
                            isReplace = true;
                        }
                        if (isReplace){
                            System.out.println("---------------");
                            System.out.println(key +" ->> " + newValue);
                            map.put(key,newValue);
                            updateValueMap.put(key,newValue);
                            isUpdate = true;
                        }
                    }
                    if (isUpdate){
                        Query query = new Query();
                        query.addCriteria(Criteria.where("_id").is(formValue.getId()));
                        Update update = new Update();
                        update.set("formValue", objectMapper.writeValueAsString(map));
                        mongoTemplate.upsert(query, update,ProcessFormValue.class);
                        log.info("更新数据成功");
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }

        // 添加审核日志
        ProcessApproveLog approveLog = new ProcessApproveLog();
        approveLog.setProcessId(po.getProcessId());
        approveLog.setApproveComment(po.getComment());
        approveLog.setApproveTime(new Date());
        approveLog.setApproveResult(po.getType());
        approveLog.setUpdateData(objectMapper.writeValueAsString(updateValueMap));
        approveLog.setSpId(operUserId);
        approveLog.setNodeId(instance.getStatusId());
        processApproveLogService.save(approveLog);
        //  保存审批人 label
        // 不保存系统跳过
        if(ProcessStatusEnum.REJECT.getKey() != po.getType()){
            saveSignInfoByProcessInstance(po.getProcessId()
                    ,!Objects.equals(po.getComment(),"系统跳过")?operUserId:"",instance.getStatusId(),po.getType());
        }

        // 修改实例状态
        LambdaUpdateWrapper<ProcessInstance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProcessInstance::getId,po.getProcessId());

        // 如果拒绝，直接设置状态
        if (ProcessStatusEnum.REJECT.getKey() == po.getType()){
            instance.setStatus(String.valueOf(po.getType()));
            instance.setFinishTime(new Date());

            updateWrapper.set(ProcessInstance::getStatus, instance.getStatus());
            updateWrapper.set(ProcessInstance::getFinishTime,instance.getFinishTime());
            updateWrapper.set(ProcessInstance::getStatusId,instance.getStatusId());

            if("CurrentAccountApplication".equals(instance.getFlowCode())){
                LambdaUpdateWrapper<ComeToPaymentWater> updateWrapper1 = new LambdaUpdateWrapper<>();
                updateWrapper1.eq(ComeToPaymentWater::getProcessId,instance.getId())
                        .set(ComeToPaymentWater::getIsFinish,2);
                comeToPaymentWaterService.update(updateWrapper1);
            }

        }else{
            ProcessDefinition processDefinition = repositoryService.getProcessDefinitionByCode(instance.getFlowCode());
            Map<String,Object> fmValue;
            if (po.getFormValue() == null || po.getFormValue().size() == 0){
                List<DProcessInstance> PformValues = mongoTemplate.find(new Query(Criteria.where("Id").is(po.getProcessId())), DProcessInstance.class);
                fmValue = PformValues.get(0).getFromvalue().toBean(Map.class);
            } else {
                fmValue= po.getFormValue();
            }
            //初始化审批流程
            processInstanceService.processInitProcess(processDefinition,instance,fmValue);
//            Iterator<ProcessNode> iterator = processDefinition.getProcess().iterator();
//            while (iterator.hasNext()) {
//                ProcessNode item = iterator.next();
//                if (item.getProcessbycompany() == null) {
//                    continue;
//                }
//                List<Map<String, Object>> mapList = item.getProcessbycompany();
//                for (Map<String, Object> stringObjectMap : mapList) {
//                    if (!instance.getGlobalParam4().equals(stringObjectMap.get("cid"))) {
//                        continue;
//                    }
//                    if (stringObjectMap.get("peopleid") != null) {
//                        item.setOperId(String.valueOf(stringObjectMap.get("peopleid")));
//                        item.setRule(null);
//                    } else if (stringObjectMap.get("roleid") != null) {
//                        item.setRule(String.valueOf(stringObjectMap.get("roleid")));
//                    }
//                    //跳过时，删除当前节点，修改上一节点的next
//                    else if (stringObjectMap.get("isSkip") != null) {
//                        for (ProcessNode process : processDefinition.getProcess()) {
//                            if (Objects.equals(process.getNext(),item.getId())) {
//                                process.setNext(item.getNext());
//                            }
//                            if (process.getDynamicNexts() != null) {
//                                for (ProcessNode.DynamicNext dynamicNext : process.getDynamicNexts()) {
//                                    if (Objects.equals(dynamicNext.getNext(), item.getId())) {
//                                        dynamicNext.setNext(item.getNext());
//                                    }
//                                }
//                            }
//                        }
//                        iterator.remove();
//                        break;
//                    }
//                }
//
//            }

//            if (po.getFormValue() == null || po.getFormValue().size() == 0){
//                List<DProcessInstance> PformValues = mongoTemplate.find(new Query(Criteria.where("Id").is(po.getProcessId())), DProcessInstance.class);
//                Map<String,Object> frommap = PformValues.get(0).getFromvalue().toBean(Map.class);
//                processInstanceService.setNextId(processDefinition, instance, instance.getStatusId(),frommap);
//            } else {
//                processInstanceService.setNextId(processDefinition, instance, instance.getStatusId(), po.getFormValue());
//            }
            processInstanceService.setNextId(processDefinition, instance, instance.getStatusId(), fmValue);
			updateWrapper.set(ProcessInstance::getStatusId, instance.getStatusId());
			ProcessNode nextNode = processDefinition.getProcess().stream()
					.filter(c -> Objects.equals(c.getId(),instance.getStatusId()))
					.findFirst().orElse(null);

			if (nextNode != null) {
				instance.setStatus(String.valueOf(ProcessStatusEnum.SPING.getKey()));
				updateWrapper.set(ProcessInstance::getStatus, ProcessStatusEnum.SPING.getKey());
				if (NodeTypeEnum.END.getCode() != nextNode.getId()){
					processInstanceService.setShenpiren(instance, nextNode, postProcessor, updateWrapper);
				}else{
					instance.setStatus(po.getType()+"");
					instance.setFinishTime(new Date());
					updateWrapper.set(ProcessInstance::getStatus, po.getType());
					updateWrapper.set(ProcessInstance::getFinishTime,instance.getFinishTime());
				}
			}

        }
        processInstanceService.update(updateWrapper);


//        ProcessInstance newinstance = processInstanceService.getById(po.getProcessId());
//        if("paymentApprove".equals(newinstance.getFlowCode())) {
//            String json = JSON.toJSONString(newinstance);
//            if (wxDepartmentService.isHeShengById(newinstance.getGlobalParam4())){
//                hsProcessClient.updateProcessInstance(json);
//            }else{
//                processClient.updateProcessInstance(json);
//            }
//        }
        String btnStr = ProcessStatusEnum.getDescByKey(po.getType());
        //更改推送卡片按钮状态
        Set<String> set = stringRedisTemplate.opsForSet().members("sendMessageOverDue");
        if(set != null && set.size()>0){
            List<MessageInfoDto> list = new ArrayList<>();
            for(String str:set){
                MessageInfoDto messageInfoDto = JSON.parseObject(str, MessageInfoDto.class);
                list.add(messageInfoDto);
            }

            String btnDroStr = po.getBtnDroStr();
            UpdateButtonDto updateButtonDto = new UpdateButtonDto();
            if(StrUtil.isNotBlank(btnDroStr)){
                updateButtonDto = JSON.parseObject(btnDroStr,UpdateButtonDto.class);
                LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysUser::getWxUserId,updateButtonDto.getFromUserName());
                SysUser sysUser = sysUserService.getOne(queryWrapper, false);
                updateButtonDto.setButtonName(sysUser.getNickName()+btnStr);
            } else {
                MessageInfoDto messageInfoDto1 = list.stream()
                        .filter(messageInfoDto -> messageInfoDto.getProcessId().equals(instance.getId())).findFirst()
                        .orElse(new MessageInfoDto());
                updateButtonDto.setTaskId(messageInfoDto1.getTaskId());
                updateButtonDto.setResult(messageInfoDto1.getResCode());
                String userId = SecurityUtils.getCurrentUserId();
                SysUser sysUser = sysUserService.getById(userId);
                updateButtonDto.setButtonName(sysUser.getNickName()+btnStr);
            }
            messageClient.updateBtnStatus(updateButtonDto);

            //审批完成后，删除redis中保存的MessageInfoDto
            MessageInfoDto del = list.stream()
                    .filter(messageInfoDto -> messageInfoDto.getProcessId().equals(instance.getId()))
                    .findFirst()
                    .orElse(new MessageInfoDto());
            String s = JSON.toJSONString(del);
            stringRedisTemplate.opsForSet().remove("sendMessageOverDue",s);
        }

        // 后置操作
        if (StrUtil.isNotBlank(instance.getAfterEveryNode())){
            if (null != postProcessor && Skip){
                // do something..
                postProcessor.handler(instance);
            }
        }
        log.info("审批完成");
    }

    private void saveSignInfoByProcessInstance(String processId,String userId,Integer statusId,Integer resultType){
        List<DProcessInstance> pformValues = mongoTemplate.find(new Query(Criteria.where("Id").is(processId)), DProcessInstance.class);
        if(pformValues==null || pformValues.size()==0){
            return;
        }

        DProcessInstance pi= pformValues.get(0);
        cn.hutool.json.JSONArray jsonArray= pi.getSignInfos();
        if(jsonArray==null){
            jsonArray=new cn.hutool.json.JSONArray();
        }
        // 获取label
        String userName ="";
        if(StrUtil.isBlank(userId)){
            // 系统跳过
            userName = "-";
        }else{
            SysUser userDto = sysUserService.getById(userId);
            if(userDto!=null && StrUtil.isNotBlank(userDto.getNickName())){
                userName= userDto.getNickName();
            }
        }

        String code= pi.getFlowCode();
        AtomicReference<String> label= new AtomicReference<>("");
        AtomicReference<String> sort= new AtomicReference<>("");
        ProcessDefinition processDefinition= repositoryService.getProcessDefinitionByCode(code);
        //
        List<ProcessNode> processNodeList= processDefinition.getProcess();
        processNodeList.stream().filter(processNode -> Objects.equals(processNode.getId(), statusId)).findFirst().ifPresent(processNode -> {
            JSONObject jo= processNode.getSignInfo();
            if(jo!=null){
                label.set(jo.getStr("label"));
                sort.set(jo.getStr("sort"));
            }
        });
        // 同一节点覆盖
        Integer isCurrent=null;
        if(jsonArray.size()>0){
            for(int i=0;i<jsonArray.size();i++){
                JSONObject jo= jsonArray.getJSONObject(i);
                if(Objects.equals(jo.getInt("statusId"),statusId)){
                    isCurrent=i;
                    break;
                }
            }
        }
        JSONObject jsonObject=new JSONObject();
        jsonObject.set("label",label.toString());
        jsonObject.set("sort",sort.toString());
        jsonObject.set("userId",userId);
        jsonObject.set("userName",userName);
        jsonObject.set("statusId",statusId);
        jsonObject.set("createDate",ZonedDateTime.now(ZoneId.of("Asia/Shanghai")).toEpochSecond());
        jsonObject.set("resultType",resultType);

        if(isCurrent!=null){
            jsonArray.set(isCurrent,jsonObject);
        }else{
            jsonArray.add(jsonObject);
        }

        Query query = new Query();
        query.addCriteria(Criteria.where("_id").is(processId));
        Update update = new Update();
        update.set("signInfos", jsonArray);
        mongoTemplate.upsert(query,update,DProcessInstance.class);
    }
    /**
     * 移动端完成任务
     */
//    @RequestShared
//    @RequestTokenShared
    @Transactional
    public void mobilecomplete(ProcessSubmitPo po, Boolean Skip) throws Exception {
        ProcessInstance instance = processInstanceService.getById(po.getProcessId());
        if (po.getNodeId() == null) {
            throw new AlertException("缺少nodeId");
        }
        if (!Objects.equals(instance.getStatusId(), po.getNodeId())) {
            throw new AlertException("本次审批已通过其他方式或其他人完成！");
        }

        IPostProcessor postProcessor = processorMap.get(instance.getAfterEveryNode());
        if (null == instance){
            throw new AlertException("未找到任务");
        }
        if (NodeTypeEnum.END.getCode() == instance.getStatusId()){
            throw new AlertException("该任务已完成,请勿重复审批");
        }
        log.info("处理审批...");
//        String operUserId = null;
//        if (SecurityUtils.getCurrentUser() != null){
//            operUserId = String.valueOf(SecurityUtils.getCurrentUserId());
//        }
//        // TODO 企业微信用户id
//        if(StrUtil.isNotBlank(po.getUserid())){
////            SysUser user = sysUserService.getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getWxUserId, po.getUserid()).last("limit 1"));
////            operUserId = String.valueOf(user.getUserId());
//        }
//        if(StrUtil.isBlank(operUserId)){
//            throw new AlertException("当前操作人异常");
//        }
        if(po.getWaterType() != null){
            LambdaUpdateWrapper<ProcessInstance> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(ProcessInstance::getId,po.getProcessId()).set(ProcessInstance::getGlobalParam10,po.getWaterType());
            processInstanceService.update(updateWrapper);

        }
        // 修改数据
        Map<String,Object> updateValueMap = new HashMap<>();
        if (null != po.getFormValue()){
            List<ProcessFormValue> formValues = mongoTemplate.find(new Query(Criteria.where("processorId").is(po.getProcessId())), ProcessFormValue.class);
            if(CollectionUtil.isNotEmpty(formValues)){
                ProcessFormValue formValue = formValues.get(0);
                try{
                    Map map = objectMapper.readValue(formValue.getFormValue(), Map.class);
                    boolean isUpdate = false;
                    for (String key : po.getFormValue().keySet()) {
                        Object originValue = map.get(key);
                        Object newValue = po.getFormValue().get(key);

                        boolean isReplace = false;
                        if (originValue != null){
                            if (newValue == null){
                                continue;
                            }

                            if (originValue instanceof String){
                                if (!StrUtil.equals((String)newValue,(String)originValue)){
                                    isReplace = true;
                                }
                            }else if(originValue instanceof Boolean){
                                if ((Boolean) originValue != (Boolean)newValue){
                                    isReplace = true;
                                }
                            }else if (originValue instanceof ArrayList){
                                List<Object> l1 = (ArrayList) originValue;
                                List<Object> l2 = (ArrayList) newValue;

                                if (CollectionUtil.isNotEmpty(l1) && CollectionUtil.isNotEmpty(l2)){
                                    if (l1.get(0) instanceof String){
                                        List<String> list1 = l1.stream().map(String::valueOf).collect(Collectors.toList());
                                        List<String> list2 = l2.stream().map(String::valueOf).collect(Collectors.toList());
                                        list1.sort(Comparator.comparing(String::hashCode));
                                        list2.sort(Comparator.comparing(String::hashCode));
                                        if (!list1.toString().equals(list2.toString())){
                                            isReplace = true;
                                        }
                                    }
                                }
                            }
                        }else if(newValue != null){
                            isReplace = true;
                        }
                        if (isReplace){
                            System.out.println("---------------");
                            System.out.println(key +" ->> " + newValue);
                            map.put(key,newValue);
                            updateValueMap.put(key,newValue);
                            isUpdate = true;
                        }
                    }
                    if (isUpdate){
                        Query query = new Query();
                        query.addCriteria(Criteria.where("_id").is(formValue.getId()));
                        Update update = new Update();
                        update.set("formValue", objectMapper.writeValueAsString(map));
                        mongoTemplate.upsert(query, update,ProcessFormValue.class);
                        log.info("更新数据成功");
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }

        // 添加审核日志
        ProcessApproveLog approveLog = new ProcessApproveLog();
        approveLog.setProcessId(po.getProcessId());
        approveLog.setApproveComment(po.getComment());
        approveLog.setApproveTime(new Date());
        approveLog.setApproveResult(po.getType());
        approveLog.setUpdateData(objectMapper.writeValueAsString(updateValueMap));
        approveLog.setSpId(po.getUserid());
        approveLog.setNodeId(instance.getStatusId());
        processApproveLogService.save(approveLog);
        //  保存审批人 label

        if(ProcessStatusEnum.REJECT.getKey() != po.getType()){
            // 系统跳过 userid = ""
            saveSignInfoByProcessInstance(po.getProcessId()
                    ,!Objects.equals(po.getComment(),"系统跳过")?po.getUserid():"",instance.getStatusId(),po.getType());

        }

        // 修改实例状态
        LambdaUpdateWrapper<ProcessInstance> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProcessInstance::getId,po.getProcessId());

        // 如果拒绝，直接设置状态
        if (ProcessStatusEnum.REJECT.getKey() == po.getType()){
            instance.setStatus(String.valueOf(po.getType()));
            instance.setFinishTime(new Date());

            updateWrapper.set(ProcessInstance::getStatus, instance.getStatus());
            updateWrapper.set(ProcessInstance::getFinishTime,instance.getFinishTime());

            if("CurrentAccountApplication".equals(instance.getFlowCode())){
                LambdaUpdateWrapper<ComeToPaymentWater> updateWrapper1 = new LambdaUpdateWrapper<>();
                updateWrapper1.eq(ComeToPaymentWater::getProcessId,instance.getId())
                        .set(ComeToPaymentWater::getIsFinish,2);
                comeToPaymentWaterService.update(updateWrapper1);
            }
        }else{
            ProcessDefinition processDefinition = repositoryService.getProcessDefinitionByCode(instance.getFlowCode());
            Map<String,Object> frommap;
            if (po.getFormValue() == null || po.getFormValue().size() == 0){
                List<DProcessInstance> PformValues = mongoTemplate.find(new Query(Criteria.where("Id").is(po.getProcessId())), DProcessInstance.class);
                frommap = PformValues.get(0).getFromvalue().toBean(Map.class);

            } else {
                frommap = po.getFormValue();
            }
            processInstanceService.processInitProcess(processDefinition,instance,frommap);
//            Iterator<ProcessNode> iterator = processDefinition.getProcess().iterator();
//            while (iterator.hasNext()) {
//                ProcessNode item = iterator.next();
//                if (item.getProcessbycompany() == null) {
//                    continue;
//                }
//                List<Map<String, Object>> mapList = item.getProcessbycompany();
//                for (Map<String, Object> stringObjectMap : mapList) {
//                    if (!instance.getGlobalParam4().equals(stringObjectMap.get("cid"))) {
//                        continue;
//                    }
//                    if (stringObjectMap.get("peopleid") != null) {
//                        item.setOperId(String.valueOf(stringObjectMap.get("peopleid")));
//                        item.setRule(null);
//                    } else if (stringObjectMap.get("roleid") != null) {
//                        item.setRule(String.valueOf(stringObjectMap.get("roleid")));
//                    }
//                    //跳过时，删除当前节点，修改上一节点的next
//                    else if (stringObjectMap.get("isSkip") != null) {
//                        for (ProcessNode process : processDefinition.getProcess()) {
//                            if (Objects.equals(process.getNext(), item.getId())) {
//                                process.setNext(item.getNext());
//                            }
//                            if (process.getDynamicNexts() != null) {
//                                for (ProcessNode.DynamicNext dynamicNext : process.getDynamicNexts()) {
//                                    if (Objects.equals(dynamicNext.getNext(), item.getId())) {
//                                        dynamicNext.setNext(item.getNext());
//                                    }
//                                }
//                            }
//                        }
//                        iterator.remove();
//                        break;
//                    }
//                }
//
//            }
//            if (po.getFormValue() == null || po.getFormValue().size() == 0){
//                List<DProcessInstance> PformValues = mongoTemplate.find(new Query(Criteria.where("Id").is(po.getProcessId())), DProcessInstance.class);
//                Map<String,Object> frommap = PformValues.get(0).getFromvalue().toBean(Map.class);
//                processInstanceService.setNextId(processDefinition, instance, instance.getStatusId(),frommap);
//            } else {
//                processInstanceService.setNextId(processDefinition, instance, instance.getStatusId(), po.getFormValue());
//            }
            processInstanceService.setNextId(processDefinition, instance, instance.getStatusId(), frommap);
            updateWrapper.set(ProcessInstance::getStatusId, instance.getStatusId());
            ProcessNode nextNode = processDefinition.getProcess().stream()
                    .filter(c -> Objects.equals(c.getId(),instance.getStatusId()))
                    .findFirst().orElse(null);

            if (nextNode != null) {
                instance.setStatus(String.valueOf(ProcessStatusEnum.SPING.getKey()));
                updateWrapper.set(ProcessInstance::getStatus, ProcessStatusEnum.SPING.getKey());
                if (NodeTypeEnum.END.getCode() != nextNode.getId()){
                    processInstanceService.setShenpiren(instance, nextNode, postProcessor, updateWrapper);
                }else{
                    instance.setStatus(po.getType()+"");
                    instance.setFinishTime(new Date());
                    updateWrapper.set(ProcessInstance::getStatus, po.getType());
                    updateWrapper.set(ProcessInstance::getFinishTime,instance.getFinishTime());
                }
            }

        }
        processInstanceService.update(updateWrapper);
        // 同步 物流，照抄 【sungf 2022年4月28日】
//        ProcessInstance newinstance = processInstanceService.getById(po.getProcessId());
//        if("paymentApprove".equals(newinstance.getFlowCode())){
//            String json = JSON.toJSONString(newinstance);
//            if(wxDepartmentService.isHeShengById(newinstance.getGlobalParam4())){
//                hsProcessClient.updateProcessInstance(json);
//            }else{
//                processClient.updateProcessInstance(json);
//            }
//        }
        //更改推送卡片按钮状态
        Set<String> set = stringRedisTemplate.opsForSet().members("sendMessageOverDue");
        if(set != null && set.size()>0){
            List<MessageInfoDto> list = new ArrayList<>();
            for(String str:set){
                MessageInfoDto messageInfoDto = JSON.parseObject(str, MessageInfoDto.class);
                list.add(messageInfoDto);
            }
            String btnStr = ProcessStatusEnum.getDescByKey(po.getType());
            String btnDroStr = po.getBtnDroStr();
            UpdateButtonDto updateButtonDto = new UpdateButtonDto();
            if(StrUtil.isNotBlank(btnDroStr)){
                updateButtonDto = JSON.parseObject(btnDroStr,UpdateButtonDto.class);
                LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(SysUser::getWxUserId,updateButtonDto.getFromUserName());
                SysUser sysUser = sysUserService.getOne(queryWrapper, false);
                updateButtonDto.setButtonName(sysUser.getNickName()+btnStr);
            } else {
                MessageInfoDto messageInfoDto1 = list.stream()
                        .filter(messageInfoDto -> messageInfoDto.getProcessId().equals(instance.getId())).findFirst()
                        .orElse(new MessageInfoDto());
                updateButtonDto.setTaskId(messageInfoDto1.getTaskId());
                updateButtonDto.setResult(messageInfoDto1.getResCode());
                String userId = po.getUserid();
                if(StrUtil.isNotBlank(userId)){
                    SysUser sysUser = sysUserService.getById(userId);
                    if(sysUser!=null){
                        updateButtonDto.setButtonName(sysUser.getNickName()+btnStr);
                    }else{
                        updateButtonDto.setButtonName(btnStr);
                    }
                }else{
                    updateButtonDto.setButtonName(btnStr);
                }

            }
            messageClient.updateBtnStatus(updateButtonDto);

            //审批完成后，删除redis中保存的MessageInfoDto
            MessageInfoDto del = list.stream()
                    .filter(messageInfoDto -> messageInfoDto.getProcessId().equals(instance.getId()))
                    .findFirst()
                    .orElse(new MessageInfoDto());
            String s = JSON.toJSONString(del);
            stringRedisTemplate.opsForSet().remove("sendMessageOverDue",s);
        }

        // 后置操作
        if (StrUtil.isNotBlank(instance.getAfterEveryNode())){
            if (null != postProcessor && Skip){
                // do something..
                postProcessor.handler(instance);
            }
        }
        log.info("审批完成");
    }

    public Map<String, Integer> getPointCount(String id) {
        Map<String, Integer> map = new HashMap<>();
		List<String> roles = sysUserService.getUserRole(id);
		LambdaQueryWrapper<ProcessInstance> queryWrapper = new LambdaQueryWrapper<>();
        // 1：待我处理的
		queryWrapper.and(query -> query.eq(ProcessInstance::getSpId, id).or().in(ProcessInstance::getSpRoleId, roles));
        queryWrapper.eq(ProcessInstance::getStatus,ProcessStatusEnum.SPING.getKey());
        map.put("point1",processInstanceService.count(queryWrapper));
//        // 2：我已处理
//        queryWrapper = new LambdaQueryWrapper<>();
//        List<ProcessApproveLog> logs = processApproveLogService.list(new LambdaQueryWrapper<ProcessApproveLog>()
//                .eq(ProcessApproveLog::getSpId, id)
//        );
//        List<String> ids = logs.stream()
//                .map(ProcessApproveLog::getProcessId)
//                .distinct()
//                .collect(Collectors.toList());
//        if (CollectionUtil.isNotEmpty(ids)){
//            queryWrapper.in(ProcessInstance::getId,ids);
//            map.put("point2",processInstanceService.count(queryWrapper));
//        }
//        // 3：我发起的
//        queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(ProcessInstance::getSponsorId,id);
//        map.put("point3",processInstanceService.count(queryWrapper));
//        // 4：抄送我的
//        queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.like(ProcessInstance::getCcs,id);
//        map.put("point4",processInstanceService.count(queryWrapper));
        return map;
    }

    /**
     * 流程催办
     * @param processId 流程实例ID
     */
    public void reminder(String processId) {
        // 数据校验
        Assert.notBlank(processId, "流程实例ID不能为空");
        ProcessInstance instance = processInstanceService.getById(processId);
        Assert.notNull(instance, "系统中未找到流程实例");
        Assert.isTrue((ProcessStatusEnum.SPING.getKey()+"").equals(instance.getStatus()),"流程已结束");
        Assert.notBlank(instance.getSponsorId(),"发起人ID不能为空");
        ProcessDefinition definition = repositoryService.getProcessDefinitionByCode(instance.getFlowCode());
        Assert.notNull(definition, "未找到该流程定义");
        SysUser sponsor = sysUserService.getById(instance.getSponsorId());
        Assert.notNull(sponsor, "未找到发起人");

        // 获取审批人信息
        Map<String, SysUser> userCache = getSpPeople(instance);

        // 1、撤回已经推送给审批人的消息
        recallMessage(instance);

        // 2、重新给审批人推送消息
        MessageDto messageDto = pkgMessageBody(instance, definition, userCache, sponsor);

        messageSend(messageDto);
    }

    private void recallMessage(ProcessInstance instance) {
        List<MessageInfoDto> messages = Optional.ofNullable(stringRedisTemplate.opsForSet().members("sendMessageOverDue"))
                .orElse(new HashSet<>()).stream()
                .map(item -> JSON.parseObject(item, MessageInfoDto.class))
                .filter(item -> StrUtil.equals(item.getProcessId(), instance.getId()))
                .collect(Collectors.toList());
        for (MessageInfoDto item : messages) {
            // 撤回已发消息
            messageClient.recallMsg(item);
            // 删除redis当前code
            String messageStr = JSON.toJSONString(item);
            stringRedisTemplate.opsForSet().remove("sendMessageOverDue",messageStr);
        }
    }
    private Map<String, SysUser> getSpPeople(ProcessInstance instance) {
        // 查询当前审批人是谁
        // 两种情况：具体审批人、审批角色(含多个审批人)
        Map<String, SysUser> userCache = new HashMap<>();
        if (StrUtil.isNotBlank(instance.getSpId())) {
            SysUser user = sysUserService.getById(instance.getSpId());
            if (user != null) {
                userCache.put(user.getUserId(),user);
            }
        } else if (StrUtil.isNotBlank(instance.getSpRoleId())) {
            if (StrUtil.isNotBlank(instance.getGlobalParam4())) {
                // globalParams4：板块ID
                // 如果存在板块，则查找该板块下的该角色下的审批人
                sysUserService.selectByRoleIdAndPlate(instance.getSpRoleId(), instance.getGlobalParam4())
                        .forEach(item -> userCache.put(item.getUserId(), item));
            } else {
                // 未指定板块，根据角色查找审批人
                sysUserService.selectByRoleId(instance.getSpRoleId())
                        .forEach(item -> userCache.put(item.getUserId(),item));
            }
        }
        Assert.notEmpty(userCache, "未指定审批人");
        return userCache;
    }

    private MessageDto pkgMessageBody(ProcessInstance instance, ProcessDefinition definition, Map<String, SysUser> userCache, SysUser sponsor) {
        MessageDto messageDto = new MessageDto();
        String status = "";
        List<String> paymentCode = paymentConfig.getPaymentCode();
//        if(PaymentConfig.PAYMENTCODE.contains(instance.getFlowCode()) && (instance.getStatusId() >= 80 && instance.getStatusId() <= 99)){
        if(paymentCode.contains(instance.getFlowCode()) && (instance.getStatusId() >= 80 && instance.getStatusId() <= 99)){
            messageDto.setIsCss(1); // 抄送消息（过期触发重发机制）
            messageDto.setMessageType(MessageTypeEnums.TEMPLATECARD.getCode());
            messageDto.setCardType(MessageTypeEnums.TEXTCARDTYPE.getCode());
            if (NodeTypeEnum.NODE7.getCode() == instance.getStatusId()/10*10){
                status = "付款";
            }else if(NodeTypeEnum.NODE8.getCode() == instance.getStatusId()/10*10){
                status = "记账";
            }
            messageDto.setSubTitleText("请前往财务子系统操作"+status);
            status = "请前往财务子系统操作"+status+"，摘要 ";
        }else{
            messageDto.setIsCss(0); // 抄送消息（过期触发重发机制）
            messageDto.setMessageType(MessageTypeEnums.TEMPLATECARD.getCode());
            messageDto.setCardType(MessageTypeEnums.CARDTYPE.getCode());
            MessageTaskButtonDto btn1 = new MessageTaskButtonDto();
            btn1.setKey(ProcessStatusEnum.AGREE.getKey()+"");
            btn1.setName("同意");
            btn1.setStyle(1);
//            MessageTaskButtonDto btn2 = new MessageTaskButtonDto();
//            btn2.setKey(ProcessStatusEnum.REJECT.getKey()+"");
//            btn2.setName("拒绝");
//            btn2.setKey("");
//            btn2.setName("详情");
//            btn2.setStyle(2);
            messageDto.setBtns(Arrays.asList(btn1));
        }


        if (userCache.size() > 1) {
            // 如果审批人超过一个，则消息中显示其他审批人
            QuoteAreaDto quoteAreaDto = new QuoteAreaDto();
            quoteAreaDto.setType(0);
            quoteAreaDto.setTitle("同时审批人包括：");
            String otherUsers = userCache.values().stream().map(SysUser::getNickName)
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.joining(","));
            quoteAreaDto.setQuoteText(otherUsers);
            messageDto.setQuoteArea(quoteAreaDto);
        }
        List<ContentDto> contentDtos = null;
        try{
            String afterEveryNode = definition.getAfterEveryNode();
            IPostProcessor postProcessor = processorMap.get(afterEveryNode);
            contentDtos = processInstanceService.invokeGetMessageContentDto(instance, postProcessor, "getMessageContent");
        }catch (Exception e) {
            log.warn("获取消息内容体失败,flowCode={},{}",definition.getCode(),e.getMessage());
        }
        if(contentDtos != null && contentDtos.size()>0){
            messageDto.setHorizontalContentList(contentDtos);
        } else {
            messageDto.setSubTitleText(status + (StrUtil.isBlank(instance.getBriefContent()) ? "--" : instance.getBriefContent()));
        }
        List<String> wxuids = userCache.values().stream()
                .map(SysUser::getWxUserId)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
        Assert.notEmpty(wxuids, "计划推送给%s人,但这些人没有配置wxUserId", userCache.size());
        messageDto.setToUser(wxuids);
        messageDto.setTitle(String.format("%s提交的%s", sponsor.getNickName(), StrUtil.blankToDefault(instance.getFlowName(),"审批")));
        messageDto.setUrl(String.format("%s/approval-detail?processId=%s&nodeId=%s",oaProperties.getMobileUrl(), instance.getId(), instance.getStatusId()));
        // 为避免TaskId重复，参数格式：processId + "_" + 当前时间戳
        messageDto.setTaskId(instance.getId() + "_" + instance.getStatusId()+ "_" + System.currentTimeMillis());


        return messageDto;
    }

    private void messageSend(MessageDto messageDto) {
        try {
            ResultBody resultBody = messageClient.send(messageDto);
            if(!resultBody.isSuccess()){
                log.error("发送模版消息失败:{}",resultBody.getMsg());
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
