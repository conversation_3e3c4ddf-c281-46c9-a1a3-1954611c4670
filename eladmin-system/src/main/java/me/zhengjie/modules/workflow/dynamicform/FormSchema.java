package me.zhengjie.modules.workflow.dynamicform;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

@Data
public class FormSchema implements Serializable {

    private static final long serialVersionUID = 7190543519721077635L;
    private Map<String,FormItem> properties = new LinkedHashMap<>();


    @JsonIgnore
    public String toJsonString(){
        ObjectMapper objectMapper = new ObjectMapper();
        // null值的属性不进行序列化
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        try {
            return objectMapper.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return null;
    }
}
