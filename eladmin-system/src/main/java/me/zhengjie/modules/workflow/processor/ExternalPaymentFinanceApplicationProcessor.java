package me.zhengjie.modules.workflow.processor;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import me.zhengjie.modules.business.domain.SysUser;
import me.zhengjie.modules.business.service.SysUserService;
import me.zhengjie.modules.workflow.model.entity.ProcessInstance;
import me.zhengjie.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 对外付款
 */
@Slf4j
@Component("ExternalPaymentFinanceApplicationProcessor")
public class ExternalPaymentFinanceApplicationProcessor extends BaseProcessor implements IPostProcessor{

    @Autowired
    SysUserService sysUserService;

    @Override
    public void handler(ProcessInstance instance) {
        log.info("qingjiaPostProcessor 正在执行");
        super.handler(instance);
        log.info("qingjiaPostProcessor 执行完毕");
    }

}

