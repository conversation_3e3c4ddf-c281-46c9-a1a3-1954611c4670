package me.zhengjie.modules.system.service;

import me.zhengjie.base.BaseService;
import me.zhengjie.modules.system.domain.OpenTokenCompany;
import me.zhengjie.modules.system.service.dto.OpenTokenCompanyDto;
import me.zhengjie.modules.system.service.dto.OpenTokenCompanyQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @description 服务接口
* <AUTHOR>
* @date 2025-01-15
**/
public interface OpenTokenCompanyService extends BaseService<OpenTokenCompany> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(OpenTokenCompanyQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<OpenTokenCompanyDto>
    */
    List<OpenTokenCompany> queryAll(OpenTokenCompanyQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<OpenTokenCompanyDto> all, HttpServletResponse response) throws IOException;




}