    package me.zhengjie.modules.system.rest;

    import cn.hutool.core.lang.Assert;
    import cn.hutool.core.util.StrUtil;
    import com.dlcg.mobile.model.ResultBody;
    import com.dlcg.oa.client.SysDictionaryClient;
    import com.dlcg.tms.client.BaseDataClient;
    import com.dlcg.tms.client.OnAccountClient;
    import com.dlcg.tms.client.PaymentClient;
    import com.dlcg.tms.client.ShipLineClient;
    import com.zthzinfo.common.ResponseMapBuilder;
    import io.swagger.annotations.Api;
    import lombok.RequiredArgsConstructor;
    import me.zhengjie.annotation.AnonymousAccess;
    import me.zhengjie.modules.business.domain.SysUser;
    import me.zhengjie.modules.business.service.SysUserService;
    import me.zhengjie.modules.contract.domain.Contract;
    import me.zhengjie.modules.contract.service.ContractService;
    import me.zhengjie.modules.system.domain.CertTemplTemp;
    import me.zhengjie.modules.system.domain.OpenTokenCompany;
    import me.zhengjie.modules.system.domain.dto.CertTemplDto;
    import me.zhengjie.modules.system.domain.vo.CertTemplVo;
    import me.zhengjie.modules.system.service.*;
    import me.zhengjie.modules.system.service.dto.OpenTokenCompanyDto;
    import me.zhengjie.modules.system.service.dto.OpenTokenCompanyQueryCriteria;
    import me.zhengjie.modules.until.ParamsKeys;
    import me.zhengjie.modules.wechat.code.service.WxDepartmentService;
    import me.zhengjie.modules.client.shipmanage.ShipManageClient;
    import me.zhengjie.utils.SecurityUtils;
    import org.springframework.beans.factory.annotation.Autowired;
    import org.springframework.beans.factory.annotation.Value;
    import org.springframework.security.core.userdetails.UserDetails;
    import org.springframework.web.bind.annotation.*;

    import java.util.HashMap;
    import java.util.List;
    import java.util.Map;

@RestController
@RequiredArgsConstructor
@Api(tags = "系统：初始化基础数据")
@RequestMapping("/api/baseInit")
public class BaseDataController {
    
    @Autowired
    SysDictionaryClient sysDictionaryClient;
    @Autowired
    com.zthz.wuliu.client.SysDictionaryClient hsSysDictionaryClient;
    @Autowired
    OnAccountClient onAccountClient;
    @Autowired
    com.zthz.wuliu.client.OnAccountClient hsOnAccountClient;
    @Autowired
    BaseDataClient baseDataClient;
    @Autowired
    com.zthz.wuliu.client.BaseDataClient hsBaseDataClient;
    @Autowired
    PaymentClient paymentClient;
    @Autowired
    com.zthz.wuliu.client.PaymentClient hsPaymentClient;
    @Autowired
    SysUserService sysUserService;

    @Autowired
    ShipLineClient shipLineClient;
    @Autowired
    com.zthz.wuliu.client.ShipLineClient hsShipLineClient;
    @Autowired
    ContractService contractService;
    @Autowired
    WxDepartmentService wxDepartmentService;
    @Autowired
    HaoOpenTokenService haoOpenTokenService;
    @Autowired
    CertTemplService certTemplService;
    @Autowired
    OpenTokenCompanyService openTokenCompanyService;
    @Autowired
    CertTemplTempService certTemplTempService;
    @Autowired
    CertTemplRelationService certTemplRelationService;

    
    @RequestMapping("/getDictionaryList")
    @AnonymousAccess
    public Map<String,Object> getDictionaryList(
            @RequestParam(value = "keys", required = false) String keys,
            @RequestParam(value = "deptCode", required = false) String deptCode,
            @RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsSysDictionaryClient.getDictionaryListBase(keys);
            return map;
        }
        if(ShipManageClient.SHIP_EXPENSE.equals(deptCode)){
            Map<String, Object> map = new HashMap<>();
            return map;
        }
        Map<String, Object> map = sysDictionaryClient.getDictionaryListBase(keys);
        return map;
    }
    
    @RequestMapping("/getAllSysSupplier")
    @AnonymousAccess
    public Map<String,Object> getAllSysSupplier( @RequestParam(value = "deptCode", required = false) String deptCode,
                                                 @RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsOnAccountClient.getAllSysSupplier();
            return map;
        }
        Map<String, Object> map = onAccountClient.getAllSysSupplier();
        return map;
    }
    
    @RequestMapping("/getAllCusTomer")
    @AnonymousAccess
    public Map<String,Object> getAllCusTomer( @RequestParam(value = "deptCode", required = false) String deptCode,
                                              @RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsOnAccountClient.getAllCusTomer();
            return map;
        }
        Map<String, Object> map = onAccountClient.getAllCusTomer();
        return map;
    }

    @RequestMapping("/getAllUser")
    @AnonymousAccess
    public Map<String,Object> getAllUser(@RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsSysDictionaryClient.getAllUser();
            return map;
        }
        Map<String, Object> map = sysDictionaryClient.getAllUser();
        return map;
    }
    
    @RequestMapping("/getSysSupplierContactList")
    @AnonymousAccess
    public Map<String,Object> getSysSupplierContactList(@RequestParam(value = "supplierId",required = false) String supplierId,@RequestParam(value="deptCode",required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsBaseDataClient.getSysSupplierContactList(supplierId);
            return map;
        }
        Map<String, Object> map = baseDataClient.getSysSupplierContactList(supplierId);
        return map;
    }
    
    @RequestMapping("/getPersonalListById")
    @AnonymousAccess
    public Map<String,Object> getPersonalListById(@RequestParam(value = "id", required = false) String id,@RequestParam(value="deptCode",required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsBaseDataClient.getPersonalListById(id);
            return map;
        }
        Map<String, Object> map = baseDataClient.getPersonalListById(id);
        return map;
    }

    @RequestMapping("/getContractStatusByShipLineId")
    @AnonymousAccess
    public Map<String,Object> getContractStatusByShipLineId(@RequestParam("id") String id,@RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsShipLineClient.getContractStatusByShipLineId(id);
        }
        return null;
    }
    @RequestMapping("/getUserInfoById")
    @AnonymousAccess
    public Map<String,Object> getUserInfoById(@RequestParam(value = "userid",required = false) String userid,@RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        SysUser byId = sysUserService.getById(userid);
        if (byId != null) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", byId.getUserId());
            user.put("loginName", byId.getUsername());
            user.put("name", byId.getNickName());
            return ResponseMapBuilder.newBuilder().putSuccess().put("user", user).getResult();
        } else {
            if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
                Map<String, Object> map = hsSysDictionaryClient.getUserInfoById(userid);
                return map;
            }
            Map<String, Object> map = sysDictionaryClient.getUserInfoById(userid);
            return map;
        }
    }
    
    @RequestMapping("/selectbycode")
    @AnonymousAccess
    public Map<String,Object> selectbycode(@RequestParam(value = "code", required = false) Integer code,@RequestParam(value="deptCode",required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsBaseDataClient.selectbycode(code);
            return map;
        }
        Map<String, Object> map = baseDataClient.selectbycode(code);
        return map;
    }
    
    @RequestMapping("/selectbyId")
    @AnonymousAccess
    public Map<String,Object> selectbyId(@RequestParam(value = "id", required = false) Integer id,@RequestParam(value="deptCode",required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsBaseDataClient.selectbyId(id);
            return map;
        }
        Map<String, Object> map = baseDataClient.selectbyId(id);
        return map;
    }
    
    @RequestMapping("/getPayment")
    @AnonymousAccess
    public Map<String , Object> getPayment(
            @RequestParam(value = "paymentId", required = false) String paymentId,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsPaymentClient.getPayment(paymentId,type);
            return map;
        }
        if(ShipManageClient.SHIP_EXPENSE.equals(deptCode)){
            Map<String, Object> map = ShipManageClient.getPayment(paymentId);
            return map;
        }
        Map<String, Object> map = paymentClient.getPayment(paymentId,type);
        return map;
    }

    @RequestMapping("/fanDianCustomerInfo")
    @AnonymousAccess
    public Map<String , Object> fanDianCustomerInfo(
            @RequestParam(value = "costAggregationIds",required = false) String costAggregationIds,
            @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
           return null;
        }
        Map<String, Object> map = paymentClient.fanDianCustomerInfo(costAggregationIds);
        return map;
    }
    @RequestMapping("/heTongInfo")
    @AnonymousAccess
    public Map<String , Object> heTongInfo(
            @RequestParam(value = "costAggregationIds",required = false) String costAggregationIds,
            @RequestBody Map<String, Object> params,
            @RequestParam(value = "type",required = false) String type,
            @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        if(costAggregationIds == null && params != null && params.containsKey("costAggregationIds") == true){
            costAggregationIds = params.get("costAggregationIds").toString();
        }
        if(type == null && params != null && params.containsKey("type") == true){
            type = params.get("type").toString();
        }
        if(deptCode == null && params != null && params.containsKey("deptCode") == true){
            deptCode = params.get("deptCode").toString();
        }
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return null;
        }
        Map<String, Object> map = paymentClient.heTongInfoNew(params);
        return map;
    }
    
    @RequestMapping("/getCommonAndRecordByKey")
    @AnonymousAccess
    Map<String , Object> getCommonAndRecordByKey(
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        UserDetails currentUser = SecurityUtils.getCurrentUser();
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsBaseDataClient.getCommonAndRecordByKey(key,currentUser.getUsername());
            return map;
        }
        Map<String, Object> map = baseDataClient.getCommonAndRecordByKey(key,currentUser.getUsername());
        return map;
    }
    
    @RequestMapping("/saveRecord")
    @AnonymousAccess
    Map<String , Object> saveRecord(
            @RequestParam(value = "key",required = false) String key,
            @RequestParam(value = "datakey",required = false) String datakey,
            @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    
    ){
        UserDetails currentUser = SecurityUtils.getCurrentUser();
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsBaseDataClient.saveRecord(key,datakey,currentUser.getUsername());
            return map;
        }
        Map<String, Object> map = baseDataClient.saveRecord(key,datakey,currentUser.getUsername());
        return map;
    }
    
    @RequestMapping("/getReceive")
    @AnonymousAccess
    public Map<String , Object> getReceive(
            @RequestParam(value = "paymentId", required = false) String paymentId,
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            Map<String, Object> map = hsPaymentClient.getPayment(paymentId,type);
            return map;
        }
        Map<String, Object> map = paymentClient.getPayment(paymentId,type);
        return map;
    }
    @GetMapping("/getContractByIds")
    @AnonymousAccess
    public Map<String, Object> getContractByIds(@RequestParam("ids") String ids) {
        Assert.notBlank(ids);
        List<Contract> contracts = contractService.listByIds(StrUtil.split(ids, ',', -1));
        return ResultBody.ok().data(contracts);
    }

    @GetMapping("/getContractByShipLineId")
    @AnonymousAccess
    public Map<String, Object> getContractByShipLineId(@RequestParam("ids") String ids,@RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId) {
        String conIds=null;
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
             conIds = hsShipLineClient.getContractByShipLineId(ids);
        }else{
            conIds = shipLineClient.getContractByShipLineId(ids);
        }
        if(StrUtil.isNotBlank(conIds)){
            List<Contract> contracts = contractService.listByIds(StrUtil.split(conIds, ',', -1));
            return ResultBody.ok().data(contracts);
        }
        return null;
    }


    @RequestMapping("/getShipLineDingjingById")
    @AnonymousAccess
    Map<String , Object> getShipLineDingjingById(
        @RequestParam(value = "shipLineId", required = false) String shipLineId,
        @RequestParam(value = "processId", required = false) String processId,
        @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId) {
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsShipLineClient.getShipLineDingjingById(shipLineId, processId);
        }
        return shipLineClient.getShipLineDingjingById(shipLineId, processId);
    }

    @RequestMapping("/saveSysCustomerSecondlevel")
    public Map<String , Object> saveSysCustomerSecondlevel(
            @RequestParam(value = "name",required = false) String name,
            @RequestParam(value = "deptCode", required = false) String deptCode,
            @RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsOnAccountClient.saveSysCustomerSecondlevel(name);
        }
       return onAccountClient.saveSysCustomerSecondlevel(name);
    }
    @RequestMapping("/saveSysSupplierName")
    public Map<String , Object> saveSysSupplierName(
            @RequestParam(value = "name",required = false) String name,
            @RequestParam(value = "deptCode", required = false) String deptCode,
            @RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsOnAccountClient.saveSysSupplierName(name);
        }
        return onAccountClient.saveSysSupplierName(name);
    }

    @RequestMapping("/shipLinePageByStatus")
    public Map<String, Object> shipLinePageByStatus(@RequestParam(value = "shipChuanQiTime", required = false) String shipChuanQiTime, @RequestParam(value = "shipName", required = false) String shipName, @RequestParam(value = "status", required = false) String status, @RequestParam(value = "pageNum", required = false) Integer pageNum, @RequestParam(value = "pageSize", required = false) Integer pageSize, @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsShipLineClient.shipLinePageByStatus(shipChuanQiTime,shipName,status,pageNum,pageSize);
        }
        return shipLineClient.shipLinePageByStatus(shipChuanQiTime,shipName,status,pageNum,pageSize);
    }
    @RequestMapping("/getYewuCostomerCostNoComfirm")
    public Map<String, Object> getYewuCostomerCostNoComfirm(@RequestParam(value = "shipLineId", required = false) String shipLineId, @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsShipLineClient.getYewuCostomerCostNoComfirm(shipLineId);
        }
        return shipLineClient.getYewuCostomerCostNoComfirm(shipLineId);
    }
    @RequestMapping("/getShipCostNoComfirmByShipLineIds")
    public Map<String, Object> getShipCostNoComfirmByShipLineIds(@RequestBody Map<String, Object> map){
//        String shipLineIds = (String) map.get("shipLineIds");
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(map.get("deptCode")) || wxDepartmentService.isHeShengById((String) map.get("wxDepartmentId"))){
            return hsShipLineClient.getShipCostNoComfirmByShipLineIds(map);
        }
        return shipLineClient.getShipCostNoComfirmByShipLineIds(map);
    }
    @RequestMapping("/getGoodsCostNoComfirmByShipLineIds")
    public Map<String, Object> getGoodsCostNoComfirmByShipLineIds(@RequestBody Map<String, Object> map){
//        String goodsCostIds = (String) map.get("goodsCostIds");
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(map.get("deptCode")) || wxDepartmentService.isHeShengById((String) map.get("wxDepartmentId"))){
            return hsShipLineClient.getGoodsCostNoComfirmByShipLineIds(map);
        }
        return shipLineClient.getGoodsCostNoComfirmByShipLineIds(map);
    }

    @RequestMapping("/getShipCostNoComfirm")
    public Map<String, Object> getShipCostNoComfirm(@RequestParam(value = "shipLineId", required = false) String shipLineId, @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsShipLineClient.getShipCostNoComfirm(shipLineId);
        }
        return shipLineClient.getShipCostNoComfirm(shipLineId);
    }
    @RequestMapping("/getSupplierDictionaryList")
    public Map<String, Object> getSupplierDictionaryList(
            @RequestParam(value = "Key",required = false) String Key,
            @RequestParam(value = "Code",required = false) String Code,
            @RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId
    ){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsBaseDataClient.getSupplierDictionaryList(Key,Code);
        }
        return baseDataClient.getSupplierDictionaryList(Key,Code);
    }
    @RequestMapping("/getAllShipPort")
    public Map<String, Object> getAllShipPort(@RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsBaseDataClient.getAllShipPort();
        }
        return baseDataClient.getAllShipPort();
    }
    @RequestMapping("/getAllSysWharf")
    public Map<String, Object> getAllSysWharf(@RequestParam(value = "deptCode", required = false) String deptCode,@RequestParam(value = "wxDepartmentId", required = false) String wxDepartmentId){
        if(ParamsKeys.HESHENG_DEPT_CODE.equals(deptCode) || wxDepartmentService.isHeShengById(wxDepartmentId)){
            return hsBaseDataClient.getAllSysWharf();
        }
        return baseDataClient.getAllSysWharf();
    }
    @Value("${haokj.msg.datakey}")
    String haokjMsgDataKey;
    @Value("${haokj.msg.key}")
    String haokjMsgKey;
    @Value("${haokj.msg.appcode}")
    String haokjAppCode;

    @PostMapping("/haoKuaiJiMsg")
    @AnonymousAccess
    public Map<String,Object> haoKuaiJiMsg(@RequestBody Map<String, Object> map){
//        返回Json格式的｛'result'：'success'｝
        if(map.containsKey(haokjMsgDataKey)){
            haoOpenTokenService.processingMsg(map.get(haokjMsgDataKey).toString(),haokjMsgKey);
        }
        HashMap<String,Object> result = new HashMap<>();
        result.put("result","success");
        return result;
    }
   /* @GetMapping("/haoKuaiJiToken")
    @AnonymousAccess
    public Map<String,Object> haoKuaiJiToken(){
        JSONObject token = haoOpenTokenService.getToken(haokjAppCode);
        HashMap<String,Object> result = new HashMap<>();
        result.put("result","success");
        result.put("token",token);
        return result;
    }*/

    // 凭证模版编辑 cert_templ  cert_templ_relation
    // certTempl certTemplRelation
    @RequestMapping("/getCertTemplList")
    public Map<String, Object> getCertTemplList(@RequestParam(value = "name", required = false) String name){
        List<CertTemplVo> list = certTemplService.getCertTemplList(name);
        return ResponseMapBuilder.newBuilder()
                .put("list",list)
                .putSuccess()
                .getResult();
    }
    // 新增
    @PostMapping("/saveCertTemplVo")
    public Map<String, Object> saveCertTemplVo(@RequestBody CertTemplVo certTemplVo){
        certTemplService.saveCertTemplVo(certTemplVo);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    // 修改
    @PostMapping("/updateCertTemplVo")
    public Map<String, Object> updateCertTemplVo(@RequestBody CertTemplVo certTemplVo){
        certTemplService.updateCertTemplVo(certTemplVo);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @DeleteMapping("/removeCertTemplVo")
    public Map<String, Object> removeCertTemplVo(@RequestParam("id") Long id){
        certTemplRelationService.removeById(id);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    // 公司帐套列表
    @RequestMapping("/getTokenCompanyList")
    public Map<String, Object> getTokenCompanyList(OpenTokenCompanyQueryCriteria criteria){
        List<OpenTokenCompany> list = openTokenCompanyService.queryAll(criteria);
        return ResponseMapBuilder.newBuilder()
                .put("list",list)
                .putSuccess()
                .getResult();
    }
    // 公司帐套修改
    @PostMapping("/updateTokenCompany")
    public Map<String, Object> updateTokenCompany(@RequestBody OpenTokenCompany openTokenCompany) {
        openTokenCompanyService.updateById(openTokenCompany);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    // 与帐套绑定的模版科目
    @RequestMapping("/getCertTemplTempList")
    public Map<String, Object> getCertTemplTempList(){
        List<CertTemplTemp> list = certTemplTempService.list();
        return ResponseMapBuilder.newBuilder()
                .put("list",list)
                .putSuccess()
                .getResult();
    }

    // 新增
    @PostMapping("/saveCertTemplTemp")
    public Map<String, Object> saveCertTemplTemp(@RequestBody List<CertTemplTemp> certTemplVo){
        certTemplTempService.saveTemp(certTemplVo);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    // 修改
    @PostMapping("/removeCertTemplTemp")
    public Map<String, Object> removeCertTemplTemp(@RequestBody CertTemplTemp certTemplVo){
        certTemplTempService.removeAll(certTemplVo);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }
}
