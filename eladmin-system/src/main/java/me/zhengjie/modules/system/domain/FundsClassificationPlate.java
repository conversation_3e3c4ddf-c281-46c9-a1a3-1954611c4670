package me.zhengjie.modules.system.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
* @description /
* <AUTHOR>
* @date 2023-02-27
**/
@Data
@TableName("funds_classification_plate")
public class FundsClassificationPlate implements Serializable {


    @TableId
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "板块id")
    private String plateId;

    @ApiModelProperty(value = "资金分类id")
    private String fundsClassId;

    @ApiModelProperty(value = "公司id")
    private String comId;

    @ApiModelProperty(value = "排序")
    private String sortby;

    @ApiModelProperty(value = "1 正常 0 删除")
    @TableLogic(value = "1",delval = "0")
    private Integer delFlag;

    private String deptCode;

    public void copy(FundsClassificationPlate source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}