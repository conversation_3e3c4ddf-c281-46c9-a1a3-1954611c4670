/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-08-12
**/
@Data
public class ImprestFundReocrdDto implements Serializable {

    private String id;

    /** 账户id */
    private String imprestFundId;

    /** 消费日期 */
    private String consumptionDate;

    /** 1日常、2招待、3业务 */
    private Integer consumptionType;

    /** 消费金额 */
    private BigDecimal balance;

    /** 1支出、2收入 */
    private Integer state;

    /** 0 无 1 有 2收据 */
    private Integer isBill;

    /** 备注 */
    private String remarks;

    /** 附件 */
    private String attach;

    /** 年-月 */
    private String monthRec;

    /** 排序 */
    private String orderBy;

    /** 月账单id */
    private String monthBillId;

    /** 0 创建， 1提交中，3审批成功，2 审批失败 */
    private Integer status;

    /** 0正常 1删除 */
    private Integer delFlag;

    private String createBy;

    private String updateBy;

    private Timestamp createTime;

    private Timestamp updateTime;

    private String param1;

    private String param2;

    private String param3;
}