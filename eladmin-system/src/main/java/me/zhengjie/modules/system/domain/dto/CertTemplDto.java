package me.zhengjie.modules.system.domain.dto;

import lombok.Data;
import me.zhengjie.modules.system.domain.CertTemplRelation;

import java.sql.Timestamp;
import java.util.List;

@Data
public class CertTemplDto extends CertTemplRelation {
//    ct.app_code,
//    ct.code,
//    ct.flow_code,
//    ct.condition_str,
//    ct.name,
//    ct.regular_expressions,
//    ct.create_time,
//    ct.last_time,
//    ct.templ_data
    private Integer parentId;
    private String parentAppCode;
    private String parentCode;
    private String parentFlowCode;
    private String parentConditionStr;
    private String parentName;
    private String parentRegularExpressions;
    private String parentTemplData;
    private Timestamp parentCreateTime;
    private Timestamp parentLastTime;


}
