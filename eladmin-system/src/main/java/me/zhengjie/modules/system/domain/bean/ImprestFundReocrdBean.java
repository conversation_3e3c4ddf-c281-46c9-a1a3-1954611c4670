package me.zhengjie.modules.system.domain.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;
import me.zhengjie.modules.system.domain.ImprestFundReocrd;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImprestFundReocrdBean extends ImprestFundReocrd {
    // 船期id|离港日期｜船名,船期id|离港日期｜船名...
    private String departParam;

    private BigDecimal incomeMoney;

    private BigDecimal expendMoney;

    private BigDecimal diffMoney;

    private String tijiaocaiwu;
    private String costIsComplete;

    private String type; // 货物费用  船费

    private String shipLineId;

    private String shipCostId;

    private String goodsCostId;

    private String goodsCostDetailId;

    private  Integer tax;

}
