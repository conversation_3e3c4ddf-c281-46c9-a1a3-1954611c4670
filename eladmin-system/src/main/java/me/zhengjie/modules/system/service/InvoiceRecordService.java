package me.zhengjie.modules.system.service;

import cn.hutool.json.JSONArray;
import com.dlcg.tms.bean.InvoiceUpdateBillMonthBean;
import me.zhengjie.base.BaseService;
import me.zhengjie.modules.system.domain.InvoiceRecord;
import me.zhengjie.modules.system.domain.bean.QueryParamBean;
import me.zhengjie.modules.system.service.dto.InvoiceRecordDto;
import me.zhengjie.modules.system.service.dto.InvoiceRecordQueryCriteria;
import me.zhengjie.modules.workflow.model.entity.ProcessInstance;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @description 服务接口
* <AUTHOR>
* @date 2024-12-26
**/
public interface InvoiceRecordService extends BaseService<InvoiceRecord> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(InvoiceRecordQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<InvoiceRecordDto>
    */
    List<InvoiceRecordDto> queryAll(InvoiceRecordQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<InvoiceRecordDto> all, HttpServletResponse response) throws IOException;
    // 同步销项
    void addInvoiceRecordSales(InvoiceRecordDto invoiceRecord);
    // 同步进项
        // 业务收票
    void addInvoiceRecordPurchase(InvoiceRecordDto invoiceRecord);
    // 报销收票
    void addInvoiceRecordPurchaseReimburse(ProcessInstance instance);

    Map<String,Object> listBySelf(QueryParamBean bean);

    Map<String,Object> updateBilMonthSlef(InvoiceUpdateBillMonthBean bean);

    void uploadSalesInvoice(List<InvoiceRecordDto> list);

    void uploadPurchaseInvoice(List<InvoiceRecordDto> list);

    void uploadReceiptInvoice(List<InvoiceRecordDto> list);

    void delInvoiceDialog(List<String> ids);

    List<InvoiceRecord> listPrepaymentReimbursementInvoice(String... ids);

    List<Map<String, Object>> getInvoiceCountByProcessIds(String ids);

    void addPrepaymentReimbursementInvoice(List<InvoiceRecordDto> invoiceRecordDto);
}