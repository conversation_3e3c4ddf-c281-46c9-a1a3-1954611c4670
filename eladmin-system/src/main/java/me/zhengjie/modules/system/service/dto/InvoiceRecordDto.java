/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.service.dto;

import lombok.Data;
import me.zhengjie.modules.system.domain.InvoiceRecord;

import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2024-12-26
**/
@Data
public class InvoiceRecordDto extends InvoiceRecord implements Serializable {

    private String id;

    /** 发票号 */
    private String invoiceNo;

    /** 发票代码 */
    private String invoiceCode;

    /** 发票日期 */
    private String invoiceDate;

    /** 发票不含税金额 */
    private BigDecimal invoicePrice;

    /** 发票税额 */
    private BigDecimal invoicePriceTax;

    /** 发票含税金额 */
    private BigDecimal invoicePriceTaxSum;

    /** 税率 */
    private String invoiceTax;

    /** 买方名称 */
    private String buyerName;

    /** 买方纳税人识别号 */
    private String buyerTaxpayerNo;

    /** 买方地址电话 */
    private String buyerAddressTel;

    /** 买方开户行及账号 */
    private String buyerAccount;

    /** 卖方名称 */
    private String sellerName;

    /** 卖方纳税人识别号 */
    private String sellerTaxpayerNo;

    /** 卖方地址电话 */
    private String sellerAddressTel;

    /** 卖方开户行及账号 */
    private String sellerAccount;

    /** 发票图片 */
    private String originFile;

    /** 收款人 */
    private String invoicePayee;

    /** 开票人 */
    private String invoiceDrawer;

    /** 复核 */
    private String invoiceReview;

    /** 发票备注 */
    private String invoiceRemark;

    /** 创建时间 */
    private Timestamp createTime;

    /** 修改时间 */
    private Timestamp updateTime;

    /** 记账月份 */
    private String recordAccountMonth;

    /** 状态 0 待提交审批 1 审批中 2待入账 3已完成 */
    private Integer status;

    /** 购买方ID */
    private String buyerId;

    /** 销售方ID */
    private String sellerId;

    /** 费用类型ID */
    private String costTypeId;

    /** 费用类型名称 */
    private String costTypeName;

    /** 费用字典key */
    private String costDictKey;

    /** 服务名称 */
    private String serviceName;

    /** 拆分人 */
    private String splitUserId;

    /** 拆分时间 */
    private Timestamp splitTime;

    /** 发票类型 普票、专票 */
    private String invoiceType;

    /** 销项发票、进项发票 */
    private String outInputInvoice;

    /** 是否重复 */
    private Integer isItRepeated;

    /** 数据来源 */
    private String invoiceDataSource;

    /** 关联流程id */
    private String associatedProcessId;

    private String title;
    private String subTypeName;
    /** 是否生成凭证 */
    private Integer isCertificate;

    /** 凭证id */
    private String credentialId;
}