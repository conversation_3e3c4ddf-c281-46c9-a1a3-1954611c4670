/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-01-25
**/
@Data
public class SysExternalCustomerDto implements Serializable {

    private Integer id;

    /** 全称 */
    private String name;

    /** 简称 */
    private String shortName;

    /** 合同名头 */
    private String contractHeader;

    /** 纳税识别号 */
    private String taxIdNumber;

    /** 注册地址 */
    private String registeredAddress;

    /** 母公司id */
    private Integer parendId;

    private String createBy;

    private Timestamp createDate;

    private String updateBy;

    private Timestamp updateDate;

    private String delFlag;

    private String spare1;

    private String spare2;

    private String spare3;

    private String spare4;

    private String spare5;
}