package me.zhengjie.modules.system.service.impl;

import me.zhengjie.modules.system.domain.SysCashRole;
import me.zhengjie.base.BaseServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import me.zhengjie.utils.QueryHelpPlus;
import me.zhengjie.utils.ValidationUtil;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.modules.system.mapper.SysCashRoleMapper;
import me.zhengjie.modules.system.service.SysCashRoleService;
import me.zhengjie.modules.system.service.dto.SysCashRoleDto;
import me.zhengjie.modules.system.service.dto.SysCashRoleQueryCriteria;
import me.zhengjie.modules.system.service.mapstruct.SysCashRoleSMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import me.zhengjie.utils.PageUtil;
import me.zhengjie.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @description 服务实现
* <AUTHOR>
* @date 2021-12-27
**/
@Service
public class SysCashRoleServiceImpl extends BaseServiceImpl<SysCashRoleMapper,SysCashRole> implements SysCashRoleService {

    @Resource
    private SysCashRoleSMapper sysCashRoleSMapper;
    @Resource
    private SysCashRoleMapper sysCashRoleMapper;

    @Override
    public Map<String,Object> queryAll(SysCashRoleQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<SysCashRoleDto> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<SysCashRoleDto> queryAll(SysCashRoleQueryCriteria criteria){
        return listToDto(list(QueryHelpPlus.getPredicate(SysCashRole.class, criteria)),sysCashRoleSMapper);
    }

    @Override
    public void download(List<SysCashRoleDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SysCashRoleDto sysCashRole : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put(" userId",  sysCashRole.getUserId());
            map.put(" cashId",  sysCashRole.getCashId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}