package me.zhengjie.modules.system.service;

import cn.hutool.json.JSONArray;
import com.github.pagehelper.Page;
import me.zhengjie.base.BaseService;
import me.zhengjie.modules.business.service.bean.ReceiveWaterBean;
import me.zhengjie.modules.system.domain.ReceiveWater;
import me.zhengjie.modules.system.domain.vo.DeptMoneyVo;
import me.zhengjie.modules.system.domain.vo.ReceiveWaterVo;
import me.zhengjie.modules.system.service.dto.ReceiveWaterDto;
import me.zhengjie.modules.system.service.dto.ReceiveWaterQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* @description 服务接口
* <AUTHOR>
* @date 2021-10-25
**/
public interface ReceiveWaterService extends BaseService<ReceiveWater> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ReceiveWaterQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ReceiveWaterDto>
    */
    List<ReceiveWaterDto> queryAll(ReceiveWaterQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ReceiveWaterDto> all, HttpServletResponse response) throws IOException;
    
    Page<me.zhengjie.modules.system.domain.bean.ReceiveWaterBean> getReceiveWaterList(
            String receiveCompany,String customer,Integer costType,Integer receiveStatus,Integer receiveType,
            Date startTime,Date endTime,Integer pageNo,Integer pageSize,List<Long> ids,List<Long> receiveCompanyTypes,String accid,String fundsClassId);
    
    Page<ReceiveWater> getReceiveWaterListBusiness(
            String receiveCompany,String customer,Integer receiveStatus,Integer receiveType,
            Date startTime,Date endTime,Integer pageNo,Integer pageSize);

    List<ReceiveWaterBean> getUnusedAcceptances();

    boolean updateAcceptanceInProcessid(String processId, String...ticket_number);

    List<DeptMoneyVo> getDeptMoney(String deptIds, String startTime, String endTime);
    List<DeptMoneyVo> getDeptMoneyAndFundClass(String deptIds, String startTime, String endTime);

    ReceiveWaterVo getReceiveVoById(String receiveId);

    void addOtherReceiveWater(JSONArray moneys);
}