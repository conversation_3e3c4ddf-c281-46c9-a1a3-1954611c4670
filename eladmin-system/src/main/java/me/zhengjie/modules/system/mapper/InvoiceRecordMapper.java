package me.zhengjie.modules.system.mapper;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.zhengjie.modules.system.domain.InvoiceRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-12-26
 */
@Mapper
public interface InvoiceRecordMapper extends BaseMapper<InvoiceRecord> {
    List<Map<String, Object>> getInvoiceCountByProcessIds(@Param("ids") List<String> ids);
}
