package me.zhengjie.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.zhengjie.modules.system.domain.ImprestFundApply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-08-12
 */
@Mapper
public interface ImprestFundApplyMapper extends BaseMapper<ImprestFundApply> {
    BigDecimal selectIncomeMoney(@Param("fundId")String fundId, @Param("startMonth")String startMonth, @Param("endMonth")String endMonth);
    Integer getIncomeCount(@Param("fundId")String fundId,@Param("startMonth")String startMonth,@Param("endMonth")String endMonth);

    List<ImprestFundApply> incomeMoneyList(@Param("fundId")String fundId,@Param("startMonth")String startMonth,@Param("endMonth")String endMonth);
}
