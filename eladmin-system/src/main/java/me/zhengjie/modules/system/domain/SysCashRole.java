package me.zhengjie.modules.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.io.Serializable;

/**
* @description /
* <AUTHOR>
* @date 2021-12-27
**/
@Data
@TableName("sys_cash_role")
public class SysCashRole implements Serializable {


    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "userId")
    private String userId;

    @ApiModelProperty(value = "cashId")
    private Integer cashId;

    public void copy(SysCashRole source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}