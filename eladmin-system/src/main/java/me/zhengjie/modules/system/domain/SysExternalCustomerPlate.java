package me.zhengjie.modules.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @description /
* <AUTHOR>
* @date 2022-01-29
**/
@Data
@TableName("sys_external_customer_plate")
public class SysExternalCustomerPlate implements Serializable {


    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "customerId")
    private Integer customerId;

    @ApiModelProperty(value = "dictId")
    private Long dictId;

    @ApiModelProperty(value = "是否客户")
    private Integer isCustomer;

    @ApiModelProperty(value = "是否供应商 1 是 0 否")
    private Integer isSupplier;

    @ApiModelProperty(value = "createBy")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "createDate")
    private Timestamp createDate;

    @ApiModelProperty(value = "updateBy")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "updateDate")
    private Timestamp updateDate;

    @ApiModelProperty(value = "delFlag")
    @TableLogic
    private String delFlag;

    @ApiModelProperty(value = "spare1")
    private String spare1;

    @ApiModelProperty(value = "spare2")
    private String spare2;

    @ApiModelProperty(value = "spare3")
    private String spare3;

    @ApiModelProperty(value = "spare4")
    private String spare4;

    @ApiModelProperty(value = "spare5")
    private String spare5;

    public void copy(SysExternalCustomerPlate source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}