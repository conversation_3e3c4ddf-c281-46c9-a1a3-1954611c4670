/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.rest;

import me.zhengjie.annotation.Log;
import me.zhengjie.modules.system.domain.AccountMoneyForMonthFundsClass;
import me.zhengjie.modules.system.service.AccountMoneyForMonthFundsClassService;
import me.zhengjie.modules.system.service.dto.AccountMoneyForMonthFundsClassQueryCriteria;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.Arrays;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2023-03-27
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "accountMoneyForMonthFundsClass管理")
@RequestMapping("/api/accountMoneyForMonthFundsClass")
public class AccountMoneyForMonthFundsClassController {

    private final AccountMoneyForMonthFundsClassService accountMoneyForMonthFundsClassService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('accountMoneyForMonthFundsClass:list')")
    public void download(HttpServletResponse response, AccountMoneyForMonthFundsClassQueryCriteria criteria) throws IOException {
        accountMoneyForMonthFundsClassService.download(accountMoneyForMonthFundsClassService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询accountMoneyForMonthFundsClass")
    @ApiOperation("查询accountMoneyForMonthFundsClass")
    @PreAuthorize("@el.check('accountMoneyForMonthFundsClass:list')")
    public ResponseEntity<Object> query(AccountMoneyForMonthFundsClassQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(accountMoneyForMonthFundsClassService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增accountMoneyForMonthFundsClass")
    @ApiOperation("新增accountMoneyForMonthFundsClass")
    @PreAuthorize("@el.check('accountMoneyForMonthFundsClass:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody AccountMoneyForMonthFundsClass resources){
        return new ResponseEntity<>(accountMoneyForMonthFundsClassService.save(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改accountMoneyForMonthFundsClass")
    @ApiOperation("修改accountMoneyForMonthFundsClass")
    @PreAuthorize("@el.check('accountMoneyForMonthFundsClass:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody AccountMoneyForMonthFundsClass resources){
        accountMoneyForMonthFundsClassService.updateById(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除accountMoneyForMonthFundsClass")
    @ApiOperation("删除accountMoneyForMonthFundsClass")
    @PreAuthorize("@el.check('accountMoneyForMonthFundsClass:del')")
    @DeleteMapping
    public ResponseEntity<Object> delete(@RequestBody Integer[] ids) {
        accountMoneyForMonthFundsClassService.removeByIds(Arrays.asList(ids));
        return new ResponseEntity<>(HttpStatus.OK);
    }
}