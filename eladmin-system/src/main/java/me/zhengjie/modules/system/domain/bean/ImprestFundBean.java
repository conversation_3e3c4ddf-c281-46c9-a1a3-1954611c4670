package me.zhengjie.modules.system.domain.bean;

import lombok.Data;
import lombok.EqualsAndHashCode;
import me.zhengjie.modules.system.domain.ImprestFund;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class ImprestFundBean extends ImprestFund {
    private String companyDepartId;

    private String companyParentId;

    private BigDecimal startMoney;

    private BigDecimal endMoney;

    private BigDecimal incomeMoney;

    private BigDecimal expendMoney;

    private Integer incomeCount;
    private Integer expendCount;

}
