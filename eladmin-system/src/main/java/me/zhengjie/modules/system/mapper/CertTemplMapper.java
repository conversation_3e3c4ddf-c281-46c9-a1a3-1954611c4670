package me.zhengjie.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.zhengjie.modules.system.domain.CertTempl;
import me.zhengjie.modules.system.domain.dto.CertTemplDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-14
 */
@Mapper
public interface CertTemplMapper extends BaseMapper<CertTempl> {
    List<CertTemplDto> getCertTemplList(@Param("name")String name);
}
