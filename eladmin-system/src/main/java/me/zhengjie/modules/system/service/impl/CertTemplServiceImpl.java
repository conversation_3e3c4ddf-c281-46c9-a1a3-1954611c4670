package me.zhengjie.modules.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONNull;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.enums.SqlKeyword;
import com.dlcg.oa.client.ProcessClient;
import com.dlcg.oa.client.SysDictionaryClient;
import com.dlcg.oa.entity.SysDictionary;
import com.dlcg.tms.client.InvoiceClient;
import com.dlcg.tms.client.OnAccountClient;
import com.dlcg.tms.entity.InvoiceOcrRecord;
import com.dlcg.tms.entity.SysSupplier;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import lombok.RequiredArgsConstructor;
import me.zhengjie.base.BaseServiceImpl;
import me.zhengjie.modules.business.service.SysUserService;
import me.zhengjie.modules.client.shipmanage.ShipManageClient;
import me.zhengjie.modules.system.domain.*;
import me.zhengjie.modules.system.domain.dto.CertTemplDto;
import me.zhengjie.modules.system.domain.vo.CertTemplVo;
import me.zhengjie.modules.system.mapper.*;
import me.zhengjie.modules.system.service.*;
import me.zhengjie.modules.wechat.code.domain.WxDepartment;
import me.zhengjie.modules.wechat.code.mapper.WxDepartmentMapper;
import me.zhengjie.modules.wechat.code.service.WxDepartmentService;
import me.zhengjie.modules.workflow.common.PaymentConfig;
import me.zhengjie.modules.workflow.model.document.DProcessInstance;
import me.zhengjie.modules.workflow.model.entity.ProcessInstance;
import me.zhengjie.modules.workflow.service.ProcessInstanceService;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CertTemplServiceImpl extends BaseServiceImpl<CertTemplMapper,CertTempl> implements CertTemplService {

    private final CertTemplMapper certTemplMapper;

    private final CertTemplRelationMapper certTemplRelationMapper;

    private final CertDataMapper certDataMapper;

    private final CertDataProcessMapper certDataProcessMapper;
    private final CertDataProcessService certDataProcessService;

    private final WxDepartmentMapper wxDepartmentMapper;

    private final MongoTemplate mongoTemplate;

    private final FundsClassificationService fundsClassificationService;

    private final ReceiveWaterService receiveWaterService;
    private static final Logger log = LoggerFactory.getLogger(CertTemplServiceImpl.class);

    private final HaoOpenTokenService openTokenService;

    private final InvoiceRecordService invoiceRecordService;

    private final OnAccountClient onAccountClient;

    private final OpenTokenCompanyMapper openTokenCompanyMapper;

    private final RedisTemplate redisTemplate;

    private final ProcessInstanceService processInstanceService;

    private final SysUserService sysUserService;

    private final ProcessClient processClient;

    private final SysDictionaryClient sysDictionaryClient;

    private final ShipManageClient shipManageClient;

    private final WxDepartmentService wxDepartmentService;

    private final PaymentWaterService paymentWaterService;

    private final CertTemplRelationService certTemplRelationService;

    private final InvoiceClient invoiceClient;
    @Value("${haokj.msg.appcode}")
    String haokjAppCode;
    @Value("${haokj.api.url}")
    String baseUrl;
    @Value("${haokj.api.bookid}")
    String bookId;
    // 应付
    String PAYING = "paying";
    // 实付
    String PAYEND = "payend";
    // 工资
    String SALARY = "salary";
    // 差旅
    String TRAVEL = "travel";
    // 业务招待费
    String BUSINESS = "business";
    // 费用报销
    String EXPENSES = "expenses";
    // 借款
    String LOAN = "loan";
    // 集团外部归还单位借款
    String GROUP_LOAN = "groupLoan";
    // 集团外归还个人借款
    String GROUP_PERSONAL="groupPersonal";
    //归还长期短期银行借款
    String GROUP_BANK = "groupBank";
    // 集团资金调剂付款申请
    String CURRENT_ACCOUNT = "currentAccount";
    // 银行账户资金调剂申请
    String CURRENT_ACCOUNT_BANK = "currentAccountBank";
    // 承兑贴现
    String ACCEPTANCE = "acceptance";
    //税费划转流程
    String TAX_TRANSFER = "taxTransfer";
    // 银行服务划款流程
    String BANK_TRANSFER = "bankTransfer";
    String SPLI = "_";
    String SALARY_PAYING = SALARY+SPLI+PAYING;
    // 报销
    String TRAVEL_PAYING = TRAVEL+SPLI+PAYING;
    String BUSINESS_PAYING = BUSINESS+SPLI+PAYING;

    String EXPENSES_PAYING = EXPENSES+SPLI+PAYING;
    // end

    String SALARY_PAYEND = SALARY+SPLI+PAYEND;

    String TRAVEL_PAYEND = TRAVEL+SPLI+PAYEND;

    String LOAN_PAYEND = LOAN+SPLI+PAYEND;

    String BUSINESS_PAYEND = BUSINESS+SPLI+PAYEND;

    String EXPENSES_PAYEND = EXPENSES+SPLI+PAYEND;


    String SALARY_PAYING_SUMMARY = "计提[MM]月工资及社保公积金";

    String SALARY_PAYEND_SUMMARY = "工资";

    String TRAVEL_PAYING_SUMMARY = "报销[username]差旅费";

    String TRAVEL_PAYEND_SUMMARY = "支付给[username]差旅费";

    String SALARY_TEMPL_NAME = "姓名";
    String SALARY_TEMPL_DEPT ="部门";

    private Map<String,Object> voucherListByCode(String tmplCode,String id,JSONObject fromValue,String single,String money,String appCode,String companyId,List<JSONObject> applys){
        // 判断是否已创建, 付款 可以有多个凭证，根据已付款金额 科目 返回未付款的科目

        LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
        if(StrUtil.isNotBlank(appCode)){
            queryWrapper.eq(CertData::getAppCode, appCode);
        }
//        queryWrapper.eq(CertData::getProcessId, certDataGetId(id,tmplCode));
        queryWrapper.eq(CertData::getProcessId, certDataGetIdGet(id,tmplCode));
        queryWrapper.eq(CertData::getAccModel, getAccModel());
        queryWrapper.eq(CertData::getCode, tmplCode);
        List<CertData> certDatas = certDataMapper.selectList(queryWrapper);
        if(tmplCode.endsWith(PAYING)){
            if(certDatas!=null && certDatas.size()>0){
                log.info("197");
                return null;
            }
        }
        // curdata
       /* if(fromValue.containsKey("cruddata")){
            JSONObject curdata = fromValue.getJSONObject("cruddata");
            if(curdata.containsKey("data0")){
                JSONArray datas = new JSONArray();
                for(String key:curdata.keySet()){
                    JSONArray ja = curdata.getJSONArray(key);
                    if(ja!=null && ja.size()>0){
                        datas.addAll(ja);
                    }
                }
                if(datas.size()>0){
                    fromValue.set("cruddata",datas);
                }
            }
        }*/
        // 获取支付金额
        if(StrUtil.isNotBlank(money)){
            ProcessInstance processInstance = processInstanceService.getById(id);
            if(processInstance!=null && null != processInstance.getPaidMoney()){
                money = processInstance.getPaidMoney().toString();
            }
        }
        if(applys!=null && applys.size()==0){
            if(fromValue.containsKey("bakPaymentWaterList")){
                JSONArray ja = fromValue.getJSONArray("bakPaymentWaterList");
                if(ja!=null && ja.size()>0){
                    applys = ja.toList(JSONObject.class);
                }
            }
        }


        // 请求好会计
//        JSONObject fromValue = codeJson.getJSONObject("data");
        // 工资 ExternalPaymentApplication ， shebao 是
//        if(flowCode.equals("ExternalPaymentApplication") && fv.get("shebao")!=null){

        // 查询模版 appcode , tmplCode
        List<CertTemplRelation> list = getCertTemplRelation(tmplCode);
        if(CollectionUtil.isNotEmpty(list)){
            if(tmplCode.equals(SALARY_PAYEND) || tmplCode.equals(SALARY_PAYING)){
                if(StrUtil.isBlank(single)){
                    single="0";
                }
                JSONArray moneyJson=null;
                // 获取list
                if(fromValue.containsKey("travel")){
                    JSONObject j = fromValue.getJSONObject("travel");
                    if(tmplCode.equals(SALARY_PAYING) && j.containsKey(SALARY_PAYING)){
                        moneyJson = j.getJSONArray(SALARY_PAYING);
                    }
                    if(tmplCode.equals(SALARY_PAYEND) && !"1".equals(single) && j.containsKey(SALARY_PAYEND)){
                        moneyJson = j.getJSONArray(SALARY_PAYEND);
                    }
                    if(tmplCode.equals(SALARY_PAYEND) && "1".equals(single) && j.containsKey(SALARY_PAYEND)){
                        moneyJson = j.getJSONArray(SALARY_PAYEND+"_person");
                    }
                }
                if(moneyJson==null || moneyJson.size()==0){
                    CertTempl certTempl = getCertTempl(tmplCode);
                    // 读取工资表
                    JSONObject jFileList = fromValue.getJSONObject("fileListg");
                    if(jFileList==null || jFileList.size()==0){
                        JSONArray ja = fromValue.getJSONArray("fileList");
                        moneyJson = moneyByFileArray(ja,list,certTempl,single);
                    }else{
                        moneyJson = moneyByFileArray(jFileList,list,certTempl,single);
                    }

                    if(moneyJson!=null && moneyJson.size()>0){
                        JSONObject j = fromValue.getJSONObject("travel");
                        if(j==null){
                            j = new JSONObject();
                        }
                        j.set(tmplCode.equals(SALARY_PAYING)?SALARY_PAYING:"1".equals(single)?SALARY_PAYEND+"_person":SALARY_PAYEND,moneyJson);
                        fromValue.put("travel",j);
                        mongoTemplate.updateFirst(new Query(Criteria.where("_id").is(id)),
                                new Update().set("fromvalue", fromValue), DProcessInstance.class);
                    }
                }

                if(moneyJson==null){
                    moneyJson = new JSONArray();
                }
                // 读取金额 根据金额会写
                if(tmplCode.equals(SALARY_PAYEND) && StrUtil.isNotBlank(money)){
                    // 已付款科目 及 金额
                    // certDatas
                    Map<String,BigDecimal> mapCodeBd = new HashMap<>();
                    for(CertData certData : certDatas){
                        JSONObject jo =JSONUtil.parseObj(certData.getDetails());
                        JSONArray ja = jo.getJSONObject("sysData").getJSONArray("details");
                        for(int i=0;i<ja.size();i++){
                            JSONObject j = ja.getJSONObject(i);
                            if(!j.getStr("basePostedDr").equals("0")){
                                String key = j.getJSONObject("glAccount").getStr("code");
                                if(mapCodeBd.containsKey(key)){
                                    mapCodeBd.get(key).add(j.getBigDecimal("basePostedDr"));
                                }else {
                                    mapCodeBd.put(key, j.getBigDecimal("basePostedDr"));
                                }
                            }
                        }
                    }
                    BigDecimal allMoney = new BigDecimal(money);
                    for(int i = moneyJson.size()-1; i > 0; i=i-2) {
                        BigDecimal c1 =  moneyJson.getJSONObject(i).getBigDecimal("credit",BigDecimal.ZERO);
                        BigDecimal d1 = moneyJson.getJSONObject(i).getBigDecimal("debit",BigDecimal.ZERO);

//                            BigDecimal d2 =  moneyJson.getJSONObject(i-1).getBigDecimal("debit");
//                            BigDecimal c2 = moneyJson.getJSONObject(i-1).getBigDecimal("credit");
                        BigDecimal cd = c1.add(d1);
                        // kemu
                        String keymu = moneyJson.getJSONObject(i-1).getStr("subject");
                        if(mapCodeBd.containsKey(keymu)){
                            BigDecimal kemuMoney = mapCodeBd.get(keymu);
                            // 是否大于0
                            if(kemuMoney.compareTo(BigDecimal.ZERO) > 0){
                                // 科目金额 减去 已付款
                                if(kemuMoney.subtract(cd).compareTo(BigDecimal.ZERO) <= 0){
                                    cd = cd.subtract(kemuMoney);
                                    kemuMoney = BigDecimal.ZERO;
                                    mapCodeBd.put(keymu,kemuMoney);
                                }else{
                                    kemuMoney = kemuMoney.subtract(cd);
                                    cd = BigDecimal.ZERO;
                                    mapCodeBd.put(keymu,kemuMoney);
                                }
                                if(!moneyJson.getJSONObject(i).getBigDecimal("debit",BigDecimal.ZERO).equals(BigDecimal.ZERO)){
                                    moneyJson.getJSONObject(i).set("debit",cd);
                                }
                                if(!moneyJson.getJSONObject(i).getBigDecimal("credit",BigDecimal.ZERO).equals(BigDecimal.ZERO)){
                                    moneyJson.getJSONObject(i).set("credit",cd);
                                }
                                if(!moneyJson.getJSONObject(i-1).getBigDecimal("debit",BigDecimal.ZERO).equals(BigDecimal.ZERO)){
                                    moneyJson.getJSONObject(i-1).set("debit",cd);
                                }
                                if(!moneyJson.getJSONObject(i-1).getBigDecimal("credit",BigDecimal.ZERO).equals(BigDecimal.ZERO)){
                                    moneyJson.getJSONObject(i-1).set("credit",cd);
                                }
                            }
                        }

                        if(allMoney.subtract(cd).compareTo(BigDecimal.ZERO) <= 0){
                            if(!moneyJson.getJSONObject(i).getBigDecimal("debit",BigDecimal.ZERO).equals(BigDecimal.ZERO)){
                                moneyJson.getJSONObject(i).set("debit",allMoney);
                            }
                            if(!moneyJson.getJSONObject(i).getBigDecimal("credit",BigDecimal.ZERO).equals(BigDecimal.ZERO)){
                                moneyJson.getJSONObject(i).set("credit",allMoney);
                            }
                            if(!moneyJson.getJSONObject(i-1).getBigDecimal("debit",BigDecimal.ZERO).equals(BigDecimal.ZERO)){
                                moneyJson.getJSONObject(i-1).set("debit",allMoney);
                            }
                            if(!moneyJson.getJSONObject(i-1).getBigDecimal("credit",BigDecimal.ZERO).equals(BigDecimal.ZERO)){
                                moneyJson.getJSONObject(i-1).set("credit",allMoney);
                            }
                            allMoney = allMoney.subtract(allMoney);
                        }else{
                            allMoney = allMoney.subtract(cd);
                        }
                    }
                }
               /* JSONObject jsonObject = new JSONObject();
                jsonObject.put("data",moneyJson);
                jsonObject.put("code",tmplCode);
                if(StrUtil.isBlank(companyId)){
                    companyId = fromValue.getStr("company");
                }
                jsonObject.put("company",companyId);
                // 根据公司返回 对应帐套
                JSONArray account = getAccountByCompany(companyId);
                jsonObject.put("accounts",account);*/
                return jsonVoucher(moneyJson,tmplCode,companyId,fromValue,applys);
            }
//            if("receivableShipVouchers_pzd".equals(tmplCode)){
//                // 船舶航次凭证
////                JSONArray moneyJson =
//                JSONArray moneyJson = moneyByShipVoyage(fromValue,list);
//
//                return jsonVoucher(moneyJson,tmplCode,companyId,fromValue,applys);
//            }

//                if(tmplCode.equals(TRAVEL_PAYING) || tmplCode.equals(TRAVEL_PAYEND)){
            if("receivableVouchers_pzdyf".equals(tmplCode)){
//                fromValue
                // list
                // 对应关系
//                收入
//                    3种情况 负数、整数gaizhangtype != 1 && gaizhangtype != 0，整数gaizhangtype == 1，负数gaizhangtype == 0
//                gaizhangtype != 1 && gaizhangtype != 0 对应关系 old,now
                // 整数gaizhangtype == 1 对应关系 now
                // 负数gaizhangtype == 0 对应关系 now

//                支出
                JSONArray moneyJson = moneyByUpdateStowage(fromValue,list);
                log.info("397 json:{},result:{}",moneyJson,jsonVoucher(moneyJson,tmplCode,companyId,fromValue,applys));
                return jsonVoucher(moneyJson,tmplCode,companyId,fromValue,applys);
           }
            // 读取报销
            if(StrUtil.isNotBlank(tmplCode) && list!=null && list.size()>0){
                JSONArray moneyJson = moneyByTravelArray(fromValue,list);
                if(moneyJson==null){
                    moneyJson = new JSONArray();
                }
//                    if( tmplCode.equals(TRAVEL_PAYEND) && StrUtil.isNotBlank(money)){
                if(tmplCode.endsWith(PAYEND) && StrUtil.isNotBlank(money)){
                    BigDecimal debitMoney = new BigDecimal(money);
                    BigDecimal creditMoney = new BigDecimal(money);
                    for(int i=0;i<moneyJson.size();i++){
                        JSONObject j = moneyJson.getJSONObject(i);
                        if(j.getBigDecimal("debit")!=null &&  j.getBigDecimal("debit").compareTo(BigDecimal.ZERO)>0 ){
                            if(j.getBigDecimal("debit").compareTo(debitMoney)>0){
//                                j.set("debit",new BigDecimal(money));
                                if(debitMoney.compareTo(BigDecimal.ZERO)>0){
                                    j.set("debit",debitMoney);
                                    debitMoney = debitMoney.subtract(debitMoney);
                                }else{
                                    j.set("debit",null);
                                }
                            }else{
                                debitMoney = debitMoney.subtract(j.getBigDecimal("debit"));
                            }
                        }
                        if(j.getBigDecimal("credit") !=null && j.getBigDecimal("credit").compareTo(BigDecimal.ZERO)>0 ){
                            // 1000 =  100,500,400
                            // 200 = 100,500,400
                            // 500 >100
                            if(j.getBigDecimal("credit").compareTo(creditMoney)>0){
                                if(creditMoney.compareTo(BigDecimal.ZERO)>0){
                                    j.set("credit",creditMoney);
                                    creditMoney = creditMoney.subtract(creditMoney);
                                }else{
                                    j.set("credit",null);
                                }

                            }else{
                                creditMoney = creditMoney.subtract(j.getBigDecimal("credit"));
                                // 200 -100=100
                            }

                        }
                    }
                }
//                if(StrUtil.isBlank(companyId)){
//                    companyId = fromValue.getStr("company");
//                }
             /*   JSONObject jsonObject = new JSONObject();
                jsonObject.put("data",moneyJson);
                jsonObject.put("code",tmplCode);
                jsonObject.put("company",companyId);
                // 根据公司返回 对应帐套
                JSONArray account = getAccountByCompany(companyId);
                jsonObject.put("accounts",account);
                return jsonObject;*/
                log.info("457 json:{},reslut:{}",moneyJson,jsonVoucher(moneyJson,tmplCode,companyId,fromValue,applys));
                return jsonVoucher(moneyJson,tmplCode,companyId,fromValue,applys);
            }

        }
        log.info("458 null");
        return null;
    }
    // 对应关系
    //const oldTypeIn = {
    //  'ShipCostIn':{
    //    'type':'costProjectold',
    //    'name':'sysSupplierIdold',
    //    'price':'costPriceNoold',
    //    'tax':'taxold'
    //  },
    //  'GoodsCost':{
    //    'type':'运费',
    //    'name':'seckehu',
    //    'price':'tonnageold*freightNoold',
    //    'tax':'taxFreightold'
    //  },
    //  'GoodsCostDaiLi':{
    //    'type':'代理费',
    //    'name':'seckehu',
    //    'price':'tonnageold*dailiNoold',
    //    'tax':'taxDailiold'
    //  },
    //  'CostDetailIn':{
    //    'name':'companyName',
    //    'type':'costNameold',
    //    'price':'tonnageold*priceNoold',
    //    'tax':'invoiceTypeold'
    //  }
    //}
    //const oldTypeOut = {
    //  'ShipCostOut':{ // costProjectta == '承担方 暂不 显示此项'
    //    'type':'costProjectold',
    //    'name':'sysSupplierIdold',
    //    'price':'costPriceNoold',
    //    'tax':'taxold'
    //  },
    //  'ShipPay':{
    //    'type':'运费',
    //    'name':'costProjectC',
    //    'price':'settleTonnage*shipPayold',
    //    'tax':'payBillold'
    //  },
    //  'CostDetail':{
    //    'type':'costNameold',
    //    'name':'companyNameold',
    //    'price':'tonnageold*priceNoold',
    //    'tax':'invoiceTypeold'
    //  },
    //}
    //const nTypeIn = {
    //  'ShipCostIn':{
    //    'type':'costProject',
    //    'name':'sysSupplierId',
    //    'price':'costPriceNo',
    //    'tax':'tax'
    //  },
    //  'GoodsCost':{
    //    'type':'运费',
    //    'name':'seckehu',
    //    'price':'tonnage*freightNo',
    //    'tax':'taxFreight'
    //  },
    //  'GoodsCostDaiLi':{
    //    'type':'代理费',
    //    'name':'seckehu',
    //    'price':'tonnage*dailiNo',
    //    'tax':'taxDaili'
    //  },
    //  'CostDetailIn':{
    //    'name':'companyName',
    //    'type':'costName',
    //    'price':'tonnage*priceNo',
    //    'tax':'invoiceType'
    //  }
    //}
    //const nTypeOut = {
    //  'ShipCostOut':{ // costProjectta == '承担方 暂不 显示此项'
    //    'type':'costProject',
    //    'name':'sysSupplierId',
    //    'price':'costPriceNo',
    //    'tax':'tax'
    //  },
    //  'ShipPay':{
    //    'type':'运费',
    //    'name':'costProjectC',
    //    'price':'settleTonnage*shipPay',
    //    'tax':'payBill'
    //  },
    //  'CostDetail':{
    //    'type':'costName',
    //    'name':'companyName',
    //    'price':'tonnage*priceNo',
    //    'tax':'invoiceType'
    //  },
    //}
    private List<String> getTypesByStowageTypeIn(){
        // 费用类型
        List<String> list = new ArrayList<>();
        list.add("ShipCostIn");
        list.add("GoodsCost");
        list.add("CostDetailIn");
        return list;
    }
    private List<String> getTypesByStowageTypeOut(){
        // 费用类型
        List<String> list = new ArrayList<>();
        list.add("ShipPay");
        list.add("ShipCostOut");
        list.add("CostDetail");
        return list;
    }
    private List<JSONObject> getTypeDetailByTypeOld(String type){
        return getTypeByStrAndType(type,"old");
    }

    private String taxPriceGs(String priceField,String taxField){
        return taxField+" ==0 ? 0 :"+priceField+"*"+taxField+"/(1+"+taxField+")";
    }
    private String noTaxPriceGs(String priceField,String taxField){
        return taxField+" ==0 ? "+priceField+":"+priceField+"/(1+"+taxField+")";
    }
//    List<SysDictionary> dict = sysDictionaryClient.getDictionaryList("bill_tax");
    private List<JSONObject> typeShipCostIn(String model){
        List<JSONObject> list = new ArrayList<>();
        String priceField = "costPriceNo"+model;
        String taxField = "tax"+model;
        String taxPrice = taxPriceGs(priceField,taxField);
        String priceNo = noTaxPriceGs(priceField,taxField);
        String dataType = "SysSupplier";

//        税额=1000元×13%÷(1+13%)
        // 不含税金额 = 1000元÷(1+13%)

        JSONObject jo = new JSONObject();
        jo.set("type","costProject"+model);
        jo.set("name","sysSupplierId"+model);
        jo.set("dataType",dataType);
        jo.set("price",priceField);
        jo.set("tax",taxField);
        jo.set("subject","1122");
        jo.set("model","in");
        jo.set("borrowing",1);
        jo.set("modelName","船上费用收入");
        jo.set("querySubject","应收账款_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type","costProject"+model);
        jo.set("name","sysSupplierId"+model);
        jo.set("dataType",dataType);
//            jo.set("price","costPriceNo");
        jo.set("price",priceNo);
        jo.set("tax",taxField);
        jo.set("subject","50010014");
        jo.set("model","in");
        jo.set("borrowing",-1);
        jo.set("modelName","船上费用收入");
        jo.set("querySubject","主营业务收入_改帐_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type","costProject"+model);
        jo.set("name","sysSupplierId"+model);
        jo.set("dataType",dataType);
        jo.set("price",taxPrice);
        jo.set("tax",taxField);
        jo.set("subject","222100010007");
        jo.set("model","in");
        jo.set("borrowing",-1);
        jo.set("modelName","船上费用收入");
        jo.set("querySubject","应交税费_应交增值税_待开票销项税额");
        list.add(jo);
        return list;
    }
    private List<JSONObject> typeGoodsCost(String model){
        List<JSONObject> list = new ArrayList<>();
        JSONObject jo = new JSONObject();
        String priceField = "tonnage"+model+"*freightNo"+model;
        String taxField = "taxFreight"+model;
//        String taxPrice = taxField+"==0 ? 0 :"+priceField+"*"+taxField+"/(1+"+taxField+")";
//        String priceNo = taxField+"==0 ? "+priceField+":"+priceField+"/(1+"+taxField+")";
        String taxPrice = taxPriceGs(priceField,taxField);
        String priceNo = noTaxPriceGs(priceField,taxField);

        jo.set("type","运费");
        jo.set("name","seckehu");
        jo.set("price",priceNo);
        jo.set("tax",taxField);
        jo.set("subject","1122");
        jo.set("model","in");
        jo.set("borrowing",1);
        jo.set("modelName","运费收入");
        jo.set("querySubject","应收账款_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type","运费");
        jo.set("name","seckehu");
        jo.set("price",taxPrice);
        jo.set("tax",taxField);
        jo.set("model","in");
        jo.set("subject","50010014");
        jo.set("borrowing",-1);
        jo.set("modelName","运费收入");
        jo.set("querySubject","主营业务收入_改帐_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type","运费");
        jo.set("name","seckehu");
        jo.set("price",priceField);
        jo.set("tax",taxField);
        jo.set("subject","222100010007");
        jo.set("model","in");
        jo.set("borrowing",-1);
        jo.set("modelName","运费收入");
        jo.set("querySubject","应交税费_应交增值税_待开票销项税额");
        list.add(jo);

        String priceFieldDaili = "tonnage"+model+"*dailiNo"+model;
        String taxFieldDaili = "taxDaili"+model;
//        String taxPriceDaili = taxFieldDaili+"==0 ? 0 :"+priceFieldDaili+"*"+taxFieldDaili+"/(1+"+taxFieldDaili+")";
//        String priceNoDaili = taxFieldDaili+"==0 ? "+priceFieldDaili+":"+priceFieldDaili+"/(1+"+taxFieldDaili+")";
        String taxPriceDaili = taxPriceGs(priceFieldDaili,taxFieldDaili);
        String priceNoDaili = noTaxPriceGs(priceFieldDaili,taxFieldDaili);

        jo = new JSONObject();
        jo.set("type","代理费");
        jo.set("name","seckehu");
        jo.set("price",priceFieldDaili);
        jo.set("tax",taxFieldDaili);
        jo.set("subject","1122");
        jo.set("model","in");
        jo.set("borrowing",1);
        jo.set("modelName","代理费收入");
        jo.set("querySubject","应收账款_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type","代理费");
        jo.set("name","seckehu");
        jo.set("price",priceNoDaili);
        jo.set("tax",taxFieldDaili);
        jo.set("subject","50010014");
        jo.set("model","in");
        jo.set("borrowing",-1);
        jo.set("modelName","代理费收入");
        jo.set("querySubject","主营业务收入_改帐_");
        list.add(jo);
        jo = new JSONObject();
        jo.set("type","代理费");
        jo.set("name","seckehu");
        jo.set("price",taxPriceDaili);
        jo.set("tax",taxFieldDaili);
        jo.set("subject","222100010007");
        jo.set("model","in");
        jo.set("borrowing",-1);
        jo.set("modelName","代理费收入");
        jo.set("querySubject","应交税费_应交增值税_待开票销项税额");
        list.add(jo);

        return list;
    }

    private List<JSONObject> typeCostDetailIn(String model){
        List<JSONObject> list = new ArrayList<>();
        JSONObject jo = new JSONObject();
        String type = "costName"+model;
        String priceField = "tonnage"+model+"*priceNo"+model;
        String taxField = "invoiceType"+model;
//        String taxPrice = taxField+"==0 ? 0 :"+priceField+"*"+taxField+"/(1+"+taxField+")";
//        String priceNo = taxField+"==0 ? "+priceField+":"+priceField+"/(1+"+taxField+")";
        String taxPrice = taxPriceGs(priceField,taxField);
        String priceNo = noTaxPriceGs(priceField,taxField);

        jo.set("type",type);
        jo.set("name","companyName");
        jo.set("price",priceField);
        jo.set("tax",taxField);
        jo.set("subject","1122");
        jo.set("model","in");
        jo.set("borrowing",1);
        jo.set("modelName","货物费用收入");
        jo.set("querySubject","应收账款_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type",type);
        jo.set("name","companyName");
        jo.set("price",priceNo);
        jo.set("tax",taxField);
        jo.set("subject","50010014");
        jo.set("model","in");
        jo.set("borrowing",1);
        jo.set("modelName","货物费用收入");
        jo.set("querySubject","主营业务收入_改帐_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type",type);
        jo.set("name","companyName");
        jo.set("price",taxPrice);
        jo.set("tax",taxField);
        jo.set("subject","222100010007");
        jo.set("model","in");
        jo.set("borrowing",-1);
        jo.set("modelName","货物费用收入");
        jo.set("querySubject","应交税费_应交增值税_待开票销项税额");
        list.add(jo);
        return list;
    }

    private List<JSONObject> typeShipCostOut(String model){
        List<JSONObject> list = new ArrayList<>();
        JSONObject jo = new JSONObject();
        String type = "costProject"+model;
        String name = "sysSupplierId"+model;
        String priceField = "costPriceNo"+model;
        String taxField = "tax"+model;
        String dataType = "SysSupplier";
//        String taxPrice = taxField+"==0 ? 0 :"+priceField+"*"+taxField+"/(1+"+taxField+")";
//        String priceNo = taxField+"==0 ? "+priceField+":"+ priceField+"/(1+"+taxField+")";
        String taxPrice = taxPriceGs(priceField,taxField);
        String priceNo = noTaxPriceGs(priceField,taxField);

        jo.set("type",type);
        jo.set("name",name);
        jo.set("dataType",dataType);
        jo.set("price",priceNo);
        jo.set("tax",taxField);

        jo.set("subject","54010018");
        jo.set("model","out");
        jo.set("borrowing",1);
        jo.set("modelName","船上费用支出");
        jo.set("querySubject","主营业务成本_改帐_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type",type);
        jo.set("name",name);
        jo.set("dataType",dataType);
        jo.set("price",taxPrice);
        jo.set("tax",taxField);
        jo.set("subject","22210001");
        jo.set("model","out");
        jo.set("borrowing",1);
        jo.set("modelName","船上费用支出");
        jo.set("querySubject","应交税费_应交增值税_待抵扣进项税");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type",type);
        jo.set("name",name);
        jo.set("dataType",dataType);
        jo.set("price",priceField);
        jo.set("tax",taxField);
        jo.set("subject","2202");
        jo.set("model","out");
        jo.set("borrowing",-1);
        jo.set("modelName","船上费用支出");
        jo.set("querySubject","应付账款_");
        list.add(jo);
        return list;
    }

    private List<JSONObject> typeShipPay(String model){
        List<JSONObject> list = new ArrayList<>();
        JSONObject jo = new JSONObject();
        String shipPay = "shipPay"+model;
        String settleTonnage = "settleTonnage";
        String taxField = "payBill"+model;

        String priceField = settleTonnage+"*"+shipPay;;
//        String taxPrice = taxField+"==0 ? 0 :"+priceField+"*"+taxField+"/(1+"+taxField+")";
//        String priceNo = taxField+"==0 ? "+priceField+":"+priceField+"/(1+"+taxField+")";
        String taxPrice = taxPriceGs(priceField,taxField);
        String priceNo = noTaxPriceGs(priceField,taxField);

        jo.set("type","运费");
        jo.set("name","company");
        jo.set("price",priceNo);
        jo.set("tax",taxField);
        jo.set("subject","54010018");
        jo.set("model","out");
        jo.set("borrowing",1);
        jo.set("modelName","船运费");
        jo.set("querySubject","主营业务成本_改帐_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type","运费");
        jo.set("name","company");
        jo.set("price",taxPrice);
        jo.set("tax",taxField);
        jo.set("subject","22210001");
        jo.set("model","out");
        jo.set("borrowing",1);
        jo.set("modelName","船运费");
        jo.set("querySubject","应交税费_应交增值税_待抵扣进项税");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type","运费");
        jo.set("name","company");
        jo.set("price",priceField);
        jo.set("tax",taxField);
        jo.set("subject","2202");
        jo.set("model","out");
        jo.set("borrowing",-1);
        jo.set("modelName","船运费");
        jo.set("querySubject","应付账款_");
        list.add(jo);
        return list;
    }
    private List<JSONObject> typeCostDetail(String model){
        List<JSONObject> list = new ArrayList<>();
        JSONObject jo = new JSONObject();
        String type = "costName"+model;
        String name = "companyName"+model;
        String priceField = "tonnage"+model+"*priceNo"+model;
        String taxField = "invoiceType"+model;
//        String taxPrice = taxField+"==0 ? 0 :"+priceField+"*"+taxField+"/(1+"+taxField+")";
//        String priceNo = taxField+"==0 ? "+priceField+":"+priceField+"/(1+"+taxField+")";
        String taxPrice = taxPriceGs(priceField,taxField);
        String priceNo = noTaxPriceGs(priceField,taxField);

        jo.set("type",type);
        jo.set("name",name);
        jo.set("price",priceNo);
        jo.set("tax",taxField);
        jo.set("subject","54010018");
        jo.set("model","out");
        jo.set("borrowing",1);
        jo.set("modelName","货物费用支出");
        jo.set("querySubject","主营业务成本_改帐_");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type",type);
        jo.set("name",name);
        jo.set("price",taxPrice);
        jo.set("tax",taxField);
        jo.set("subject","22210001");
        jo.set("model","out");
        jo.set("borrowing",1);
        jo.set("modelName","货物费用支出");
        jo.set("querySubject","应交税费_应交增值税_待抵扣进项税");
        list.add(jo);

        jo = new JSONObject();
        jo.set("type",type);
        jo.set("name",name);
        jo.set("price",priceField);
        jo.set("tax",taxField);
        jo.set("subject","2202");
        jo.set("model","out");
        jo.set("borrowing",-1);
        jo.set("modelName","货物费用支出");
        jo.set("querySubject","应付账款_");
        list.add(jo);
        return list;
    }
    private List<JSONObject> getTypeByStrAndType(String type,String model){
        List<JSONObject> list = null;
        switch (type){
            case "ShipCostIn":
                list = typeShipCostIn(model);
                break;
            case "GoodsCost":
                list = typeGoodsCost(model);
                break;
            case "CostDetailIn":
                list = typeCostDetailIn(model);
                break;
            case "ShipCostOut":
                list = typeShipCostOut(model);
                break;
            case "ShipPay":
                list = typeShipPay(model);
                break;
            case "CostDetail":
                list = typeCostDetail(model);
                break;
            default:
                break;
        }
        return list;
     /*   List<JSONObject> list = new ArrayList<>();
        JSONObject jo = new JSONObject();
        if("ShipCostIn".equals(type)){
            jo.set("type","costProject");
            jo.set("name","sysSupplierId");
            jo.set("price","costPrice");
            jo.set("tax","tax");
            jo.set("model","in");
            jo.set("borrowing",1);
            jo.set("modelName","船上费用收入");
            jo.set("querySubject","应收账款_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","costProject");
            jo.set("name","sysSupplierId");
//            jo.set("price","costPriceNo");
            jo.set("price","costPriceNo");
            jo.set("tax","tax");
            jo.set("model","in");
            jo.set("borrowing",-1);
            jo.set("modelName","船上费用收入");
            jo.set("querySubject","主营业务收入_改帐_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","costProject");
            jo.set("name","sysSupplierId");
            jo.set("price","costPrice-costPriceNo");
            jo.set("tax","tax");
            jo.set("model","in");
            jo.set("borrowing",-1);
            jo.set("modelName","船上费用收入");
            jo.set("querySubject","应交税费_应交增值税_待开票销项税额");
            list.add(jo);
        }
        if("GoodsCost".equals(type)){
            jo.set("type","运费");
            jo.set("name","seckehu");
            jo.set("price","tonnage*freightNo");
            jo.set("tax","taxFreight");
            jo.set("model","in");
            jo.set("borrowing",1);
            jo.set("modelName","运费收入");
            jo.set("querySubject","应收账款_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","运费");
            jo.set("name","seckehu");
            jo.set("price","tonnage*freight");
            jo.set("tax","taxFreight");
            jo.set("model","in");
            jo.set("borrowing",-1);
            jo.set("modelName","运费收入");
            jo.set("querySubject","主营业务收入_改帐_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","运费");
            jo.set("name","seckehu");
            jo.set("price","tonnage*freightNo-tonnage*freight");
            jo.set("tax","taxFreight");
            jo.set("model","in");
            jo.set("borrowing",-1);
            jo.set("modelName","运费收入");
            jo.set("querySubject","应交税费_应交增值税_待开票销项税额");
            list.add(jo);



            jo = new JSONObject();
            jo.set("type","代理费");
            jo.set("name","seckehu");
            jo.set("price","tonnage*dailiNo");
            jo.set("tax","taxDaili");
            jo.set("model","in");
            jo.set("borrowing",1);
            jo.set("modelName","代理费收入");
            jo.set("querySubject","应收账款_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","代理费");
            jo.set("name","seckehu");
            jo.set("price","tonnage*daili");
            jo.set("tax","taxDaili");
            jo.set("model","in");
            jo.set("borrowing",-1);
            jo.set("modelName","代理费收入");
            jo.set("querySubject","主营业务收入_改帐_");
            list.add(jo);
            jo = new JSONObject();
            jo.set("type","代理费");
            jo.set("name","seckehu");
            jo.set("price","tonnage*dailiNo-tonnage*dailiNo");
            jo.set("tax","taxDaili");
            jo.set("model","in");
            jo.set("borrowing",-1);
            jo.set("modelName","代理费收入");
            jo.set("querySubject","应交税费_应交增值税_待开票销项税额");
            list.add(jo);
        }
        if("CostDetailIn".equals(type)){
            jo.set("name","companyName");
            jo.set("type","costName");
            jo.set("price","tonnage*priceNo");
            jo.set("tax","invoiceType");
            jo.set("model","in");
            jo.set("borrowing",1);
            jo.set("modelName","货物费用收入");
            jo.set("querySubject","应收账款_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("name","companyName");
            jo.set("type","costName");
            jo.set("price","tonnage*priceNo");
            jo.set("tax","invoiceType");
            jo.set("model","in");
            jo.set("borrowing",-1);
            jo.set("modelName","货物费用收入");
            jo.set("querySubject","主营业务收入_改帐_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("name","companyName");
            jo.set("type","costName");
            jo.set("price","tonnage*priceNo");
            jo.set("tax","invoiceType");
            jo.set("model","in");
            jo.set("borrowing",-1);
            jo.set("modelName","货物费用收入");
            jo.set("querySubject","应交税费_应交增值税_待开票销项税额");
            list.add(jo);
        }
        if("ShipCostOut".equals(type)){
            jo.set("type","costProject");
            jo.set("name","sysSupplierId");
            jo.set("price","costPriceNo");
            jo.set("tax","tax");
            jo.set("model","out");
            jo.set("borrowing",1);
            jo.set("modelName","船上费用");
            jo.set("querySubject","主营业务成本_改帐_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","costProject");
            jo.set("name","sysSupplierId");
            jo.set("price","costPriceNo");
            jo.set("tax","tax");
            jo.set("model","out");
            jo.set("borrowing",1);
            jo.set("modelName","船上费用");
            jo.set("querySubject","应交税费_应交增值税_待抵扣进项税");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","costProject");
            jo.set("name","sysSupplierId");
            jo.set("price","costPriceNo");
            jo.set("tax","tax");
            jo.set("model","out");
            jo.set("borrowing",-1);
            jo.set("modelName","船上费用");
            jo.set("querySubject","应付账款_");
            list.add(jo);


        }
        if("ShipPay".equals(type)){

        }
        if("CostDetail".equals(type)){
            jo.set("type","costName");
            jo.set("name","companyName");
            jo.set("price","tonnage*priceNo");
            jo.set("tax","invoiceType");
            jo.set("model","out");
            jo.set("borrowing",1);
            jo.set("modelName","货物费用");
            jo.set("querySubject","主营业务成本_改帐_");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","costName");
            jo.set("name","companyName");
            jo.set("price","tonnage*priceNo");
            jo.set("tax","invoiceType");
            jo.set("model","out");
            jo.set("borrowing",1);
            jo.set("modelName","货物费用");
            jo.set("querySubject","应交税费_应交增值税_待抵扣进项税");
            list.add(jo);

            jo = new JSONObject();
            jo.set("type","costName");
            jo.set("name","companyName");
            jo.set("price","tonnage*priceNo");
            jo.set("tax","invoiceType");
            jo.set("model","out");
            jo.set("borrowing",-1);
            jo.set("modelName","货物费用");
            jo.set("querySubject","应付账款_");
            list.add(jo);
        }
        return list;*/
    }

    private List<JSONObject> getTypeDetailByType(String type){
        return getTypeByStrAndType(type,"");
    }
    private void typeListByGai(List<JSONObject> types,JSONObject json,int d,List<SysDictionary> dict,List<SysDictionary> companyDict,String onAccountNum,String shipName,JSONArray moneyJson,List<Map<String,Object>> sysSupplierList){
        // 克隆
//        JSONObject j = (JSONObject)json.clone(); // 不能直接修改json
        JSONObject j = new JSONObject();
        for(String key:json.keySet()){
            j.set(key,json.get(key));
        }
        for(JSONObject typeJo:types){
            JSONObject jo = new JSONObject();
            String model = typeJo.getStr("model");
            String tax = typeJo.getStr("tax");
            String taxVal = j.getStr(tax);
            Double taxValue = new Double("0");
            if(taxVal!=null){
                for(SysDictionary sysDictionary:dict){
                    if(ObjectUtil.equal(taxVal,sysDictionary.getCode().toString())){
                        taxVal = sysDictionary.getValue();
                        break;
                    }
                }
                if(taxVal.equals("不开")){
                    taxVal = "0";
                }
                if(taxVal.contains("%")){
                    taxVal = taxVal.replace("%","");
                    taxValue = NumberUtil.div(Double.parseDouble(taxVal),100);
                }else{
                    taxValue = Double.parseDouble(taxVal);
                }

            }
            j.set(tax,taxValue);


            String findSub = j.containsKey(typeJo.getStr("name"))? j.getStr(typeJo.getStr("name")):typeJo.getStr("name");
            if("SysSupplier".equals(typeJo.getStr("dataType"))){
                boolean isBoo = false;
                for(Map<String,Object> sysSupplier:sysSupplierList){
                    if(ObjectUtil.equal(findSub,sysSupplier.get("id").toString())){
                        findSub = sysSupplier.get("name").toString();
                        isBoo = true;
                        break;
                    }
                }
                if(!isBoo){
                    findSub="";
                }
            }else{
                if(StrUtil.isNotBlank(findSub)){
                    for(SysDictionary sysDictionary:companyDict){
                        if(ObjectUtil.equal(findSub,sysDictionary.getCode().toString())){
                            findSub = sysDictionary.getValue();
                            break;
                        }
                    }
                }
            }


            Object money = null;
            try {
                Map<String,Object> mj = new HashMap<>();
                for(String key:j.keySet()){
                    mj.put(key,NumberUtil.isNumber(j.get(key).toString())?j.getDouble(key):j.get(key));
                }
                 money = AviatorEvaluator.execute(typeJo.getStr("price"), mj);
            }catch (Exception e){
                log.error(e.getMessage());
            }
            if(money==null || money.toString().equals("") ){
                continue;
            }
            // || NumberUtil.parseNumber(money.toString())==0
            Number number = NumberUtil.parseNumber(money.toString());
            if(number==null || number.doubleValue()==0){
                continue;
            }
            BigDecimal m = new BigDecimal(money.toString()).multiply(new BigDecimal(d));
            String summary = onAccountNum+"-"+shipName+"改帐-"+typeJo.getStr("modelName")+typeJo.getStr("querySubject")+findSub;
            jo.set("summary",summary);
            jo.set("subject",typeJo.getStr("subject","")); // 会计科目id
            jo.set("findSub",findSub);

            jo.set("debit",typeJo.getInt("borrowing")==1?m : "0");
            jo.set("credit",typeJo.getInt("borrowing")==-1?m : "0");
            jo.set("money",m);


//                jo.set("tax",j.getStr(typeJo.getStr("tax")));
            jo.set("querySubject",typeJo.getStr("querySubject"));

            moneyJson.add(jo);


        }
    }
    private JSONArray getTypeInfoByStowageType(JSONArray rows,String type,String onAccountNum,String shipName){
        if(rows==null || rows.size()==0){
            return null;
        }
        List<JSONObject> types = getTypeDetailByType(type);
        List<SysDictionary> dict = sysDictionaryClient.getDictionaryList("bill_tax");
        List<SysDictionary> companyDict = sysDictionaryClient.getDictionaryList("contract_company");
        Map<String,Object> onAcc = onAccountClient.getAllSysSupplier();
        List<Map<String,Object>> sysList = (List<Map<String,Object>>) onAcc.get("list");

        JSONArray moneyJson = new JSONArray();
        for(int i=0;i<rows.size();i++){
            JSONObject j = rows.getJSONObject(i);
            // 三种情况 gaizhangtype == 1 ==0
            String gaizhangtype = j.getStr("gaizhangtype","");
            int d = 1;
            if("1".equals(gaizhangtype)){

            }else if("0".equals(gaizhangtype)){
                d = -1;
            }else{
                List<JSONObject> typeOlds = getTypeDetailByTypeOld(type);
                d = -1;
                typeListByGai(typeOlds,j,d,dict,companyDict,onAccountNum,shipName,moneyJson,sysList);
                d = 1;
            }
//            修改
//                旧 负数 新
//            新增
//                新
//            删除
//                新 负数
            typeListByGai(types,j,d,dict,companyDict,onAccountNum,shipName,moneyJson,sysList);
            /*for(JSONObject typeJo:types){
                JSONObject jo = new JSONObject();
                String model = typeJo.getStr("model");
                String tax = typeJo.getStr("tax");
                String taxVal = j.getStr(tax);
                Double taxValue = new Double("0");
                if(taxVal!=null){
                    for(SysDictionary sysDictionary:dict){
                        if(ObjectUtil.equal(taxVal,sysDictionary.getCode().toString())){
                            taxVal = sysDictionary.getValue();
                            break;
                        }
                    }
                    if(taxVal.equals("不开")){
                        taxVal = "";
                    }
                    if(taxVal.contains("%")){
                        taxVal = taxVal.replace("%","");
                        taxValue = NumberUtil.div(Double.parseDouble(taxVal),100);
                    }
                    j.set(tax,taxValue);
                }


                String findSub = j.containsKey(typeJo.getStr("name"))? j.getStr(typeJo.getStr("name")):typeJo.getStr("name");
                if(StrUtil.isNotBlank(findSub)){
                    for(SysDictionary sysDictionary:companyDict){
                        if(ObjectUtil.equal(findSub,sysDictionary.getCode().toString())){
                            findSub = sysDictionary.getValue();
                            break;
                        }
                    }
                }
                Object money = AviatorEvaluator.execute(typeJo.getStr("price"),j);
                if(money==null || money.toString().equals("") ){
                    continue;
                }
                // || NumberUtil.parseNumber(money.toString())==0
                Number number = NumberUtil.parseNumber(money.toString());
                if(number==null || number.doubleValue()==0){
                    continue;
                }
                String summary = onAccountNum+"-"+shipName+"改帐"+typeJo.getStr("modelName")+typeJo.getStr("querySubject")+findSub;
                jo.set("summary",summary);
                jo.set("subject",""); // 会计科目id
                jo.set("findSub",findSub);

                jo.set("debit",typeJo.getInt("borrowing")==1?money : "0");
                jo.set("credit",typeJo.getInt("borrowing")==-1?money : "0");
                jo.set("money",money);


//                jo.set("tax",j.getStr(typeJo.getStr("tax")));
                jo.set("querySubject",typeJo.getStr("querySubject"));

                moneyJson.add(jo);


            }*/
        }
        return moneyJson;
    }
    private JSONArray moneyByShipVoyage(JSONObject fromValue,List<CertTemplRelation> list){
        JSONArray moneyJson = new JSONArray();

        /*JSONObject jo = new JSONObject();
        // 模版名
        jo.set("summary", StrUtil.isNotBlank(certTemplRelation.getName())
                ? replaceTem(certTemplRelation.getName(),fromValue)
                :"");
        // 摘要
        jo.set("abstract", certTemplRelation.getTemplAbstract());
        // 科目
        jo.set("subject", certTemplRelation.getAccountingSubjects());
        // 子条件
        jo.set("findSub",StrUtil.isNotBlank(certTemplRelation.getFindSub())? j.getStr("findSub",fromValue.getStr(certTemplRelation.getFindSub())) :"");
        // 借
        jo.set("debit", certTemplRelation.getBorrowing()==1?j.getStr("money", fromValue.getStr(certTemplRelation.getFieldCode(),"0")):"0");
        // 贷
        jo.set("credit", certTemplRelation.getBorrowing()==1?"0":j.getStr("money", fromValue.getStr(certTemplRelation.getFieldCode(),"0")));
        // 金额
        jo.set("money",j.getStr("money", fromValue.getStr(certTemplRelation.getFieldCode(),"0")) );
        // 分组
        jo.set("groupCode", certTemplRelation.getGroupCode());
        // 对应科目
        jo.set("querySubject",certTemplRelation.getFieldSummary());
        moneyJson.add(jo);*/
        return moneyJson;
    }
    private JSONArray moneyByUpdateStowage(JSONObject fromValue,List<CertTemplRelation> list){
        // 对应关系
//                收入
//                    3种情况 负数、整数gaizhangtype != 1 && gaizhangtype != 0，整数gaizhangtype == 1，负数gaizhangtype == 0
//                gaizhangtype != 1 && gaizhangtype != 0 对应关系 old,now
        // 整数gaizhangtype == 1 对应关系 now
        // 负数gaizhangtype == 0 对应关系 now
        fromValue = fromValue.getJSONObject("data");
        JSONObject params = fromValue.getJSONObject("globalParams");
//                支出
        String shipName = params.getStr("params5");
        JSONObject shipDatas = params.getJSONObject("params2");
        // ShipLine
//        挂帐号
        String onAccountNum = shipDatas.getJSONObject("ShipLine").getStr("onAccountNum");
        JSONArray moneyJson = new JSONArray();
        // 收入
        List<String> inTypes = getTypesByStowageTypeIn();
        for(String type:inTypes){
            JSONArray ja = getTypeInfoByStowageType(shipDatas.getJSONArray(type),type,onAccountNum,shipName);
            if(ja!=null && ja.size()>0){
                moneyJson.addAll(ja);
            }
        }
        // 支出
       List<String> outTypes = getTypesByStowageTypeOut();
        for(String type:outTypes){
            JSONArray ja = getTypeInfoByStowageType(shipDatas.getJSONArray(type),type,onAccountNum,shipName);
            if(ja!=null && ja.size()>0){
                moneyJson.addAll(ja);
            }
        }
        return moneyJson;
    }
    private Integer getAccModel(){
        return sysUserService.getAccModel(SecurityUtils.getCurrentUserId());
    }

    private JSONObject jsonVoucher(JSONArray data,String code,String companyId,JSONObject fromValue,List<JSONObject> applys){
        if(applys!=null && applys.size()>0 && data!=null && data.size()>0){
            for(int i=0;i<applys.size();i++){
                JSONObject j = applys.get(i);
                // bankName,accountName,accountNumber,paymentMoney
                if("1002".equals(data.getJSONObject(data.size()-1).getStr("subject"))){
                    data.getJSONObject(data.size()-1).set("findSub",j.getStr("bankName",j.getStr("accountBank")));
                }
                if("1002".equals(data.getJSONObject(0).getStr("subject"))){
                    data.getJSONObject(0).set("findSub",j.getStr("bankName",j.getStr("accountBank")));
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("data",data);
        jsonObject.put("code",code);
        if(StrUtil.isBlank(companyId)){
            companyId = fromValue.getStr("company");
        }
        jsonObject.put("company",companyId);
        // 根据公司返回 对应帐套
        JSONArray account = getAccountByCompany(companyId);
        jsonObject.put("accounts",account);
        return jsonObject;
    }
    private JSONArray getAccountByCompany(String companyId){
        LambdaQueryWrapper<OpenTokenCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpenTokenCompany::getCompanyId, companyId);
        queryWrapper.eq(OpenTokenCompany::getAccModel, getAccModel());
        queryWrapper.eq(OpenTokenCompany::getDelFlag,0);
        queryWrapper.orderByAsc(OpenTokenCompany::getOrderNum);
        List<OpenTokenCompany> openTokenCompanies = openTokenCompanyMapper.selectList(queryWrapper);
        if(openTokenCompanies!=null && openTokenCompanies.size()>0){
            JSONArray jsonArray = new JSONArray();
            for(OpenTokenCompany openTokenCompany : openTokenCompanies){
                JSONObject jo = new JSONObject();
                jo.set("code",openTokenCompany.getAppCode());
                jo.set("name",openTokenCompany.getAppName());
                jsonArray.add(jo);
            }
            return jsonArray;
        }
        JSONArray jsonArray = new JSONArray();
//        JSONObject jo=new JSONObject();
//        jo.set("code",haokjAppCode);
//        jo.set("name","默认帐套");
//        jsonArray.add(jo);

        return jsonArray;
    }
    @Override
    public Map<String, Object> getVoucherList(String id, String type,String single,String money,List<com.alibaba.fastjson.JSONObject> applys) {
            JSONObject codeJson = codeByProcessId(id,type);
            if(codeJson==null){
                codeJson = new JSONObject();
                codeJson.set("code",type);
                codeJson.set("data",new JSONObject());
            }
            String tmplCode = codeJson.getStr("code",null);
            if(StrUtil.isBlank(tmplCode)){
                return null;
            }
            List<JSONObject> applyList = new ArrayList<>();
            if(applys!=null && applys.size()>0){
                for(com.alibaba.fastjson.JSONObject j : applys){
                  /*  JSONObject jo = new JSONObject();
                    jo.set("bankName",j.getString("bankName"));
                    jo.set("accountName",j.getString("accountName"));
                    jo.set("accountNumber",j.getString("accountNumber"));
                    jo.set("paymentMoney",j.getString("paymentMoney"));*/
                    JSONObject jo = JSONUtil.parseObj(j);
                    applyList.add(jo);
                }
            }
            return voucherListByCode(tmplCode,id,codeJson.getJSONObject("data"),single,money,null,null,applyList);
    }
    private static String buildExpression(String fields) {
        String[] fieldArray = fields.split("\\|");
        StringBuilder expressionBuilder = new StringBuilder();

        for (int i = 0; i < fieldArray.length; i++) {
            String field = fieldArray[i].trim();
            if (i > 0) {
                expressionBuilder.append(" : ");
            }
            expressionBuilder.append(field);
            if (i < fieldArray.length - 1) {
                expressionBuilder.append(" ? ").append(field);
            }
        }

        return expressionBuilder.toString();
    }

    private static List<JSONObject> pFieldCodeArray(JSONObject fromValue, String fieldCode,String findSub,List<JSONObject> fv){
//        final String[] finalFieldCode = {fieldCode};
//        List<JSONObject> fv = new ArrayList<>();
        if(fv==null){
            fv = new ArrayList<>();
        }
        if (fieldCode.contains(".")) {
            // 判断是否是JSONArray，还是JSONObject
            String[] split = fieldCode.split("\\.",2);
            if (fromValue.containsKey(split[0])) {
                Object o = fromValue.get(split[0]);
                if(o.getClass().equals(JSONArray.class)){
                    JSONArray ja = fromValue.getJSONArray(split[0]);
                    if (ja == null || ja.size() == 0) {
                        return null;
                    }
                    for (int i = 0; i < ja.size(); i++) {
                        JSONObject j = ja.getJSONObject(i);
                        pFieldCodeArray(j,split[1],findSub,fv);
                    }
                     return fv;
                }else{
                    JSONObject j = fromValue.getJSONObject(split[0]);
                    return pFieldCodeArray(j,split[1],findSub,null);
                }
            }
            return fv;
        }else{
            BigDecimal expVal = fromValue.getBigDecimal(fieldCode, BigDecimal.ZERO);
            JSONObject jo = new JSONObject();
            jo.set("money",expVal);
            jo.set("findSub",getJsonFindSub(fromValue,findSub));
            fv.add(jo);
        }
        return fv;
    }
    private static List<JSONObject> processFieldCodeItem(JSONObject fromValue, String fieldCode,String findSub) {
        final String[] finalFieldCode = {fieldCode};
//        Map<String, BigDecimal> fv = new HashMap<>();
        List<JSONObject> fv = new ArrayList<>();
        if (fieldCode.contains(".")) {
            String[] split = fieldCode.split("\\.");
            if (!fromValue.containsKey(split[0])) {
                return null;
            }
            if(split.length>2){
                return pFieldCodeArray(fromValue, fieldCode, findSub, fv);
            }
            // 判断是否是JSONArray
            if(fromValue.get(split[0]).getClass().equals(JSONArray.class)) {
                JSONArray ja = fromValue.getJSONArray(split[0]);
                if (ja == null || ja.size() == 0) {
                    return null;
                }
                // findSub
                for (int i = 0; i < ja.size(); i++) {
                    JSONObject j = ja.getJSONObject(i);
                    if (j.containsKey(split[1])) {
//                        String findSubV = findSub;
//                        if(findSubV.contains("|")){
//                            findSubV = buildExpression(findSubV);
//                        }
//                        // 编译表达式
//                        Expression compiledExp = AviatorEvaluator.compile(findSubV);

                        // 执行表达式
                        try{
                            BigDecimal expVal = j.getBigDecimal(split[1], BigDecimal.ZERO);
//                            expVal = expVal.add(fv.getOrDefault(res + i, BigDecimal.ZERO));
                            JSONObject jo = new JSONObject();
                            jo.set("money",expVal);
                            jo.set("findSub",getJsonFindSub(j,findSub));
                            fv.add(jo);
//                            fv.put(res + i, expVal);
                        }catch (Exception e){
                            log.error("processFieldCodeItem error:{}",e.getMessage());
                        }
                    }
                }
            }else{
                JSONObject json = fromValue.getJSONObject(split[0]);
                processJsonArrayItem(json, split[1], findSub, fv);
            }

        }


        if (fieldCode.contains("[") && fieldCode.contains("]")) {
            String exp = fieldCode.substring(fieldCode.indexOf("[") + 1, fieldCode.indexOf("]"));
            if (exp.contains(".")) {
                String[] split = exp.split("\\.");
                if (!fromValue.containsKey(split[0])) {
                    return null;
                }
                JSONObject json = fromValue.getJSONObject(split[0]);
                 processJsonArrayWithConditionItem(json, split[1], finalFieldCode, fv);
                return fv;
            }else{
                boolean b=(Boolean) AviatorEvaluator.execute(exp,fromValue);
                if(b) {
                    return fv;
                }
            }
        }else{
//            return processJsonArrayWithConditionItem(fv);
            return fv;
        }

        return null;
    }
    private static String getJsonFindSub(JSONObject j,String findSub){
        if(j==null || StrUtil.isBlank(findSub)){
            return null;
        }
        Map<String,Object> mj = new HashMap<>();
        j.forEach((k, v) -> mj.put(k, v instanceof JSONNull ? null : v));
        Object result =  AviatorEvaluator.execute(findSub,mj);
        if(result==null || !result.getClass().equals(String.class)){
            return null;
        }
        return result.toString();
    }
    private static BigDecimal processFieldCode(JSONObject fromValue, String fieldCode) {
        final String[] finalFieldCode = {fieldCode};
        Map<String, BigDecimal> fv = new HashMap<>();
        if (fieldCode.contains(".")) {
            String[] split = fieldCode.split("\\.");
            if (!fromValue.containsKey(split[0])) {
                return null;
            }

            // 判断是否是JSONArray
            if(fromValue.get(split[0]).getClass().equals(JSONArray.class)) {
                JSONArray ja = fromValue.getJSONArray(split[0]);
                if (ja == null || ja.size() == 0) {
                    return null;
                }
                for (int i = 0; i < ja.size(); i++) {
                    JSONObject j = ja.getJSONObject(i);
                    if (j.containsKey(split[1])) {
                        BigDecimal expVal = j.getBigDecimal(split[1], BigDecimal.ZERO);
                        expVal = expVal.add(fv.getOrDefault(split[0] + i, BigDecimal.ZERO));
                        fv.put(split[0] + i, expVal);
                    }
                }
            }else{
                JSONObject json = fromValue.getJSONObject(split[0]);
                processJsonArray(json, split[1], finalFieldCode, fv);
            }

        }

        if (fieldCode.contains("[") && fieldCode.contains("]")) {
            String exp = fieldCode.substring(fieldCode.indexOf("[") + 1, fieldCode.indexOf("]"));
            if (exp.contains(".")) {
                String[] split = exp.split("\\.");
                if (!fromValue.containsKey(split[0])) {
                    return null;
                }
                JSONObject json = fromValue.getJSONObject(split[0]);
                return processJsonArrayWithCondition(json, split[1], finalFieldCode, fv);
            }
        }else{
            return processJsonArrayWithCondition(fv);
        }

        return null;
    }
    private static void processJsonArrayItem(JSONObject json, String keyExp, String findSub, List<JSONObject> fv) {
        json.keySet().forEach(key -> {
            // 判断key是否是JSONArray
            if(!json.get(key).getClass().equals(JSONArray.class)){
                if(key.equals(keyExp)){
                    BigDecimal expVal = json.getBigDecimal(keyExp, BigDecimal.ZERO);
//                    expVal = expVal.add(fv.getOrDefault(key + i, BigDecimal.ZERO));
//                    fv.put(key + i, expVal);
                    JSONObject jo = new JSONObject();
                    jo.set("money",expVal);
                    jo.set("findSub",getJsonFindSub(json,findSub));
                    fv.add(jo);
                }
                return;
            }
            JSONArray ja = json.getJSONArray(key);
            if (ja == null || ja.size() == 0) {
                return;
            }
            String keyExpV = keyExp;
//            String extStr = null;
            if (keyExpV.contains("[")) {
//                extStr = keyExpV.substring(keyExpV.indexOf("[")+1,keyExpV.indexOf("]"));
                keyExpV = keyExpV.substring(0, keyExpV.indexOf("["));

            }
            for (int i = 0; i < ja.size(); i++) {
                JSONObject j = ja.getJSONObject(i);
             /*   if(StrUtil.isNotBlank(extStr)){
                    Boolean boo = (Boolean) AviatorEvaluator.execute(extStr,j);
                    if(!boo){
                        continue;
                    }
                }*/
                if (j.containsKey(keyExpV)) {
                    BigDecimal expVal = j.getBigDecimal(keyExpV, BigDecimal.ZERO);
//                    expVal = expVal.add(fv.getOrDefault(key + i, BigDecimal.ZERO));
//                    fv.put(key + i, expVal);
                    JSONObject jo = new JSONObject();
                    jo.set("money",expVal);
                    jo.set("findSub",getJsonFindSub(j,findSub));
                    fv.add(jo);
                }
            }
        });
    }
    private static void processJsonArray(JSONObject json, String keyExp, String[] finalFieldCode, Map<String, BigDecimal> fv) {
        json.keySet().forEach(key -> {
            // 判断key是否是JSONArray
            if(!json.get(key).getClass().equals(JSONArray.class)){
                return;
            }
            JSONArray ja = json.getJSONArray(key);
            if (ja == null || ja.size() == 0) {
                return;
            }
            String keyExpV = keyExp;
            if (keyExpV.contains("[")) {
                keyExpV = keyExpV.substring(0, keyExpV.indexOf("["));
            }
            for (int i = 0; i < ja.size(); i++) {
                JSONObject j = ja.getJSONObject(i);
                if (j.containsKey(keyExpV)) {
                    BigDecimal expVal = j.getBigDecimal(keyExpV, BigDecimal.ZERO);
                    expVal = expVal.add(fv.getOrDefault(key + i, BigDecimal.ZERO));
                    fv.put(key + i, expVal);
                }
            }
        });
    }
    private static BigDecimal processJsonArrayWithConditionItem(Map<String, BigDecimal> fv){
        final BigDecimal[] dmoney = {null};
        fv.keySet().forEach(key -> {
            BigDecimal fmoney = fv.get(key);
            if (dmoney[0] == null) {
                dmoney[0] = fmoney;
            }else{
                dmoney[0] = dmoney[0].add(fmoney);
            }
        });
        return dmoney[0];
    }
    private static BigDecimal processJsonArrayWithCondition(Map<String, BigDecimal> fv){
        final BigDecimal[] dmoney = {null};
        fv.keySet().forEach(key -> {
            BigDecimal fmoney = fv.get(key);
            if (dmoney[0] == null) {
                dmoney[0] = fmoney;
            }else{
                dmoney[0] = dmoney[0].add(fmoney);
            }
        });
        return dmoney[0];
    }
    private static BigDecimal processJsonArrayWithConditionItem(JSONObject json, String expDetail, String[] finalFieldCode, List<JSONObject> fv) {
        final BigDecimal[] dmoney = {null};
        json.keySet().forEach(key -> {
            JSONArray ja = json.getJSONArray(key);
            if (ja == null || ja.size() == 0) {
                return;
            }
            String keyExp = expDetail.contains("=") ? expDetail.split("=")[0] : expDetail;
            for (int i = 0; i < ja.size(); i++) {
                JSONObject j = ja.getJSONObject(i);
                if (j.containsKey(keyExp)) {
                    String expVal = j.getStr(keyExp,"");
                    Map<String, Object> map = new HashMap<>();
                    map.put(keyExp, expVal);
                    boolean result = (Boolean) AviatorEvaluator.execute(expDetail, map);
                    if (result) {
//                        BigDecimal fmoney = fv.getOrDefault(key + i, BigDecimal.ZERO);
//                        if (dmoney[0] == null) {
//                            dmoney[0] = fmoney;
//                        } else {
//                            dmoney[0] = dmoney[0].add(fmoney);
//                        }
                    }else{
                        JSONObject jo = fv.get(i);
                        jo.set("money",null);
                    }
                }
            }
        });
        return dmoney[0] != null ? dmoney[0] : null;
    }
    private static BigDecimal processJsonArrayWithCondition(JSONObject json, String expDetail, String[] finalFieldCode, Map<String, BigDecimal> fv) {
        final BigDecimal[] dmoney = {null};
        json.keySet().forEach(key -> {
            JSONArray ja = json.getJSONArray(key);
            if (ja == null || ja.size() == 0) {
                return;
            }
            String keyExp = expDetail.contains("=") ? expDetail.split("=")[0] : expDetail;
            for (int i = 0; i < ja.size(); i++) {
                JSONObject j = ja.getJSONObject(i);
                if (j.containsKey(keyExp)) {
                    String expVal = j.getStr(keyExp,"");
                    Map<String, Object> map = new HashMap<>();
                    map.put(keyExp, expVal);
                    boolean result = (Boolean) AviatorEvaluator.execute(expDetail, map);
                    if (result) {
                        BigDecimal fmoney = fv.getOrDefault(key + i, BigDecimal.ZERO);
                        if (dmoney[0] == null) {
                            dmoney[0] = fmoney;
                        } else {
                            dmoney[0] = dmoney[0].add(fmoney);
                        }
                    }
                }
            }
        });
        return dmoney[0] != null ? dmoney[0] : null;
    }

    private List<JSONObject> dataByField(JSONObject fv,String fieldCode,String findSub){
        List<JSONObject> list = new ArrayList<>();
        if(StrUtil.isBlank(fieldCode)){
            return list;
        }
        List<JSONObject> mt = null;
        String fkey = fieldCode;
        if(fieldCode.contains(",")){
            String[] split = fieldCode.split(",");
            for(int i=0;i<split.length;i++){
                String key = split[i];
                if (key.contains("[") && key.contains("]")) {
                    String k = key.substring(0, key.indexOf("["));
                    String exp = key.substring(key.indexOf("[") + 1, key.indexOf("]"));
                    // 判断exp 是否正确，不正确下一个
                    if(StrUtil.isBlank(exp)){
                        continue;
                    }
                    if(exp.contains("=")){
                        Boolean execute = (Boolean) AviatorEvaluator.execute(exp, fv);
                        if(execute){
                            fkey=k;
                            break;
                        }
                    }
                }
            }
        }
        if(fkey.contains("-")){
            String[] split = fkey.split("-");
            for(int i=0;i<split.length;i++){
                List<JSONObject> j =moneyByKeyItem(fv,split[i],findSub);
                if(j==null || j.size()==0){
                    continue;
                }
                if(mt==null){
                    mt = j;
                }else{
//                    mt = money.subtract(m);
                    for(int k=0;k<mt.size();k++){
                        JSONObject jo = mt.get(k);
                        JSONObject o = j.get(k);
                        try {
                            jo.set("money", jo.getBigDecimal("money").subtract(o.getBigDecimal("money")));
                        }catch (Exception e){
                            log.error("dataByField error:{}",e.getMessage());
                        }
                    }
                }
            }
        }
        if(mt==null){
            return  moneyByKeyItem(fv,fkey,findSub);
        }
        return mt;

    }

    private BigDecimal dataByCalc(JSONObject fromValue, String fieldCode) {
        // fromValue
        BigDecimal money = null;
        // fieldCode
        if(StrUtil.isBlank(fieldCode)){
            return null;
        }
        // 多条件,
        if(fieldCode.contains(",")){
            String[] split = fieldCode.split(",");
            for(int i=0;i<split.length;i++){
                String key = split[i];
                if (key.contains("[") && key.contains("]")) {
                    String k = key.substring(0, key.indexOf("["));
                    String exp = key.substring(key.indexOf("[") + 1, key.indexOf("]"));
                    // 判断exp 是否正确，不正确下一个
                    if(StrUtil.isBlank(exp)){
                        continue;
                    }
                    if(exp.contains("=")){
                        Boolean execute = (Boolean) AviatorEvaluator.execute(exp, fromValue);
                        if(execute){
                            fieldCode=k;
                            break;
                        }
                    }
                }
            }
        }
        if(fieldCode.contains("-")){
            String[] split = fieldCode.split("-");
            for(int i=0;i<split.length;i++){
                BigDecimal m =moneyByKey(fromValue,split[i]);
                if(m==null){
                    continue;
                }
                if(money==null){
                    money = m;
                }else{
                    money = money.subtract(m);
                }
            }
        }
        if(money==null){
            money = moneyByKey(fromValue,fieldCode);
        }
        return money;
    }
    private List<JSONObject> moneyByKeyItem(JSONObject fromValue,String fieldCode,String findSub){
        List<JSONObject> money = new ArrayList<>();
        if(fromValue.containsKey(fieldCode)){
            JSONObject jsonObject=new JSONObject();
            jsonObject.set("money",fromValue.getBigDecimal(fieldCode));
            jsonObject.set("findSub",getJsonFindSub(fromValue,findSub));
            money.add(jsonObject);
            return money;
        }
        money = processFieldCodeItem(fromValue,fieldCode,findSub);
        return money;
    }
    private BigDecimal moneyByKey(JSONObject fromValue,String fieldCode){
        BigDecimal money = null;
        if(fromValue.containsKey(fieldCode)){
            money = fromValue.getBigDecimal(fieldCode);
            return money;
        }
        money = processFieldCode(fromValue,fieldCode);
        return money;
    }

    private JSONArray moneyByTravelArray(JSONObject fromValue, List<CertTemplRelation> list){
        JSONArray moneyJson = new JSONArray();
//        BigDecimal sumNum = new BigDecimal(0);
//        if(fromValue!=null && fromValue.containsKey("sumNum")){
//            sumNum = fromValue.getBigDecimal("sumNum");
//        }
        boolean isInvoice = false;
        boolean isCompany = true;
        String shuiCode="2";
        // 公司规模是一般纳税人general_taxpayer 才有税额抵扣，小规模small_scale
        String companyId = fromValue.getStr("company");
        if(StrUtil.isNotBlank(companyId)){
           WxDepartment wxDepartment = wxDepartmentMapper.selectById(companyId);
           if(wxDepartment!=null && "small_scale".equals(wxDepartment.getMbody())){
               isCompany = false;
           }
        }
        if(isCompany && "有".equals(fromValue.getStr("invoice",""))){
            isInvoice=true;
        }
        fromValue.set("isCompany_",isCompany);
        for(CertTemplRelation certTemplRelation : list) {
            if(!isInvoice && shuiCode.equals(certTemplRelation.getGroupCode())){
                continue;
            }
            // 是否有 - 号
            String fieldCode = certTemplRelation.getFieldCode();
//            BigDecimal money = null;
            // code 判断
            List<JSONObject> dj = null;
            if(StrUtil.isNotBlank(fieldCode)){
              dj =   dataByField(fromValue,fieldCode,certTemplRelation.getFindSub());
//                money = dataByCalc(fromValue,fieldCode);
            }
            if(dj==null || dj.size()==0){
                continue;
            }
            for(JSONObject j : dj){
                if(!j.containsKey("money") || j.getBigDecimal("money")==null){
                    continue;
                }
                JSONObject jo = new JSONObject();
                // 模版名
                jo.set("summary", StrUtil.isNotBlank(certTemplRelation.getName())
                        ? replaceTem(certTemplRelation.getName(),fromValue)
                        :"");
                // 摘要
                jo.set("abstract", certTemplRelation.getTemplAbstract());
                // 科目
                jo.set("subject", certTemplRelation.getAccountingSubjects());
                // 子条件
//                jo.set("findSub",StrUtil.isNotBlank(certTemplRelation.getFindSub())? j.getStr("findSub",fromValue.getStr(certTemplRelation.getFindSub())) :"");
                jo.set("findSub",j.getStr("findSub",getJsonKey(certTemplRelation.getFindSub(),fromValue)));
                // 借
                jo.set("debit", certTemplRelation.getBorrowing()==1?j.getStr("money", fromValue.getStr(certTemplRelation.getFieldCode(),"0")):"0");
                // 贷
                jo.set("credit", certTemplRelation.getBorrowing()==1?"0":j.getStr("money", fromValue.getStr(certTemplRelation.getFieldCode(),"0")));
                // 金额
                jo.set("money",j.getStr("money", fromValue.getStr(certTemplRelation.getFieldCode(),"0")) );
                // 分组
                jo.set("groupCode", certTemplRelation.getGroupCode());
                // 对应科目
//                jo.set("querySubject",certTemplRelation.getFieldSummary());
                jo.set("querySubject",StrUtil.isNotBlank(certTemplRelation.getFieldSummary())
                        ? replaceTem(certTemplRelation.getFieldSummary(),fromValue)
                        :"");
                moneyJson.add(jo);
            }


//            JSONObject jo = new JSONObject();
//            jo.set("summary", StrUtil.isNotBlank(certTemplRelation.getName())
//                    ? replaceTem(certTemplRelation.getName(),fromValue)
//                    :"");
//            jo.set("abstract", certTemplRelation.getTemplAbstract());
//            jo.set("subject", certTemplRelation.getAccountingSubjects());
//            jo.set("findSub",StrUtil.isNotBlank(certTemplRelation.getFindSub())?fromValue.getStr(certTemplRelation.getFindSub()):"");
//            jo.set("debit", certTemplRelation.getBorrowing()==1?money:"0");
//            jo.set("credit", certTemplRelation.getBorrowing()==1?"0":money);
//            jo.set("money",fromValue.getStr(certTemplRelation.getFieldCode(),"0"));
//            jo.set("groupCode", certTemplRelation.getGroupCode());
//            moneyJson.add(jo);
        }
        return moneyJson;
    }

    private String replaceTem(String template, JSONObject json) {
        StringBuilder result = new StringBuilder();
        int start = 0;
        while (true) {
            int beginIndex = template.indexOf("[", start);
            if (beginIndex == -1) {
                result.append(template.substring(start));
                break;
            }
            int endIndex = template.indexOf("]", beginIndex);
            if (endIndex == -1) {
                result.append(template.substring(start));
                break;
            }
            result.append(template, start, beginIndex); // 添加非变量部分
            String variableName = template.substring(beginIndex + 1, endIndex); // 获取变量名
//            String replacement = jsonNode.has(variableName) ? jsonNode.get(variableName).asText() : ""; // 从 JSON 中获取值
//            String replacement = json.getStr(variableName,""); // 从 JSON 中获取值
            String replacement = getJsonKey(variableName,json); // 从 JSON 中获取值
            result.append(replacement); // 添加替换值
            start = endIndex + 1;
        }

        return result.toString();
    }
    private String getJsonKey(String key,JSONObject json){
        if (StrUtil.isBlank(key) || json == null) {
            return "";
        }
        if(json.containsKey(key)){
            return json.getStr(key,"");
        }
        if(key.contains(".")){
            String[] keys = key.split("\\.",2);
            String currentKey = keys[0];
            String remainingKey = keys[1];
            // 检查子节点是否为JSONObject
            if (json.containsKey(currentKey) && json.get(currentKey) instanceof JSONObject) {
                return getJsonKey(remainingKey, json.getJSONObject(currentKey));
            }
        }
        return "";
    }
    private String[] certDataCodeGetIdGet(String id, String tmplCode){
       Integer pid = certDataGetId(id,tmplCode);
        LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(CertDataProcess::getAppCode, haokjAppCode);
        queryWrapper.eq(CertData::getProcessId, pid);
        queryWrapper.eq(CertData::getAccModel, getAccModel());
        queryWrapper.eq(CertData::getCode, tmplCode);
        List<CertData> certDatas = certDataMapper.selectList(queryWrapper);
        if(certDatas!=null && certDatas.size()>0){
//            return certDatas.get(0).getAppCode();
            // 多个不同的 appCode
            Set<String> set = new HashSet<>();
            for(CertData certData : certDatas){
                set.add(certData.getAppCode());
            }
            return set.toArray(new String[0]);
        }
        return null;
    }
    @Override
    public Map<String, Object> getAcctgTransList(String id, String type) {
        JSONObject codeJson = codeByProcessId(id,type);
        String tmplCode = codeJson.getStr("code",null);
        if(StrUtil.isBlank(tmplCode)){
            return null;
        }

        String[]  appCode = certDataCodeGetIdGet(id,tmplCode);
        return getAcctgTransListByCodeList(id,tmplCode,appCode);
    }
    @Override
    public Map<String, Object> getAcctgTransListByCode(String id, String code) {
        String[]  appCode = certDataCodeGetIdGet(id,code);
        return getAcctgTransListByCodeList(id,code,appCode);
    }

    public Map<String, Object> getAcctgTransListByCodeList(String id, String code,String... appCodes) {
        Map<String,Object> map=new HashMap<>();
        if(appCodes==null || appCodes.length==0){
            return null;
        }
        for(String appCode : appCodes){
            Map<String, Object> acctgTransListByCode = getAcctgTransListByCode(id, code, appCode);
            if(acctgTransListByCode!=null){
                // {totalCount:num,result:[]},合并 并添加appCode
                if(map.containsKey("totalCount")){
                    map.put("totalCount",Integer.parseInt(map.get("totalCount").toString())+Integer.parseInt(acctgTransListByCode.get("totalCount").toString()));
                }else{
                    map.put("totalCount",acctgTransListByCode.get("totalCount"));
                }
                if(acctgTransListByCode.containsKey("result")){
                    JSONArray result = (JSONArray)acctgTransListByCode.get("result");
                    if(map.containsKey("result")){
                        JSONArray jsonArray = (JSONArray) map.get("result");
                        jsonArray.addAll(result);
                    }else{
                        map.put("result",result);
                    }
                }

            }
        }
        return map;
    }
    private String getAppName(String appCode){
        LambdaQueryWrapper<OpenTokenCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpenTokenCompany::getAppCode, appCode);
//        OpenTokenCompany openTokenCompany = openTokenCompanyMapper.selectOne(queryWrapper);
        List<OpenTokenCompany> openTokenCompanies = openTokenCompanyMapper.selectList(queryWrapper);
        if(openTokenCompanies!=null && openTokenCompanies.size()>0){
            return openTokenCompanies.get(0).getAppName();
        }
        return "";
    }
    public Map<String, Object> getAcctgTransListByCode(String id, String code,String appCode) {
        if(StrUtil.isBlank(appCode)){
//            appCode = haokjAppCode;
            return null;
        }
        String currentBookId = getCurrentBookId(appCode);
        if(StrUtil.isBlank(currentBookId)){
            return null;
        }
        String url= "/accounting/gl/AcctgTrans/acctgTransExactSearch/"+currentBookId;
        JSONObject searchParam = new JSONObject();
        searchParam.set("refVoucherId",certDataGetId(id,code));
        searchParam.set("refVoucherCode",code);
//        searchParam str
        // encodeURIComponent
        try {
            String searchParamStr = URLEncoder.encode(searchParam.toString(),"utf-8");
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("searchParam",searchParamStr);
            JSONObject json = haokjRequestGet(url, hashMap,appCode);
            String appName = getAppName(appCode);
            if(json.containsKey("result")){
                JSONArray result = json.getJSONArray("result");
                for(int i=0;i<result.size();i++){
                    JSONObject jo = result.getJSONObject(i);
                    jo.set("appCode",appCode);
                    jo.set("appName",appName);
                }
            }
//            log.info(json.toString());
            return json;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Map<String, Object> getIsCodeByPid(String ids, String type) {
//        StopWatch stopWatch = new StopWatch("getIsCodeByPid");
//        stopWatch.start();
//        查询是否支持生成凭证，是否已生成凭证
//        StopWatch stopWatch1 = new StopWatch("880");
//        stopWatch1.start();
        Map<String,Object> map=new HashMap<>();
        Map<String,JSONObject> codeJsons = codeByProcessIds(type,ids.split(","));
//        stopWatch1.stop();
//        log.info(stopWatch1.prettyPrint());
//        CertData 按id code 分组统计
      /*  List<Integer> processIds = new ArrayList<>();
        for(String key : codeJsons.keySet()){
            JSONObject codeJson = codeJsons.get(key);
            String tmplCode = codeJson.getStr("code",null);
            if(StrUtil.isBlank(tmplCode)){
                continue;
            }
            processIds.add(certDataGetIdGet(key,tmplCode));
        }*/
//        StopWatch stopWatch2 = new StopWatch("896");
//        stopWatch2.start();
        List<String> processIds = new ArrayList<>();
        List<String> codes = new ArrayList<>();
        for(String key : codeJsons.keySet()){
            JSONObject codeJson = codeJsons.get(key);
            String tmplCode = codeJson.getStr("code",null);
            if(StrUtil.isBlank(tmplCode)){
                continue;
            }
            processIds.add(key);
            codes.add(tmplCode);
        }
        if(processIds.size()==0){
            return null;
        }
//        stopWatch2.stop();
//        log.info(stopWatch2.prettyPrint());
//        StopWatch stopWatch3 = new StopWatch("907");
//        stopWatch3.start();
        List<CertDataProcess> idsCert = certDataGetIdsGet(processIds,codes);
//        stopWatch3.stop();
//        log.info(stopWatch3.prettyPrint());
        if(idsCert==null || idsCert.size()==0){
            return null;
        }
//        StopWatch stopWatch4 = new StopWatch("915");
//        stopWatch4.start();
        QueryWrapper<CertData> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("process_id",idsCert.stream().map(CertDataProcess::getId).collect(Collectors.toList()));
        queryWrapper.eq("acc_model",getAccModel());
        queryWrapper.groupBy("process_id","code");
        queryWrapper.select("process_id","code", "count(*) as count");
        List<Map<String, Object>> maps = certDataMapper.selectMaps(queryWrapper);
//        stopWatch4.stop();
//        log.info(stopWatch4.prettyPrint());
//        StopWatch stopWatch5 = new StopWatch("925");
//        stopWatch5.start();
        Map<String,Integer> countMap = new HashMap<>();
        for(Map<String, Object> m : maps){
            String key = m.get("process_id")+"_"+m.get("code");
            countMap.put(key,Integer.parseInt(m.get("count").toString()));
        }
        for(CertDataProcess certDataProcess : idsCert){
            String key = certDataProcess.getId()+"_"+certDataProcess.getCode();
            Integer count = countMap.getOrDefault(key,0);
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("code",certDataProcess.getCode());
            jsonObject.set("count",count);
            map.put(certDataProcess.getProcessId(),jsonObject);
        }
//        stopWatch5.stop();
//        log.info(stopWatch5.prettyPrint());
//        for(String key : codeJsons.keySet()){
//            JSONObject codeJson = codeJsons.get(key);
//            String tmplCode = codeJson.getStr("code",null);
//            if(StrUtil.isBlank(tmplCode)){
//                continue;
//            }
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.set("code",tmplCode);
//            Integer keyCodeGet = certDataGetIdGet(key,tmplCode);
//            if(keyCodeGet==null){
//                continue;
//            }
//            String keyCode = keyCodeGet+"_"+tmplCode;
//            Integer count = countMap.getOrDefault(keyCode,0);
//            jsonObject.set("count",count);
//            map.put(key,jsonObject);
//        }






/*
        for(String key : codeJsons.keySet()) {
            JSONObject codeJson = codeJsons.get(key);
            String tmplCode = codeJson.getStr("code", null);
            if (StrUtil.isBlank(tmplCode)) {
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("code", tmplCode);
            LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CertData::getProcessId, certDataGetIdGet(key, tmplCode));
            queryWrapper.eq(CertData::getCode, tmplCode);
            Integer count = certDataMapper.selectCount(queryWrapper);
            jsonObject.set("count", count);
            map.put(key, jsonObject);
        }*/


     /*   for(String id : ids.split(",")){
            JSONObject codeJson = codeByProcessId(id,type);
            String tmplCode = codeJson.getStr("code",null);
            if(StrUtil.isBlank(tmplCode)){
//                map.put(id,null);
                continue;
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("code",tmplCode);
            LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(CertData::getAppCode, haokjAppCode);
            queryWrapper.eq(CertData::getProcessId, certDataGetIdGet(id,tmplCode));
            queryWrapper.eq(CertData::getCode, tmplCode);
            Integer count = certDataMapper.selectCount(queryWrapper);
            jsonObject.set("count",count);
            map.put(id,jsonObject);
        }*/
        // 根据id，code
//        stopWatch.stop();
//        log.info(stopWatch.prettyPrint());
        return map;
    }

    @Override
    public Map<String, Object> getIsCodeByPidAndCode(String ids, String tmplCode) {
        //        查询是否支持生成凭证，是否已生成凭证
        Map<String,Object> map=new HashMap<>();
//        for(String id : ids.split(",")){
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.set("code",tmplCode);
//            LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
////            queryWrapper.eq(CertData::getAppCode, haokjAppCode);
//            queryWrapper.eq(CertData::getProcessId, certDataGetIdGet(id,tmplCode));
//            queryWrapper.eq(CertData::getCode, tmplCode);
//            Integer count = certDataMapper.selectCount(queryWrapper);
//            jsonObject.set("count",count);
//            map.put(id,jsonObject);
//        }
//        certDataGetIdsGet
        List<String> idsP = new ArrayList<>(Arrays.asList(ids.split(",")));

        List<CertDataProcess> idsCert = certDataGetIdsGet(idsP,tmplCode);
        if(idsCert==null || idsCert.size()==0){
            return null;
        }
        QueryWrapper<CertData> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("process_id",idsCert.stream().map(CertDataProcess::getId).collect(Collectors.toList()));
        queryWrapper.eq("code",tmplCode);
        queryWrapper.eq("acc_model",getAccModel());
        queryWrapper.groupBy("process_id","code");
        queryWrapper.select("process_id","code", "count(*) as count");
        List<Map<String, Object>> maps = certDataMapper.selectMaps(queryWrapper);
        Map<String,Integer> countMap = new HashMap<>();
        for(Map<String, Object> m : maps){
            String key = m.get("process_id")+"_"+m.get("code");
            countMap.put(key,Integer.parseInt(m.get("count").toString()));
        }
        for(CertDataProcess certDataProcess : idsCert){
            String key = certDataProcess.getId()+"_"+tmplCode;
            Integer count = countMap.getOrDefault(key,0);
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("code",tmplCode);
            jsonObject.set("count",count);
            map.put(certDataProcess.getProcessId(),jsonObject);
        }
        // 根据id，code
        return map;
    }

    @Override
    public Map<String, Object> getHaokjSub(String appCode) {
        if(StrUtil.isBlank(appCode)){
//            appCode = haokjAppCode;
            return null;
        }
        String currentBookId = getCurrentBookId(appCode);
        if(StrUtil.isBlank(currentBookId)){
            return null;
        }
        // redis 缓存 半小时失效
        String key = "haokjSub_"+currentBookId;
        JSONArray json = JSONUtil.parseArray(redisTemplate.opsForValue().get(key));
        if(json==null){
            String url = "/accounting/gl/glaccount/getInitBalanceLists/"+currentBookId;
            json = haokjRequestGetArray(url, null,appCode);
            if(json!=null){
                redisTemplate.opsForValue().set(key,json,30, TimeUnit.MINUTES);
            }
        }

        ///accounting/gl/glaccount/getInitBalanceList/{bookid}

//        log.info(json.toString());
        Map<String, Object> map = new HashMap<>();
        if(json!=null){
            map.put("data",json);
            return map;
        }
        return null;
    }
    private JSONArray moneyByFileArray(JSONArray fromValue,List<CertTemplRelation> templList,CertTempl certTempl,String single) {
        if(fromValue==null || fromValue.size()==0){
            return null;
        }
        if(templList==null || templList.size()==0){
            return null;
        }
        if(certTempl==null || StrUtil.isBlank(certTempl.getTemplData())){
            return null;
        }
        String[] execls = {".xlsx", ".xls"};
        List<String> fileUrlList = new ArrayList<>();
//        for (String key : fromValue.keySet()) {
//            JSONArray value = fromValue.getJSONArray(key);
            JSONArray value = fromValue;
            if(value==null || value.size()==0){
                return null;
            }
            for (int i = 0; i < value.size(); i++) {
                JSONObject v = value.getJSONObject(i);
                String fileName = v.getStr("name");
                if (StrUtil.isNotBlank(fileName)) {
                    for (String ext : execls) {
                        if (fileName.endsWith(ext)) {
                            JSONObject response =  v.getJSONObject("response");
                            if(response==null || response.size()==0){
                                continue;
                            }
                            fileUrlList.add(response.getStr("url",null));
                            break;
                        }
                    }
                }

            }
//        }
        // 获取工资表数据
        if(fileUrlList.size()==0){
            return null;
        }
        Map<String,String> fName = new HashMap<>();
        String checkName = null;
        JSONArray fList = JSONUtil.parseArray(certTempl.getTemplData());
        // 获取 计算字段与模版字段对应关系
        for (int i = 0; i < fList.size(); i++) {
            if(i==0){
                checkName = fList.getJSONObject(i).getStr("value");
            }
            JSONObject f = fList.getJSONObject(i);
            fName.put(f.getStr("name"),f.getStr("value"));
        }
        // 获取工资表
        for(String fileUrl : fileUrlList){
            if(StrUtil.isBlank(fileUrl)){
                continue;
            }
            JSONArray jsonObject = getExcelDataArray(fileUrl,templList,fName,checkName,single);
            if(jsonObject!=null && jsonObject.size()>0){
                return jsonObject;
            }
        }

        return null;
    }
    private JSONArray moneyByFileArray(JSONObject fromValue,List<CertTemplRelation> templList,CertTempl certTempl,String single) {
        if(fromValue==null || fromValue.size()==0){
            return null;
        }
        if(templList==null || templList.size()==0){
            return null;
        }
        if(certTempl==null || StrUtil.isBlank(certTempl.getTemplData())){
            return null;
        }
        String[] execls = {".xlsx", ".xls"};
        List<String> fileUrlList = new ArrayList<>();
        for (String key : fromValue.keySet()) {
            JSONArray value = fromValue.getJSONArray(key);
            if(value==null || value.size()==0){
                continue;
            }
            for (int i = 0; i < value.size(); i++) {
                JSONObject v = value.getJSONObject(i);
                String fileName = v.getStr("name");
                if (StrUtil.isNotBlank(fileName)) {
                    for (String ext : execls) {
                        if (fileName.endsWith(ext)) {
                            JSONObject response =  v.getJSONObject("response");
                            if(response==null || response.size()==0){
                                continue;
                            }
                            fileUrlList.add(response.getStr("url",null));
                            break;
                        }
                    }
                }

            }
        }
        // 获取工资表数据
        if(fileUrlList.size()==0){
            return null;
        }
        Map<String,String> fName = new HashMap<>();
        String checkName = null;
        JSONArray fList = JSONUtil.parseArray(certTempl.getTemplData());
        // 获取 计算字段与模版字段对应关系
        for (int i = 0; i < fList.size(); i++) {
            if(i==0){
                checkName = fList.getJSONObject(i).getStr("value");
            }
            JSONObject f = fList.getJSONObject(i);
            fName.put(f.getStr("name"),f.getStr("value"));
        }
        // 获取工资表
        for(String fileUrl : fileUrlList){
            if(StrUtil.isBlank(fileUrl)){
                continue;
            }
            JSONArray jsonObject = getExcelDataArray(fileUrl,templList,fName,checkName,single);
            if(jsonObject!=null && jsonObject.size()>0){
                return jsonObject;
            }
        }

        return null;
    }
    private List<JSONObject> persionFileUrl(String fileUrl,List<CertTemplRelation> templList,Map<String,String> nameMap,String checkName) {
        if(StrUtil.isBlank(fileUrl)){
            return null;
        }
        try {
            // 下载远程文件，保存到临时目录，读取文件内容
            // 下载远程文件到本地
//            String localFilePath = "local_file.xlsx";
            String localFilePath = FileUtil.file(FileUtil.getTmpDir(), UUID.randomUUID().toString() + fileUrl.substring(fileUrl.lastIndexOf("."))).getPath();
            HttpUtil.downloadFile(fileUrl, FileUtil.file(localFilePath));
            ExcelReader reader = ExcelUtil.getReader(localFilePath,0);
            // 个人相关费用
            List<JSONObject> persion= new ArrayList<>();
            // 读取Excel数据
//             reader.readAll().forEach(row -> {
//              int rint =  addPersion(row,persion,templList);
//              if(rint==0){
//                  return;
//              }
//            });
            if(reader.readAll().size()==0 || !reader.readAll().get(0).containsKey(checkName)){
                return null;
            }
            for (int i = 0; i < reader.readAll().size(); i++) {
                // 循环工资表 加载工资数据
                int rint =  addPersion(reader.readAll().get(i),persion,templList,nameMap);
                if(rint==0){
                    break;
                }
            }

            // 关闭读取器
            reader.close();

            // 删除本地临时文件（可选）
            FileUtil.del(localFilePath);
            return  persion;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private JSONArray getExcelDataArray(String fileUrl,List<CertTemplRelation> templList,Map<String,String> nameMap,String checkName,String single) {

        if(StrUtil.isBlank(fileUrl)){
            return null;
        }
        try {
            // 下载远程文件，保存到临时目录，读取文件内容
            // 下载远程文件到本地
//            String localFilePath = "local_file.xlsx";
            String localFilePath = FileUtil.file(FileUtil.getTmpDir(), UUID.randomUUID().toString() + fileUrl.substring(fileUrl.lastIndexOf("."))).getPath();
            HttpUtil.downloadFile(fileUrl, FileUtil.file(localFilePath));
            ExcelReader reader = ExcelUtil.getReader(localFilePath,0);
            // 个人相关费用
            List<JSONObject> persion= new ArrayList<>();
            // 读取Excel数据
//             reader.readAll().forEach(row -> {
//              int rint =  addPersion(row,persion,templList);
//              if(rint==0){
//                  return;
//              }
//            });
            if(reader.readAll().size()==0 || !reader.readAll().get(0).containsKey(checkName)){
                return null;
            }
            for (int i = 0; i < reader.readAll().size(); i++) {
                // 循环工资表 加载工资数据
                int rint =  addPersion(reader.readAll().get(i),persion,templList,nameMap);
                if(rint==0){
                    break;
                }
            }

            // 关闭读取器
            reader.close();

            // 删除本地临时文件（可选）
            FileUtil.del(localFilePath);
            // 合并数据
            JSONObject jsonObject = new JSONObject();
            for (JSONObject jsonObject1 : persion) {
                if(jsonObject1==null || jsonObject1.size()==0){
                    continue;
                }
                // 同一个名字，计算合计
//                String name = jsonObject1.getStr("name");
//                if(jsonObject.containsKey(name)){
//                    String value = jsonObject1.get("value");
//                    jsonObject.put(name,jsonObject.getBigDecimal(name).add(jsonObject1.getBigDecimal(value)));
//                }
                for(String key : jsonObject1.keySet()){
                    String value = jsonObject1.getStr(key);
                    if(StrUtil.isBlank(value) || "key".equals(key)){
                        continue;
                    }
                    if(jsonObject.containsKey(key)){
//                        jsonObject.set(key,jsonObject.getBigDecimal(key).add(new BigDecimal(value)));
                        // 保留2位小数 四舍五入
                        jsonObject.set(key, jsonObject.getBigDecimal(key).add(new BigDecimal(value)).setScale(2, RoundingMode.HALF_UP));
                    }else{
                        jsonObject.set(key,value);
                    }
                }
            }

            // data [{name,kemu,j,d}]
            JSONArray data = new JSONArray();
            if("1".equals(single)){
                for (JSONObject jsonObject1 : persion) {
                    if(jsonObject1==null || jsonObject1.size()==0){
                        continue;
                    }
                    for (CertTemplRelation certTemplRelation : templList) {
                        if(!"1".equals(certTemplRelation.getGroupCode())){
                            continue;
                        }
                        JSONObject jo = new JSONObject();
                        jo.set("summary", jsonObject1.getStr("key")+"-"+certTemplRelation.getName());
                        jo.set("abstract", certTemplRelation.getTemplAbstract());
                        jo.set("subject", certTemplRelation.getAccountingSubjects());
                        jo.set("debit", certTemplRelation.getBorrowing()==1?jsonObject1.getStr(certTemplRelation.getTemplAbstract(),"0"):"0");
                        jo.set("credit", certTemplRelation.getBorrowing()==1?"0":jsonObject1.getStr(certTemplRelation.getTemplAbstract(),"0"));
                        jo.set("money",jsonObject1.getStr(certTemplRelation.getTemplAbstract(),"0"));
                        jo.set("groupCode", certTemplRelation.getGroupCode());
                        data.add(jo);
                    }
                }
            }

            for (CertTemplRelation certTemplRelation : templList) {
                if("1".equals(single) && "1".equals(certTemplRelation.getGroupCode())){
                    continue;
                }
                JSONObject jo = new JSONObject();
                jo.set("summary", certTemplRelation.getName());
                jo.set("abstract", certTemplRelation.getTemplAbstract());
                jo.set("subject", certTemplRelation.getAccountingSubjects());
                jo.set("debit", certTemplRelation.getBorrowing()==1?jsonObject.getStr(certTemplRelation.getTemplAbstract(),"0"):"0");
                jo.set("credit", certTemplRelation.getBorrowing()==1?"0":jsonObject.getStr(certTemplRelation.getTemplAbstract(),"0"));
                jo.set("groupCode", certTemplRelation.getGroupCode());
                jo.set("money",jsonObject.getStr(certTemplRelation.getTemplAbstract(),"0"));
                data.add(jo);
            }
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private int addPersion(Map<String,Object> row,List<JSONObject> persion,List<CertTemplRelation> templList,Map<String,String> nameMap) {
        if(row==null || row.size()==0){
            return 0;
        }
        JSONObject jo = new JSONObject(); // 个人 分类
       for (CertTemplRelation certTemplRelation : templList) {
    // 循环模版 获取对应金额
           String name = certTemplRelation.getTemplAbstract();
//           jo.set("name",name);
           // 公式
           String formula = certTemplRelation.getFieldCode();
           // + 分割，只有加法
           String[] split = formula.split("\\+");
           String value;
           List<Object> valList = new ArrayList<>();
           for (String s : split) {
               if(nameMap.containsKey(s)){
                   value = nameMap.get(s);
               }else{
                   value = s;
               }
               valList.add(row.getOrDefault(value,null));
           }
           if(valList.size()>0){
               // sum BigDecimal
               BigDecimal sum = new BigDecimal("0");
               for (Object o : valList) {
                   if(o!=null){
                       BigDecimal z = new BigDecimal("0");
                       if(StrUtil.isNotBlank(o.toString())){
                           z = new BigDecimal(o.toString());
                       }
//                       sum = sum.add(new BigDecimal(o.toString()));
                       // 保留2位小数 四舍五入
                       sum = sum.add(z).setScale(2, RoundingMode.HALF_UP);
                   }
               }
               jo.set(name,sum);

           }

       }
       if(jo.size()>0){
           String nameKey = nameMap.getOrDefault(SALARY_TEMPL_NAME,SALARY_TEMPL_NAME);
           String deptKey = nameMap.getOrDefault(SALARY_TEMPL_DEPT,SALARY_TEMPL_DEPT);

           jo.set("key",row.getOrDefault(nameKey, "").toString()+"-"+row.getOrDefault(deptKey, "").toString());
           persion.add(jo);
           return 1;
       }
        return 0;
    }
    @Override
    public Map<String,Object> saveVoucher(String processId, String type,String month, List<CertTemplVo> list,String single,String appCode) {
        Map<String,Object> result = new HashMap<>();
        JSONObject codeJson = codeByProcessId(processId,type);
        String tmplCode = codeJson!=null ? codeJson.getStr("code",null):null;
        if(!"1".equals(type) && !"-1".equals(type) && StrUtil.isNotBlank(type)){
            tmplCode = type;
        }
        if(StrUtil.isBlank(tmplCode)){
            result.put("msg","模版不存在");
            result.put("success",false);
//            return null;
            return result;
        }
        if(list==null || list.size()==0){
            result.put("msg","没有数据");
            result.put("success",false);
            return result;
        }
//        List<CertTemplRelation> templList = getCertTemplRelation(tmplCode);
        if(tmplCode.equals(SALARY_PAYEND)){
            String defSummary = SALARY_PAYEND_SUMMARY;
            String cyear = month.substring(0,4);
            String cmonth = month.substring(4,6);
            // 按groupcode ,summary 分组发送
            Map<String,List<CertTemplVo>> groupMap = list.stream().collect(Collectors.groupingBy(CertTemplVo::getGroupCode));

            for (Map.Entry<String, List<CertTemplVo>> entry : groupMap.entrySet()) {

                String groupCode = entry.getKey();
                List<CertTemplVo> certTemplVos = entry.getValue();
                if("1".equals(single)){
                    // 按summary 分组
                    Map<String,List<CertTemplVo>> summaryMap = certTemplVos.stream().collect(Collectors.groupingBy(CertTemplVo::getSummary));
                    for (Map.Entry<String, List<CertTemplVo>> entry1 : summaryMap.entrySet()) {
                        JSONArray ja = new JSONArray();
                        BigDecimal sum = new BigDecimal("0");
//                        String summary = entry1.getKey();
                        for (CertTemplVo certTemplVo : entry1.getValue()) {
                            // 金额 0 不添加
                            BigDecimal m = BigDecimal.ZERO;
                            String summary1 = StrUtil.isBlank(certTemplVo.getSummary()) ? defSummary : certTemplVo.getSummary();
                            // 替换[YYYY][MM]
                            summary1 = summary1.replace("[YYYY]",cyear).replace("[MM]",cmonth);
                            JSONObject jo = new JSONObject();
                            jo.set("comments",summary1);
                            if(StrUtil.isNotBlank(certTemplVo.getDebit())){
                                jo.set("basePostedDr", certTemplVo.getDebit());
//                                sum = sum.add(new BigDecimal(certTemplVo.getDebit()));
                                m = m.add(new BigDecimal(certTemplVo.getDebit()));
                            }
                            if(StrUtil.isNotBlank(certTemplVo.getCredit())){
                                jo.set("basePostedCr", certTemplVo.getCredit());
                                sum = sum.add(new BigDecimal(certTemplVo.getCredit()));
                                m = m.add(new BigDecimal(certTemplVo.getCredit()));
                            }
                            if(m.compareTo(BigDecimal.ZERO)<=0){
                                continue;
                            }
                            JSONObject acode = new JSONObject();
                            acode.set("code", certTemplVo.getSubject());
                            jo.set("glAccount", acode);
                            ja.add(jo);
                        }
                        sendAndSavePingZheng(month,processId,tmplCode,ja,groupCode,sum,appCode);
                    }

                }else{
                    JSONArray ja = new JSONArray();
                    BigDecimal sum = new BigDecimal("0");
                    for (CertTemplVo certTemplVo : certTemplVos) {
                        BigDecimal m = BigDecimal.ZERO;
                        String summary = StrUtil.isBlank(certTemplVo.getSummary()) ? defSummary : certTemplVo.getSummary();
                        // 替换[YYYY][MM]
                        summary = summary.replace("[YYYY]",cyear).replace("[MM]",cmonth);
                        JSONObject jo = new JSONObject();
                        jo.set("comments",summary);
                        if(StrUtil.isNotBlank(certTemplVo.getDebit())){
                            jo.set("basePostedDr", certTemplVo.getDebit());
//                            sum = sum.add(new BigDecimal(certTemplVo.getDebit()));
                            m = m.add(new BigDecimal(certTemplVo.getDebit()));
                        }
                        if(StrUtil.isNotBlank(certTemplVo.getCredit())){
                            jo.set("basePostedCr", certTemplVo.getCredit());
                            sum = sum.add(new BigDecimal(certTemplVo.getCredit()));
                            m = m.add(new BigDecimal(certTemplVo.getCredit()));
                        }
                        if(m.compareTo(BigDecimal.ZERO)<=0){
                            continue;
                        }
                        JSONObject acode = new JSONObject();
                        acode.set("code", certTemplVo.getSubject());
                        jo.set("glAccount", acode);
                        ja.add(jo);
                    }
                    sendAndSavePingZheng(month,processId,tmplCode,ja,groupCode,sum,appCode);
                }

            }
//            return "1";
            result.put("success",true);
            result.put("msg","发送成功");
            return result;
        }
        // 请求好会计，保存数据库
        if(tmplCode.equals(SALARY_PAYING)){
            String defSummary = tmplCode.equals(SALARY_PAYEND) ? SALARY_PAYEND_SUMMARY :SALARY_PAYING_SUMMARY;
//            JSONArray ja = JSONUtil.parseArray(list);
            String cyear = month.substring(0,4);
            String cmonth = month.substring(4,6);
            String groupCode = null;
            JSONArray ja = new JSONArray();
            BigDecimal total = new BigDecimal(0);
            for (CertTemplVo certTemplVo : list) {
                JSONObject jo = new JSONObject();
                BigDecimal m = BigDecimal.ZERO;
                String summary = StrUtil.isBlank(certTemplVo.getSummary()) ? defSummary : certTemplVo.getSummary();
                // 替换[YYYY][MM]
                summary = summary.replace("[YYYY]",cyear).replace("[MM]",cmonth);
                jo.set("comments",summary);
//                jo.set("comments", certTemplVo.getSummary());
                if(StrUtil.isNotBlank(certTemplVo.getDebit())){
                    jo.set("basePostedDr", certTemplVo.getDebit());
//                    total = total.add(new BigDecimal(certTemplVo.getDebit()));
                    m = m.add(new BigDecimal(certTemplVo.getDebit()));
                }
                if(StrUtil.isNotBlank(certTemplVo.getCredit())){
                    jo.set("basePostedCr", certTemplVo.getCredit());
                    total = total.add(new BigDecimal(certTemplVo.getCredit()));
                    m = m.add(new BigDecimal(certTemplVo.getCredit()));
                }
                if(m.compareTo(BigDecimal.ZERO)<=0){
                    continue;
                }
                JSONObject acode = new JSONObject();
                acode.set("code", certTemplVo.getSubject());
                jo.set("glAccount", acode);

                ja.add(jo);
            }
            // 新增 工资 预付款

            return sendAndSavePingZheng(month,processId,tmplCode,ja,groupCode,total,appCode);
        }
        String defSummary = "";
        if(tmplCode.equals(TRAVEL_PAYEND) || tmplCode.equals(TRAVEL_PAYING)) {
            defSummary = tmplCode.equals(TRAVEL_PAYEND) ? TRAVEL_PAYEND_SUMMARY : TRAVEL_PAYING_SUMMARY;
        }
//        if(list.size() > 0){
            JSONArray ja = new JSONArray();
            BigDecimal total = new BigDecimal(0);
            for (CertTemplVo certTemplVo : list) {
                JSONObject jo = new JSONObject();
                BigDecimal m = BigDecimal.ZERO;
                String summary = StrUtil.isBlank(certTemplVo.getSummary()) ? defSummary : certTemplVo.getSummary();
                String fullName = "";
                if(codeJson!=null && null != codeJson.getJSONObject("data")){
                    fullName = codeJson.getJSONObject("data").getStr("bak_full_name","");
                }
                summary = summary.replace("[username]",fullName);
                jo.set("comments",summary);
//                jo.set("comments", certTemplVo.getSummary());
                if(StrUtil.isNotBlank(certTemplVo.getDebit())){
                    jo.set("basePostedDr", certTemplVo.getDebit());
//                    total = total.add(new BigDecimal(certTemplVo.getDebit()));
                    m = m.add(new BigDecimal(certTemplVo.getDebit()));
                }
                if(StrUtil.isNotBlank(certTemplVo.getCredit())){
                    jo.set("basePostedCr", certTemplVo.getCredit());
                    total = total.add(new BigDecimal(certTemplVo.getCredit()));
                    m = m.add(new BigDecimal(certTemplVo.getCredit()));
                }
                if(m.compareTo(BigDecimal.ZERO)<=0){
                    continue;
                }
                JSONObject acode = new JSONObject();
                acode.set("code", certTemplVo.getSubject());
                jo.set("glAccount", acode);

                ja.add(jo);
            }
            return sendAndSavePingZheng(month,processId,tmplCode,ja,null,total,appCode);
//        }
//        return null;
//        result.put("success",false);
//        result.put("msg","没有数据");
////            return null;
//        return result;

    }


    private List<JSONObject> getPerGzByUrls(JSONArray ja,String templCode) {
        String[] execls = {".xlsx", ".xls"};
        List<String> fileUrlList = new ArrayList<>();
        for(int i=0;i<ja.size();i++){
            JSONObject fromValue = ja.getJSONObject(i);
            if(fromValue==null || fromValue.size()==0) {
                continue;
            }
            String fileName = fromValue.getStr("name");
            if (StrUtil.isNotBlank(fileName)) {
                for (String ext : execls) {
                    if (fileName.endsWith(ext)) {
                        JSONObject response =  fromValue.getJSONObject("response");
                        if(response==null || response.size()==0){
                            continue;
                        }
                        fileUrlList.add(response.getStr("url",null));
                        break;
                    }
                }

            }
        }
        // 获取工资表数据
        if(fileUrlList.size()==0){
            return null;
        }
        CertTempl certTempl = getCertTempl(templCode);
        if(certTempl==null){
            return null;
        }
        List<CertTemplRelation> templList = getCertTemplRelationById(certTempl.getId());
        Map<String,String> fName = new HashMap<>();
        String checkName = null;
        JSONArray fList = JSONUtil.parseArray(certTempl.getTemplData());
        // 获取 计算字段与模版字段对应关系
        for (int i = 0; i < fList.size(); i++) {
            if(i==0){
                checkName = fList.getJSONObject(i).getStr("value");
            }
            JSONObject f = fList.getJSONObject(i);
            fName.put(f.getStr("name"),f.getStr("value"));
        }
        // 获取工资表
        for(String fileUrl : fileUrlList){
            if(StrUtil.isBlank(fileUrl)){
                continue;
            }
//            JSONArray jsonObject = getExcelDataArray(fileUrl,templList,fName,checkName,null);
            List<JSONObject> jsonObject  =   persionFileUrl(fileUrl,templList,fName,checkName );
            if(jsonObject!=null && jsonObject.size()>0){
                return jsonObject;
            }
        }
        return null;
    }

    @Override
    public Map<String, Object> getGzTemplate(JSONArray ja) {
        Map<String, Object> map = new HashMap<>();

        List<JSONObject> jsonObjectIng = getPerGzByUrls(ja, SALARY_PAYING);
        List<JSONObject> jsonEnd = getPerGzByUrls(ja, SALARY_PAYEND);

        List<JSONObject> travelIng = new ArrayList<>();
        List<JSONObject> travelEnd = new ArrayList<>();
        List<JSONObject> travelPersonEnd = new ArrayList<>();

        Map<String,BigDecimal> moneyMap;
        if (jsonObjectIng != null && jsonObjectIng.size() > 0) {
            List<CertTemplRelation> templList = getCertTemplRelation(SALARY_PAYING);
            moneyMap=new HashMap<>();
            for (JSONObject o : jsonObjectIng) {
                for (String key : o.keySet()) {
                    if ("key".equals(key)) {
                        continue;
                    }
                    BigDecimal m = moneyMap.getOrDefault(key, new BigDecimal(0));
                    m = m.add(new BigDecimal(o.getStr(key)));
                    moneyMap.put(key, m);
                }
            }
            for (CertTemplRelation certTemplRelation : templList) {
                JSONObject jo = new JSONObject();
                jo.set("summary", certTemplRelation.getName());
                jo.set("abstract", certTemplRelation.getTemplAbstract());
                jo.set("subject", certTemplRelation.getAccountingSubjects());
                jo.set("debit", certTemplRelation.getBorrowing()==1?moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)):"0");
                jo.set("credit", certTemplRelation.getBorrowing()==1?"0":moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)));
                jo.set("groupCode", certTemplRelation.getGroupCode());
                jo.set("money", moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)));
                jo.set("borrowing", certTemplRelation.getBorrowing());
                travelIng.add(jo);
            }
            map.put(SALARY_PAYING, travelIng);
        }
        if(jsonEnd!=null && jsonEnd.size()>0){
            moneyMap = new HashMap<>();
            List<CertTemplRelation> templListEnd = getCertTemplRelation(SALARY_PAYEND);
            for (JSONObject o : jsonEnd) {
                for (String key : o.keySet()) {
                    if ("key".equals(key)) {
                        continue;
                    }
                    BigDecimal m = moneyMap.getOrDefault(key, new BigDecimal(0));
                    m = m.add(new BigDecimal(o.getStr(key)));
                    moneyMap.put(key, m);
                }
            }
            for (CertTemplRelation certTemplRelation : templListEnd) {
                JSONObject jo = new JSONObject();
                jo.set("summary", certTemplRelation.getName());
                jo.set("abstract", certTemplRelation.getTemplAbstract());
                jo.set("money", moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)));
                jo.set("borrowing", certTemplRelation.getBorrowing());
                jo.set("subject", certTemplRelation.getAccountingSubjects());
                jo.set("groupCode", certTemplRelation.getGroupCode());

                jo.set("debit", certTemplRelation.getBorrowing()==1?moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)):"0");
                jo.set("credit", certTemplRelation.getBorrowing()==1?"0":moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)));
                travelEnd.add(jo);
            }
            map.put(SALARY_PAYEND, travelEnd);
            for (JSONObject jsonObject1 : jsonEnd) {
                if(jsonObject1==null || jsonObject1.size()==0){
                    continue;
                }
                for (CertTemplRelation certTemplRelation : templListEnd) {
                    if(!"1".equals(certTemplRelation.getGroupCode())){
                        continue;
                    }
                    JSONObject jo = new JSONObject();
                    jo.set("summary", jsonObject1.getStr("key")+"-"+certTemplRelation.getName());
                    jo.set("abstract", certTemplRelation.getTemplAbstract());
                    jo.set("money", jsonObject1.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)));
                    jo.set("borrowing", certTemplRelation.getBorrowing());
                    jo.set("subject", certTemplRelation.getAccountingSubjects());
                    jo.set("groupCode", certTemplRelation.getGroupCode());

                    jo.set("debit", certTemplRelation.getBorrowing()==1?jsonObject1.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)):"0");
                    jo.set("credit", certTemplRelation.getBorrowing()==1?"0":jsonObject1.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)));
                    travelPersonEnd.add(jo);
                }
            }

            for (CertTemplRelation certTemplRelation : templListEnd) {
                if( "1".equals(certTemplRelation.getGroupCode())){
                    continue;
                }
                JSONObject jo = new JSONObject();
                jo.set("summary", certTemplRelation.getName());
                jo.set("abstract", certTemplRelation.getTemplAbstract());
                jo.set("money", moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)));
                jo.set("borrowing", certTemplRelation.getBorrowing());
                jo.set("subject", certTemplRelation.getAccountingSubjects());
                jo.set("groupCode", certTemplRelation.getGroupCode());

                jo.set("debit", certTemplRelation.getBorrowing()==1?moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)):"0");
                jo.set("credit", certTemplRelation.getBorrowing()==1?"0":moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(), new BigDecimal(0)));
                travelPersonEnd.add(jo);
            }

            map.put(SALARY_PAYEND+"_person", travelPersonEnd);
        }
        return map;
    }

    @Override
    public Map<String, Object> getWageTemplate(JSONArray ja) {
        //
        Map<String,Object> map = new HashMap<>();
        BigDecimal money = new BigDecimal(0);
        List<JSONObject> jsonObject = getPerGzByUrls(ja,SALARY_PAYING);
        if(jsonObject!=null && jsonObject.size()>0){
            List<CertTemplRelation> templList = getCertTemplRelation(SALARY_PAYING);
            Map<String,BigDecimal> moneyMap = new HashMap<>();
            for(JSONObject o : jsonObject){
                for(String key : o.keySet()){
                    if("key".equals(key)){
                        continue;
                    }
                    BigDecimal m = moneyMap.getOrDefault(key,new BigDecimal(0));
                    m = m.add(new BigDecimal(o.getStr(key)));
                    moneyMap.put(key,m);
                }
            }

            for(CertTemplRelation certTemplRelation : templList){
                if(certTemplRelation.getBorrowing()==1){
                    continue;
                }
                money = money.add(moneyMap.getOrDefault(certTemplRelation.getTemplAbstract(),new BigDecimal(0)));
            }

        }
        map.put("data",money);
        return map;
    }

    @Override
    public String updateVoucher(JSONObject j) {
        // 查询code
        LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CertData::getHaokjId, j.getStr("id"));
        List<CertData> certDatas = certDataMapper.selectList(queryWrapper);
        if(certDatas==null || certDatas.size()==0){
            return null;
        }
        String appCode = certDatas.get(0).getAppCode();
        if(StrUtil.isBlank(appCode)){
            appCode = haokjAppCode;
        }
        String currentBookId = getCurrentBookId(appCode);
        if(StrUtil.isBlank(currentBookId)){
            return null;
        }
        // 修改凭证
        String url = "/accounting/gl/AcctgTrans/"+currentBookId;
        JSONObject body = new JSONObject();
        body.set("id", j.getStr("id"));
        body.set("code", j.getStr("voucherNo"));
        body.set("acctgPeriod",j.getStr("period"));
        body.set("isSave",false);
        body.set("boName","AcctgTrans");
        body.set("bizDate",j.getStr("voucherDate"));
        body.set("isCodeType",true);
        body.set("categoryCodeExist",true);
        body.set("acctgTransCategoryId",100001);
        body.set("bizTypeId",100501);
        body.set("carryForwardTemplateEnum","final1");
        body.set("isFinal",false);
        JSONArray detailList = new JSONArray();
        JSONArray ds = j.getJSONArray("details");
        for(int i=0;i<ds.size();i++){
            JSONObject d = ds.getJSONObject(i);
            JSONObject o = new JSONObject();
            o.set("comments",d.getStr("comments"));
            o.set("basePostedDr",d.getStr("basePostedDr"));
            o.set("basePostedCr",d.getStr("basePostedCr"));
            o.set("glAccount",new JSONObject().set("code", d.getJSONObject("glAccount").getStr("code")));
            detailList.add(o);
        }
        body.set("details",detailList);
        String result = haokjPut(url, body,appCode);
        return result;
    }

    @Override
    public Map<String, Object> getVocherListByCode(String id, String code) {
        DProcessInstance processInstance = getProcessInstance(id);
        if(processInstance==null){
            return null;
        }
        return voucherListByCode(code,id,processInstance.getFromvalue(),null,null,null,null,null);
    }
    private String companyIdByName(String name){
        if(StrUtil.isBlank(name)){
            return null;
        }
        LambdaQueryWrapper<WxDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxDepartment::getName,name);
        queryWrapper.eq(WxDepartment::getDelFlag,"0");
        List<WxDepartment> list = wxDepartmentMapper.selectList(queryWrapper);
        if(list==null || list.size()==0){
            return null;
        }
        return list.get(0).getId();
    }
    @Override
    public Map<String, Object> getInvoiceVocherListByCode(String id, String code,String dataType) {
        if("invoiceStatisticalPyf".equals(dataType)){
            Map<String,Object> map =  invoiceClient.getId(id);
            JSONObject invoiceRecord = JSONUtil.parseObj(map.getOrDefault("data",""));
            if(invoiceRecord==null){
                return null;
            }
            String companyId = companyIdByName(invoiceRecord.getStr("buyerName"));
            return voucherListByCode(code,id,invoiceRecord,null,null,null,companyId,null);
        }

        InvoiceRecord invoiceRecord = invoiceRecordService.getById(id);
        if(invoiceRecord==null){
            return null;
        }
        String companyId = null;
        if("in".equals(invoiceRecord.getOutInputInvoice())){
            companyId = companyIdByName(invoiceRecord.getBuyerName());
        }else{
            companyId = companyIdByName(invoiceRecord.getSellerName());
        }
        return voucherListByCode(code,id,JSONUtil.parseObj(invoiceRecord),null,null,null,companyId,null);
    }

    public JSONArray getListByPaymentWaterPayment(String id){
        Map<String,String> result =paymentCodeByRwId(id);
        if(result==null){
            return null;
        }
        String tmplCode = result.getOrDefault("code",null);
        if(StrUtil.isBlank(tmplCode)){
            return null;
        }
        // 判断是否已生成凭证
        LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(CertData::getAppCode, haokjAppCode);
        queryWrapper.eq(CertData::getProcessId, certDataGetIdGet(id,tmplCode));
        queryWrapper.eq(CertData::getCode, tmplCode);
        queryWrapper.eq(CertData::getAccModel,getAccModel());
        List<CertData> certDatas = certDataMapper.selectList(queryWrapper);
        if(certDatas!=null && certDatas.size()>0){
            return null;
        }
        PaymentWater pw = paymentWaterService.getById(id);
        if(pw==null){
            return null;
        }
        BigDecimal money = new BigDecimal(pw.getMoney());
        String fcName = result.getOrDefault("fcname", null);
        // 根据code 查询模版，根据name 、type、 money 填充模版内容
        List<CertTemplRelation> list = getCertTemplRelation(tmplCode);
        JSONArray moneyJson = new JSONArray();
        for(CertTemplRelation certTemplRelation : list) {
            // 是否有 - 号
//            String fieldCode = certTemplRelation.getFieldCode();

            JSONObject jo = new JSONObject();
            jo.set("summary", StrUtil.isNotBlank(fcName)?fcName:certTemplRelation.getName());
            jo.set("abstract", certTemplRelation.getTemplAbstract());
            jo.set("subject", certTemplRelation.getAccountingSubjects());
            jo.set("findSub",StrUtil.isNotBlank(certTemplRelation.getFindSub())?result.getOrDefault(certTemplRelation.getFindSub(),null):null);
            jo.set("debit", certTemplRelation.getBorrowing()==1?money:"0");
            jo.set("credit", certTemplRelation.getBorrowing()==1?"0":money);
            jo.set("money",money);
            jo.set("groupCode", certTemplRelation.getGroupCode());
            moneyJson.add(jo);
        }
        return moneyJson;

    }

    @Override
    public void saveCertTemplVo(CertTemplVo certTemplVo) {
//        CertTempl
//        CertTemplRelation
//        CertTempl certTempl=certTemplVo.getParent();
        this.save(certTemplVo);

        List<CertTemplRelation> certTemplRelations = certTemplVo.getChildren();
        for(CertTemplRelation certTemplRelation : certTemplRelations){
            certTemplRelation.setCertTemplId(certTemplVo.getId());
        }
        certTemplRelationService.saveBatch(certTemplRelations);
    }

    @Override
    public void updateCertTemplVo(CertTemplVo certTemplVo) {
//        CertTempl certTempl=certTemplVo.getParent();
//        this.updateById(certTempl);
        this.saveOrUpdate(certTemplVo);

        List<CertTemplRelation> certTemplRelations = certTemplVo.getChildren();
        for(CertTemplRelation certTemplRelation : certTemplRelations){
            certTemplRelation.setCertTemplId(certTemplVo.getId());
        }
        certTemplRelationService.saveOrUpdateBatch(certTemplRelations);
    }

    @Override
    public List<CertTemplVo> getCertTemplList(String name) {
//        List<CertTemplDto> list = certTemplMapper.getCertTemplList(name);
//        and ( ct.name like concat('%',#{name},'%')
//        or ctr.name like concat('%',#{name},'%')
//        or ctr.templ_abstract like concat('%',#{name},'%')
//        or ctr.field_summary like concat('%',#{name},'%')
//        or ctr.accounting_subjects like concat('%',#{name},'%')
//        or ctr.field_code like concat('%',#{name},'%')
//            )
        LambdaQueryWrapper<CertTempl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StrUtil.isNotBlank(name),CertTempl::getName,name);
        queryWrapper.orderByDesc(CertTempl::getCreateTime);
        List<CertTempl> plist = this.list(queryWrapper);
        LambdaQueryWrapper<CertTemplRelation> queryRelationWrapper = new LambdaQueryWrapper<>();
        queryRelationWrapper.orderByAsc(CertTemplRelation::getCertTemplId,CertTemplRelation::getOrdernum);
        List<CertTemplRelation> certTemplRelationList= certTemplRelationService.list(queryRelationWrapper);
        Map<Integer,List<CertTemplRelation>> relationMap = certTemplRelationList.stream().collect(Collectors.groupingBy(CertTemplRelation::getCertTemplId));
        List<CertTemplVo> result = new ArrayList<>();
        for(CertTempl certTempl : plist){
            CertTemplVo certTemplVo = new CertTemplVo();
//            certTemplVo.setParent(certTempl);
            BeanUtil.copyProperties(certTempl,certTemplVo);
            certTemplVo.setChildren(relationMap.get(certTempl.getId()));
            certTemplVo.setHasChildren(certTemplVo.getChildren()!=null && certTemplVo.getChildren().size()>0);
            result.add(certTemplVo);
        }
        return result;
    }

    @Override
    public JSONArray getListByReceiptPayment(String id) {
//        String tmplCode = receCodeByRwId(id);
        Map<String,String> result =receCodeByRwId(id);
        String tmplCode = result.getOrDefault("code",null);
        if(StrUtil.isBlank(tmplCode)){
            return null;
        }


        // 判断是否已生成凭证
        LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(CertData::getAppCode, haokjAppCode);
        queryWrapper.eq(CertData::getProcessId, certDataGetIdGet(id,tmplCode));
        queryWrapper.eq(CertData::getCode, tmplCode);
        queryWrapper.eq(CertData::getAccModel,getAccModel());
        List<CertData> certDatas = certDataMapper.selectList(queryWrapper);
        if(certDatas!=null && certDatas.size()>0){
            return null;
        }
        //// balance 金额,receviceType type ,customerName 付款
        ReceiveWater rw = receiveWaterService.getById(id);
        if(rw==null){
            return null;
        }
        BigDecimal money = rw.getBalance();
        Integer type = rw.getReceviceType();
        String customerName = rw.getCustomerName();
        String fcName = result.getOrDefault("fcname", null);
        // 根据code 查询模版，根据name 、type、 money 填充模版内容
        List<CertTemplRelation> list = getCertTemplRelation(tmplCode);
        JSONArray moneyJson = new JSONArray();
        for(CertTemplRelation certTemplRelation : list) {
            // 是否有 - 号
//            String fieldCode = certTemplRelation.getFieldCode();

            JSONObject jo = new JSONObject();
            jo.set("summary", StrUtil.isNotBlank(fcName)?fcName:certTemplRelation.getName());
            jo.set("abstract", certTemplRelation.getTemplAbstract());
            jo.set("subject", certTemplRelation.getAccountingSubjects());
            jo.set("findSub",StrUtil.isNotBlank(certTemplRelation.getFindSub())?result.getOrDefault(certTemplRelation.getFindSub(),null):null);
            jo.set("debit", certTemplRelation.getBorrowing()==1?money:"0");
            jo.set("credit", certTemplRelation.getBorrowing()==1?"0":money);
            jo.set("money",money);
            jo.set("groupCode", certTemplRelation.getGroupCode());
            moneyJson.add(jo);
        }
        return moneyJson;

    }

    @Override
    public Map<String, Object> getVocherStatusByReceIdAndCode(String ids, String code) {
        if(StrUtil.isBlank(ids)){
            return Collections.emptyMap();
        }
        // 获取凭证code，需要 name 金额类型 付款客户
        QueryWrapper<ReceiveWater> queryWrapperReceiveWater=new QueryWrapper<>();
        queryWrapperReceiveWater.lambda().in(ReceiveWater::getId, Arrays.asList(ids.split(",")));
        List<ReceiveWater> list = receiveWaterService.list(queryWrapperReceiveWater);
        if(list==null || list.size()==0){
            return Collections.emptyMap();
        }
        Map<String,Object> idsAndCode = new HashMap<>();
        Map<String,String> fcode = new HashMap<>();
        for(ReceiveWater rw:list){
            if(StrUtil.isBlank(rw.getFundsClassId())){
                continue;
            }
            String c;
            if(fcode.containsKey(rw.getFundsClassId())){
                c = fcode.get(rw.getFundsClassId());
            }else{
                Map<String,String> m = receCodeByFundId(rw.getFundsClassId());
                c = m.getOrDefault("code",null);
                if(StrUtil.isBlank(c)){
                    continue;
                }
                fcode.put(rw.getFundsClassId(),c);
            }
            // 根据code id 查询
            // 查询数量
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("code",c);
            LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(CertData::getAppCode, haokjAppCode);
            queryWrapper.eq(CertData::getProcessId,certDataGetIdGet( rw.getId(),c));
            queryWrapper.eq(CertData::getAccModel,getAccModel());
            queryWrapper.eq(CertData::getCode, c);
            Integer count = certDataMapper.selectCount(queryWrapper);
            jsonObject.set("count",count);
            idsAndCode.put(rw.getId(),jsonObject);
        }
        return idsAndCode;
    }

    @Override
    public Map<String, Object> getVocherStatusByPaymentIdAndCode(String ids, String code) {
        if(StrUtil.isBlank(ids)){
            return Collections.emptyMap();
        }
        // 获取凭证code，需要 name 金额类型 付款客户
        QueryWrapper<PaymentWater> queryWrapperReceiveWater=new QueryWrapper<>();
        queryWrapperReceiveWater.lambda().in(PaymentWater::getId, Arrays.asList(ids.split(",")));
        List<PaymentWater> list = paymentWaterService.list(queryWrapperReceiveWater);
        if(list==null || list.size()==0){
            return Collections.emptyMap();
        }
        Map<String,Object> idsAndCode = new HashMap<>();
        Map<String,String> fcode = new HashMap<>();
        for(PaymentWater rw:list){
            if(StrUtil.isBlank(rw.getFundsClassId())){
                continue;
            }
            String c;
            if(fcode.containsKey(rw.getFundsClassId())){
                c = fcode.get(rw.getFundsClassId());
            }else{
                Map<String,String> m = receCodeByFundId(rw.getFundsClassId());
                c = m.getOrDefault("code",null);
                if(StrUtil.isBlank(c)){
                    continue;
                }
                fcode.put(rw.getFundsClassId(),c);
            }
            // 根据code id 查询
            // 查询数量
            JSONObject jsonObject = new JSONObject();
            jsonObject.set("code",c);
            LambdaQueryWrapper<CertData> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(CertData::getAppCode, haokjAppCode);
            queryWrapper.eq(CertData::getProcessId,certDataGetIdGet( rw.getId(),c));
            queryWrapper.eq(CertData::getAccModel,getAccModel());
            queryWrapper.eq(CertData::getCode, c);
            Integer count = certDataMapper.selectCount(queryWrapper);
            jsonObject.set("count",count);
            idsAndCode.put(rw.getId(),jsonObject);
        }
        return idsAndCode;
    }

    @Value("${peizaidan.company.id}")
    private String peizaidanCompanyId;

    @Override
    public Map<String, Object> getStowageVocherListByCode(String id, String code) {
        // code
        if("receivableShipVouchers_pzd".equals(code)){
            // 查询船舶系统航次
            // 航次
//             费用 科目 金额 公司
            Map<String,Object> list = shipManageClient.getShipLineSummary(id);
            // 凭证
            return voucherListByCode(code,id,JSONUtil.parseObj(list.get("data")),null,null,null,peizaidanCompanyId,null);
        }
        if("receivableShipMonthVouchers_pzd".equals(code)){
            Map<String,Object> list = shipManageClient.shipCostRecordDetail(id);
            // 凭证
            return voucherListByCode(code,id,JSONUtil.parseObj(list),null,null,null,peizaidanCompanyId,null);
        }
        if("oilWater_pzd".equals(code) || "oilWaterConsumption_pzd".equals(code)){
            // oilWaterPzd
            Map<String,Object> list = shipManageClient.oilWaterDetail(id);
            log.info("oilWaterPzd:{}",list);
            return voucherListByCode(code,id,JSONUtil.parseObj(list.get("data")),null,null,null,peizaidanCompanyId,null);
        }
        // 根据id 查询配载单
      Map<String,Object>  result  = onAccountClient.getShipLineSummary(id);
        if(result==null || result.size()==0){
            return null;
        }

        return voucherListByCode(code,id,JSONUtil.parseObj(result),null,null,null,peizaidanCompanyId,null);

        // sumc 收入 costtype 费用类型，customerid 客户名称 price 金额 priceNo不含税金额 settlePrice单价 settleTong 吨位
        // yunFeiList 成本 costtype 费用类型 customerid 客户 settleTong settlePrice单价 settleTong 吨位
        // 根据配载单返回应收、应付凭证

    }

    @Override
    public Map<String, Object> getUpdateStowageVocherListByCode(String id, String code) {
        Map<String, Object> result = null;
        try {
            result = processClient.taskGet(id);
        } catch (JsonProcessingException e) {
            return null;
        }
        if(result==null || result.size()==0){
            return null;
        }

        return voucherListByCode(code,id,JSONUtil.parseObj(result),null,null,null,peizaidanCompanyId,null);
    }

    @Override
    public JSONArray getReceiveWaterAccountsById(String id) {
        ReceiveWater rw = receiveWaterService.getById(id);
        if(rw==null || StrUtil.isBlank(rw.getReceviceCompany())){
            return null;
        }
        LambdaQueryWrapper<WxDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxDepartment::getDeptId,rw.getReceviceCompany());
        List<WxDepartment> departments =  wxDepartmentService.list(queryWrapper);
        if(departments==null || departments.size()==0){
            return null;
        }
         String companyId = departments.get(0).getId();
        return getAccountByCompany(companyId);
    }

    @Override
    public JSONArray getPaymentWaterAccountsById(String id) {
        PaymentWater pw = paymentWaterService.getById(id);
        if(pw==null || StrUtil.isBlank(pw.getPaymentCompany())){
            return null;
        }
        LambdaQueryWrapper<WxDepartment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WxDepartment::getDeptId,pw.getPaymentCompany());
        List<WxDepartment> departments =  wxDepartmentService.list(queryWrapper);
        if(departments==null || departments.size()==0){
            return null;
        }
        String companyId = departments.get(0).getId();
        return getAccountByCompany(companyId);
    }

    public Map<String,String> receCodeByRwId(String id){
        ReceiveWater rw = receiveWaterService.getById(id);
        if(rw==null || null == rw.getFundsClassId()){
            return null;
        }
        return receCodeByFundId(rw.getFundsClassId());
    }
    public Map<String,String> paymentCodeByRwId(String id){
        PaymentWater rw = paymentWaterService.getById(id);
        if(rw==null || null == rw.getFundsClassId()){
            return null;
        }
        return receCodeByFundId(rw.getFundsClassId());
    }

    private Map<String,String> receCodeByFundId(String id){
        FundsClassification fc = fundsClassificationService.getById(id);
        Map<String,String> result = new HashMap<>();
        if(fc==null || null == fc.getCertTemplId()){
            return result;
        }
        CertTempl certTempl = certTemplMapper.selectById(fc.getCertTemplId());
        if(certTempl==null){
            return result;
        }

        result.put("code",certTempl.getCode());
        result.put("fcname",fc.getName());
        return result;
    }

    private String getCurrentBookId(String appCode){
        if(StrUtil.isBlank(appCode)){
            return null;
        }
        LambdaQueryWrapper<OpenTokenCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OpenTokenCompany::getAppCode, appCode);
        List<OpenTokenCompany> openTokenCompanies = openTokenCompanyMapper.selectList(queryWrapper);
        if(openTokenCompanies!=null && openTokenCompanies.size()>0){
            return openTokenCompanies.get(0).getBookid();
        }
        return null;
    }
    private Map<String,Object> sendAndSavePingZheng(String month,String processId,String tmplCode,JSONArray ja,String groupCode,BigDecimal money,String appCode){
        Map<String,Object> result = new HashMap<>();
        if(ja==null || ja.size()==0){
            result.put("msg","没有数据");
            result.put("success",false);
            return result;
        }
        if(StrUtil.isBlank(appCode)){
//            appCode = haokjAppCode;
            result.put("msg","没有账簿");
            result.put("success",false);
            return result;
        }
//        查询bookid
        String currentBookId = getCurrentBookId(appCode);
        if(StrUtil.isBlank(currentBookId)){
            result.put("msg","没有账簿");
            result.put("success",false);
            return result;
        }
        String url = "/accounting/gl/AcctgTrans4/"+currentBookId;
        // 新增 工资 预付款
//        String url = "/accounting/gl/AcctgTrans4/"+bookId;
//            String month = "202406";
        String code = "001";
        // 凭证月份是当前月，取当前时间，非当前月, 取month取1号
        String currDate = DateUtil.format(new Date(), "yyyyMM");
        Long bizDate;
        if(!month.equals(currDate)){
//                month+"01"
            bizDate = DateUtil.parse(month+"01").getTime();
        }else{
            bizDate = new Date().getTime();
        }

        String origCreatedUserName= SecurityUtils.getCurrentUsername();
        String createId = SecurityUtils.getCurrentUserId();
        Long acctgTransCategoryId=100001L;
        JSONObject body = new JSONObject();

        body.set("acctgPeriod",month);
        body.set("code",code);
        body.set("boName","AcctgTrans");
        body.set("bizDate",bizDate);
//        body.set("refVoucherId",processId);
        body.set("refVoucherId",certDataGetId(processId,tmplCode));
        body.set("refVoucherCode",tmplCode);
        body.set("isCodeType",true);
        body.set("categoryCodeExist",true);
        body.set("origCreatedUserName",origCreatedUserName);
        body.set("acctgTransCategoryId",acctgTransCategoryId);
        body.set("bizTypeId",100501L);
        body.set("isFinal",false);
        body.set("details",ja);


        JSONObject json = haokjRequestPost(url,body,appCode);
        if(json==null || json.getStr("id",null)==null){
//            return json.getStr("msg","保存失败");
            result.put("msg",json.getStr("msg","保存失败"));
            result.put("success",false);
            return result;
        }

        JSONObject allData = new JSONObject();
        allData.set("sysData",body);
        allData.set("haokjData",json);
        // 保存
        CertData certData = new CertData();
        certData.setProcessId(certDataGetId(processId,tmplCode)+"");
//        certData.setAppCode(haokjAppCode);
        if(StrUtil.isBlank(appCode)){
            certData.setAppCode(haokjAppCode);
        }else{
            certData.setAppCode(appCode);
        }
        certData.setCode(tmplCode);
        certData.setHaokjId(json.getStr("id"));
        certData.setCertCode(json.getStr("code"));
        certData.setDetails(JSONUtil.toJsonStr(allData));
        certData.setCreateId(createId);
        certData.setUpdateId(createId);
        certData.setSummary(ja.getJSONObject(0).getStr("comments"));
        certData.setMoney(money);
        certData.setGroupCode(groupCode);
        certData.setYearMonths(month);
        certDataMapper.insert(certData);
//        return json.getStr("id");
        result.put("data",json.getStr("id"));
        result.put("success",true);
        return result;
    }
    private List<CertDataProcess> certDataGetIdsGet(List<String> pIds,String tmpCode){
       /* LambdaQueryWrapper<CertDataProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CertDataProcess::getAppCode, haokjAppCode);
        queryWrapper.and(wrapper -> {
                    for (int i = 0; i < pIds.size(); i++) {
                        String pId = pIds.get(i);
                        wrapper.or(war -> war.eq(CertDataProcess::getProcessId, pId).eq(CertDataProcess::getCode, tmpCode));
                    }
                });
        List<CertDataProcess> certDatas = certDataProcessMapper.selectList(queryWrapper);
        return certDatas;*/
        List<String> codes = Collections.nCopies(pIds.size(), tmpCode);
        return certDataGetIdsGet(pIds,codes);
    }
    private List<CertDataProcess> certDataGetIdsGet(List<String> pIds,List<String> tmpCodes){
        LambdaQueryWrapper<CertDataProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CertDataProcess::getAppCode, haokjAppCode);
        queryWrapper.and(wrapper -> {
                    for (int i = 0; i < pIds.size(); i++) {
                        String pId = pIds.get(i);
                        String tmpCode = tmpCodes.get(i);
                        wrapper.or(war
                                -> war.eq(CertDataProcess::getProcessId, pId)
                                .eq(CertDataProcess::getCode, tmpCode));
                    }
                });
        List<CertDataProcess> certDatas = certDataProcessMapper.selectList(queryWrapper);
        // 无id 则 新增
        if(certDatas.size()<pIds.size()){
            // 将 certDatas 转换为 Map，方便快速查找
            Map<String, CertDataProcess> certDataMap = certDatas.stream()
                    .collect(Collectors.toMap(
                            cdp -> cdp.getProcessId() + "_" + cdp.getCode(), // 使用 processId 和 code 作为唯一键
                            Function.identity()
                    ));

            // 需要插入的记录列表
            List<CertDataProcess> insertList = new ArrayList<>();

            for (int i = 0; i < pIds.size(); i++) {
                String pId = pIds.get(i);
                String tmpCode = tmpCodes.get(i);
                String key = pId + "_" + tmpCode;

                // 如果记录不存在，则创建新对象并添加到插入列表
                if (!certDataMap.containsKey(key)) {
                    CertDataProcess certDataProcess = new CertDataProcess();
                    certDataProcess.setProcessId(pId);
                    certDataProcess.setAppCode(haokjAppCode);
                    certDataProcess.setCode(tmpCode);
                    insertList.add(certDataProcess);
                }
            }

            // 批量插入
            if (!insertList.isEmpty()) {
//                certDataProcessMapper.insertBatchSomeColumn(insertList); // MyBatis-Plus 批量插入
                certDataProcessService.saveBatch(insertList); // 使用 Service 批量插入
                certDatas.addAll(insertList); // 将新插入的记录添加到 certDatas
            }
        }

        return certDatas;
    }

    private Integer certDataGetIdGet(String processId,String tmplCode){
        LambdaQueryWrapper<CertDataProcess> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CertDataProcess::getAppCode, haokjAppCode);
        queryWrapper.eq(CertDataProcess::getProcessId, processId);
        queryWrapper.eq(CertDataProcess::getCode, tmplCode);
        List<CertDataProcess> certDatas = certDataProcessMapper.selectList(queryWrapper);
        if(certDatas!=null && certDatas.size()>0){
            return certDatas.get(0).getId();
        }
        return null;
    }
    private Integer certDataGetId(String processId,String tmplCode){
        Integer integer = certDataGetIdGet(processId, tmplCode);
        if(integer!=null){
            return integer;
        }
        CertDataProcess certDataProcess = new CertDataProcess();
        certDataProcess.setProcessId(processId);
        certDataProcess.setAppCode(haokjAppCode);
        certDataProcess.setCode(tmplCode);
        certDataProcessMapper.insert(certDataProcess);
        return certDataProcess.getId();
    }

    private CertTempl getCertTempl(String code){
        LambdaQueryWrapper<CertTempl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CertTempl::getAppCode, haokjAppCode);
        queryWrapper.eq(CertTempl::getCode, code);
        return certTemplMapper.selectOne(queryWrapper);
    }
    private List<CertTemplRelation> getCertTemplRelationById(Integer id){
        if(id!=null){
            LambdaQueryWrapper<CertTemplRelation> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(CertTemplRelation::getCertTemplId, id);
            queryWrapper1.orderByAsc(CertTemplRelation::getOrdernum,CertTemplRelation::getCerateTime);
            List<CertTemplRelation> list = certTemplRelationMapper
                    .selectList(queryWrapper1);
            return list;
        }
        return null;
    }
    private List<CertTemplRelation> getCertTemplRelation(String code){
        CertTempl certTempl = getCertTempl(code);
        if(certTempl!=null){
            LambdaQueryWrapper<CertTemplRelation> queryWrapper1 = new LambdaQueryWrapper<>();
            queryWrapper1.eq(CertTemplRelation::getCertTemplId, certTempl.getId());
            queryWrapper1.orderByAsc(CertTemplRelation::getOrdernum,CertTemplRelation::getCerateTime);
            List<CertTemplRelation> list = certTemplRelationMapper
                    .selectList(queryWrapper1);
            return list;
        }
        return null;
    }
    private DProcessInstance getProcessInstance(String processId){
        List<DProcessInstance> processInstance = mongoTemplate.find(new Query(Criteria.where("_id").is(processId)), DProcessInstance.class);
        if (CollectionUtil.isNotEmpty(processInstance)) {
            try {
//                flowCode = processInstance.get(0).getFlowCode();
//                formValue = processInstance.get(0).getFromvalue();
                return processInstance.get(0);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }
    private List<DProcessInstance> getProcessInstance(String ...processIds){
        List<DProcessInstance> processInstance = mongoTemplate.find(new Query(Criteria.where("_id").in(processIds)), DProcessInstance.class);
        return processInstance;
    }
    /*Date CertTemplListLastTime = new Date();
    List<CertTempl> certTemplListPriv = new ArrayList<>();
    private List<CertTempl> getCertTemplListByAutoTime(){
        if(certTemplListPriv.size()>0 && DateUtil.between(new Date(),CertTemplListLastTime, DateUnit.MINUTE)<10){
            return certTemplListPriv;
        }
        LambdaQueryWrapper<CertTempl> queryWrapper = new LambdaQueryWrapper<>();
        List<CertTempl> certTempls = certTemplMapper.selectList(queryWrapper);
        certTemplListPriv = certTempls;
        CertTemplListLastTime = new Date();
        return certTempls;
    }
    private List<CertTempl> getCertTemplListByAutoTime(String flowCode){
        List<CertTempl> certTempls = getCertTemplListByAutoTime();
        if(certTempls==null || certTempls.size()==0){
            return null;
        }
        List<CertTempl> result = new ArrayList<>();
        for(CertTempl ct:certTempls){
            if(flowCode.equals(ct.getFlowCode())){
                result.add(ct);
            }
        }
        return result;
    }*/
    private List<CertTempl> getCertTemplListByAutoTime(String flowCode){
        // StringRedisTemplate
//        StopWatch stopWatch = new StopWatch("getCertTemplListByAutoTime");
//        stopWatch.start();
        String key = "certTemplListByAutoTime:"+flowCode;
        // 从缓存中获取 5分钟过期
        List<CertTempl> certTempls = (List<CertTempl>) redisTemplate.opsForValue().get(key);
//        stopWatch.stop();
//        log.info(stopWatch.prettyPrint());
        if(certTempls!=null){
            return certTempls;
        }
//        StopWatch stopWatch1 = new StopWatch("getCertTemplListByAutoTime-1");
//        stopWatch1.start();
        LambdaQueryWrapper<CertTempl> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CertTempl::getFlowCode, flowCode);
        List<CertTempl> certTempls1 = certTemplMapper.selectList(queryWrapper);
//        stopWatch1.stop();
//        log.info(stopWatch1.prettyPrint());
//        StopWatch stopWatch2 = new StopWatch("getCertTemplListByAutoTime-2");
//        stopWatch2.start();
        redisTemplate.opsForValue().set(key,certTempls1,15, TimeUnit.MINUTES);
//        stopWatch2.stop();
//        log.info(stopWatch2.prettyPrint());
        return certTempls1;
    }
    private Map<String,JSONObject> codeByProcessIds(String type,String ...processIds){
//        StopWatch allStopWatch = new StopWatch("all");
//        allStopWatch.start();
//        StopWatch stopWatch = new StopWatch("2255");
//        stopWatch.start();
        List<DProcessInstance> processInstance = getProcessInstance(processIds);
//        stopWatch.stop();
//        log.info(stopWatch.prettyPrint());
        if(processInstance==null){
            return null;
        }
//        StopWatch stopWatch1 = new StopWatch("2265");
//        stopWatch1.start();
        Map<String,JSONObject> resultMap = new HashMap<>();
        String tmplCodeLast = "1".equals(type) ? PAYING : PAYEND;
        for(DProcessInstance pi:processInstance){
//            StopWatch stopWatch2 = new StopWatch("2265-1");
//            stopWatch2.start();
            String flowCode = pi.getFlowCode();
            JSONObject formValue = pi.getFromvalue();
//            stopWatch2.stop();
//            log.info(stopWatch2.prettyPrint());


            if(StrUtil.isNotBlank(flowCode) && formValue!=null) {
//                StopWatch stopWatch3 = new StopWatch("2265-2");
//                stopWatch3.start();
                formValue.set("tmplCodeLast_",tmplCodeLast);
                List<CertTempl> certTempls = getCertTemplListByAutoTime(flowCode);
//                stopWatch3.stop();
//                log.info(stopWatch3.prettyPrint());
                if(certTempls==null || certTempls.size()==0){
                    continue;
                }
//                StopWatch stopWatch4 = new StopWatch("2265-3");
//                stopWatch4.start();
                for(CertTempl ct:certTempls){
                    if(StrUtil.isNotBlank(ct.getConditionStr())){
                        boolean result = (Boolean) AviatorEvaluator.execute(ct.getConditionStr(), formValue);
                        if(!result){
                            continue;
                        }
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.set("code", ct.getCode());
                        jsonObject.set("data",formValue);
                        resultMap.put(pi.getId(),jsonObject);
                        break;
                    }
                }
//                stopWatch4.stop();
//                log.info(stopWatch4.prettyPrint());
            }
        }
//        stopWatch1.stop();
//        log.info(stopWatch1.prettyPrint());
//        allStopWatch.stop();
//        log.info(allStopWatch.prettyPrint());
        return resultMap;
    }
    // 根据流程id，应付 1 、已付 -1 获取对应凭证类型code
    private JSONObject codeByProcessId(String processId,String type){
//        List<DProcessInstance> processInstance = mongoTemplate.find(new Query(Criteria.where("_id").is(processId)), DProcessInstance.class);
        DProcessInstance processInstance = getProcessInstance(processId);
        if(processInstance==null){
            return null;
        }
        String flowCode = processInstance.getFlowCode();
        JSONObject formValue = processInstance.getFromvalue();
//        if (CollectionUtil.isNotEmpty(processInstance)) {
//            try {
//                flowCode = processInstance.get(0).getFlowCode();
//                formValue = processInstance.get(0).getFromvalue();
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//        String tmplCodeLast = type.equals("1") ? PAYING : PAYEND;
        String tmplCodeLast = "1".equals(type) ? PAYING : PAYEND;
        JSONObject jsonObject = new JSONObject();

        if(StrUtil.isNotBlank(flowCode) && formValue!=null) {
            jsonObject.set("data",formValue);
            formValue.set("tmplCodeLast_",tmplCodeLast);
//            LambdaQueryWrapper<CertTempl> queryWrapper = new LambdaQueryWrapper<>();
//            queryWrapper.eq(CertTempl::getFlowCode, flowCode);
//            List<CertTempl> certTempls = certTemplMapper.selectList(queryWrapper);
            List<CertTempl> certTempls = getCertTemplListByAutoTime(flowCode);
            if(certTempls==null || certTempls.size()==0){
                return jsonObject;
            }
            for(CertTempl ct:certTempls){
                if(StrUtil.isNotBlank(ct.getConditionStr())){
                    boolean result = (Boolean) AviatorEvaluator.execute(ct.getConditionStr(), formValue);
                    if(!result){
                        continue;
                    }
                    jsonObject.set("code", ct.getCode());
                    break;
                }
            }


           /* // 工资
            if (PaymentConfig.ExternalPaymentApplication.equals(flowCode) && "是".equals(formValue.getOrDefault("shebao", "否"))) {
                String tmplCode = SALARY+ SPLI + tmplCodeLast;
                jsonObject.set("code", tmplCode);
            }
            // 差旅
            if(PaymentConfig.TravelReimbursement.equals(flowCode)){
                String tmplCode = TRAVEL + SPLI + tmplCodeLast;
                jsonObject.set("code", tmplCode);
            }
            // 报销
            if(PaymentConfig.Reimbursement.equals(flowCode)){
                // 是否业务招待费
                String processJian = formValue.getStr("processJian", "");
                String tmplCode;
                if("YWZD".equals(processJian)){
                     tmplCode = BUSINESS + SPLI + tmplCodeLast;
                }else{
                     tmplCode = EXPENSES + SPLI + tmplCodeLast;
                }
                jsonObject.set("code", tmplCode);
            }
            // 集团外部归还单位借款
            if(PaymentConfig.RepaymentOfLoansFromExternalEntitie.equals(flowCode)){
                // 只付款
                if(PAYEND.equals(tmplCodeLast)){
                    String tmplCode = GROUP_LOAN + SPLI + tmplCodeLast;
                    jsonObject.set("code", tmplCode);
                }
            }
            if(PaymentConfig.RepaymentOfLoansFromExternalEntitieInterest.equals(flowCode)){
                String tmplCode = GROUP_LOAN + SPLI + tmplCodeLast;
                jsonObject.set("code", tmplCode);
            }
            if(PaymentConfig.RepaymentOfPersonalLoansOutsideTheGroup.equals(flowCode) ){
                if(PAYEND.equals(tmplCodeLast)) {
                    String tmplCode = GROUP_PERSONAL + SPLI + tmplCodeLast;
                    jsonObject.set("code", tmplCode);
                }
            }
            if(PaymentConfig.RepaymentOfPersonalLoansOutsideTheGroupInterest.equals(flowCode) ){
                String tmplCode = GROUP_PERSONAL + SPLI + tmplCodeLast;
                jsonObject.set("code", tmplCode);
            }
            if(PaymentConfig.RepaymentOfLongTermAndShortTermBankLoan.equals(flowCode) ){
                if(PAYEND.equals(tmplCodeLast)) {
                    String tmplCode = GROUP_BANK + SPLI + tmplCodeLast;
                    jsonObject.set("code", tmplCode);
                }
            }
            if(PaymentConfig.RepaymentOfLongTermAndShortTermBankLoanInterest.equals(flowCode) ){
                String tmplCode = GROUP_BANK + SPLI + tmplCodeLast;
                jsonObject.set("code", tmplCode);
            }
            if(PaymentConfig.CurrentAccountApplication.equals(flowCode) ){
                // 判断是否银行账户资金调剂申请
                String processJian = formValue.getStr("processJian", "");
                String tmplCode;
                if("YHZHWLK".equals(processJian)){
                    tmplCode = CURRENT_ACCOUNT_BANK+ SPLI + tmplCodeLast;
                }else{
                    tmplCode = CURRENT_ACCOUNT+ SPLI + tmplCodeLast;
                }
                jsonObject.set("code", tmplCode);
            }
            if(PaymentConfig.AcceptanceDiscount.equals(flowCode) ){
                String tmplCode = ACCEPTANCE + SPLI + tmplCodeLast;
                jsonObject.set("code", tmplCode);
            }
            if(PaymentConfig.TaxTransferProcessApplication.equals(flowCode)){
                String tmplCode = TAX_TRANSFER + SPLI + tmplCodeLast;
                jsonObject.set("code", tmplCode);
            }
            if(PaymentConfig.BankingServiceTransferProcessApplication.equals(flowCode) && PAYEND.equals(tmplCodeLast)){
                String tmplCode = BANK_TRANSFER + SPLI + tmplCodeLast;
                jsonObject.set("code", tmplCode);
            }*/
            // 借款
//            if("LoanApplication".equals(flowCode) && !"1".equals(type)){
//
//            }
        }
        return jsonObject;
    }

    private JSONObject baseHaokjRequestPost(String url,String appKey,String appSecret,String openToken,String body){
        HttpRequest request = HttpRequest.post(baseUrl+url);
        request.header(Header.CONTENT_TYPE,"application/json");
        request.header("appKey",appKey);
        request.header("appSecret",appSecret);
        request.header("openToken",openToken);
        if(StrUtil.isNotBlank(body)){
            request.body(body);
        }
        String response = request.execute().body();
        return JSONUtil.parseObj(response);
    }
    private String baseHaokjRequestGet(String url,String appKey,String appSecret,String openToken,Map<String, Object> paramMap){
//        HttpRequest request = HttpRequest.get(baseUrl+url);
////        request.header(Header.CONTENT_TYPE,"application/json");
//        request.header(Header.CONTENT_TYPE,"application/json");
//        request.header("appKey",appKey);
//        request.header("appSecret",appSecret);
//        request.header("openToken",openToken);
//        if(paramMap!=null){
//            request.form(paramMap);
//        }
//        String response = request.execute().body();
        try {
            String uri = baseUrl + url;
            if (paramMap != null) {
                StringBuilder sb = new StringBuilder();
                for (String key : paramMap.keySet()) {
                    sb.append(key + "=" + paramMap.get(key) + "&");
                }
                sb.deleteCharAt(sb.length() - 1);
                String query = sb.toString();
                uri += "?" + query;
            }
            URL obj = new URL(uri);
            HttpURLConnection con = (HttpURLConnection) obj.openConnection();
            con.setRequestMethod("GET");
            con.setRequestProperty("appKey", appKey);
            con.setRequestProperty("appSecret", appSecret);
            con.setRequestProperty("openToken", openToken);
            con.setRequestProperty("Content-Type", "application/json");


            int responseCode = con.getResponseCode();
            if(responseCode==200){
                BufferedReader in = new BufferedReader(new InputStreamReader(con.getInputStream()));
                String inputLine;
                StringBuffer response = new StringBuffer();
                while ((inputLine = in.readLine()) != null) {
                    response.append(inputLine);
                }
                in.close();
                return response.toString();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String baseHaokjPut(String url,String appKey,String appSecret,String openToken,String body){
        HttpRequest request = HttpRequest.put(baseUrl+url);
        request.header(Header.CONTENT_TYPE,"application/json");
        request.header("appKey",appKey);
        request.header("appSecret",appSecret);
        request.header("openToken",openToken);
        if(StrUtil.isNotBlank(body)){
            request.body(body);
        }
        String response = request.execute().body();
        return response;
    }

    private JSONObject haokjRequestPost(String url,JSONObject body,String appCode){
        JSONObject json = openTokenService.getToken(appCode);
        if(json==null){
            return null;
        }
        return baseHaokjRequestPost(url,json.getStr("appKey"),json.getStr("appSecret"),json.getStr("openToken"),body.toJSONString(0));
    }

    private JSONObject haokjRequestGet(String url,Map<String, Object> paramMap,String appCode){
        JSONObject json = openTokenService.getToken(appCode);
        if(json==null){
            return null;
        }
        return JSONUtil.parseObj( baseHaokjRequestGet(url,json.getStr("appKey"),json.getStr("appSecret"),json.getStr("openToken"),paramMap));
    }

    private String haokjPut(String url,JSONObject body,String appCode){
        JSONObject json = openTokenService.getToken(appCode);
        if(json==null){
            return null;
        }
        return baseHaokjPut(url,json.getStr("appKey"),json.getStr("appSecret"),json.getStr("openToken"),body.toJSONString(0));
    }


    private JSONArray haokjRequestGetArray(String url,Map<String, Object> paramMap,String appCode){
        JSONObject json = openTokenService.getToken(appCode);
        if(json==null){
            return null;
        }
        return JSONUtil.parseArray( baseHaokjRequestGet(url,json.getStr("appKey"),json.getStr("appSecret"),json.getStr("openToken"),paramMap));
    }
}
