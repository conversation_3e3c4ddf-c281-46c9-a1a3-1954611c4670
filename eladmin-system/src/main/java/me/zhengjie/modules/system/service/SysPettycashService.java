package me.zhengjie.modules.system.service;

import me.zhengjie.base.BaseService;
import me.zhengjie.modules.system.domain.SysPettycash;
import me.zhengjie.modules.system.service.dto.SysPettycashDto;
import me.zhengjie.modules.system.service.dto.SysPettycashQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @description 服务接口
* <AUTHOR>
* @date 2021-12-27
**/
public interface SysPettycashService extends BaseService<SysPettycash> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SysPettycashQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SysPettycashDto>
    */
    List<SysPettycashDto> queryAll(SysPettycashQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SysPettycashDto> all, HttpServletResponse response) throws IOException;
}