package me.zhengjie.modules.system.domain.vo;

import lombok.Data;
import me.zhengjie.modules.system.domain.CertTempl;
import me.zhengjie.modules.system.domain.CertTemplRelation;

import java.util.List;

@Data
public class CertTemplVo extends CertTempl {

    private String summary;
    private String subject;
    private String subjectName;
    private String debit;
    private String credit;
    private String groupCode;
    private boolean hasChildren=false;

//    private CertTempl parent;
    private List<CertTemplRelation> children;

}
