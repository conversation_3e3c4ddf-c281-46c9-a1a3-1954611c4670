package me.zhengjie.modules.system.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description /
* <AUTHOR>
* @date 2025-01-15
**/
@Data
@TableName("cert_templ_temp")
public class CertTemplTemp implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String companyId;

    private Integer templId;

    private Integer relationId;

    private Integer accModel;

}