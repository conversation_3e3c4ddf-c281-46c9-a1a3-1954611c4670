/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.rest;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.oa.entity.WxDepartUser;
import me.zhengjie.annotation.AnonymousAccess;
import me.zhengjie.annotation.Log;
import me.zhengjie.modules.business.domain.SysExternalAccount;
import me.zhengjie.modules.business.service.SysCompanyPlateService;
import me.zhengjie.modules.business.service.SysExternalAccountService;
import me.zhengjie.modules.business.service.bean.UserCompany;
import me.zhengjie.modules.system.domain.SysExternalCustomer;
import me.zhengjie.modules.system.domain.SysExternalCustomerContact;
import me.zhengjie.modules.system.domain.SysExternalCustomerPlate;
import me.zhengjie.modules.system.domain.bean.SysExternalCustomerBean;
import me.zhengjie.modules.system.domain.vo.SysExternalCustomerAccountVo;
import me.zhengjie.modules.system.domain.vo.SysExternalCustomerVo;
import me.zhengjie.modules.system.service.SysExternalCustomerContactService;
import me.zhengjie.modules.system.service.SysExternalCustomerPlateService;
import me.zhengjie.modules.system.service.SysExternalCustomerService;
import me.zhengjie.modules.system.service.dto.SysExternalCustomerDto;
import me.zhengjie.modules.system.service.dto.SysExternalCustomerQueryCriteria;
import me.zhengjie.modules.wechat.code.service.WxDepartUserService;
import me.zhengjie.modules.wechat.code.service.WxDepartmentService;
import me.zhengjie.modules.wechat.code.service.dto.WxDepartUserDto;
import me.zhengjie.modules.wechat.code.service.dto.WxDepartUserQueryCriteria;
import me.zhengjie.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-01-25
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "externalCustomer管理")
@RequestMapping("/api/sysExternalCustomer")
public class SysExternalCustomerController {

    private final SysExternalCustomerService sysExternalCustomerService;
    private final SysExternalAccountService sysExternalAccountService;
    private final SysExternalCustomerContactService sysExternalCustomerContactService;

    private final SysCompanyPlateService sysCompanyPlateService;


    private final WxDepartmentService wxDepartmentService;
    private final SysExternalCustomerPlateService sysExternalCustomerPlateService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sysExternalCustomer:list')")
    public void download(HttpServletResponse response, SysExternalCustomerQueryCriteria criteria) throws IOException {
        sysExternalCustomerService.download(sysExternalCustomerService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询externalCustomer")
    @ApiOperation("查询externalCustomer")
    @PreAuthorize("@el.check('sysExternalCustomer:list')")
    public ResponseEntity<Object> query(SysExternalCustomerQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sysExternalCustomerService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @GetMapping(value = "/qureyCustomerAllOrDictId")
    public ResponseEntity<Object> qureyCustomerAllOrDictId(SysExternalCustomerQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sysExternalCustomerService.qureyCustomerAllOrDictId(criteria.getDictId(),criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增externalCustomer")
    @ApiOperation("新增externalCustomer")
    @PreAuthorize("@el.check('sysExternalCustomer:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody SysExternalCustomerBean resources){

        return new ResponseEntity<>(sysExternalCustomerService.createAndUpdateCreateId(resources,SecurityUtils.getCurrentUserId()),HttpStatus.CREATED);
    }

    @PostMapping(value = "/createAndPlate")
    public ResponseEntity<Object> createAndPlate(@Validated @RequestBody SysExternalCustomerBean resources){
       Integer customerId  = sysExternalCustomerService.createAndUpdateCreateIdAndSaveSysExternalCustomerPlate(resources,SecurityUtils.getCurrentUserId());
        // 绑定版块
        // 根据客户id 版块id添加绑定
        if (customerId == null) {
            return new ResponseEntity<>(HttpStatus.CREATED);
        }
        // 保存账户
        if(StrUtil.isNotBlank(resources.getSpare3()) || Objects.equals(resources.getAccountType(),1) || Objects.equals(resources.getAccountType(),2) || Objects.equals(resources.getAccountType(),3)){
            SysExternalAccount sysExternalAccount=new SysExternalAccount();
            sysExternalAccount.setBankAccount(StrUtil.isBlank(resources.getSpare3())?"无":resources.getSpare3());
            sysExternalAccount.setBankName(StrUtil.isBlank(resources.getSpare2())?"无":resources.getSpare2());
            sysExternalAccount.setCompanyName(resources.getName());
            sysExternalAccount.setSpare1(customerId+"");
            sysExternalAccount.setSpare2(resources.getName());
            sysExternalAccountService.accountSaveAndPlateByCustomerContactsAndCreateBy(sysExternalAccount,SecurityUtils.getCurrentUserId(),null,null);
        }
        Map<String, String> res=new HashMap<>();
        res.put("id",customerId+"");
        return new ResponseEntity<>(res,HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改externalCustomer")
    @ApiOperation("修改externalCustomer")
    @PreAuthorize("@el.check('sysExternalCustomer:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody SysExternalCustomer resources){
        sysExternalCustomerService.updateByCreateBy(resources,SecurityUtils.getCurrentUserId());
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除externalCustomer")
    @ApiOperation("删除externalCustomer")
    @PreAuthorize("@el.check('sysExternalCustomer:del')")
    @DeleteMapping
    public ResponseEntity<Object> delete(@RequestBody Integer[] ids) {
        sysExternalCustomerService.removeByIds(Arrays.asList(ids));
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @GetMapping(value = "/queryTree")
    public ResponseEntity<Object> queryTree(Integer id){
        JSONObject jo= sysExternalCustomerService.treeAll(id);
        return new ResponseEntity<>(jo,HttpStatus.OK);
    }

    @GetMapping(value = "/detail")
    public ResponseEntity<Object> detail(Integer id){
        SysExternalCustomer su= sysExternalCustomerService.getById(id);
        return new ResponseEntity<>(su,HttpStatus.OK);
    }

    @GetMapping(value = "/queryCustomerByDictId")
    public ResponseEntity<Object> queryCustomerByDictId(Integer dictId,String isCustomer,String isSupplier){
        List<SysExternalCustomer> list = sysExternalCustomerService.qureyCustomerByDictId(dictId,isCustomer,isSupplier);
        return new ResponseEntity<>(list,HttpStatus.OK);
    }

    @GetMapping(value = "/queryCustomerByDeptId")
    public ResponseEntity<Object> qureyCustomerByDeptId(Integer deptId,String isCustomer,String isSupplier){
        Integer dictid=null;
        if(deptId!=null){
            Long tmpId = sysCompanyPlateService.getPlateIdByDeptId(deptId+"");
            if(tmpId!=null){
                dictid=tmpId.intValue();
            }
        }
        List<SysExternalCustomer>  list = sysExternalCustomerService.qureyCustomerByDictId(dictid,isCustomer,isSupplier);
        return new ResponseEntity<>(list,HttpStatus.OK);
    }
    @GetMapping(value = "/list")
    public ResponseEntity<Object> list(SysExternalCustomerQueryCriteria criteria){
        List<SysExternalCustomerDto> list=sysExternalCustomerService.queryAll(criteria);
        return new ResponseEntity<>(list,HttpStatus.OK);
    }
    @GetMapping(value = "/customerListAll")
    public ResponseEntity<Object> customerListAll(SysExternalCustomerQueryCriteria criteria){
        List<SysExternalCustomer> list=sysExternalCustomerService.queryCustomerAll(criteria);
        return new ResponseEntity<>(list,HttpStatus.OK);
    }
//    @RequestParam(required = false,value = "cgwlId")String cgwlId, @RequestParam(required = false,value = "name")String name
//            , @RequestParam(required = false,value = "shortName")String shortName
//            , @RequestParam(required = false,value = "contractTitle")String contractTitle
//            , @RequestParam(required = false,value = "taxIdentificationNumber")String taxIdentificationNumber
//            , @RequestParam(required = false,value = "registeredAddress")String registeredAddress
//            , @RequestParam(required = false,value = "registeredPhone")String registeredPhone
//            , @RequestParam(required = false,value = "account")String account
//            , @RequestParam(required = false,value = "openingBank")String openingBank
    @AnonymousAccess
    @PostMapping(value = "/updateCustomerNameAndShortNameByCgwlId")
    public ResponseEntity<Object> updateCustomerNameAndShortNameByCgwlId(@RequestBody JSONObject resources
    ){
        sysExternalCustomerService.updateCustomerNameAndShortNameByCgwlId(resources);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @AnonymousAccess
    @GetMapping(value = "/listByCurrentUserAndDictId")
    public ResponseEntity<Object> listByCurrentUserAndDictId(SysExternalCustomerQueryCriteria criteria, Pageable pageable) {
        // 根据当前登陆用户查询版块，根据版块查询用户
        List<String> dicts=null;
        if(criteria.getDictId()!=null){
            dicts=Arrays.asList(criteria.getDictId()+"");
        }
        Map<String,Object> map = sysExternalCustomerService.qureyCustomerByDictIds(dicts,criteria,pageable);

        List<SysExternalCustomerVo> list = (List<SysExternalCustomerVo>)map.get("content");
        List<Integer> ids= list.stream().map(item->item.getId()).collect(Collectors.toList());

        LambdaQueryWrapper<SysExternalAccount> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.in(SysExternalAccount::getSpare1,ids);
        List<SysExternalAccount> list1= sysExternalAccountService.list(queryWrapper);

        LambdaQueryWrapper<SysExternalCustomerContact> queryWrapper1=new LambdaQueryWrapper<>();
        queryWrapper1.in(SysExternalCustomerContact::getCustomerId,ids);
        List<SysExternalCustomerContact> list2= sysExternalCustomerContactService.list(queryWrapper1);

        map.put("accountList",list1);
        map.put("contactList",list2);
        return new ResponseEntity<>(map,HttpStatus.OK);
    }

    @GetMapping(value = "/listByCurrentUser")
    public ResponseEntity<Object> listByCurrentUser(SysExternalCustomerQueryCriteria criteria, Pageable pageable){
        // 根据当前登陆用户查询版块，根据版块查询用户
//        WxDepartUserQueryCriteria queryCriteria=new WxDepartUserQueryCriteria();
//        queryCriteria.setUserId(SecurityUtils.getCurrentUserId());
//        List<WxDepartUserDto> list= wxDepartUserService.queryAll(queryCriteria);

            // 根据绑定公司查询版块
//            List<String> deptlist= list.stream().map(item->item.getDeptId()).collect(Collectors.toList());
            // 查询公司相关id
        List<UserCompany> list=   wxDepartmentService.getUserPlateInfoByUserId(SecurityUtils.getCurrentUserId(),null);
        if(list!=null && list.size()>0){
           List<String> dictIds= list.stream().filter(item->StrUtil.isNotBlank(item.getPlateId())).map(item->item.getPlateId()).collect(Collectors.toList());
            Map<String,Object> map= sysExternalCustomerService.qureyCustomerByDictIds(dictIds,criteria,pageable);

            return new ResponseEntity<>(map,HttpStatus.OK);
        }else{
            // 没有版块
            return new ResponseEntity<>(HttpStatus.OK);
        }



    }

    /** 加载甲乙方的公司数据以及公司下第一个的账号和联系人 */
    @GetMapping("/loadPartyAB")
    public ResponseEntity<Object> loadPartyAB(
            @RequestParam(required = false,value = "partyAName") String partyAName,
            @RequestParam(required = false,value = "partyBName") String partyBName
    ){
        Assert.isFalse(StrUtil.isBlank(partyAName) && StrUtil.isBlank(partyBName));
        Map<String, Object> map = new HashMap<>();
        if (StrUtil.isNotBlank(partyAName)) {
            SysExternalCustomerAccountVo partyA = sysExternalCustomerService.getCustomerByNameWithAccount(partyAName);
            if (partyA != null) {
                map.put("partyA", partyA);
            }
        }
        if (StrUtil.isNotBlank(partyBName)) {
            SysExternalCustomerAccountVo partyB = sysExternalCustomerService.getCustomerByNameWithAccount(partyBName);
            if (partyB != null) {
                map.put("partyB",partyB);
            }
        }

        return new ResponseEntity<>(map,HttpStatus.OK);
    }
}