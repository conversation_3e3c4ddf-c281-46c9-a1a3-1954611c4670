package me.zhengjie.modules.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import me.zhengjie.base.BaseServiceImpl;
import me.zhengjie.modules.business.service.bean.ReceiveWaterBean;
import me.zhengjie.modules.system.domain.ReceiveWater;
import me.zhengjie.modules.system.domain.ReceiveWaterSub;
import me.zhengjie.modules.system.domain.SysAccount;
import me.zhengjie.modules.system.mapper.ReceiveWaterSubMapper;
import me.zhengjie.modules.system.service.ReceiveWaterSubService;
import me.zhengjie.modules.system.service.SysAccountService;
import me.zhengjie.modules.system.service.dto.ReceiveWaterSubDto;
import me.zhengjie.modules.system.service.dto.ReceiveWaterSubQueryCriteria;
import me.zhengjie.modules.system.service.mapstruct.ReceiveWaterSubSMapper;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.utils.QueryHelpPlus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
* @description 服务实现
* <AUTHOR>
* @date 2022-05-09
**/
@Service
public class ReceiveWaterSubServiceImpl extends BaseServiceImpl<ReceiveWaterSubMapper,ReceiveWaterSub> implements ReceiveWaterSubService {

    @Resource
    private ReceiveWaterSubSMapper receiveWaterSubSMapper;
    @Resource
    private ReceiveWaterSubMapper receiveWaterSubMapper;
    @Autowired
    SysAccountService sysAccountService;

    @Override
    public Map<String,Object> queryAll(ReceiveWaterSubQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ReceiveWaterSubDto> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<ReceiveWaterSubDto> queryAll(ReceiveWaterSubQueryCriteria criteria){
        return listToDto(list(QueryHelpPlus.getPredicate(ReceiveWaterSub.class, criteria)),receiveWaterSubSMapper);
    }

    @Override
    public void download(List<ReceiveWaterSubDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ReceiveWaterSubDto receiveWaterSub : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("收款单id", receiveWaterSub.getReceiveWaterId());
            map.put("收款单编号", receiveWaterSub.getWaterNo());
            map.put("交易金额", receiveWaterSub.getBalance());
            map.put("创建者", receiveWaterSub.getCreateBy());
            map.put("创建时间", receiveWaterSub.getCreateTime());
            map.put("备注", receiveWaterSub.getRemark());
            map.put("逻辑删除标记，1：已删除，0：正常", receiveWaterSub.getDelFlag());
            map.put("付款客户id", receiveWaterSub.getSysCustomerId());
            map.put("客户或供应商：0：客户 1：供应商", receiveWaterSub.getCustorOrSupplier());
            map.put("核销状态：0未核销 1：已核销 2:已录入凭证", receiveWaterSub.getApplyStatus());
            map.put("账户类型：key:settlement_type 1 现金 2网银 3汇票", receiveWaterSub.getReceviceType());
            map.put("合同公司: 业务收款为物流系统字典表code,其他类型收款时，私户为plate_id,公户为dept_id", receiveWaterSub.getReceviceCompany());
            map.put("账户id", receiveWaterSub.getAccountId());
            map.put("票号", receiveWaterSub.getTicketNumber());
            map.put("付款银行", receiveWaterSub.getPaymentBank());
            map.put("付款账户", receiveWaterSub.getPaymentAccount());
            map.put("是否是预收款  0：不是 1 ：是", receiveWaterSub.getIsAdvance());
            map.put("收款银行", receiveWaterSub.getReceiveBank());
            map.put("收款账号", receiveWaterSub.getReceiveAccount());
            map.put("票种", receiveWaterSub.getTicketType());
            map.put("到期日", receiveWaterSub.getExpireDate());
            map.put("收款类型：key:fdbiz_cost_type", receiveWaterSub.getCostType());
            map.put("收款公司名", receiveWaterSub.getReceviceCompanyName());
            map.put("客户名", receiveWaterSub.getCustomerName());
            map.put("财务系统截图", receiveWaterSub.getPicList());
            map.put("合同id", receiveWaterSub.getContractId());
            map.put("审批id", receiveWaterSub.getPiId());
            map.put("发生时间", receiveWaterSub.getOccurDate());
            map.put("承兑人", receiveWaterSub.getAcceptorName());
            map.put("出票人", receiveWaterSub.getDrawerName());
            map.put("出票日", receiveWaterSub.getVotingDate());
            map.put("能否转让", receiveWaterSub.getIsTransferred());
            map.put("承兑汇票使用状态；0:未使用、其他:流程id", receiveWaterSub.getAcceptanceInProcessid());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
    private  List<ReceiveWaterBean> convertToBean(List<ReceiveWaterSub> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<ReceiveWaterBean> res = new ArrayList<>();
        for (ReceiveWaterSub water : list) {
            ReceiveWaterBean bean = new ReceiveWaterBean();
            BeanUtil.copyProperties(water, bean);
            res.add(bean);
        }
        return res;
    }
    public List<ReceiveWaterSub> getUnusedAcceptancesAll(String acceptance) {
        QueryWrapper<ReceiveWaterSub> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.isNull("acceptance_in_processid");
        queryWrapper.ne("ticket_number", "");
        if(StrUtil.isNotBlank(acceptance)){
            // like
            queryWrapper.like("ticket_number", acceptance);
        }
        queryWrapper.eq("recevice_type", 3);
        queryWrapper.orderByDesc("create_time");
        return this.list(queryWrapper);
    }



    @Override
    public List<ReceiveWaterSub> getAmountByAcceptanceId(String acceptanceId) {
        if(StrUtil.isBlank(acceptanceId)){
            return null;
        }
        QueryWrapper<ReceiveWaterSub> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.isNull("acceptance_in_processid");
        queryWrapper.ne("ticket_number", "");
        queryWrapper.eq("recevice_type", 3);
        queryWrapper.eq("id", acceptanceId);
//        ReceiveWaterSub receiveWaterSub = this.getOne(queryWrapper);
        return this.list(queryWrapper);
    }

    @Override
    public List<ReceiveWaterSub> getAmountByAcceptanceNumber(String acceptanceNumber) {
        if(StrUtil.isBlank(acceptanceNumber)){
            return null;
        }
        QueryWrapper<ReceiveWaterSub> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.isNull("acceptance_in_processid");
        queryWrapper.eq("recevice_type", 3);
        queryWrapper.eq("ticket_number", acceptanceNumber);
//        ReceiveWaterSub receiveWaterSub = this.getOne(queryWrapper);
        return this.list(queryWrapper);
    }

    @Override
    public List<ReceiveWaterSub> getAmountByAcceptanceNumbers(List<String> acceptanceNumber) {
        if(acceptanceNumber == null || acceptanceNumber.size() == 0){
            return null;
        }
        QueryWrapper<ReceiveWaterSub> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.isNull("acceptance_in_processid");
        queryWrapper.eq("recevice_type", 3);
        queryWrapper.in("ticket_number", acceptanceNumber);
        return this.list(queryWrapper);
    }

    @Override
    public List<String> checkAcceptance(String[] strs) {
        // 承兑号,金额
        if(strs == null || strs.length == 0){
            return null;
        }
        List<String> strArr = Arrays.asList(strs);
        // 获取承兑号
//        List<String> numbers = strs.stream().filter(str -> str.split(",").length == 2).map(str -> str.split(",")[0]).collect(Collectors.toList());
        List<String> numbers = strArr.stream().filter(str -> str.split(",").length == 2).map(str -> str.split(",")[0]).collect(Collectors.toList());
        if(numbers == null || numbers.size() == 0){
            return null;
        }
        List<ReceiveWaterSub> list = this.getAmountByAcceptanceNumbers(numbers);
        if(list == null || list.size() == 0){
            return null;
        }
        // 比对承兑号 金额，列出未命中的数据
//        List<String> moneys = strs.stream().filter(str -> str.split(",").length == 2).map(str -> str.split(",")[1]).collect(Collectors.toList());
        List<String> moneys = strArr.stream().filter(str -> str.split(",").length == 2).map(str -> str.split(",")[1]).collect(Collectors.toList());
        List<String> unMatched = new ArrayList<>();
        for (int i = 0; i < moneys.size(); i++) {
        // 比对承兑号 金额, 已比对成功的在list中去除，list ticket_number 承兑号，balance 金额
            String number = numbers.get(i);
            BigDecimal money = new BigDecimal(moneys.get(i));
            boolean isMat = true;
            for (ReceiveWaterSub receiveWaterSub : list) {
                if(Objects.equals(number, receiveWaterSub.getTicketNumber())
                        && money.compareTo(receiveWaterSub.getBalance()) == 0){
                    list.remove(receiveWaterSub);
                    isMat = false;
                    break;
                }
            }
            if(isMat) { // 没有匹配上的
                unMatched.add(number + "," + money);
            }
        }
        return unMatched;
    }

    @Override
    public List<ReceiveWaterBean> getUnusedAcceptances(String isShanDong) {

        // 根据山东公司 选择账户
        List<SysAccount> accounts = sysAccountService.getAccountsByCompany("山东");

         /*
           select rw.* from receive_water rw
                where rw.del_flag = 0
                and rw.acceptance_in_processid is null
                and rw.ticket_number is not null
                and rw.recevice_company = '10'
                and rw.recevice_type = 3
                and rw.cost_type = 1
            order by rw.create_time desc;
         */
        QueryWrapper<ReceiveWaterSub> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.isNull("acceptance_in_processid");
        queryWrapper.ne("ticket_number", "");
        queryWrapper.eq("recevice_company", "10");
        queryWrapper.eq("recevice_type", 3);
        queryWrapper.eq("cost_type", 1);
        if(Objects.equals(isShanDong, "1")){
            queryWrapper.in("account_id", accounts.stream().map(SysAccount::getId).collect(Collectors.toList()));
        }else{
            queryWrapper.notIn("account_id", accounts.stream().map(SysAccount::getId).collect(Collectors.toList()));
        }
        queryWrapper.orderByDesc("create_time");






        List<ReceiveWaterBean> list = convertToBean(this.list(queryWrapper));
        List<Integer> accountIds = list.stream()
                .map(ReceiveWater::getAccountId)
                .filter(i -> i != null)
                .distinct().collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(accountIds)) {
            QueryWrapper<SysAccount> queryWrapperAcc = new QueryWrapper<>();
            queryWrapperAcc.in("id", accountIds);
            List<SysAccount> sysAccounts = sysAccountService.list(queryWrapperAcc);
            list.forEach(receiveWater -> {
                SysAccount sysAccount = sysAccounts.stream()
                        .filter(sysAccount1 -> sysAccount1.getId().equals(receiveWater.getAccountId()))
                        .findFirst().orElse(null);
                if (sysAccount != null) {
                    receiveWater.setAccountName(sysAccount.getAccountName());
                    receiveWater.setBankName(sysAccount.getBankName());
                    receiveWater.setBankNum(sysAccount.getAccountNumber());
                }
            });
        }
        return list;
    }

    @Override
    public boolean updateAcceptanceInProcessid(String processId, String...ticket_number) {
        if (ticket_number.length == 0) {
            return false;
        }
        // update receive_water set acceptance_in_processid = ? where ticket_number in (?)
        UpdateWrapper<ReceiveWaterSub> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("ticket_number", ticket_number);
        updateWrapper.set("acceptance_in_processid", processId);

        return this.update(updateWrapper);
    }

    @Override
    public Page<ReceiveWaterSub> getReceiveWaterListByAccountId(Long accountId,  Date startTime, Date endTime, String[] number,Double balance,Integer pageNo,Integer pageSize) {
        com.github.pagehelper.Page<ReceiveWaterSub> page = PageHelper.startPage(pageNo, pageSize)
                .doSelectPage(() -> receiveWaterSubMapper.getReceiveWaterListByAccountId(
                        accountId,startTime,endTime,number,balance
                ));
        return page;
    }

    @Override
    public void updateCdsByStart(List<ReceiveWaterSub> list,String processId) {
        // 批量修改，承兑号，金额，del_flag=0，null=acceptance_in_processid,recevice_type=3, 修改acceptance_in_processid=processId
//        queryWrapper.eq("del_flag", 0);
//        queryWrapper.isNull("acceptance_in_processid");
//        queryWrapper.eq("recevice_type", 3);
        if(list==null || list.size()==0){
            return;
        }
        if(StrUtil.isBlank(processId)){
            return;
        }
        list.forEach(receiveWaterSub -> {
            UpdateWrapper<ReceiveWaterSub> updateWrapper=new UpdateWrapper<>();
            updateWrapper.eq("ticket_number",receiveWaterSub.getTicketNumber());
            updateWrapper.eq("del_flag",0);
            updateWrapper.isNull("acceptance_in_processid");
            updateWrapper.eq("recevice_type",3);
            updateWrapper.eq("balance",receiveWaterSub.getBalance());
            updateWrapper.set("acceptance_in_processid",processId);
            // 修改
            this.update(updateWrapper);
        });
    }

    @Override
    public void updateCdsByCanel(String processId) {
        if(StrUtil.isBlank(processId)){
            return;
        }
        UpdateWrapper<ReceiveWaterSub> updateWrapper=new UpdateWrapper<>();
        updateWrapper.eq("del_flag",0);
        updateWrapper.eq("recevice_type",3);
        updateWrapper.eq("acceptance_in_processid",processId);
        updateWrapper.set("acceptance_in_processid",null);
        // 修改
        this.update(updateWrapper);
    }

    @Override
    public List<ReceiveWaterSub> getUnusedAcceptancesAllByAccountId(String accoundId) {
        if(StrUtil.isBlank(accoundId)){
            return null;
        }
        QueryWrapper<ReceiveWaterSub> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("del_flag",0);
        queryWrapper.eq("recevice_type",3);
        queryWrapper.isNull("acceptance_in_processid");
        queryWrapper.in("account_id",accoundId.split(","));
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void splitTicket(String ticketId, BigDecimal money) {
        // 拆分汇票
        if(StrUtil.isBlank(ticketId)){
            return;
        }
        if(money==null || money.compareTo(BigDecimal.ZERO)<=0){
            return;
        }
        QueryWrapper<ReceiveWaterSub> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("id",ticketId);
        queryWrapper.eq("del_flag",0);
        queryWrapper.isNull("acceptance_in_processid");
        queryWrapper.eq("recevice_type",3);
        List<ReceiveWaterSub> list=this.list(queryWrapper);
        if(list==null || list.size()==0){
            return;
        }
        ReceiveWaterSub receiveWaterSub=list.get(0);
        if(receiveWaterSub==null){
            return;
        }
        if(receiveWaterSub.getBalance().compareTo(money)<0){
            return;
        }
        // 复制一份票，金额拆分
        ReceiveWaterSub newTicket=new ReceiveWaterSub();
        BeanUtil.copyProperties(receiveWaterSub,newTicket);
        newTicket.setId(null);
        newTicket.setBalance(money);
        this.save(newTicket);
        // 修改原票金额
        receiveWaterSub.setBalance(receiveWaterSub.getBalance().subtract(money));
        this.updateById(receiveWaterSub);

    }

    @Override
    public void updateTicketById(String ticketIds, String processId) {
        if(StrUtil.isBlank(ticketIds) || StrUtil.isBlank(processId)){
            return;
        }
        UpdateWrapper<ReceiveWaterSub> updateWrapper=new UpdateWrapper<>();
        updateWrapper.in("id",ticketIds.split(","));
        updateWrapper.set("acceptance_in_processid",processId);
        this.update(updateWrapper);
    }


}