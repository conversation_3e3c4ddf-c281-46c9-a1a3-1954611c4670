package me.zhengjie.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.zhengjie.modules.system.domain.ComeToPaymentWater;
import me.zhengjie.modules.system.domain.vo.ComeToPaymentWaterVo;
import me.zhengjie.modules.system.domain.vo.DeptMoneyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-11-15
 */
@Mapper
public interface ComeToPaymentWaterMapper extends BaseMapper<ComeToPaymentWater> {

    List<ComeToPaymentWater> getWaterList(
            @Param("companyDeptId") String companyDeptId,
            @Param("companyDeptIdOther") String companyDeptIdOther
    );
    
    List<ComeToPaymentWater> getWaterListByIds(
            @Param("companyDeptId") String companyDeptId,
            @Param("orCompanyDeptId")String orCompanyDeptId,
            @Param("startTime")Date startTime,
            @Param("endTime")Date endTime,
            @Param("isWangLaiType")String isWangLaiType,
            @Param("isNeiWai")String isNeiWai
    );
    
    List<ComeToPaymentWaterVo> getWaterVoList(
            @Param("companyDeptId") String companyDeptId,
            @Param("companyDeptIdOther") String companyDeptIdOther,
            @Param("bankOne") String bankOne,
            @Param("bankTwo") String bankTwo,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime,
            @Param("wlType") String wlType
    );
    // 不同公司的往来付款，同一个公司不统计
    @Select("<script>" +
            " select sum(cast(cw.money as DECIMAL(16,2))) money,cw.payment_company_id as dept_id from come_to_payment_water cw  " +
            "WHERE cw.payment_company_id!=cw.receive_company_id  " +
            "and cw.is_finish=1  " +
            "<if test='deptIds!=null and deptIds.size()>0'>  " +
            "and cw.payment_company_id in  " +
            "<foreach collection='deptIds' item='item' open='(' separator=',' close=')'>  " +
            "#{item}  " +
            "</foreach>  " +
            "</if>  " +
            "<if test='startDate!=null and startDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') >= #{startDate}  " +
            "</if>  " +
            "<if test='endDate!=null and endDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') &lt;= #{endDate}  " +
            "</if>  " +
            "GROUP by cw.payment_company_id"+
            "</script>")
    List<DeptMoneyVo> paySumMoneyDiffDept(@Param("deptIds")List<String> deptIds, @Param("startDate")String startDate, @Param("endDate")String endDate);
    // 不同公司的往来收款，同一个公司不统计
    @Select("<script>" +
            "select sum(cast(cw.money as DECIMAL(16,2))) money,cw.receive_company_id as dept_id from come_to_payment_water cw  " +
            "WHERE cw.payment_company_id!=cw.receive_company_id  " +
            "and cw.is_finish=1  " +
            "<if test='deptIds!=null and deptIds.size()>0'>  " +
            "and cw.receive_company_id in  " +
            "<foreach collection='deptIds' item='item' open='(' separator=',' close=')'>  " +
            "#{item}  " +
            "</foreach>  " +
            "</if>  " +
            "<if test='startDate!=null and startDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') >= #{startDate}  " +
            "</if>  " +
            "<if test='endDate!=null and endDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') &lt;= #{endDate}  " +
            "</if>  " +
            "GROUP by cw.receive_company_id"+
            "</script>")
    List<DeptMoneyVo> receiveSumMoneyDiffDept(@Param("deptIds")List<String> deptIds, @Param("startDate")String startDate, @Param("endDate")String endDate);

    // 往来款  付款 全统计
    @Select("<script>" +
            " select sum(cast(cw.money as DECIMAL(16,2))) money,cw.payment_company_id as dept_id from come_to_payment_water cw  " +
            "WHERE   " +
            " cw.is_finish=1  " +
            "<if test='deptIds!=null and deptIds.size()>0'>  " +
            "and cw.payment_company_id in  " +
            "<foreach collection='deptIds' item='item' open='(' separator=',' close=')'>  " +
            "#{item}  " +
            "</foreach>  " +
            "</if>  " +
            "<if test='startDate!=null and startDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') >= #{startDate}  " +
            "</if>  " +
            "<if test='endDate!=null and endDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') &lt;= #{endDate}  " +
            "</if>  " +
            "GROUP by cw.payment_company_id"+
            "</script>")
    List<DeptMoneyVo> paySumMoneyDept(@Param("deptIds")List<String> deptIds, @Param("startDate")String startDate, @Param("endDate")String endDate);
    // 往来款 收款 全统计
    @Select("<script>" +
            "select sum(cast(cw.money as DECIMAL(16,2))) money,cw.receive_company_id as dept_id from come_to_payment_water cw  " +
            "WHERE   " +
            " cw.is_finish=1  " +
            "<if test='deptIds!=null and deptIds.size()>0'>  " +
            "and cw.receive_company_id in  " +
            "<foreach collection='deptIds' item='item' open='(' separator=',' close=')'>  " +
            "#{item}  " +
            "</foreach>  " +
            "</if>  " +
            "<if test='startDate!=null and startDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') >= #{startDate}  " +
            "</if>  " +
            "<if test='endDate!=null and endDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') &lt;= #{endDate}  " +
            "</if>  " +
            "GROUP by cw.receive_company_id"+
            "</script>")
    List<DeptMoneyVo> receiveSumMoneyDept(@Param("deptIds")List<String> deptIds, @Param("startDate")String startDate, @Param("endDate")String endDate);

    @Select("<script>" +
            " select sum(cast(cw.money as DECIMAL(16,2))) money,cw.payment_company_id as dept_id from come_to_payment_water cw  " +
            "WHERE cw.payment_company_id=cw.receive_company_id  " +
            "and cw.is_finish=1  " +
            "<if test='deptIds!=null and deptIds.size()>0'>  " +
            "and cw.payment_company_id in  " +
            "<foreach collection='deptIds' item='item' open='(' separator=',' close=')'>  " +
            "#{item}  " +
            "</foreach>  " +
            "</if>  " +
            "<if test='startDate!=null and startDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') >= #{startDate}  " +
            "</if>  " +
            "<if test='endDate!=null and endDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') &lt;= #{endDate}  " +
            "</if>  " +
            "GROUP by cw.payment_company_id"+
            "</script>")
    List<DeptMoneyVo> paySumMoneyNeiDept(@Param("deptIds")List<String> deptIds, @Param("startDate")String startDate, @Param("endDate")String endDate);
    // 不同公司的往来收款，同一个公司不统计
    @Select("<script>" +
            "select sum(cast(cw.money as DECIMAL(16,2))) money,cw.receive_company_id as dept_id from come_to_payment_water cw  " +
            "WHERE cw.payment_company_id=cw.receive_company_id  " +
            "and cw.is_finish=1  " +
            "<if test='deptIds!=null and deptIds.size()>0'>  " +
            "and cw.receive_company_id in  " +
            "<foreach collection='deptIds' item='item' open='(' separator=',' close=')'>  " +
            "#{item}  " +
            "</foreach>  " +
            "</if>  " +
            "<if test='startDate!=null and startDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') >= #{startDate}  " +
            "</if>  " +
            "<if test='endDate!=null and endDate!=\"\"'>  " +
            "and DATE_FORMAT(ifnull(cw.update_time,cw.create_time),'%Y-%m-%d') &lt;= #{endDate}  " +
            "</if>  " +
            "GROUP by cw.receive_company_id"+
            "</script>")
    List<DeptMoneyVo> receiveSumMoneyNeiDept(@Param("deptIds")List<String> deptIds, @Param("startDate")String startDate, @Param("endDate")String endDate);
}