package me.zhengjie.modules.system.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import me.zhengjie.base.BaseService;
import me.zhengjie.modules.system.domain.CertTempl;
import me.zhengjie.modules.system.domain.CertTemplRelation;
import me.zhengjie.modules.system.domain.dto.CertTemplDto;
import me.zhengjie.modules.system.domain.vo.CertTemplVo;

import java.util.List;
import java.util.Map;

public interface CertTemplService extends BaseService<CertTempl> {
    // 根据id，type返回凭证列表
    Map<String,Object> getVoucherList(String id, String type, String single, String money, List<com.alibaba.fastjson.JSONObject> applys);

    Map<String,Object> getAcctgTransList(String id, String type);

    Map<String,Object> getAcctgTransListByCode(String id, String code);
    Map<String,Object> getIsCodeByPid(String ids, String type);

    Map<String,Object> getIsCodeByPidAndCode(String ids, String code);

    // 好会计科目
    Map<String,Object> getHaokjSub(String appCode);

    Map<String,Object> saveVoucher(String processId, String type,String month,List<CertTemplVo> list,String single,String appCode);

    Map<String,Object> getWageTemplate(JSONArray ja);

    Map<String,Object> getGzTemplate(JSONArray ja);

    String updateVoucher(JSONObject jsonObject);

    Map<String,Object> getVocherListByCode(String id,String code);

    Map<String,Object> getInvoiceVocherListByCode(String id,String code,String dataType);
// 根据id，type返回凭证列表
    JSONArray getListByReceiptPayment(String id);

    Map<String,Object> getVocherStatusByReceIdAndCode(String id,String code);

    Map<String,Object> getVocherStatusByPaymentIdAndCode(String id,String code);

    Map<String,Object> getStowageVocherListByCode(String id, String code);

    Map<String,Object> getUpdateStowageVocherListByCode(String id, String code);

    JSONArray getReceiveWaterAccountsById(String id);

    JSONArray getPaymentWaterAccountsById(String id);

    Map<String,String> receCodeByRwId(String id);



    Map<String,String> paymentCodeByRwId(String id);

    JSONArray getListByPaymentWaterPayment(String id);

    void saveCertTemplVo(CertTemplVo certTemplVo);

    void updateCertTemplVo(CertTemplVo certTemplVo);
    List<CertTemplVo> getCertTemplList(String name);
}
