package me.zhengjie.modules.system.service;

import com.tencentcloudapi.common.exception.TencentCloudSDKException;

public interface OcrService {
    /**
     * 识别票据
     * @param imgUrl
     * @return
     */
    public String billOcrByImgurl(String imgUrl) throws TencentCloudSDKException;

    public String billOcrByImgbase64(String imgBase64) throws TencentCloudSDKException;

    String invoiceOcrByImgurl(String imgUrl) throws TencentCloudSDKException;

    String recognizeOcrByImgurl(String imgUrl) throws TencentCloudSDKException;
}
