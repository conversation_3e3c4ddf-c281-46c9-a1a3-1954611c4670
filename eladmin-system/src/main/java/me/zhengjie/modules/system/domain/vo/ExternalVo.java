package me.zhengjie.modules.system.domain.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class ExternalVo {
    private Integer id;
    private Integer contactId;
    private Integer accountId;
    private Long dictId;
    private String dictName;
    private Integer isCustomer;
    private Integer isSupplier;
    private String createBy;
    private Timestamp createDate;
    private String updateBy;
    private Timestamp updateDate;
    @TableLogic
    private String delFlag;
}
