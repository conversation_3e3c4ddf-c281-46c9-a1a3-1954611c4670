/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-03-17
**/
@Data
public class SysAccountDto implements Serializable {

    private Integer id;

    /** 类型 */
    private String type;

    /** 银行名称 */
    private String bankName;

    /** 账户名称 */
    private String accountName;

    /** 账户号 */
    private String accountNumber;

    /** 初始化余额 */
    private Double initializationMoney;

    /** 字典表值 */
    private Integer dictionaryCode;

    private String createBy;

    private Timestamp createDate;

    private String updateBy;

    private Timestamp updateDate;

    private String delFlag;

    private String remarks;

    /** 贴现类型: 0:现金账户 1：贴现账户 2：库存现金 */
    private Integer discountOrCash;

    /** 公司部门id(公户) */
    private Integer deptId;

    /** 板块id (私户) */
    private Integer plateId;

    /** 账户性质：0 人民币账户 1 美元账户 */
    private Integer accountNationality;


}