package me.zhengjie.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import me.zhengjie.base.BaseServiceImpl;
import me.zhengjie.modules.system.domain.CertTemplRelation;
import me.zhengjie.modules.system.domain.CertTemplTemp;
import me.zhengjie.modules.system.mapper.CertTemplRelationMapper;
import me.zhengjie.modules.system.mapper.CertTemplTempMapper;
import me.zhengjie.modules.system.service.CertTemplRelationService;
import me.zhengjie.modules.system.service.CertTemplTempService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CertTemplTempServiceImpl extends BaseServiceImpl<CertTemplTempMapper, CertTemplTemp> implements CertTemplTempService {

    @Override
    public void saveTemp(List<CertTemplTemp> list) {
        if(list==null || list.size()==0){
            return;
        }
//        String companyId = list.get(0).getCompanyId();
//        Integer accModel = list.get(0).getAccModel();
//        Integer templId = list.get(0).getTemplId();
//        // 删除
//        LambdaQueryWrapper<CertTemplTemp> queryWrapper = new LambdaQueryWrapper<>();
//        queryWrapper.eq(CertTemplTemp::getCompanyId,companyId)
//                .eq(CertTemplTemp::getAccModel,accModel)
//                .eq(CertTemplTemp::getTemplId,templId);
//        this.remove(queryWrapper);
        this.removeAll(list.get(0));
        // 添加
        this.saveBatch(list);
    }

    @Override
    public void removeAll(CertTemplTemp certTemplTemp) {
        if(certTemplTemp==null){
            return;
        }
        LambdaQueryWrapper<CertTemplTemp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
//                .eq(CertTemplTemp::getCompanyId,certTemplTemp.getCompanyId())
                .eq(CertTemplTemp::getAccModel,certTemplTemp.getAccModel())
                .eq(CertTemplTemp::getTemplId,certTemplTemp.getTemplId());
        this.remove(queryWrapper);
    }
}
