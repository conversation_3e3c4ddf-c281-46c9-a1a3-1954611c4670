package me.zhengjie.modules.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.Page;
import me.zhengjie.modules.system.domain.ImprestFund;
import me.zhengjie.modules.system.domain.ImprestFundReocrd;
import me.zhengjie.base.BaseServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import me.zhengjie.modules.system.domain.bean.ImprestFundReocrdBean;
import me.zhengjie.modules.system.service.ImprestFundService;
import me.zhengjie.utils.QueryHelpPlus;
import me.zhengjie.utils.ValidationUtil;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.modules.system.mapper.ImprestFundReocrdMapper;
import me.zhengjie.modules.system.service.ImprestFundReocrdService;
import me.zhengjie.modules.system.service.dto.ImprestFundReocrdDto;
import me.zhengjie.modules.system.service.dto.ImprestFundReocrdQueryCriteria;
import me.zhengjie.modules.system.service.mapstruct.ImprestFundReocrdSMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.IdUtil;
import javax.annotation.Resource;

import org.springframework.data.domain.Pageable;
import me.zhengjie.utils.PageUtil;
import me.zhengjie.utils.QueryHelp;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.io.IOException;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

/**
* @description 服务实现
* <AUTHOR>
* @date 2022-08-12
**/
@Service
public class ImprestFundReocrdServiceImpl extends BaseServiceImpl<ImprestFundReocrdMapper,ImprestFundReocrd> implements ImprestFundReocrdService {

    @Resource
    private ImprestFundReocrdSMapper imprestFundReocrdSMapper;
    @Resource
    private ImprestFundReocrdMapper imprestFundReocrdMapper;
    @Resource
    private ImprestFundService imprestFundService;

    @Override
    public Map<String,Object> queryAll(ImprestFundReocrdQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<ImprestFundReocrdDto> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<ImprestFundReocrdDto> queryAll(ImprestFundReocrdQueryCriteria criteria){
        return listToDto(list(QueryHelpPlus.getPredicate(ImprestFundReocrd.class, criteria)),imprestFundReocrdSMapper);
    }

    @Override
    public void download(List<ImprestFundReocrdDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ImprestFundReocrdDto imprestFundReocrd : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("账户id", imprestFundReocrd.getImprestFundId());
            map.put("消费日期", imprestFundReocrd.getConsumptionDate());
            map.put("1日常、2招待、3业务", imprestFundReocrd.getConsumptionType());
            map.put("消费金额", imprestFundReocrd.getBalance());
            map.put("1支出、2收入", imprestFundReocrd.getState());
            map.put("0 无 1 有 2收据", imprestFundReocrd.getIsBill());
            map.put("备注", imprestFundReocrd.getRemarks());
            map.put("附件", imprestFundReocrd.getAttach());
            map.put("年-月", imprestFundReocrd.getMonthRec());
            map.put("排序", imprestFundReocrd.getOrderBy());
            map.put("月账单id", imprestFundReocrd.getMonthBillId());
            map.put("0 创建， 1提交中，3审批成功，2 审批失败", imprestFundReocrd.getStatus());
            map.put("0正常 1删除", imprestFundReocrd.getDelFlag());
            map.put(" createBy",  imprestFundReocrd.getCreateBy());
            map.put(" updateBy",  imprestFundReocrd.getUpdateBy());
            map.put(" createTime",  imprestFundReocrd.getCreateTime());
            map.put(" updateTime",  imprestFundReocrd.getUpdateTime());
            map.put(" param1",  imprestFundReocrd.getParam1());
            map.put(" param2",  imprestFundReocrd.getParam2());
            map.put(" param3",  imprestFundReocrd.getParam3());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }


    @Override
    public boolean addRecordBean(ImprestFundReocrdBean imprestFundReocrdBean) {
        ImprestFundReocrd imprestFundReocrd=new ImprestFundReocrd();
        BeanUtil.copyProperties(imprestFundReocrdBean,imprestFundReocrd);
        imprestFundReocrd.setId(IdUtil.fastSimpleUUID());
        imprestFundReocrd.setCreateTime(new Timestamp(new Date().getTime()));
        // 排序
        // 根据 month_rec imprest_fund_id
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMddHHmmss");
        String orderby=simpleDateFormat.format(new Date());
        imprestFundReocrd.setOrderBy(orderby);

        return this.save(imprestFundReocrd);
    }

    @Override
    public boolean updateRecordBean(ImprestFundReocrdBean imprestFundReocrdBean) {

        imprestFundReocrdBean.setUpdateTime(new Timestamp(new Date().getTime()));
        boolean boo = this.updateById(imprestFundReocrdBean);
        return boo;
    }


    @Override
    public boolean addRecordListBean(List<ImprestFundReocrdBean> imprestFundReocrdBean,String userId) {
        if(imprestFundReocrdBean==null){
            return false;
        }
        List<ImprestFundReocrd> list=new ArrayList<>();
        Timestamp timestamp=new Timestamp(new Date().getTime());
        SimpleDateFormat simpleDateFormat=new SimpleDateFormat("yyyyMMddHHmmss");
        Date date=new Date();
        int size = imprestFundReocrdBean.size();
        size = size/10 + 1;
        StringBuilder fmtNum = new StringBuilder();
        for(int i=0;i<size;i++){
            fmtNum.append("0");
        }
        BigDecimal sum = new BigDecimal("0");
        String fundId = null;
        int i=1;
        for(ImprestFundReocrdBean imprestFund:imprestFundReocrdBean){
            ImprestFundReocrd imprestFundReocrd=new ImprestFundReocrd();
            BeanUtil.copyProperties(imprestFund,imprestFundReocrd);
            BigDecimal balance = imprestFundReocrd.getBalance();
            if(balance==null){
                balance=new BigDecimal("0");
                imprestFundReocrd.setBalance(balance);
            }
            if(fundId==null){
                fundId = imprestFundReocrd.getImprestFundId();
            }
            // 总额
            if(imprestFundReocrd.getState()==1){
                // 支出
                sum = sum.add(balance.multiply(new BigDecimal("-1")));
            }else{
                // 收入
                sum = sum.add(balance);
            }
            imprestFundReocrd.setId(IdUtil.fastSimpleUUID());
            imprestFundReocrd.setCreateBy(userId);
            imprestFundReocrd.setCreateTime(timestamp);
            imprestFundReocrd.setOrderBy(simpleDateFormat.format(date)+NumberUtil.decimalFormat(fmtNum.toString(),i));
            list.add(imprestFundReocrd);
            i++;
        }
        LambdaUpdateWrapper<ImprestFund> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImprestFund::getId,fundId);
        updateWrapper.setSql(" balance =  balance + "+ sum);
        imprestFundService.update(updateWrapper);

        return this.saveBatch(list);
    }

    @Override
    public boolean delRecord(String id) {
        // 钱
        ImprestFundReocrd imprestFundReocrd = this.getById(id);
        if(imprestFundReocrd.getDelFlag()==0){
            // 还原回去
            BigDecimal b;
            if(imprestFundReocrd.getState()==1){
                // 支出
                // +
                b = imprestFundReocrd.getBalance();
            }else{
                // 收入
                // -
                b= imprestFundReocrd.getBalance().multiply(new BigDecimal("-1"));
            }
           LambdaUpdateWrapper<ImprestFund> imprestFundLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            imprestFundLambdaUpdateWrapper.eq(ImprestFund::getId,imprestFundReocrd.getImprestFundId());
            imprestFundLambdaUpdateWrapper.setSql(" balance =  balance + "+ b);
            imprestFundService.update(imprestFundLambdaUpdateWrapper);

        }



        LambdaUpdateWrapper<ImprestFundReocrd> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImprestFundReocrd::getId,id);
        updateWrapper.set(ImprestFundReocrd::getDelFlag,1);
        return this.update(updateWrapper);
    }

    @Override
    public BigDecimal recordSum(String fundId, Integer state, Integer consumptionType, String monthRec, String status) {
        QueryWrapper<ImprestFundReocrd> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("imprest_fund_id",fundId);
        if(state!=null){
            queryWrapper.eq("state",state);
        }
        if(consumptionType!=null){
            queryWrapper.eq("consumption_type",consumptionType);
        }
        if(StrUtil.isNotBlank(monthRec)){
            queryWrapper.eq("month_rec",monthRec);
        }
        if(StrUtil.isNotBlank(status)){
            queryWrapper.in("status",Arrays.asList(status.split(",")));
        }
        queryWrapper.eq("del_flag",0);
        queryWrapper.select("sum(balance) as balance");
        Map<String, Object> stringObjectMap=this.getMap(queryWrapper);
        if(stringObjectMap==null){
            return new BigDecimal("0");
        }
        return (BigDecimal) stringObjectMap.getOrDefault("balance",new BigDecimal("0"));
    }

    @Override
    public BigDecimal recordSumByStartAndEnd(String fundId, Integer state, String start, String end, String status) {
        QueryWrapper<ImprestFundReocrd> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("imprest_fund_id",fundId);
        if(state!=null){
            queryWrapper.eq("state",state);
        }


//        if(StrUtil.isNotBlank(start)){
//            // 付款时间相等，创建时间大于最后一次时间的
//            queryWrapper.apply("date_format(consumption_date,'%Y-%m-%d') >= {0}",start);
//        }
//        if(StrUtil.isNotBlank(end)){
//            // end + 1day
//            queryWrapper.apply("date_format(consumption_date,'%Y-%m-%d') <= {0}",end);
//        }
        if(StrUtil.isNotBlank(status)){
            queryWrapper.in("status",Arrays.asList(status.split(",")));
        }
        queryWrapper.eq("del_flag",0);
        if(StrUtil.isNotBlank(start) && StrUtil.isNotBlank(end)){
//            queryWrapper.between("create_time",start,end);
            // 创建时间 如果是当天的话 会有问题
            // 判断付款时间等于start 并且创建时间在start之后
//            queryWrapper.apply(" ((date_format(consumption_date,'%Y-%m-%d') = date_format({0},'%Y-%m-%d') and date_format(create_time,'%Y-%m-%d %H:%M:%S') > date_format({0},'%Y-%m-%d %H:%M:%S')) or (date_format(consumption_date,'%Y-%m-%d') > {0} and date_format(consumption_date,'%Y-%m-%d') <= {1})) ",start,end);
            queryWrapper.apply(" ((date_format(create_time,'%Y-%m-%d') = date_format({0},'%Y-%m-%d') and date_format(create_time,'%Y-%m-%d %H:%M:%S') > date_format({0},'%Y-%m-%d %H:%M:%S')) or (date_format(create_time,'%Y-%m-%d') > date_format({0},'%Y-%m-%d') and date_format(create_time,'%Y-%m-%d') <= date_format({1},'%Y-%m-%d'))) ",start,end);
            // 否则 就是 付款时间在start和end之间
//            queryWrapper.apply("date_format(consumption_date,'%Y-%m-%d') >= {0}",start);
//            queryWrapper.apply("date_format(consumption_date,'%Y-%m-%d') <= {0}",end);
        }
        queryWrapper.select("sum(balance) as balance");
        Map<String, Object> stringObjectMap=this.getMap(queryWrapper);
        if(stringObjectMap==null){
            return new BigDecimal("0");
        }
        return (BigDecimal) stringObjectMap.getOrDefault("balance",new BigDecimal("0"));
    }


    @Override
    public List<ImprestFundReocrd> list(String fundId,  String startMonth,
                                        String endMonth,Integer state, Integer consumptionType, String monthRec, String status,String start,String end) {
        LambdaQueryWrapper<ImprestFundReocrd> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(ImprestFundReocrd::getImprestFundId,fundId);
        if(state!=null){
            queryWrapper.eq(ImprestFundReocrd::getState,state);
        }
        if(consumptionType!=null){
            queryWrapper.eq(ImprestFundReocrd::getConsumptionType,consumptionType);
        }
        if(StrUtil.isNotBlank(monthRec)){
            queryWrapper.eq(ImprestFundReocrd::getMonthRec,monthRec);
        }
        if(StrUtil.isNotBlank(status)){
            queryWrapper.in(ImprestFundReocrd::getStatus,Arrays.asList(status.split(",")) );
//            queryWrapper.eq(ImprestFundReocrd::getStatus,status);
        }
        if(StrUtil.isNotBlank(startMonth)){
//            queryWrapper.apply("DATE_FORMAT(consumption_date,'%Y-%m')>={0}",startMonth);
            queryWrapper.apply("DATE_FORMAT(create_time,'%Y-%m')>={0}",startMonth);
        }
        if(StrUtil.isNotBlank(endMonth)){
            queryWrapper.apply("DATE_FORMAT(param2,'%Y-%m')<{0}",endMonth);
        }
        if(StrUtil.isNotBlank(start) && StrUtil.isNotBlank(end)){
//            queryWrapper.apply(" ((date_format(consumption_date,'%Y-%m-%d') = date_format({0},'%Y-%m-%d') and date_format(create_time,'%Y-%m-%d %H:%M:%S') > date_format({0},'%Y-%m-%d %H:%M:%S')) or (date_format(consumption_date,'%Y-%m-%d') > {0} and date_format(consumption_date,'%Y-%m-%d') <= {1})) ",start,end);
            queryWrapper.apply(" ((date_format(create_time,'%Y-%m-%d') = date_format({0},'%Y-%m-%d') and date_format(create_time,'%Y-%m-%d %H:%M:%S') > date_format({0},'%Y-%m-%d %H:%M:%S')) or (date_format(create_time,'%Y-%m-%d') > {0} and date_format(create_time,'%Y-%m-%d') <= {1})) ",start,end);
        }

        queryWrapper.eq(ImprestFundReocrd::getDelFlag,0);
        queryWrapper.orderByDesc(ImprestFundReocrd::getConsumptionDate);
        queryWrapper.orderByDesc(ImprestFundReocrd::getParam2);
        queryWrapper.orderByDesc(ImprestFundReocrd::getOrderBy);
//        queryWrapper.orderByAsc(ImprestFundReocrd::getOrderBy);
        return this.list(queryWrapper);
    }

    @Override
    public Page<ImprestFundReocrd> listPage(String fundId, String startMonth,
                                            String endMonth, Integer state, Integer consumptionType, String monthRec, String status, Integer pageNum, Integer pageSize) {

        return PageHelper.startPage(pageNum,pageSize).doSelectPage(()->this.list(fundId,startMonth,endMonth,state,consumptionType,monthRec,status,null,null));
    }

    @Override
    public Page<ImprestFundReocrdBean> queryRecordAndWuLiuPage(String fundId, String startMonth, String endMonth, Integer state, Integer status, Integer pageNum, Integer pageSize) {
        Page<ImprestFundReocrdBean> page = PageHelper.startPage(pageNum,pageSize).doSelectPage(()->imprestFundReocrdMapper.queryRecordAndWuLiu(fundId,startMonth,endMonth,state,status));
        return page;
    }

    @Override
    public boolean upStatusByBillId(String billId, Integer status) {
        LambdaUpdateWrapper<ImprestFundReocrd> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(ImprestFundReocrd::getMonthBillId,billId);
        updateWrapper.set(ImprestFundReocrd::getStatus,status);
        updateWrapper.set(ImprestFundReocrd::getUpdateTime,new Timestamp(new Date().getTime()));
        return this.update(updateWrapper);
    }

    @Override
    public boolean setBillIdByFundId(String fundId, String billId,Integer status) {
        LambdaUpdateWrapper<ImprestFundReocrd> updateWrapper=new LambdaUpdateWrapper<>();
        if(StrUtil.isNotBlank(fundId)){
            updateWrapper.eq(ImprestFundReocrd::getImprestFundId,fundId);
//            updateWrapper.isNull(ImprestFundReocrd::getMonthBillId);
            if(StrUtil.isNotBlank(billId)){
                updateWrapper.in(ImprestFundReocrd::getStatus,0,2);
                updateWrapper.set(ImprestFundReocrd::getMonthBillId,billId);
            }
        }else{
            updateWrapper.eq(ImprestFundReocrd::getMonthBillId,billId);
        }
        updateWrapper.set(ImprestFundReocrd::getStatus,status);
        return this.update(updateWrapper);
    }

    @Override
    public boolean setStatusById(String recordIds, Integer status) {
        LambdaUpdateWrapper<ImprestFundReocrd> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.in(ImprestFundReocrd::getId,Arrays.asList(recordIds.split(",")));
        updateWrapper.set(ImprestFundReocrd::getStatus,status);
        return this.update(updateWrapper);
    }

    @Override
    public boolean setChangeStatusById(String recordIds, Integer status) {
        LambdaUpdateWrapper<ImprestFundReocrd> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.in(ImprestFundReocrd::getId,Arrays.asList(recordIds.split(",")));
        updateWrapper.set(ImprestFundReocrd::getChangeStatus,status);
        return this.update(updateWrapper);
    }


}