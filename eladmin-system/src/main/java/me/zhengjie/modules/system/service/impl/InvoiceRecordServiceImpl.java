package me.zhengjie.modules.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.dlcg.tms.bean.InvoiceUpdateBillMonthBean;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zthzinfo.common.ResponseMapBuilder;
import me.zhengjie.base.BaseServiceImpl;
import me.zhengjie.modules.system.domain.InvoiceRecord;
import me.zhengjie.modules.system.domain.bean.QueryParamBean;
import me.zhengjie.modules.system.mapper.InvoiceRecordMapper;
import me.zhengjie.modules.system.service.InvoiceRecordService;
import me.zhengjie.modules.system.service.dto.InvoiceRecordDto;
import me.zhengjie.modules.system.service.dto.InvoiceRecordQueryCriteria;
import me.zhengjie.modules.system.service.mapstruct.InvoiceRecordSMapper;
import me.zhengjie.modules.workflow.model.document.DProcessInstance;
import me.zhengjie.modules.workflow.model.entity.ProcessInstance;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.utils.QueryHelpPlus;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
* @description 服务实现
* <AUTHOR>
* @date 2024-12-26
**/
@Service
public class InvoiceRecordServiceImpl extends BaseServiceImpl<InvoiceRecordMapper,InvoiceRecord> implements InvoiceRecordService {

    @Resource
    private InvoiceRecordSMapper invoiceRecordSMapper;
    @Resource
    private InvoiceRecordMapper invoiceRecordMapper;
    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public Map<String,Object> queryAll(InvoiceRecordQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<InvoiceRecordDto> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<InvoiceRecordDto> queryAll(InvoiceRecordQueryCriteria criteria){
        return listToDto(list(QueryHelpPlus.getPredicate(InvoiceRecord.class, criteria)),invoiceRecordSMapper);
    }

    @Override
    public void download(List<InvoiceRecordDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (InvoiceRecordDto invoiceRecord : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("发票号", invoiceRecord.getInvoiceNo());
            map.put("发票代码", invoiceRecord.getInvoiceCode());
            map.put("发票日期", invoiceRecord.getInvoiceDate());
            map.put("发票不含税金额", invoiceRecord.getInvoicePrice());
            map.put("发票税额", invoiceRecord.getInvoicePriceTax());
            map.put("发票含税金额", invoiceRecord.getInvoicePriceTaxSum());
            map.put("税率", invoiceRecord.getInvoiceTax());
            map.put("买方名称", invoiceRecord.getBuyerName());
            map.put("买方纳税人识别号", invoiceRecord.getBuyerTaxpayerNo());
            map.put("买方地址电话", invoiceRecord.getBuyerAddressTel());
            map.put("买方开户行及账号", invoiceRecord.getBuyerAccount());
            map.put("卖方名称", invoiceRecord.getSellerName());
            map.put("卖方纳税人识别号", invoiceRecord.getSellerTaxpayerNo());
            map.put("卖方地址电话", invoiceRecord.getSellerAddressTel());
            map.put("卖方开户行及账号", invoiceRecord.getSellerAccount());
            map.put("发票图片", invoiceRecord.getOriginFile());
            map.put("收款人", invoiceRecord.getInvoicePayee());
            map.put("开票人", invoiceRecord.getInvoiceDrawer());
            map.put("复核", invoiceRecord.getInvoiceReview());
            map.put("发票备注", invoiceRecord.getInvoiceRemark());
            map.put("创建时间", invoiceRecord.getCreateTime());
            map.put("修改时间", invoiceRecord.getUpdateTime());
            map.put("记账月份", invoiceRecord.getRecordAccountMonth());
            map.put("状态 0 待提交审批 1 审批中 2待入账 3已完成", invoiceRecord.getStatus());
            map.put("购买方ID", invoiceRecord.getBuyerId());
            map.put("销售方ID", invoiceRecord.getSellerId());
            map.put("费用类型ID", invoiceRecord.getCostTypeId());
            map.put("费用类型名称", invoiceRecord.getCostTypeName());
            map.put("费用字典key", invoiceRecord.getCostDictKey());
            map.put("服务名称", invoiceRecord.getServiceName());
            map.put("拆分人", invoiceRecord.getSplitUserId());
            map.put("拆分时间", invoiceRecord.getSplitTime());
            map.put("发票类型 普票、专票", invoiceRecord.getInvoiceType());
            map.put("销项发票、进项发票", invoiceRecord.getOutInputInvoice());
            map.put("是否重复", invoiceRecord.getIsItRepeated());
            map.put("数据来源", invoiceRecord.getInvoiceDataSource());
            map.put("关联流程id", invoiceRecord.getAssociatedProcessId());
            map.put("是否生成凭证", invoiceRecord.getIsCertificate());
            map.put("凭证id", invoiceRecord.getCredentialId());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
    private List<InvoiceRecord> selectByInvoiceNo(String invoiceNo){
        if(StrUtil.isBlank(invoiceNo)){
            return null;
        }
        LambdaQueryWrapper<InvoiceRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InvoiceRecord::getInvoiceNo,invoiceNo);
        queryWrapper.eq(InvoiceRecord::getDelFlag,0);
        List<InvoiceRecord> list = invoiceRecordMapper.selectList(queryWrapper);
        return list;
    }
    private void addInvoiceRecordCheckIsItRepeated(InvoiceRecord invoice){
        if(invoice==null){
            return;
        }
        // 判断是否重复 如果重复标记
        List<InvoiceRecord> list = selectByInvoiceNo(invoice.getInvoiceNo());
        if(list!=null && list.size()>0){
            invoice.setIsItRepeated(1);
        }
        invoiceRecordMapper.insert(invoice);
    }
    private void addInvoiceRecordCheckIsItRepeatedList(List<InvoiceRecord> invoices){
        if(invoices==null || invoices.size()==0){
            return;
        }
        for(InvoiceRecord invoice : invoices){
            List<InvoiceRecord> list = selectByInvoiceNo(invoice.getInvoiceNo());
            if(list!=null && list.size()>0){
                invoice.setIsItRepeated(1);
            }
        }
        saveBatch(invoices);
    }
    private void addInvoiceRecordCheckIsItRepeated(List<InvoiceRecord> invoices){
        if(invoices==null || invoices.size()==0){
            return;
        }
        // 判断是否重复 如果重复标记
        for(InvoiceRecord invoice : invoices){
            List<InvoiceRecord> list = selectByInvoiceNo(invoice.getInvoiceNo());
            if(list!=null && list.size()>0){
                invoice.setIsItRepeated(1);
            }
        }
        saveBatch(invoices);
    }
    // 同步销项
    @Override
    public void addInvoiceRecordSales(InvoiceRecordDto invoiceRecord) {
        if(invoiceRecord==null){
            return;
        }
        InvoiceRecord invoice =  invoiceRecordSMapper.toEntity(invoiceRecord);
        invoice.setInvoiceDataSource("sales");
        invoice.setOutInputInvoice("out");

        addInvoiceRecordCheckIsItRepeated(invoice);

    }
    // 同步进项
        // 业务收票
    @Override
    public void addInvoiceRecordPurchase(InvoiceRecordDto invoiceRecord) {
        if(invoiceRecord==null){
            return;
        }
        InvoiceRecord invoice =  invoiceRecordSMapper.toEntity(invoiceRecord);
        invoice.setInvoiceDataSource("purchase");
        invoice.setOutInputInvoice("in");
        addInvoiceRecordCheckIsItRepeated(invoice);
    }
        // 报销收票
    @Override
    public void addInvoiceRecordPurchaseReimburse(ProcessInstance instance) {
        if(instance==null){
            return;
        }
        // 获取发票信息，公务报销申请，识别文件是否发票，是添加到InvoiceRecord
        List<DProcessInstance> mongoValues = mongoTemplate.find(new Query(Criteria.where("_id").is(instance.getId())), DProcessInstance.class);
        if(mongoValues==null || mongoValues.size()==0){
            return;
        }
        JSONObject fv = mongoValues.get(0).getFromvalue();
            if(fv==null || !fv.containsKey("totalShuiList")){
                return;
            }
            List<Map<String,Object>> list = (List<Map<String,Object>>)fv.get("totalShuiList");
            List<InvoiceRecord> invoiceRecords = new ArrayList<>();
            for(Map<String,Object> map:list){
                InvoiceRecord invoice = new InvoiceRecord();
                invoice.setInvoiceNo(map.getOrDefault("Number","").toString());
                invoice.setInvoiceDate(map.getOrDefault("Date","").toString());
                invoice.setTitle(map.getOrDefault("Title","").toString());
                // BuyerTaxID Buyer
                invoice.setBuyerName(map.getOrDefault("Buyer","").toString());
                invoice.setBuyerTaxpayerNo(map.getOrDefault("BuyerTaxID","").toString());
                // SellerTaxID Seller
                invoice.setSellerName(map.getOrDefault("Seller","").toString());
                invoice.setSellerTaxpayerNo(map.getOrDefault("SellerTaxID","").toString());
                // Total Tax TaxRate Remark PretaxAmount
                invoice.setInvoicePriceTaxSum(new BigDecimal(map.getOrDefault("Total","0").toString()));
                invoice.setInvoicePriceTax(new BigDecimal(map.getOrDefault("Tax","0").toString()));
                invoice.setInvoicePrice(new BigDecimal(map.getOrDefault("PretaxAmount","0").toString()));

                invoice.setInvoiceTax(map.getOrDefault("TaxRate","").toString());
                invoice.setInvoiceRemark(map.getOrDefault("Remark","").toString());
                // Issuer Reviewer Kind
                invoice.setInvoiceDrawer(map.getOrDefault("Issuer","").toString());
                invoice.setInvoiceReview(map.getOrDefault("Reviewer","").toString());
                invoice.setCostTypeName(map.getOrDefault("Kind","").toString());
                invoice.setOriginFile(map.getOrDefault("FileUrl","").toString());

                invoice.setInvoiceType(map.getOrDefault("InvoiceType","").toString());
                invoice.setAssociatedProcessId(instance.getId());

                invoice.setInvoiceDataSource("purchase_reimburse");
                invoice.setOutInputInvoice("in");

                invoiceRecords.add(invoice);
            }

            addInvoiceRecordCheckIsItRepeated(invoiceRecords);
    }

    @Override
    public Map<String, Object> listBySelf(QueryParamBean bean) {
        LambdaQueryWrapper<InvoiceRecord> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(InvoiceRecord::getDelFlag,0);
        if(StrUtil.isNotBlank(bean.getBillMonth())){
            queryWrapper.eq(InvoiceRecord::getRecordAccountMonth,bean.getBillMonth());
        }
        if(bean.getStatus()!=null){
            queryWrapper.eq(InvoiceRecord::getStatus,bean.getStatus());
        }
        if(StrUtil.isNotBlank(bean.getSellerName())){
            queryWrapper.like(InvoiceRecord::getSellerName,bean.getSellerName());
        }
        if(StrUtil.isNotBlank(bean.getInvoiceNo())){
            queryWrapper.like(InvoiceRecord::getInvoiceNo,bean.getInvoiceNo());
        }
        if(StrUtil.isNotBlank(bean.getInvoiceDate())){
            String billMonth = bean.getInvoiceDate();
            String bMonth = billMonth.replaceAll("年","-").replaceAll("月","-").replaceAll("日","");
//            queryWrapper.eq(InvoiceRecord::getInvoiceDate,bean.getInvoiceDate());
            queryWrapper.and(wrapper -> wrapper.eq(InvoiceRecord::getInvoiceDate,bean.getInvoiceDate()).or().like(InvoiceRecord::getInvoiceDate,bMonth));
        }
        if(null != bean.getIsItRepeated()){
            if(0==bean.getIsItRepeated()){
                queryWrapper.isNull(InvoiceRecord::getIsItRepeated);
            }else{
                queryWrapper.eq(InvoiceRecord::getIsItRepeated,bean.getIsItRepeated());
            }
        }
        if(StrUtil.isNotBlank(bean.getOutInputInvoice())){
            queryWrapper.eq(InvoiceRecord::getOutInputInvoice,bean.getOutInputInvoice());
        }
        queryWrapper.orderByDesc(InvoiceRecord::getCreateTime);
        Page<InvoiceRecord> page = PageHelper.startPage(bean.getPageNum(),bean.getPageSize())
                .doSelectPage(() ->{
                    list(queryWrapper);
                });
        return ResponseMapBuilder.newBuilder()
                .put("data",page)
                .put("total",page.getTotal())
                .putSuccess()
                .getResult();
    }

    @Override
    public Map<String, Object> updateBilMonthSlef(InvoiceUpdateBillMonthBean invoiceUpdateBillMonthBean) {
        Assert.isFalse(StrUtil.isBlank(invoiceUpdateBillMonthBean.getId()) && CollUtil.isEmpty(invoiceUpdateBillMonthBean.getIds()), "缺失ID参数");
        Assert.notBlank(invoiceUpdateBillMonthBean.getRecordAccountMonth(), "记账月份不能为空");
        Assert.notNull(invoiceUpdateBillMonthBean.getStatus(), "状态不能为空");

        LambdaUpdateWrapper<InvoiceRecord> updateWrapper = new LambdaUpdateWrapper<InvoiceRecord>()
                .set(InvoiceRecord::getRecordAccountMonth, invoiceUpdateBillMonthBean.getRecordAccountMonth())
                .set(InvoiceRecord::getStatus, invoiceUpdateBillMonthBean.getStatus());

        // id | ids 分别代表单选和多选
        if (StrUtil.isNotBlank(invoiceUpdateBillMonthBean.getId())) {
            updateWrapper.eq(InvoiceRecord::getId, invoiceUpdateBillMonthBean.getId());
        } else if (CollUtil.isNotEmpty(invoiceUpdateBillMonthBean.getIds())) {
            updateWrapper.in(InvoiceRecord::getId, invoiceUpdateBillMonthBean.getIds());
        }

        update(updateWrapper);
        return ResponseMapBuilder.newBuilder().putSuccess()
                .getResult();
    }

    @Override
    public void uploadSalesInvoice(List<InvoiceRecordDto> list) {
        // 添加到数据库 并且 设置类型
//        invoice.setInvoiceDataSource("sales");
//        invoice.setOutInputInvoice("out");
        List<InvoiceRecord> invoiceRecords = invoiceRecordSMapper.toEntity(list);
        invoiceRecords.forEach(invoiceRecord -> {
                    invoiceRecord.setInvoiceDataSource("sales_upload");
                    invoiceRecord.setOutInputInvoice("out");
                }
        );
        addInvoiceRecordCheckIsItRepeatedList(invoiceRecords);
    }
    final List<String> invoiceTypeList = CollUtil.newArrayList("VatSpecialInvoice","VatCommonInvoice"
            ,"VatElectronicCommonInvoice","VatElectronicSpecialInvoice","VatElectronicInvoiceBlockchain"
            ,"VatElectronicSpecialInvoiceFull","VatElectronicInvoiceFull");

    @Override
    public void uploadPurchaseInvoice(List<InvoiceRecordDto> list) {
        if(list!=null){
            list.forEach(invoiceRecordDto -> {
                invoiceRecordDto.setInvoiceDataSource("purchase_upload");
//                invoiceRecordDto.getSubTypeName()
                String type = StrUtil.isBlank(invoiceRecordDto.getSubTypeName())?invoiceRecordDto.getInvoiceType():invoiceRecordDto.getSubTypeName();
                if(invoiceTypeList.contains(type)){
                    invoiceRecordDto.setOutInputInvoice("in");
                }else{
                    invoiceRecordDto.setOutInputInvoice("in_receipt");
                }

            });
        }
        // 添加到数据库 并且 设置类型
        List<InvoiceRecord> invoiceRecords = invoiceRecordSMapper.toEntity(list);
//        invoiceRecords.forEach(invoiceRecord -> {
//                    invoiceRecord.setInvoiceDataSource("purchase_upload");
//                    // 费用小票
////                    invoiceRecord.setOutInputInvoice("in");
//                }
//        );
        addInvoiceRecordCheckIsItRepeatedList(invoiceRecords);
    }

    @Override
    public void uploadReceiptInvoice(List<InvoiceRecordDto> list) {
        if(list!=null){
            list.forEach(invoiceRecordDto -> {
                invoiceRecordDto.setInvoiceDataSource("receipt_upload");
                invoiceRecordDto.setOutInputInvoice("in_receipt");
            });
        }
        // 添加到数据库 并且 设置类型
        List<InvoiceRecord> invoiceRecords = invoiceRecordSMapper.toEntity(list);
        addInvoiceRecordCheckIsItRepeatedList(invoiceRecords);
    }

    @Override
    public void delInvoiceDialog(List<String> ids) {
        // 逻辑删除
        UpdateWrapper<InvoiceRecord> updateWrapper = new UpdateWrapper<InvoiceRecord>()
                .set("del_flag", 1)
                .in("id", ids);
        invoiceRecordMapper.update(null, updateWrapper);
    }

    @Override
    public List<InvoiceRecord> listPrepaymentReimbursementInvoice(String... ids) {
        if(ids==null){
            return null;
        }

        return invoiceRecordMapper.selectList(new LambdaQueryWrapper<InvoiceRecord>().in(InvoiceRecord::getAssociatedProcessId, ids));
    }

    @Override
    public List<Map<String, Object>> getInvoiceCountByProcessIds(String ids) {
        if(StrUtil.isBlank(ids)){
            return null;
        }
        List<String> sids = Arrays.asList(ids.split(","));
        return invoiceRecordMapper.getInvoiceCountByProcessIds(sids);
    }

    @Override
    public void addPrepaymentReimbursementInvoice(List<InvoiceRecordDto> invoiceRecordDto) {
        if(invoiceRecordDto==null){
            return;
        }
        // 获取发票信息，公务报销申请，识别文件是否发票，是添加到InvoiceRecord


        List<InvoiceRecord> invoiceRecords = new ArrayList<>();
        for(InvoiceRecordDto invoiceRecord:invoiceRecordDto){
            InvoiceRecord invoice = new InvoiceRecord();
            BeanUtil.copyProperties(invoiceRecord,invoice);
            invoice.setInvoiceDataSource("PrepaymentReimbursement");
            invoice.setOutInputInvoice("in");
            /*invoice.setInvoiceNo(map.getOrDefault("Number","").toString());
            invoice.setInvoiceDate(map.getOrDefault("Date","").toString());
            invoice.setTitle(map.getOrDefault("Title","").toString());
            // BuyerTaxID Buyer
            invoice.setBuyerName(map.getOrDefault("Buyer","").toString());
            invoice.setBuyerTaxpayerNo(map.getOrDefault("BuyerTaxID","").toString());
            // SellerTaxID Seller
            invoice.setSellerName(map.getOrDefault("Seller","").toString());
            invoice.setSellerTaxpayerNo(map.getOrDefault("SellerTaxID","").toString());
            // Total Tax TaxRate Remark PretaxAmount
            invoice.setInvoicePriceTaxSum(new BigDecimal(map.getOrDefault("Total","0").toString()));
            invoice.setInvoicePriceTax(new BigDecimal(map.getOrDefault("Tax","0").toString()));
            invoice.setInvoicePrice(new BigDecimal(map.getOrDefault("PretaxAmount","0").toString()));

            invoice.setInvoiceTax(map.getOrDefault("TaxRate","").toString());
            invoice.setInvoiceRemark(map.getOrDefault("Remark","").toString());
            // Issuer Reviewer Kind
            invoice.setInvoiceDrawer(map.getOrDefault("Issuer","").toString());
            invoice.setInvoiceReview(map.getOrDefault("Reviewer","").toString());
            invoice.setCostTypeName(map.getOrDefault("Kind","").toString());
            invoice.setOriginFile(map.getOrDefault("FileUrl","").toString());

            invoice.setInvoiceType(map.getOrDefault("InvoiceType","").toString());
            invoice.setAssociatedProcessId(id);

            invoice.setInvoiceDataSource("PrepaymentReimbursement");
            invoice.setOutInputInvoice("in");*/

            invoiceRecords.add(invoice);
        }

        addInvoiceRecordCheckIsItRepeated(invoiceRecords);
    }
}