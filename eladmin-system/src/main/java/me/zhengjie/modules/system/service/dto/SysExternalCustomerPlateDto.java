/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.service.dto;

import lombok.Data;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2022-01-29
**/
@Data
public class SysExternalCustomerPlateDto implements Serializable {

    private Integer id;

    private Integer customerId;

    private Long dictId;

    /** 是否客户 */
    private Integer isCustomer;

    /** 是否供应商 1 是 0 否 */
    private Integer isSupplier;

    private String createBy;

    private Timestamp createDate;

    private String updateBy;

    private Timestamp updateDate;

    private String delFlag;

    private String spare1;

    private String spare2;

    private String spare3;

    private String spare4;

    private String spare5;
}