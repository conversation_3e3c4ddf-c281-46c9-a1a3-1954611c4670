package me.zhengjie.modules.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProcessInstanceVo implements Serializable {
    private static final long serialVersionUID = -6215126241131027778L;
    private String flowName;
    private String processId;
    private String sponsorName;
    private String briefContent;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date sponsorTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date finishTime;
    private String operName;
    private String status;
    private String showStatus;
    private Integer statusId;
    private String sponsorAvatar;
    private String reason;
    private String flowCode;
    private String param1;
    private String param3;
    private String param4;
    private String param9;
    private String param7;
    private String param10;
    private Integer nodeId;
    
    private String dName;
    
    private String cName;
    private BigDecimal paidMoney;
    // 内外帐 摘要 付款银行
    private String bankName;
    private String bankAccount;
    private String bankAccountName;
    private String summary;
    // 内外帐
    private String payAccountTypeName;
}
