package me.zhengjie.modules.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description /
* <AUTHOR>
* @date 2022-11-01
**/
@Data
@TableName("imprest_fund_account_money_for_month")
public class ImprestFundAccountMoneyForMonth implements Serializable {


    @TableId
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "账户id")
    private String fundId;

    @ApiModelProperty(value = "余额")
    private BigDecimal money;

    @ApiModelProperty(value = "0 正常")
    @TableLogic
    private Integer delFlag;

    @ApiModelProperty(value = "createBy")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "createDate")
    private Timestamp createDate;

    @ApiModelProperty(value = "updateBy")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "updateDate")
    private Timestamp updateDate;

    @ApiModelProperty(value = "remarks")
    private String remarks;

    @ApiModelProperty(value = "spare1")
    private String spare1;

    @ApiModelProperty(value = "spare2")
    private String spare2;

    @ApiModelProperty(value = "spare3")
    private String spare3;

    @ApiModelProperty(value = "spare4")
    private String spare4;

    @ApiModelProperty(value = "spare5")
    private String spare5;

    public void copy(ImprestFundAccountMoneyForMonth source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}