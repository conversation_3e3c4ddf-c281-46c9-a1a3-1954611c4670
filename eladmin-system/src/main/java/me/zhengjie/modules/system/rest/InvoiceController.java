package me.zhengjie.modules.system.rest;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import com.dlcg.tms.bean.InvoiceQueryParamsBean;
import com.dlcg.tms.bean.InvoiceUpdateBillMonthBean;
import com.dlcg.tms.client.InvoiceClient;
import com.zthzinfo.common.ResponseMapBuilder;
import io.swagger.annotations.Api;
import me.zhengjie.modules.system.domain.InvoiceRecord;
import me.zhengjie.modules.system.domain.bean.QueryParamBean;
import me.zhengjie.modules.system.service.InvoiceRecordService;
import me.zhengjie.modules.system.service.dto.InvoiceRecordDto;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 发票
 *
 * <AUTHOR>
 * @since 2022/6/28
 */
@RestController
@Api(tags = "发票")
@RequestMapping("/api/invoice")
public class InvoiceController {

    @Resource
    InvoiceClient invoiceClient;
    @Resource
    InvoiceRecordService invoiceRecordService;

    @PostMapping("/list")
    public Map<String,Object> list(@RequestBody  InvoiceQueryParamsBean bean) {
        return invoiceClient.list(bean);
    }
    @PostMapping("/statistical")
    public Map<String,Object> statistical(@RequestBody InvoiceQueryParamsBean bean) {
        return invoiceClient.statistical(bean);
    }
    @PostMapping("/updateBilMonth")
    public Map<String,Object> updateBilMonth(@RequestBody InvoiceUpdateBillMonthBean bean) {
        return  invoiceClient.updateBilMonth(bean);
    }

    @PostMapping("/invoiceListBySales")
    public Map<String,Object> invoiceListBySales(@RequestBody  InvoiceQueryParamsBean bean) {
        return invoiceClient.invoiceListBySales(bean);
    }
    @PostMapping("/invoiceListBySalesAccount")
    public Map<String,Object> invoiceListBySalesAccount(@RequestBody  InvoiceQueryParamsBean bean) {
        return invoiceClient.invoiceListBySalesAccount(bean);
    }

    // 列表
    @PostMapping("/listBySelf")
    public Map<String,Object> listBySelf(@RequestBody QueryParamBean bean) {
        return invoiceRecordService.listBySelf(bean);
    }

    @PostMapping("/updateBilMonthSlef")
    public Map<String,Object> updateBilMonthSlef(@RequestBody InvoiceUpdateBillMonthBean bean) {
        return invoiceRecordService.updateBilMonthSlef(bean);
    }
    // 上传销项发票
    @PostMapping("/uploadSalesInvoice")
    public Map<String,Object> uploadSalesInvoice(@RequestBody List<InvoiceRecordDto> bean) {
         invoiceRecordService.uploadSalesInvoice(bean);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    // 上传进项发票
    @PostMapping("/uploadPurchaseInvoice")
    public Map<String,Object> uploadPurchaseInvoice(@RequestBody List<InvoiceRecordDto> bean) {
         invoiceRecordService.uploadPurchaseInvoice(bean);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @PostMapping("/uploadReceiptInvoice")
    public Map<String,Object> uploadReceiptInvoice(@RequestBody List<InvoiceRecordDto> bean) {
        invoiceRecordService.uploadReceiptInvoice(bean);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @PostMapping("/delInvoiceDialog")
    public Map<String,Object> delInvoiceDialog(@RequestBody List<String> bean) {
         invoiceRecordService.delInvoiceDialog(bean);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }
    @GetMapping("/getInvoiceCountByProcessIds")
    public Map<String,Object> getInvoiceCountByProcessIds(@RequestParam String ids) {
        List<Map<String, Object>> app = invoiceRecordService.getInvoiceCountByProcessIds(ids);
        return ResponseMapBuilder.newBuilder()
                .put("data",app)
                .putSuccess()
                .getResult();
    }
    // 查询预付款发票
    @GetMapping("/listPrepaymentReimbursementInvoice")
    public Map<String,Object> listPrepaymentReimbursementInvoice(@SpringQueryMap QueryParamBean bean) {
        String[] ids=null;
        if(StrUtil.isNotBlank(bean.getIds())){
            ids = bean.getIds().split(",");
        }
        List<InvoiceRecord> list = invoiceRecordService.listPrepaymentReimbursementInvoice(ids);
        return ResponseMapBuilder.newBuilder()
                .put("list",list)
                .putSuccess()
                .getResult();
    }
    // 添加预付款发票
    @PostMapping("/addPrepaymentReimbursementInvoice")
    public Map<String,Object> addPrepaymentReimbursementInvoice(@RequestBody List<InvoiceRecordDto> bean) {
        invoiceRecordService.addPrepaymentReimbursementInvoice(bean);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }
}
