package me.zhengjie.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.zhengjie.modules.system.domain.ImprestFundAccountMoneyForMonth;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022-11-01
 */
@Mapper
public interface ImprestFundAccountMoneyForMonthMapper extends BaseMapper<ImprestFundAccountMoneyForMonth> {

}
