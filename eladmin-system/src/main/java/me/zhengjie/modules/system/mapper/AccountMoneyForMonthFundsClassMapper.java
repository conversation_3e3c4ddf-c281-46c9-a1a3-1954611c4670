package me.zhengjie.modules.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.zhengjie.modules.system.domain.AccountMoneyForMonthFundsClass;
import me.zhengjie.modules.system.service.dto.DiffMonthDeptMoneyDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2023-03-27
 */
@Mapper
public interface AccountMoneyForMonthFundsClassMapper extends BaseMapper<AccountMoneyForMonthFundsClass> {
    @Select("<script>" +
            "SELECT  " +
            "  ac.*  " +
            "FROM  " +
            "  account_money_for_month_funds_class ac  " +
            "  INNER JOIN (  " +
            "    SELECT  " +
            "      aac.dept_id dept_id,  " +
            "      min(aac.create_date) create_date  " +
            "    FROM  " +
            "      account_money_for_month_funds_class AS aac  " +
            "      WHERE aac.del_flag=0   " +
            "<if test='date!=null and date!=\"\"'>"+
            "      and DATE_FORMAT(aac.create_date,'%Y-%m') = #{date}  " +
            "</if>"+
            "    GROUP BY  " +
            "      aac.dept_id) agc ON agc.dept_id = ac.dept_id  " +
            "  AND agc.create_date = ac.create_date  " +
            "  WHERE ac.del_flag=0   " +
            "<if test='date!=null and date!=\"\"'>"+
            "  and DATE_FORMAT(ac.create_date,'%Y-%m') = #{date}  " +
            "</if>"+
            "<if test='deptId!=null and deptId!=\"\"'>"+
            "  and ac.dept_id= #{deptId}  " +
            "</if>"+
            "</script>")
    AccountMoneyForMonthFundsClass getDeptIdAndDate(@Param("deptId") String deptId,@Param("date") String date);
    @Select("<script>" +
            "SELECT " +
            " sum(m.rec) AS income, " +
            " sum(m.out) AS expend, " +
            " (sum(m.rec) - sum(m.out)) AS diffMoney " +
            "FROM ( " +
            " SELECT " +
            "  sum(ifnull(pw.balance,0)) AS 'rec', " +
            "  0 AS 'out' " +
            " FROM " +
            "  receive_water pw " +
            " LEFT JOIN sys_account sa ON pw.account_id = sa.id " +
            "WHERE " +
            " sa.dept_id = #{deptId} " +
            "<if test='date!=null and date!=\"\"'>"+
            " AND DATE_FORMAT(pw.create_time, '%Y-%m') >= #{date} " +
            " AND DATE_FORMAT(pw.occur_date, '%Y-%m') &lt; #{date} " +
            "</if>"+
            "UNION ALL " +
            "SELECT " +
            " 0 AS 'rec', " +
            " sum(ifnull(pw.money,0)) AS 'out' " +
            "FROM " +
            " payment_water pw " +
            " LEFT JOIN sys_account sa ON pw.sys_account_id = sa.id " +
            "WHERE " +
            " sa.dept_id = #{deptId} " +
            "<if test='date!=null and date!=\"\"'>"+
            " AND DATE_FORMAT(pw.create_time, '%Y-%m') >= #{date} " +
            " AND DATE_FORMAT(pw.occur_date, '%Y-%m') &lt; #{date} " +
            "</if>"+
            ") m"+
            "</script>")
    DiffMonthDeptMoneyDto getDiffMonthDeptMoneyDto(@Param("deptId") String deptId,@Param("date") String date);

    @Select("<script>" +
            "SELECT " +
            " sum(m.rec) AS income, " +
            " sum(m.out) AS expend, " +
            " (sum(m.rec) - sum(m.out)) AS diffMoney " +
            "FROM ( " +
            " SELECT " +
            "  sum(ifnull(pw.balance,0)) AS 'rec', " +
            "  0 AS 'out' " +
            " FROM " +
            "  receive_water pw " +
            " LEFT JOIN sys_account sa ON pw.account_id = sa.id " +
            "WHERE " +
            " sa.dept_id = #{deptId} and ifnull(sa.discount_or_cash,0)!=1 " +
            "<if test='date!=null and date!=\"\"'>"+
            " AND DATE_FORMAT(pw.create_time, '%Y-%m') >= #{date} " +
            " AND DATE_FORMAT(pw.occur_date, '%Y-%m') &lt; #{date} " +
            "</if>"+
            "UNION ALL " +
            "SELECT " +
            " 0 AS 'rec', " +
            " sum(ifnull(pw.money,0)) AS 'out' " +
            "FROM " +
            " payment_water pw " +
            " LEFT JOIN sys_account sa ON pw.sys_account_id = sa.id " +
            "WHERE " +
            " sa.dept_id = #{deptId} and ifnull(sa.discount_or_cash,0)!=1 " +
            "<if test='date!=null and date!=\"\"'>"+
            " AND DATE_FORMAT(pw.create_time, '%Y-%m') >= #{date} " +
            " AND DATE_FORMAT(pw.occur_date, '%Y-%m') &lt; #{date} " +
            "</if>"+
            ") m"+
            "</script>")
    DiffMonthDeptMoneyDto getDiffMonthDeptMoneyDtoAndNotChengDui(@Param("deptId") String deptId,@Param("date") String date);
}
