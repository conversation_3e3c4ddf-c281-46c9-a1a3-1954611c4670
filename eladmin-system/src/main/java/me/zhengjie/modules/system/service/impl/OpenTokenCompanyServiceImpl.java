package me.zhengjie.modules.system.service.impl;

import me.zhengjie.modules.system.domain.OpenTokenCompany;
import me.zhengjie.base.BaseServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import me.zhengjie.utils.QueryHelpPlus;
import me.zhengjie.utils.ValidationUtil;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.modules.system.mapper.OpenTokenCompanyMapper;
import me.zhengjie.modules.system.service.OpenTokenCompanyService;
import me.zhengjie.modules.system.service.dto.OpenTokenCompanyDto;
import me.zhengjie.modules.system.service.dto.OpenTokenCompanyQueryCriteria;
import me.zhengjie.modules.system.service.mapstruct.OpenTokenCompanySMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import me.zhengjie.utils.PageUtil;
import me.zhengjie.utils.QueryHelp;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;

/**
* @description 服务实现
* <AUTHOR>
* @date 2025-01-15
**/
@Service
public class OpenTokenCompanyServiceImpl extends BaseServiceImpl<OpenTokenCompanyMapper,OpenTokenCompany> implements OpenTokenCompanyService {

    @Resource
    private OpenTokenCompanySMapper openTokenCompanySMapper;
    @Resource
    private OpenTokenCompanyMapper openTokenCompanyMapper;

    @Override
    public Map<String,Object> queryAll(OpenTokenCompanyQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<OpenTokenCompany> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<OpenTokenCompany> queryAll(OpenTokenCompanyQueryCriteria criteria){
//        return listToDto(list(QueryHelpPlus.getPredicate(OpenTokenCompany.class, criteria)),openTokenCompanyMapper);
        return list(QueryHelpPlus.getPredicate(OpenTokenCompany.class, criteria));
    }

    @Override
    public void download(List<OpenTokenCompanyDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (OpenTokenCompanyDto openTokenCompany : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put(" companyId",  openTokenCompany.getCompanyId());
            map.put(" companyName",  openTokenCompany.getCompanyName());
            map.put(" appCode",  openTokenCompany.getAppCode());
            map.put(" appName",  openTokenCompany.getAppName());
            map.put(" orderNum",  openTokenCompany.getOrderNum());
            map.put(" createId",  openTokenCompany.getCreateId());
            map.put(" createTime",  openTokenCompany.getCreateTime());
            map.put(" updateId",  openTokenCompany.getUpdateId());
            map.put(" updateTime",  openTokenCompany.getUpdateTime());
            map.put(" delFlag",  openTokenCompany.getDelFlag());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}