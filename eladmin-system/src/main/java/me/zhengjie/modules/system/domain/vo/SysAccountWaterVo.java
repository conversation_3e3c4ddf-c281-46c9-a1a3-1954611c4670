package me.zhengjie.modules.system.domain.vo;

import lombok.Data;
import me.zhengjie.modules.system.domain.SysAccountWater;

import java.math.BigDecimal;

@Data
public class SysAccountWaterVo extends SysAccountWater {

    private String createDateFmt;

    /** 外账现金 */
    private Double outMoney;
    /** 内帐现金 */
    private Double inMoney;
    /** 外账 承兑*/
    private Double outDraft;
    /** 总现金 内+外现金 */
    private Double allMoney;
    /** 总额*/
    private Double sumMoney;
    private Integer plateId;
    private Integer deptId;
    // 收入汇总 外帐
    private BigDecimal receiveMoneySum;
    // 支出汇总 外帐
    private BigDecimal paymentMoneySum;
    // 收入汇总 内帐
    private BigDecimal receiveNeiMoneySum;
    // 支出汇总 内帐
    private BigDecimal paymentNeiMoneySum;
    // 收入汇总 承兑
    private BigDecimal receiveDraftSum;
    // 支出汇总 承兑
    private BigDecimal paymentDraftSum;
    // 收入汇总 总现金
    private BigDecimal receiveAllMoneySum;
    // 支出汇总 总现金
    private BigDecimal paymentAllMoneySum;

    // 收入汇总 总额
    private BigDecimal receiveSum;
    // 支出汇总 总额
    private BigDecimal paymentSum;
}
