/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.service.dto;

import lombok.Data;
import java.io.Serializable;

/**
* @website https://el-admin.vip
* @description /
* <AUTHOR>
* @date 2023-02-27
**/
@Data
public class FundsClassificationPlateDto implements Serializable {

    private String id;

    /** 板块id */
    private String plateId;

    /** 资金分类id */
    private String fundsClassId;

    /** 公司id */
    private String comId;

    /** 排序 */
    private String sortby;

    /** 1 正常 0 删除 */
    private Integer delFlag;
}