/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.system.rest;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zthzinfo.common.ResponseMapBuilder;
import me.zhengjie.annotation.Log;
import me.zhengjie.modules.system.domain.ImprestFundChangeBill;
import me.zhengjie.modules.system.domain.ImprestFundReocrd;
import me.zhengjie.modules.system.domain.bean.ImprestFundChangeBillBean;
import me.zhengjie.modules.system.domain.bean.ImprestFundMonthBillBean;
import me.zhengjie.modules.system.service.ImprestFundChangeBillService;
import me.zhengjie.modules.system.service.dto.ImprestFundChangeBillQueryCriteria;
import me.zhengjie.utils.SecurityUtils;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2022-11-14
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "imprestFundChangeBill管理")
@RequestMapping("/api/imprestFundChangeBill")
public class ImprestFundChangeBillController {

    private final ImprestFundChangeBillService imprestFundChangeBillService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('imprestFundChangeBill:list')")
    public void download(HttpServletResponse response, ImprestFundChangeBillQueryCriteria criteria) throws IOException {
        imprestFundChangeBillService.download(imprestFundChangeBillService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询imprestFundChangeBill")
    @ApiOperation("查询imprestFundChangeBill")
    @PreAuthorize("@el.check('imprestFundChangeBill:list')")
    public ResponseEntity<Object> query(ImprestFundChangeBillQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(imprestFundChangeBillService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增imprestFundChangeBill")
    @ApiOperation("新增imprestFundChangeBill")
    @PreAuthorize("@el.check('imprestFundChangeBill:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody ImprestFundChangeBill resources){
        return new ResponseEntity<>(imprestFundChangeBillService.save(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改imprestFundChangeBill")
    @ApiOperation("修改imprestFundChangeBill")
    @PreAuthorize("@el.check('imprestFundChangeBill:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody ImprestFundChangeBill resources){
        imprestFundChangeBillService.updateById(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除imprestFundChangeBill")
    @ApiOperation("删除imprestFundChangeBill")
    @PreAuthorize("@el.check('imprestFundChangeBill:del')")
    @DeleteMapping
    public ResponseEntity<Object> delete(@RequestBody String[] ids) {
        imprestFundChangeBillService.removeByIds(Arrays.asList(ids));
        return new ResponseEntity<>(HttpStatus.OK);
    }
    @PostMapping("/addFundChangeBill")
    public Map<String,Object> addFundChangeBill(@RequestBody ImprestFundChangeBillBean imprestFundChangeBillBean){
        imprestFundChangeBillService.saveAndRecordStatus(imprestFundChangeBillBean);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }

    @GetMapping("/changeBillById")
    public Map<String, Object> changeBillById(@RequestParam(value = "id") String id){
        ImprestFundChangeBill imprestFundChangeBill= imprestFundChangeBillService.getById(id);

        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .put("data",imprestFundChangeBill)
                .getResult();
    }
    @GetMapping("/changeRecordUnsubByFunId")
    public Map<String, Object> changeRecordUnsubByFunId(@RequestParam(value = "fundId") String fundId){
        // 根据账户 查询未提交的改帐记录
        List<ImprestFundChangeBill> imprestFundChangeBills= imprestFundChangeBillService.queryRecordByFundId(fundId);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .put("data",imprestFundChangeBills)
                .getResult();
    }
    @GetMapping("/changeListByIds")
    public Map<String, Object> changeListByIds(@RequestParam(value = "ids") String ids){
        LambdaQueryWrapper<ImprestFundChangeBill> imprestFundChangeBillLambdaQueryWrapper=new LambdaQueryWrapper<>();
        imprestFundChangeBillLambdaQueryWrapper.in(ImprestFundChangeBill::getId, Arrays.asList(ids.split(",")));
        List<ImprestFundChangeBill> imprestFundChangeBills= imprestFundChangeBillService.list(imprestFundChangeBillLambdaQueryWrapper);
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .put("data",imprestFundChangeBills)
                .getResult();
    }
    @GetMapping("/changeListUpdateStatusByIds")
    public Map<String, Object> changeListUpdateStatusByIds(@RequestParam(value = "ids") String ids,@RequestParam(value = "status") String status){

        imprestFundChangeBillService.updateChangeBillStatus(ids,status,SecurityUtils.getCurrentUserId());
        return ResponseMapBuilder.newBuilder()
                .putSuccess()
                .getResult();
    }
}