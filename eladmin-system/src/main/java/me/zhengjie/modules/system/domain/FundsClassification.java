package me.zhengjie.modules.system.domain;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description /
* <AUTHOR>
* @date 2023-02-23
**/
@Data
@TableName("funds_classification")
public class FundsClassification implements Serializable {


    @TableId
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "code")
    private String code;

    @ApiModelProperty(value = "类型 ")
    private String model;

    @ApiModelProperty(value = "是否可选择 1 可以 0 不可以")
    private Integer isSel;

    @ApiModelProperty(value = "上级id")
    private String parentId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "释义说明")
    private String explanation;

    @ApiModelProperty(value = "对应流量表id")
    private String flowId;

    @ApiModelProperty(value = "createBy")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "updateBy")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "createTime")
    private Timestamp createTime;

    @ApiModelProperty(value = "updateTime")
    private Timestamp updateTime;

    @ApiModelProperty(value = "param1")
    private String param1;

    @ApiModelProperty(value = "param2")
    private String param2;

    @ApiModelProperty(value = "param3")
    private String param3;
    @TableLogic(value = "1",delval = "0")
    private Integer delFlag;

    @ApiModelProperty(value = "certTemplId")
    private Integer certTemplId;

    private String uniqueMark;

    private String sortby;
    public void copy(FundsClassification source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}