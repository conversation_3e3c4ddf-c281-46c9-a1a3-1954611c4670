package me.zhengjie.modules.contract.utils;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

public class StupidValueDeserializer extends JsonDeserializer {
    @Override

    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        JsonToken jsonToken = p.getCurrentToken();

        if (jsonToken == JsonToken.VALUE_STRING) {
            return p.getValueAsString();

        }

        return null;

    }

}