package me.zhengjie.modules.contract.service;

import me.zhengjie.base.BaseService;
import me.zhengjie.modules.contract.domain.TplContract;
import me.zhengjie.modules.contract.service.dto.TplContractDto;
import me.zhengjie.modules.contract.service.dto.TplContractQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @description 服务接口
* <AUTHOR>
* @date 2021-12-14
**/
public interface TplContractService extends BaseService<TplContract> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TplContractQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TplContractDto>
    */
    List<TplContractDto> queryAll(TplContractQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TplContractDto> all, HttpServletResponse response) throws IOException;
}