package me.zhengjie.modules.contract.service;

import me.zhengjie.base.BaseService;
import me.zhengjie.modules.contract.domain.ContractChangeStatusLog;
import me.zhengjie.modules.contract.model.dto.ContractChangeStatusLogQueryCriteria;
import org.springframework.data.domain.Pageable;

import java.util.Map;
import java.util.List;

/**
 * 合同状态变更记录表(ContractChangeStatusLog)表服务接口
 *
 * <AUTHOR>
 * @since 2022-01-27 10:46:45
 */
public interface ContractChangeStatusLogService extends BaseService<ContractChangeStatusLog> {

    /**
     * 查询数据分页
     *
     * @param criteria 查询条件
     * @param pageable 分页参数
     * @return Map<String, Object>
     */
    Map<String, Object> queryAll(ContractChangeStatusLogQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     *
     * @param criteria 查询条件
     * @return List<ContractChangeStatusLog>
     */
    List<ContractChangeStatusLog> queryAll(ContractChangeStatusLogQueryCriteria criteria);


}
