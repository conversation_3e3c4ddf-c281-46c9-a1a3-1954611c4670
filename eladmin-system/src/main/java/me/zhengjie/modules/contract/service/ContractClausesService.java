package me.zhengjie.modules.contract.service;

import me.zhengjie.base.BaseService;
import me.zhengjie.modules.contract.domain.ContractClauses;
import me.zhengjie.modules.contract.service.dto.ContractClausesDto;
import me.zhengjie.modules.contract.service.dto.ContractClausesQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @description 服务接口
* <AUTHOR>
* @date 2021-12-14
**/
public interface ContractClausesService extends BaseService<ContractClauses> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(ContractClausesQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<ContractClausesDto>
    */
    List<ContractClausesDto> queryAll(ContractClausesQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<ContractClausesDto> all, HttpServletResponse response) throws IOException;
}