package me.zhengjie.modules.contract.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import me.zhengjie.modules.workflow.enums.ProcessStatusEnum;

/**
 * 合同模块
 * <AUTHOR>
 * @since 2021/12/15
 */
@AllArgsConstructor
@Getter
public enum ContractModuleEnums {
    /** 钢材 */
    GANGCAI(0),
    /** 散货 */
    SANHUO(1),
    /** 其他 */
    OTHER(2),
    /** 货运协议 */
    WULIU_OTHER(3),
    GOODS_GANGCAI(4)
    ;


    private final int key;

    public static String getDescByKey(int key){
            if (key == 0){
                return "钢材";
            }
            if (key == 1){
                return "散货";
            }
            if (key == 2){
                return "其他";
            }
            if (key == 3){
                return "货运协议";
            }
            if (key == 4){
                return "货物";
            }
        return null;
    }

}
