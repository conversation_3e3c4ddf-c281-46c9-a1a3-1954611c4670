package me.zhengjie.modules.contract.service;

import me.zhengjie.base.BaseService;
import me.zhengjie.modules.contract.domain.TplContractClauses;
import me.zhengjie.modules.contract.service.dto.TplContractClausesDto;
import me.zhengjie.modules.contract.service.dto.TplContractClausesQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @description 服务接口
* <AUTHOR>
* @date 2021-12-14
**/
public interface TplContractClausesService extends BaseService<TplContractClauses> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(TplContractClausesQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<TplContractClausesDto>
    */
    List<TplContractClausesDto> queryAll(TplContractClausesQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<TplContractClausesDto> all, HttpServletResponse response) throws IOException;
}