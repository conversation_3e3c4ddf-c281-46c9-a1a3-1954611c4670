package me.zhengjie.modules.contract.handler.exchange;

import cn.hutool.core.collection.CollUtil;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import me.zhengjie.modules.contract.enums.ContractTypeEnums;
import me.zhengjie.modules.contract.model.dto.ContractClausesFormDTO;
import org.springframework.stereotype.Component;

/**
 * 散货承租合同
 *
 * <AUTHOR>
 * @since 2021/12/17
 */
@Component
public class SanhuoChengzuYunshuExchange extends AbstractBusinessContractExchange {

    @Override
    public boolean support(Integer type) {
        return type == ContractTypeEnums.SANHUO_CHENGZU_YUNSHU.getKey();
    }

    @Override
    protected String getShipName() {
        return "ship_sanhuo_chengzu";
    }

    @Override
    protected void shipTable(Document document, Font font, ContractClausesFormDTO dto) throws DocumentException {
        basicShipTable(document,font,dto,table -> {
            cellItem("订金(元)",table,font);
            cellItem(dto.getDeposit(),table,font,cell -> cell.setColspan(3));
            cellItem("滞期费率(元/吨/天)",table,font);
            cellItem(dto.getDemurrageRate(),table,font,cell -> cell.setColspan(2));
        },table -> {
            cellItem("货名",table,font);
            cellItem("货重（吨）",table,font);
            cellItem("件数",table,font);
            cellItem("包装",table,font);
            cellItem("散杂体积(m³)",table,font);
            cellItem("运价（元/吨）",table,font);
            cellItem("开票类型",table,font);
            cellItem(dto.getGoodsName(),table,font);
            cellItem(dto.getGoodsWeight(),table,font);
            cellItem(dto.getQuantity(),table,font);
            cellItem(dto.getPack(),table,font);
            cellItem(dto.getBulkVolume(),table,font);
            cellItem(dto.getFreightRate(),table,font);
            cellItem(dto.getInvoiceType(),table,font);
            // 循环添加船价信息
            if (CollUtil.isNotEmpty(dto.getShipPriceList())) {
                for (ContractClausesFormDTO.ShipPrice shipPrice : dto.getShipPriceList()) {
                    cellItem(shipPrice.getGoodsName(), table, font);
                    cellItem(shipPrice.getGoodsWeight(), table, font);
                    cellItem(shipPrice.getQuantity(), table, font);
                    cellItem(shipPrice.getPack(), table, font);
                    cellItem(shipPrice.getBulkVolume(), table, font);
                    cellItem(shipPrice.getFreightRate(), table, font);
                    cellItem(shipPrice.getInvoiceType(), table, font);
                }
            }
        });
    }
}
