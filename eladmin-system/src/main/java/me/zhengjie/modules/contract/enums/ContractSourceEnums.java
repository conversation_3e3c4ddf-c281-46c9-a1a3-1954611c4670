package me.zhengjie.modules.contract.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同来源
 *
 * <AUTHOR>
 * @since 2021/12/21
 */
@AllArgsConstructor
@Getter
public enum ContractSourceEnums {

    /** 钢材系统 */
    GANGCAI("gangcai"),
    /** 财务系统 */
    CAIWU("caiwu"),
    /** 散货 */
    SANHUO("sanhuo")
    ;

    private final String value;


    public static ContractSourceEnums parse(String v) {
        for (ContractSourceEnums value : values()) {
            if (StrUtil.equals(v,value.value)) {
                return value;
            }
        }
        return null;
    }

}
