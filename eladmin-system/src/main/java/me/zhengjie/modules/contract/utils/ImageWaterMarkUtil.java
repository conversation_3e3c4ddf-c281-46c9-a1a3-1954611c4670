package me.zhengjie.modules.contract.utils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.Arrays;
import java.util.Base64;

public class ImageWaterMarkUtil {

    // 水印透明度
    private static float alpha = 0.5f;
    // 水印横向位置
    private static int positionWidth = 150;
    // 水印纵向位置
    private static int positionHeight = 300;
    // 水印文字字体
    private static Font font = new Font("宋体", Font.PLAIN, 24);
    // 水印文字颜色
    private static Color color = new Color(193, 193, 193);

    /**
     * 给图片添加水印文字、可设置水印文字的旋转角度
     *
     * @param logoText   水印文字
     * @param srcImgPath 源文件路径
     * @param os         输出流
     * @param degree     设置角度
     */
    public static void  markImageByText(String logoText, String srcImgPath,
                                       OutputStream os, Integer degree) {

        InputStream is = null;
        try {
            // 1、源图片
            Image srcImg = ImageIO.read(new URL(srcImgPath));
            BufferedImage buffImg = new BufferedImage(srcImg.getWidth(null),
                    srcImg.getHeight(null), BufferedImage.TYPE_INT_RGB);

            // 2、得到画笔对象
            Graphics2D g = buffImg.createGraphics();
            // 3、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                    RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g.drawImage(
                    srcImg.getScaledInstance(srcImg.getWidth(null),
                            srcImg.getHeight(null), Image.SCALE_SMOOTH), 0, 0,
                    null);
            // 4、设置水印旋转
            if (null != degree) {
                g.rotate(Math.toRadians(degree),
                        (double) buffImg.getWidth() +10,
                        (double) buffImg.getHeight()-10);
            }
            // 5、设置水印文字颜色
            g.setColor(color);
            // 6、设置水印文字Font
            g.setFont(font);
            // 7、设置水印文字透明度
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP,
                    alpha));
            // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            int c =  buffImg.getWidth();
            int d =  buffImg.getHeight();

            g.drawString(logoText, 20, d-20);
            System.out.println(c + "====="+ d);
            // 9、释放资源
            g.dispose();
            // 10、生成图片
            ImageIO.write(buffImg, "PNG", os);

            System.out.println("图片完成添加水印文字");

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != is)
                    is.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (null != os)
                    os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static String getOssImageUrl(String inputImageUrl, String text_e) {
        System.out.println("text_e = " +text_e);
        System.out.println("text_e = " +text_e.length());
        String text = format(text_e);
        System.out.println("text = " +text.length());
        String file_url_mark = inputImageUrl + "?x-oss-process=image/watermark,type_d3F5LXplbmhlaQ,size_20,text_" + text + ",color_777777,shadow_50,t_79,g_south,rotate_1";
        return file_url_mark;
    }


    /**
     * 阿里云对图片进行水印文字的处理
     * @param str
     * @return
     */
    public static String format(String str){
        String text = Base64.getEncoder().encodeToString(str.getBytes());
        text = text.replace("+", "-");
        text = text.replace("/", "_");
        StringBuilder sb = new StringBuilder(text);
        while (sb.charAt(sb.length() - 1) == '=') {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }





}
