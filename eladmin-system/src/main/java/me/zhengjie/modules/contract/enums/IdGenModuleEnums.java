package me.zhengjie.modules.contract.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * IdGen module
 * <AUTHOR>
 * @since 2021/10/18
 */
@Getter
@AllArgsConstructor
public enum IdGenModuleEnums {
    /** 合同-钢材 */
    CONTRACT_GC(0,"GC"),
    /** 合同-散货 */
    CONTRACT_SH(1,"SH"),
    /** 合同-其他 */
    CONTRACT_OTHER(2,"QT"),
    ;

    final private int value;
    final private String code;

    public static String parseReturnCode(Integer value) {
        if (value == null) {
            return null;
        }
        for (IdGenModuleEnums item : values()) {
            if (item.value == value) {
                return item.code;
            }
        }
        return null;
    }

}
