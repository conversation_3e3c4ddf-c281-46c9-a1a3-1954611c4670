package me.zhengjie.modules.dbchange.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.dlcg.tms.bean.CustomerGoodsCostDetailBean;
import com.dlcg.tms.client.ShipLineClient;
import com.dlcg.tms.entity.CustomerGoodsCostDetail;
import com.dlcg.tms.entity.ShipLineShipCost;
import lombok.extern.slf4j.Slf4j;
import me.zhengjie.modules.dbchange.ImprestFundReocrdEvent;
import me.zhengjie.modules.kafka.DBChangeBaseListenerSync;
import me.zhengjie.modules.system.domain.ImprestFund;
import me.zhengjie.modules.system.domain.ImprestFundReocrd;
import me.zhengjie.modules.system.service.ImprestFundService;
import me.zhengjie.modules.until.ParamsKeys;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

@Slf4j
@Component
public class ImprestFundReocrdListener  extends DBChangeBaseListenerSync<ImprestFundReocrdEvent> {
    @Autowired
    ShipLineClient shipLineClient;
    @Autowired
    ImprestFundService imprestFundService;
    @Override
    public void onInsert(JSONObject data) {
        // 判断是否绑定船期id，根据船期id 新增船上费用，绑定日志 id
        log.info("监听到ImprestFundReocrd表发生了变化，变化字段：{}",data.toJSONString());
        ImprestFundReocrd imprestFundReocrd=data.toJavaObject(ImprestFundReocrd.class);
        if(StrUtil.isBlank(imprestFundReocrd.getParam3())){
            return;
        }
        if(StrUtil.isBlank(imprestFundReocrd.getShipLineInfo())){
            return;
        }
        // 根据船期id，类型 支出（新增船上费用） 收入（不处理）
        if(!Objects.equals(imprestFundReocrd.getState(),1)){
            return;
        }

        String shipLineId = imprestFundReocrd.getParam3().split(",")[0].split("\\|")[0];
        // 判断是货物费用还是船上费用
        JSONObject jo= JSONObject.parseObject(imprestFundReocrd.getShipLineInfo());
        String fundType = jo.getString(ParamsKeys.FUND_TYPE);
        if(StrUtil.isBlank(fundType) || fundType.equals(ParamsKeys.FUND_TYPE_SHIP)){
            ShipLineShipCost shipLineShipCost = JSONObject.parseObject(imprestFundReocrd.getShipLineInfo()).toJavaObject(ShipLineShipCost.class);
            // 新增船上费用
//        ShipLineShipCost shipLineShipCost=new ShipLineShipCost();
            shipLineShipCost.setShipLineId(shipLineId);
            shipLineShipCost.setImprestFundRecordId(imprestFundReocrd.getId());
            shipLineShipCost.setIsIncome(0);
            shipLineShipCost.setShenpi(1);

            shipLineClient.saveShipLineShipCost(shipLineShipCost);
        }else if(fundType.equals(ParamsKeys.FUND_TYPE_GOODS)){
            // 新增货物费用
            CustomerGoodsCostDetail customerGoodsCostDetail= JSONObject.parseObject(imprestFundReocrd.getShipLineInfo()).toJavaObject(CustomerGoodsCostDetail.class);
            customerGoodsCostDetail.setShenpi(1);
            customerGoodsCostDetail.setIncomeOut(0);// 支出
            customerGoodsCostDetail.setCustomerSupplier(0);// 供应商
            customerGoodsCostDetail.setInvoice(1);
            customerGoodsCostDetail.setInvoiceType("10");// 无票
            customerGoodsCostDetail.setCreateBy("ImprestFundReocrd|"+imprestFundReocrd.getCreateBy());
            customerGoodsCostDetail.setImprestFundRecordId(imprestFundReocrd.getId());

            shipLineClient.saveCustomerGoodsCostDetail(customerGoodsCostDetail);
        }


    }
    @Override
    public void onDelete(JSONObject data) {
        // 根据日志id 删除船上费用
        ImprestFundReocrd imprestFundReocrd=data.toJavaObject(ImprestFundReocrd.class);
        if(StrUtil.isBlank(imprestFundReocrd.getParam3())){
            return;
        }
        if(StrUtil.isBlank(imprestFundReocrd.getShipLineInfo())){
            return;
        }
        // 根据船期id，类型 支出（新增船上费用） 收入（不处理）
        if(!Objects.equals(imprestFundReocrd.getState(),1)){
            return;
        }
        // 旧数据
        JSONObject jo= JSONObject.parseObject(imprestFundReocrd.getShipLineInfo());
        String fundType = jo.getString(ParamsKeys.FUND_TYPE);
        if(StrUtil.isBlank(fundType) || fundType.equals(ParamsKeys.FUND_TYPE_SHIP)){
            shipLineClient.deleteShipLineShipCost(imprestFundReocrd.getId());
        }else if(fundType.equals(ParamsKeys.FUND_TYPE_GOODS)){
            shipLineClient.deleteGoodsCostDetail(imprestFundReocrd.getId());
        }

    }
    @Override
    public void onUpdate(JSONObject data, JSONObject old) {
        // 根据日志ID 修改船上费用相关数据
        log.info("监听到ImprestFundReocrdListener表发生了变化，变化字段：{}",old.toJSONString());
        if(processIfDelFlagDefault(data,old)){
            return;
        }
        // 只处理金额状态变更的事件
//        if(!old.containsKey("balance")) {
//            return;
//        }
        ImprestFundReocrd imprestFundReocrd=data.toJavaObject(ImprestFundReocrd.class);
        // 处理余额
        ImprestFundReocrd oldImprestFundReocrd=old.toJavaObject(ImprestFundReocrd.class);
        // 余额变
        if(old.containsKey("balance")){
            BigDecimal bal = imprestFundReocrd.getBalance();
            BigDecimal oldBal = oldImprestFundReocrd.getBalance();
            // 处理 账户余额
            BigDecimal diffMoney= bal.subtract(oldBal);
            // 收入 支出
            if(imprestFundReocrd.getState()==1){
                // 支出
                diffMoney = diffMoney.multiply(new BigDecimal("-1"));
            }
//            else{
//                // 收入
////                diffMoney = diffMoney;
//            }
            // 余额变化
            LambdaUpdateWrapper<ImprestFund> updateWrapper=new LambdaUpdateWrapper<>();
            updateWrapper.eq(ImprestFund::getId,imprestFundReocrd.getImprestFundId());
            updateWrapper.setSql("balance=balance+"+diffMoney);
            imprestFundService.update(updateWrapper);
        }

        if(StrUtil.isBlank(imprestFundReocrd.getParam3())){
            // 没有船期id
            return;
        }
        if(StrUtil.isBlank(imprestFundReocrd.getShipLineInfo())){
            // 没有相关数据
            return;
        }
        // 根据船期id，类型 支出（新增船上费用） 收入（不处理）
        if(!Objects.equals(imprestFundReocrd.getState(),1)){
            return;
        }
        // 判断类型
        JSONObject jo= JSONObject.parseObject(imprestFundReocrd.getShipLineInfo());
        String fundType = jo.getString(ParamsKeys.FUND_TYPE);
        if(StrUtil.isBlank(fundType) || fundType.equals(ParamsKeys.FUND_TYPE_SHIP)){
            // 单价修改 同步到shipLineInfo
            if(old.containsKey("balance")){
//                ShipLineShipCost shipLineShipCost = jo.toJavaObject(ShipLineShipCost.class);
                ShipLineShipCost updateShipLineShipCost=new ShipLineShipCost();
                updateShipLineShipCost.setImprestFundRecordId(imprestFundReocrd.getId());
                updateShipLineShipCost.setCostPriceNo(imprestFundReocrd.getBalance().doubleValue());
                updateShipLineShipCost.setCostPrice(imprestFundReocrd.getBalance().doubleValue());
                updateShipLineShipCost.setUpdateBy("ImprestFundReocrd|"+imprestFundReocrd.getUpdateBy());
                shipLineClient.updateShipLineShipCost(updateShipLineShipCost);
            }
        }else{
            if(old.containsKey("balance")){
                CustomerGoodsCostDetailBean customerGoodsCostDetail = jo.toJavaObject(CustomerGoodsCostDetailBean.class);
                CustomerGoodsCostDetail updateCustomerGoodsCostDetail=new CustomerGoodsCostDetail();
                updateCustomerGoodsCostDetail.setImprestFundRecordId(imprestFundReocrd.getId());
                updateCustomerGoodsCostDetail.setTotalPriceNo(imprestFundReocrd.getBalance());
                // 总价/吨位 = 单价
                if(StrUtil.isNotBlank(customerGoodsCostDetail.getTonnage())){
//                    updateCustomerGoodsCostDetail.setPriceNo(imprestFundReocrd.getBalance().doubleValue()/Double.parseDouble(customerGoodsCostDetail.getTonnage()));
                    updateCustomerGoodsCostDetail.setPrice(imprestFundReocrd.getBalance().divide(new BigDecimal(customerGoodsCostDetail.getTonnage()),2,BigDecimal.ROUND_HALF_UP));
                    updateCustomerGoodsCostDetail.setPrice(updateCustomerGoodsCostDetail.getPriceNo());
                }
                updateCustomerGoodsCostDetail.setUpdateBy("ImprestFundReocrd|"+imprestFundReocrd.getUpdateBy());
                shipLineClient.updateGoodsCostDetail(updateCustomerGoodsCostDetail);
            }
        }


    }
}
