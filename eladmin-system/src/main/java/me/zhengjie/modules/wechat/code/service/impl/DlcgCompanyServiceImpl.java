package me.zhengjie.modules.wechat.code.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import me.zhengjie.modules.business.domain.SysUser;
import me.zhengjie.modules.business.service.SysUserService;
import me.zhengjie.modules.system.service.DictDetailService;
import me.zhengjie.modules.system.service.DictService;
import me.zhengjie.modules.system.service.UserService;
import me.zhengjie.modules.system.service.dto.DictDetailDto;
import me.zhengjie.modules.wechat.code.domain.DlcgCompany;
import me.zhengjie.base.BaseServiceImpl;
import com.github.pagehelper.PageInfo;
import me.zhengjie.modules.wechat.code.domain.WxDepartment;
import me.zhengjie.modules.wechat.code.service.WxDepartUserService;
import me.zhengjie.modules.wechat.code.service.WxDepartmentService;
import me.zhengjie.modules.wechat.code.service.dto.DeptTreeDto;
import me.zhengjie.modules.wechat.code.service.tree.TreeUtils;
import me.zhengjie.utils.*;
import me.zhengjie.modules.wechat.code.mapper.DlcgCompanyMapper;
import me.zhengjie.modules.wechat.code.service.DlcgCompanyService;
import me.zhengjie.modules.wechat.code.service.dto.DlcgCompanyDto;
import me.zhengjie.modules.wechat.code.service.dto.DlcgCompanyQueryCriteria;
import me.zhengjie.modules.wechat.code.service.mapstruct.DlcgCompanySMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.springframework.data.domain.Pageable;

import java.util.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.stream.Collectors;

/**
* @description 服务实现
* <AUTHOR>
* @date 2021-09-07
**/
@Service
public class DlcgCompanyServiceImpl extends BaseServiceImpl<DlcgCompanyMapper,DlcgCompany> implements DlcgCompanyService {

    @Resource
    private DlcgCompanySMapper dlcgCompanySMapper;
    @Resource
    private DlcgCompanyMapper dlcgCompanyMapper;
    @Resource
    private SysUserService sysUserService1;
    @Resource
    private WxDepartmentService wxDepartmentService1;

    @Override
    public Map<String,Object> queryAll(DlcgCompanyQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<DlcgCompanyDto> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<DlcgCompanyDto> queryAll(DlcgCompanyQueryCriteria criteria){
        return listToDto(list(QueryHelpPlus.getPredicate(DlcgCompany.class, criteria)),dlcgCompanySMapper);
    }

    @Override
    public void download(List<DlcgCompanyDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (DlcgCompanyDto dlcgCompany : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("公司名称", dlcgCompany.getName());
            map.put("上级公司ID", dlcgCompany.getParentId());
            map.put("企业ID", dlcgCompany.getCorpId());
            map.put("企业secret", dlcgCompany.getCorpSecret());
            map.put("排序", dlcgCompany.getSort());
            map.put("创建时间", dlcgCompany.getCreateTime());
            map.put("更新时间", dlcgCompany.getUpdateTime());
            map.put("是否删除 0：已删除，1：正常", dlcgCompany.getDelFlag());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Resource
    private WxDepartmentService wxDepartmentService;
    @Resource
    private WxDepartUserService departUserService;
    @Resource
    private UserService sysUserService;

    @Override
    public List<DeptTreeDto> getTreeStruct(String deptName) {
        LambdaQueryWrapper<DlcgCompany> queryWrapper = new LambdaQueryWrapper<DlcgCompany>()
                .orderByDesc(DlcgCompany::getSort);
        if (StrUtil.isNotBlank(deptName)) {
            queryWrapper.like(DlcgCompany::getName, deptName);
        }
        List<DeptTreeDto> list = list(queryWrapper)
                .stream()
                .map(item -> {
                    DeptTreeDto node = new DeptTreeDto();
                    node.setName(item.getName());
                    node.setCorpId(item.getCorpId());
                    node.setCorpSecret(item.getCorpSecret());
                    node.setSort(item.getSort());
                    node.setCreateTime(item.getCreateTime());
                    node.setUpdateTime(item.getUpdateTime());
                    node.setDelFlag(item.getDelFlag());
                    node.setId(item.getId());
                    node.setParentId(item.getParentId());
                    node.setLabel(item.getName());
                    node.setAddEnable(false);
                    node.setEditEnable(true);
                    node.setSyncEnable(true);
                    node.setNodeType(0);
                    return node;
                }).collect(Collectors.toList());
        Integer maxId = list.stream().max(Comparator.comparing(DeptTreeDto::getId)).map(DeptTreeDto::getId).orElse(0);
        List<DeptTreeDto> result = TreeUtils.buildByRecursive(list, 0);


        // 查询子公司下的部门
        List<String> corpIds = list.stream()
                .filter(item -> StrUtil.isNotBlank(item.getCorpId()))
                .map(DeptTreeDto::getCorpId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(corpIds)) {
            Map<String, List<DeptTreeDto>> deptMap = wxDepartmentService.list(new LambdaQueryWrapper<WxDepartment>()
                    .in(WxDepartment::getCorpId, corpIds)
                    .orderByDesc(WxDepartment::getOrderNum))
                    .stream()
                    .map(item -> {
                        DeptTreeDto node = new DeptTreeDto();
                        node.setName(item.getName());
                        node.setLabel(item.getName());
                        node.setCorpId(item.getCorpId());
                        node.setSort(item.getOrderNum());
                        node.setCreateTime(item.getCreateDate());
                        node.setUpdateTime(item.getUpdateDate());
                        node.setId(maxId + item.getDeptId().intValue());
                        node.setParentId(item.getParentId() == 0 ? 0 : item.getParentId() + maxId);
                        node.setAddEnable(false);
                        node.setEditEnable(false);
                        node.setSyncEnable(false);
                        node.setNodeType(1);
                        return node;
                    }).collect(Collectors.groupingBy(DeptTreeDto::getCorpId));
            deptMap.forEach((key, value) -> {
                List<DeptTreeDto> childs = TreeUtils.buildByRecursive(value, 0);
                DeptTreeDto node = findNode(result, key);
                if (null != node) {
                    node.getChildren().addAll(childs);
                }
            });
        }
        return result;
    }

    @Override
    public List<DeptTreeDto> getTreeselect() {
        LambdaQueryWrapper<DlcgCompany> queryWrapper = new LambdaQueryWrapper<DlcgCompany>()
                .orderByDesc(DlcgCompany::getSort);
        List<DeptTreeDto> list = list(queryWrapper)
                .stream()
                .map(item -> {
                    DeptTreeDto node = new DeptTreeDto();
                    node.setName(item.getName());
                    node.setCorpId(item.getCorpId());
                    node.setCorpSecret(item.getCorpSecret());
                    node.setSort(item.getSort());
                    node.setCreateTime(item.getCreateTime());
                    node.setUpdateTime(item.getUpdateTime());
                    node.setDelFlag(item.getDelFlag());
                    node.setId(item.getId());
                    node.setParentId(item.getParentId());
                    node.setLabel(item.getName());
                    node.setAddEnable(true);
                    node.setEditEnable(item.getParentId() != 0);
                    node.setSyncEnable(item.getParentId() != 0);
                    node.setNodeType(0);
                    return node;
                }).collect(Collectors.toList());
        List<DeptTreeDto> result = TreeUtils.buildByRecursive(list, 0);


        // 查询子公司下的部门
        List<String> corpIds = list.stream()
                .filter(item -> StrUtil.isNotBlank(item.getCorpId()))
                .map(DeptTreeDto::getCorpId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(corpIds)) {
            Map<String, List<DeptTreeDto>> deptMap = wxDepartmentService.list(new LambdaQueryWrapper<WxDepartment>()
                    .in(WxDepartment::getCorpId, corpIds)
                    .orderByDesc(WxDepartment::getOrderNum))
                    .stream()
                    .map(item -> {
                        DeptTreeDto node = new DeptTreeDto();
                        node.setName(item.getName());
                        node.setLabel(item.getName());
                        node.setCorpId(item.getCorpId());
                        node.setSort(item.getOrderNum());
                        node.setCreateTime(item.getCreateDate());
                        node.setUpdateTime(item.getUpdateDate());
                        node.setId(item.getDeptId().intValue());
                        node.setParentId(item.getParentId());
                        node.setAddEnable(false);
                        node.setEditEnable(false);
                        node.setSyncEnable(false);
                        node.setNodeType(1);
                        return node;
                    }).collect(Collectors.groupingBy(DeptTreeDto::getCorpId));
            deptMap.forEach((key, value) -> {
                List<DeptTreeDto> childs = TreeUtils.buildByRecursive(value, 0);
                DeptTreeDto node = findNode(result, key);
                if (null != node) {
                    node.getChildren().addAll(childs);
                }
            });
        }
        return result;
    }

    @Autowired
    DictService dictService;
    @Autowired
    DictDetailService dictDetailService;

    @Override
    public List<SysUser> getTreeselectUser() {
        List<SysUser> res = new ArrayList<>();

        List<DictDetailDto> dictByName = dictDetailService.getDictByName(AppConstants.KEY_PROCESS_CC_USERS);
        if (dictByName == null || dictByName.size() == 0) {
            return res;
        }
        List<String> ids = dictByName.stream()
            .sorted(Comparator.comparing(DictDetailDto::getDictSort))
            .map(DictDetailDto::getValue)
            .collect(Collectors.toList());

        LambdaQueryWrapper<SysUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(SysUser::getUserId,ids);

        List<SysUser> userList = sysUserService1.list(lambdaQueryWrapper);

        for (String id : ids) {
            SysUser user = userList.stream()
                .filter(i -> Objects.equals(id, i.getUserId()))
                .findFirst().orElse(null);

            user.setPassword(null);
            user.setPwdResetTime(null);
            res.add(user);
        }

        return res;
    }

    private DeptTreeDto findNode(List<DeptTreeDto> list, String key) {
        for (DeptTreeDto cs : list) {
            if (StrUtil.equals(key, cs.getCorpId())) {
                return cs;
            }
            DeptTreeDto node = findNode(cs.getChildren(), key);
            if (node != null) {
                return node;
            }
        }
        return null;
    }

    private DeptTreeDto findNodeUser(List<DeptTreeDto> list, Integer key) {
        for (DeptTreeDto cs : list) {
            if (cs.getNodeType() == 1 && cs.getId() == key) {
                return cs;
            }
            DeptTreeDto node = findNodeUser(cs.getChildren(), key);
            if (node != null) {
                return node;
            }
        }
        return null;
    }
}
