package me.zhengjie.modules.wechat.code.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import me.zhengjie.modules.business.domain.SysUser;
import me.zhengjie.modules.business.service.bean.UserCompany;
import me.zhengjie.modules.business.service.bean.UserPhoneBean;
import me.zhengjie.modules.business.service.dto.DepartmentAndParentDto;
import me.zhengjie.modules.business.service.dto.UserDeptDto;
import me.zhengjie.modules.wechat.code.domain.WxDepartment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-09-09
 */
@Mapper
public interface WxDepartmentMapper extends BaseMapper<WxDepartment> {

    @Select("<script>" +
            "select su.user_id as uid, su.nick_name as `name`,su.phone as phone,wd.dept_id as deptid, wd.parent_id as parentid,wd.`name` as dname "+
            "FROM sys_user as su "+
            "Left JOIN wx_department as wd on wd.dept_id = su.main_department and wd.del_flag=0 "+
            "WHERE su.enabled = 1 "+
            "</script>")
    List<UserPhoneBean> getUserPhone(@Param("uid") String uid);

    @Select("<script>" +
        "SELECT su.nick_name as username,wd.id as deid, wxc.id as cid, wxc.name_abbreviation as jiancheng " +
        "FROM sys_user as su " +
        "LEFT JOIN wx_department as wd on wd.dept_id = su.main_department  and wd.del_flag=0 " +
        "LEFT JOIN wx_department as wxc on wd.parent_id != 1 and wd.parent_id = wxc.dept_id  and wxc.del_flag=0 " +
        "WHERE su.user_id = #{uid} " +
        "</script>")
    List<UserCompany> getuserxinxi(@Param("uid") String uid);

    @Select("SELECT\n"
                    + "\twd.dept_id\n"
                    + "FROM\n"
                    + "\twx_department wd\n"
                    + "\tLEFT JOIN wx_depart_user wdu ON wd.id = wdu.dept_id\n"
                    + "\tWHERE wdu.user_id = #{uid} \n"
                    + "\tAND wd.parent_id = 1")
    List<Long> getUserDeptIdList(@Param("uid") String uid);
    
    List<UserCompany> getUserDeptPlateByList(
            @Param("ids") List<String> ids,
            @Param("isMain") Integer isMain
    );

    List<UserCompany> getUserDeptPlateNoMainByList(
            @Param("ids") List<String> ids
    );
    List<WxDepartment> getCurrentUserDept(@Param("uid") String currentUserId);

    List<SysUser> getLeadersByDeptId(@Param("deptId") Long deptId, @Param("deptPKId") String deptPKId);

    List<UserDeptDto> getUserDepts(@Param("wxUserIds") List<String> wxUserIds, @Param("deptId") Long deptId, @Param("deptPKId") String deptPKId);
    List<UserDeptDto> getUserDeptsByUserId(@Param("userIds") List<String> userIds);

    List<DepartmentAndParentDto> getDepartmentAndParentByDeptIds(@Param("deptIds") List<String> deptIds);
}
