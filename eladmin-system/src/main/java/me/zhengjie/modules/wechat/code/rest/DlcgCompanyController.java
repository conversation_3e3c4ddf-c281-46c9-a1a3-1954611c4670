package me.zhengjie.modules.wechat.code.rest;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zthzinfo.common.ResponseMapBuilder;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import me.zhengjie.modules.exception.AlertException;
import me.zhengjie.modules.wechat.code.domain.DlcgCompany;
import me.zhengjie.modules.wechat.code.service.DlcgCompanyService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成功集团公司表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-24
 */
@RestController
@RequestMapping("/api/dlcgCompany")
@RequiredArgsConstructor
@Api(tags = "code管理")
public class DlcgCompanyController {

    @Resource
    private DlcgCompanyService dlcgCompanyService;

    @GetMapping("/table")
    public Map<String,Object> table(
            @RequestParam(required = false,value = "deptName") String deptName
    ){
        return ResponseMapBuilder
                .newBuilder().putSuccess()
                .put("data",dlcgCompanyService.getTreeStruct(deptName))
                .getResult();
    }

    @GetMapping("/treeselect")
    public Map<String,Object> treeselect(){
        return ResponseMapBuilder
                .newBuilder().putSuccess()
                .put("data",dlcgCompanyService.getTreeselect())
                .getResult();
    }

    @GetMapping("/treeselect/user")
    public Map<String,Object> treeselectUser(){
        return ResponseMapBuilder
                .newBuilder().putSuccess()
                .put("data",dlcgCompanyService.getTreeselectUser())
                .getResult();
    }



    @GetMapping("/list")
    public Map<String,Object> list(
            @RequestParam(required = false,value = "deptName") String deptName
    ){
        LambdaQueryWrapper<DlcgCompany> queryWrapper = new LambdaQueryWrapper<DlcgCompany>()
                .orderByDesc(DlcgCompany::getSort);
        if (StrUtil.isNotBlank(deptName)){
            queryWrapper.like(DlcgCompany::getName, deptName);
        }
        List<DlcgCompany> list = dlcgCompanyService.list(queryWrapper);

        return ResponseMapBuilder
                .newBuilder().putSuccess()
                .put("data",list)
                .getResult();
    }

    @GetMapping("/{id}")
    public Map<String,Object> get(@PathVariable("id") String id){
        return ResponseMapBuilder
                .newBuilder().putSuccess()
                .put("data",dlcgCompanyService.getById(id))
                .getResult();
    }

    @PostMapping
    public Map<String,Object> add(
            DlcgCompany dlcgCompany
    ){
        Assert.notNull(dlcgCompany);
        if (dlcgCompanyService.list(new LambdaQueryWrapper<DlcgCompany>().eq(DlcgCompany::getName,dlcgCompany.getName())).size() > 0){
            throw new AlertException("["+dlcgCompany.getName()+"]该名称已被使用");
        }
        dlcgCompanyService.save(dlcgCompany);
        return ResponseMapBuilder
                .newBuilder().putSuccess()
                .getResult();
    }

    @PutMapping
    public Map<String,Object> update(
            DlcgCompany dlcgCompany
    ){
        Assert.notNull(dlcgCompany);
        Assert.notNull(dlcgCompany.getId());
        DlcgCompany one = dlcgCompanyService.getById(dlcgCompany.getId());
        if (one == null){
            throw new AlertException("获取公司失败");
        }
        if (!StrUtil.equals(dlcgCompany.getName(),one.getName())){
            if (dlcgCompanyService.list(new LambdaQueryWrapper<DlcgCompany>().eq(DlcgCompany::getName,dlcgCompany.getName())).size() > 0){
                throw new AlertException("["+dlcgCompany.getName()+"]该名称已被使用");
            }
        }
        dlcgCompanyService.updateById(dlcgCompany);
        return ResponseMapBuilder
                .newBuilder().putSuccess()
                .getResult();
    }



}
