package me.zhengjie.modules.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import me.zhengjie.base.BaseServiceImpl;
import me.zhengjie.modules.business.domain.SysUser;
import me.zhengjie.modules.business.mapper.SysUserMapper;
import me.zhengjie.modules.business.service.SysUserService;
import me.zhengjie.modules.business.service.dto.SysUserDto;
import me.zhengjie.modules.business.service.dto.SysUserQueryCriteria;
import me.zhengjie.modules.business.service.dto.UserDeptDto;
import me.zhengjie.modules.business.service.mapstruct.SysUserSMapper;
import me.zhengjie.modules.security.service.UserCacheClean;
import me.zhengjie.modules.system.domain.document.DictGobal;
import me.zhengjie.modules.system.service.dto.RoleSmallDto;
import me.zhengjie.modules.system.service.dto.UserDto;
import me.zhengjie.modules.voucher.enums.DayAccountEnum;
import me.zhengjie.modules.voucher.enums.DictCodeEnum;
import me.zhengjie.modules.wechat.code.service.QywxApplicationService;
import me.zhengjie.modules.wechat.code.service.WxDepartUserService;
import me.zhengjie.modules.wechat.code.service.WxDepartmentService;
import me.zhengjie.modules.workflow.model.document.DProcessInstance;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.utils.QueryHelpPlus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
* @description 服务实现
* <AUTHOR>
* @date 2021-10-18
**/
@Service
@RequiredArgsConstructor
@Log4j2
public class SysUserServiceImpl extends BaseServiceImpl<SysUserMapper,SysUser> implements SysUserService {


    @Autowired
    ObjectMapper objectMapper;

    @Resource
    private SysUserSMapper sysUserSMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    WxDepartUserService wxDepartUserService;
    @Resource
    WxDepartmentService wxDepartmentService;
    @Resource
    QywxApplicationService qywxApplicationService;
    @Resource
    MongoTemplate mongoTemplate;

    private final UserCacheClean userCacheClean;

    @Override
    public Map<String,Object> queryAll(SysUserQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<SysUserDto> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<SysUserDto> queryAll(SysUserQueryCriteria criteria){
        return listToDto(list(QueryHelpPlus.getPredicate(SysUser.class, criteria)),sysUserSMapper);
    }

    @Override
    public void download(List<SysUserDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SysUserDto sysUser : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("部门名称", sysUser.getDeptId());
            map.put("用户名", sysUser.getUsername());
            map.put("昵称", sysUser.getNickName());
            map.put("性别", sysUser.getGender());
            map.put("手机号码", sysUser.getPhone());
            map.put("邮箱", sysUser.getEmail());
            map.put("头像地址", sysUser.getAvatarName());
            map.put("头像真实路径", sysUser.getAvatarPath());
            map.put("密码", sysUser.getPassword());
            map.put("是否为admin账号", sysUser.getIsAdmin());
            map.put("状态：1启用、0禁用", sysUser.getEnabled());
            map.put("创建者", sysUser.getCreateBy());
            map.put("更新者", sysUser.getUpdateBy());
            map.put("修改密码的时间", sysUser.getPwdResetTime());
            map.put("创建日期", sysUser.getCreateTime());
            map.put("更新时间", sysUser.getUpdateTime());
            map.put(" wxUserId",  sysUser.getWxUserId());
            map.put(" wxPosition",  sysUser.getWxPosition());
            map.put(" wxQrCode",  sysUser.getWxQrCode());
            map.put(" wxStatus",  sysUser.getWxStatus());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public SysUser getWxUserByUserId(String userId) {
        SysUser one = getOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getWxUserId, userId).last("limit 1"));
//        if(null == one){
//            // 该用户未在系统中进行注册过，需要注册
//            QywxApplication app = qywxApplicationService.getOne(new LambdaQueryWrapper<QywxApplication>().eq(QywxApplication::getAppKey, OaConstants.APPKEY_MOBILE).last("limit 1"));
//            if(app == null){
//                throw new AlertException("未配置企业应用");
//            }
//            WxCpService cpService = WxCpConfiguration.getCpService(app.getCorpId());
//            try {
//                WxCpUser wxCpUser = cpService.getUserService().getById(userId);
//                SysUser sysUser = convertUserToSys(wxCpUser,null);
//
//                // 设置部门
//                List<Long> deptList = new ArrayList<>();
//                for (Long departId : wxCpUser.getDepartIds()) {
//                    deptList.add(departId);
//                }
//                if(CollectionUtil.isNotEmpty(deptList)){
//                    List<String> list = wxDepartmentService.list(new LambdaQueryWrapper<me.zhengjie.modules.wechat.code.domain.WxDepartment>()
//                            .in(me.zhengjie.modules.wechat.code.domain.WxDepartment::getDeptId, deptList))
//                            .stream()
//                            .map(WxDepartment::getId)
//                            .distinct()
//                            .collect(Collectors.toList());
//                    if (CollectionUtil.isNotEmpty(list)){
//                        List<WxDepartUser> departUsers = list.stream().map(item -> {
//                            WxDepartUser wxDepartUser = new WxDepartUser();
//                            wxDepartUser.setId(idSnowflakeComponent.snowflakeIdStr());
//                            wxDepartUser.setUserId(sysUser.getId());
//                            wxDepartUser.setDepartmentId(item);
//                            wxDepartUser.setUserIsLeader(wxCpUser.getIsLeader());
//                            wxDepartUser.setCreateBy(OaConstants.OPERBY_MOBILE_AUTH);
//                            wxDepartUser.setCreateDate(new Date());
//                            return wxDepartUser;
//                        }).collect(Collectors.toList());
//                        wxDepartUserService.saveBatch(departUsers);
//                    }
//                }
//
//                // 保存用户信息
//                save(sysUser);
//                one = sysUser;
//            } catch (WxErrorException e) {
//                throw new AlertException(e.getError().getErrorMsg());
//            }
//        }
        return one;
    }

    @Override
    public List<SysUser> getCompanyuser(Integer id) {
        return sysUserMapper.getCompanyuser(id);
    }

    @Override
    public List<String> getUserRole(String id) {
        return sysUserMapper.getUserRole(id);
    }

    @Override
    public List<String> isUserRoleByUserIdAndRoleEname(String uid, String ename) {
        return sysUserMapper.isUserRoleByUserIdAndRoleEname(uid,ename);
    }

    @Override
    public List<String> getspRoleUser(String id, String role) {
        return sysUserMapper.getspRoleUser(id, role);
    }

    @Override
    public List<String> getroleuserbycompany(String enname, String cid) {
        return sysUserMapper.getroleuserbycompany(enname,cid);
    }

    @Override
    public List<String> getRoleUserIdByCompany(String enname, String cid) {
        if(StrUtil.isBlank(enname) || StrUtil.isBlank(cid)){
            return new ArrayList<>();
        }
        return sysUserMapper.getRoleUserIdByCompany(enname,cid);
    }

    @Override
    public List<String> getroleuserbycompanyAndPlate(String enname, String cid) {
        return sysUserMapper.getroleuserbycompanyAndPlate(enname,cid);
    }

    @Override
    public void saveBankInfoByProcessAndUserName(SysUser sysUser) {
        // 查询用户是否存在
        if(sysUser==null || StrUtil.isBlank(sysUser.getNickName()) || StrUtil.isBlank(sysUser.getBankName()) || StrUtil.isBlank(sysUser.getBankNumber())){
            return;
        }
        LambdaQueryWrapper<SysUser> sysUserLambdaQueryWrapper=new LambdaQueryWrapper<>();
        sysUserLambdaQueryWrapper.eq(SysUser::getNickName,sysUser.getNickName());
        List<SysUser> list=list(sysUserLambdaQueryWrapper);
        // 重名不处理
         if(list==null || list.size()!=1){
             return;
         }
         SysUser data= list.get(0);
         // 有银行信息 不处理
         if(StrUtil.isBlank(data.getBankName()) && StrUtil.isBlank( data.getBankNumber())){
             data.setBankName(sysUser.getBankName());
             data.setBankNumber(sysUser.getBankNumber());
             data.setUpdateTime(new Timestamp(new Date().getTime()));
             data.setUpdateBy("saveBankInfoByProcessAndUserName");
             updateById(data);
             // 更新缓存
             userCacheClean.cleanUserCache(data.getUsername());
         }
    }

    @Override
    public String getDictRoleMongoByUesrId(String userId) {
        if(StrUtil.isBlank(userId)){
            return null;
        }
        if(isBossId(userId)>0){
            return DayAccountEnum.BOSS.getCode();
        }
        if(isLeaderId(userId)>0){
            return DayAccountEnum.LEADER.getCode();
        }
        if(isOtherId(userId)>0){
            return DayAccountEnum.OTHER_NEW_TIME_INFO.getCode();
        }
        return null;
    }

    @Override
    public JSONObject getBossAndLeader() {
        Query query=new Query();
        query.addCriteria(Criteria.where("code").is(DictCodeEnum.SEND_DAY_FUNDS.getCode()));
        List<DictGobal> list = mongoTemplate.find(query,DictGobal.class);
        if(list != null && list.size()>0){
            return JSONUtil.parseObj(list.get(0).getContent());
        }
        return null;
    }

    private int isBossId(String userId){
        Query query=new Query();
        query.addCriteria(Criteria.where("code").is(DictCodeEnum.SEND_DAY_FUNDS.getCode()));
        query.addCriteria(Criteria.where("content.bossids").is(userId));
        List<DictGobal> list = mongoTemplate.find(query,DictGobal.class);
        if(list != null && list.size()>0){
            return 1;
        }
        return 0;
    }

    private int isLeaderId(String userId){
        Query query=new Query();
        query.addCriteria(Criteria.where("code").is(DictCodeEnum.SEND_DAY_FUNDS.getCode()));
        query.addCriteria(Criteria.where("content.leaderids").is(userId));
        List<DictGobal> list = mongoTemplate.find(query,DictGobal.class);
        if(list != null && list.size()>0){
            return 1;
        }
        return 0;
    }

    private int isOtherId(String userId){
        Query query=new Query();
        query.addCriteria(Criteria.where("code").is(DictCodeEnum.SEND_DAY_FUNDS.getCode()));
        query.addCriteria(Criteria.where("content.otherAndNewTime.uids").regex(java.util.regex.Pattern.compile(userId)));
        List<DictGobal> list = mongoTemplate.find(query,DictGobal.class);
        if(list != null && list.size()>0){
            return 1;
        }
        return 0;
    }

    public String getLeaderByDeptPKID(SysUser sysUser, String companyOrDeptPKId) {
        return getLeader(sysUser, null, companyOrDeptPKId);
    }
    public String getLeader(SysUser sysUser, Long companyOrDeptId) {
        return getLeader(sysUser, companyOrDeptId, null);
    }
    public String getLeaderByProcessInstance(SysUser sysUser, String processInstanceId, Map<String, Object> formValue) {
        if (processInstanceId == null && CollectionUtil.isEmpty(formValue)) {
            return getLeader(sysUser, null, null);
        }

        if (CollectionUtil.isEmpty(formValue)) {
            if (processInstanceId != null) {
                List<DProcessInstance> formValues = mongoTemplate.find(new Query(Criteria.where("_id").is(processInstanceId)), DProcessInstance.class);
                if (CollectionUtil.isNotEmpty(formValues)) {
                    formValue = formValues.get(0).getFromvalue();
                }
            }
        }
        String departmentId = formValue == null ? null : (String)formValue.get("Department");
        return getLeader(sysUser, null, departmentId);
    }

    private String getLeader(SysUser sysUser, Long companyOrDeptId, String companyOrDeptPKId) {
        if (sysUser == null) {
            return null;
        }
        String[] leaders = sysUser.calcLeaders();
        if (leaders.length == 0) {
            if (companyOrDeptId != null) {
                return wxDepartmentService.getLeaderByDeptId(companyOrDeptId, companyOrDeptPKId);
            } else {
                return null;
            }
        }
        if (leaders.length == 1 || (companyOrDeptId == null && companyOrDeptPKId == null)) {
            return leaders[0];
        }

        List<UserDeptDto> userDepts = wxDepartmentService.getUserDepts(Arrays.asList(leaders), companyOrDeptId, companyOrDeptPKId);
        if (CollectionUtil.isEmpty(userDepts) || userDepts.size() == 0) {
            // 如果没找到对应部门的直属领导，默认返回该用户配置的第一个直属领导
            return leaders[0];
        }

        if (userDepts.size() != 1) {
            log.info("{}({})中存在相同部门(deptId:{})的重复领导，请检查",
                (sysUser.getWxUserId() == null ? "" :  (sysUser.getWxUserId()+"的直属领导") ),
                ArrayUtil.join(leaders, ","),
                companyOrDeptId);
        }

        return userDepts.get(0).getWxUserId();
    }

    @Override
    public List<SysUser> selectByRoleId(String roleId) {
        return sysUserMapper.selectByRoleId(roleId);
    }

    @Override
    public List<SysUser> selectByRoleIdAndPlate(String roleId, String plateId) {
        return sysUserMapper.selectByRoleIdAndPlate(roleId,plateId);
    }

    @Override
    public void fillRoles(List<UserDto> userDtos) {
        if (CollectionUtil.isEmpty(userDtos)) {
            return;
        }
        List<String> userIds = userDtos.stream().map(UserDto::getId).distinct().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(userIds)) {
            return;
        }


        List<RoleSmallDto> roleSmallDtos = sysUserMapper.selectRoleDtoByUserId(userIds);
        if (CollectionUtil.isEmpty(roleSmallDtos)) {
            return;
        }
        for (UserDto userDto : userDtos) {
            userDto.setRoles(roleSmallDtos.stream().filter(roleSmallDto -> roleSmallDto.getUserId().equals(userDto.getId())).collect(Collectors.toSet()));
        }
    }

    @Override
    public List<SysUser> listByUserIds(List<String> userIds) {
        LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SysUser::getUserId, userIds);
        queryWrapper.eq(SysUser::getEnabled, 1);
        return sysUserMapper.selectList(queryWrapper);
    }

    @Override
    public List<String> deptIdsAndDeptMoneyByUserId(String userId) {
        if(StrUtil.isBlank(userId)){
            return new ArrayList<>();
        }
        List<AggregationOperation> list = new ArrayList<>();
        list.add(Aggregation.match(Criteria.where("code").is(DictCodeEnum.DEPT_MONEY.getCode())));
        list.add(Aggregation.unwind("content"));
        list.add(Aggregation.match(Criteria.where("content.uid").is(userId)));

//        list.add(Aggregation.sort(Sort.by(Sort.Order.asc("content.sort"))));
        Aggregation aggregation= Aggregation.newAggregation(list);
        AggregationResults<DictGobal> results = mongoTemplate.aggregate(aggregation,"dict_gobal",DictGobal.class);
        List<DictGobal> list1 = results.getMappedResults();
        List<String> deptIds = new ArrayList<>();
        if(list1.size()>0){
            DictGobal dictGobal = list1.get(0);
            if(dictGobal != null && dictGobal.getContent() != null){
                JSONObject obj =JSONUtil.parseObj(dictGobal.getContent());
                JSONArray arr1 = obj.getJSONArray("deptIds");
               // 转换
                for (int i = 0; i < arr1.size(); i++) {
                    deptIds.add(arr1.get(i).toString());
                }
            }
        }
        return deptIds;
    }

    @Override
    public String deptIdByOtherNewTime(String userId) {
        List<AggregationOperation> query = new ArrayList<>();
        query.add(Aggregation.match(Criteria.where("code").is(DictCodeEnum.SEND_DAY_FUNDS.getCode())));
        query.add(Aggregation.unwind("content.otherAndNewTime"));
        query.add(Aggregation.match(Criteria.where("content.otherAndNewTime.uids").regex(java.util.regex.Pattern.compile(userId))));

//        list.add(Aggregation.sort(Sort.by(Sort.Order.asc("content.sort"))));
        Aggregation aggregation= Aggregation.newAggregation(query);
        AggregationResults<DictGobal> results = mongoTemplate.aggregate(aggregation,"dict_gobal",DictGobal.class);
        List<DictGobal> list = results.getMappedResults();
        JSONObject jo = JSONUtil.parseObj(list.get(0).getContent());
        JSONObject j = jo.getJSONObject("otherAndNewTime");
        JSONArray ja = j.getJSONArray("deptIds");
        return ja.join(",");
    }

    @Override
    public void updateAccModel(String userId, Integer accModel) {
        SysUser sysUser = getById(userId);
        if(sysUser != null){
            sysUser.setAccModel(accModel);
            updateById(sysUser);
        }
    }

    @Override
    public Integer getAccModel(String userId) {
        SysUser sysUser = getById(userId);
        if(sysUser != null){
            return sysUser.getAccModel();
        }
        return null;
    }
}
