package me.zhengjie.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import me.zhengjie.base.BaseServiceImpl;
import me.zhengjie.modules.business.domain.AccountMoneyForMonth;
import me.zhengjie.modules.business.mapper.AccountMoneyForMonthMapper;
import me.zhengjie.modules.business.service.AccountMoneyForMonthService;
import me.zhengjie.modules.business.service.bean.AccountMoneyBean;
import me.zhengjie.modules.business.service.bean.PaymentWaterBean;
import me.zhengjie.modules.business.service.bean.ReceiveWaterBean;
import me.zhengjie.modules.business.service.dto.AccountMoneyForMonthDto;
import me.zhengjie.modules.business.service.dto.AccountMoneyForMonthQueryCriteria;
import me.zhengjie.modules.business.service.mapstruct.AccountMoneyForMonthSMapper;
import me.zhengjie.modules.system.mapper.AccountMoneyForMonthFundsClassMapper;
import me.zhengjie.modules.system.service.dto.DiffMonthDeptMoneyDto;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.utils.QueryHelpPlus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.awt.print.Pageable;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
* @description 服务实现
* <AUTHOR>
* @date 2021-12-03
**/
@Service
public class AccountMoneyForMonthServiceImpl extends BaseServiceImpl<AccountMoneyForMonthMapper,AccountMoneyForMonth> implements AccountMoneyForMonthService {

    @Resource
    private AccountMoneyForMonthSMapper accountMoneyForMonthSMapper;
    @Resource
    private AccountMoneyForMonthMapper accountMoneyForMonthMapper;

    @Resource
    private AccountMoneyForMonthFundsClassMapper accountMoneyForMonthFundsClassMapper;

    @Override
    public Map<String,Object> queryAll(AccountMoneyForMonthQueryCriteria criteria, Pageable pageable){
        getPage((org.springframework.data.domain.Pageable) pageable);
        PageInfo<AccountMoneyForMonthDto> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<AccountMoneyForMonthDto> queryAll(AccountMoneyForMonthQueryCriteria criteria){
        return listToDto(list(QueryHelpPlus.getPredicate(AccountMoneyForMonth.class, criteria)),accountMoneyForMonthSMapper);
    }

    @Override
    public void download(List<AccountMoneyForMonthDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (AccountMoneyForMonthDto accountMoneyForMonth : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put(" accountId",  accountMoneyForMonth.getAccountId());
            map.put(" money",  accountMoneyForMonth.getMoney());
            map.put(" createBy",  accountMoneyForMonth.getCreateBy());
            map.put(" createDate",  accountMoneyForMonth.getCreateDate());
            map.put(" updateBy",  accountMoneyForMonth.getUpdateBy());
            map.put(" updateDate",  accountMoneyForMonth.getUpdateDate());
            map.put(" delFlag",  accountMoneyForMonth.getDelFlag());
            map.put(" remarks",  accountMoneyForMonth.getRemarks());
            map.put(" spare1",  accountMoneyForMonth.getSpare1());
            map.put(" spare2",  accountMoneyForMonth.getSpare2());
            map.put(" spare3",  accountMoneyForMonth.getSpare3());
            map.put(" spare4",  accountMoneyForMonth.getSpare4());
            map.put(" spare5",  accountMoneyForMonth.getSpare5());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Page<AccountMoneyBean> getAccountMoney(List<Integer> ids, Date startTime, Date endTime,Integer pagesize, Integer pagenumber,Integer discount) {
        Page<AccountMoneyBean> page = new Page<>();
        if (ids.size() > 0) {
           page = PageHelper.startPage(pagenumber,pagesize).doSelectPage(() -> accountMoneyForMonthMapper.getCompanyByPlatebyid(
                   ids, startTime, endTime,discount
                   ));
        } else {
            page = PageHelper.startPage(pagenumber,pagesize).doSelectPage(() -> accountMoneyForMonthMapper.getCompanyByPlate(
                    startTime, endTime,discount));
        }
        return page;
    }

    @Override
    public Page<PaymentWaterBean> getPaymentWater(List<Integer> ids, String costType, String startTime, String endTime, Integer pagesize, Integer pagenumber, Double moenyMax, Double moneyMin,String comecompany,String paymentType,
                                                  String fundsClassId,String sort,String order,String startCreateTime, String endCreateTime,String isChild) {
        Page<PaymentWaterBean> page = new Page<>();
        if (ids.size() > 0) {
            page = PageHelper.startPage(pagenumber, pagesize).doSelectPage(() -> accountMoneyForMonthMapper.getPaymentWaterbyid(
                    ids, costType, startTime, endTime, moenyMax, moneyMin,comecompany,paymentType,fundsClassId,sort,order,startCreateTime,endCreateTime,isChild
            ));
        } else {
            page = PageHelper.startPage(pagenumber, pagesize).doSelectPage(() -> accountMoneyForMonthMapper.getPaymentWater(
                    costType, startTime, endTime, moenyMax, moneyMin,comecompany,paymentType,fundsClassId,sort,order,startCreateTime,endCreateTime,isChild
            ));
        }
        return page;
    }

    @Override
    public Page<ReceiveWaterBean> getReceiveWater(List<Integer> ids, String costType, String startTime, String endTime, Integer pagesize, Integer pagenumber, Double moenyMax, Double moneyMin,String comecompany,String fundsClassId,String sort,String order,String startCreateTime, String endCreateTime,String isChild) {
        Page<ReceiveWaterBean> page = new Page<>();
        if (ids.size() > 0) {
            page = PageHelper.startPage(pagenumber, pagesize).doSelectPage(() -> accountMoneyForMonthMapper.getReceiveWaterbyid(
                    ids, costType, startTime, endTime, moenyMax, moneyMin,comecompany,fundsClassId,sort,order,startCreateTime,endCreateTime,isChild
            ));
        } else {
            page = PageHelper.startPage(pagenumber, pagesize).doSelectPage(() -> accountMoneyForMonthMapper.getReceiveWater(
                    costType, startTime, endTime, moenyMax, moneyMin,comecompany,fundsClassId,sort,order,startCreateTime,endCreateTime,isChild
            ));
        }
        return page;
    }

    @Override
    public Double getReceiveWaterSum(List<Integer> ids, String costType, String startTime, String endTime, Integer pagesize, Integer pagenumber, Double moenyMax, Double moneyMin, String comecompany,String fundsClassId,String startCreateTime, String endCreateTime,String isChild) {
        Double sum=null;
        if (ids.size() > 0) {
           sum = accountMoneyForMonthMapper.getReceiveWaterbyidSum(
                    ids, costType, startTime, endTime, moenyMax, moneyMin,comecompany,fundsClassId,startCreateTime,endCreateTime,isChild
            );
        }else{
            sum = accountMoneyForMonthMapper.getReceiveWaterSum(
                    costType, startTime, endTime, moenyMax, moneyMin,comecompany,fundsClassId,startCreateTime,endCreateTime,isChild
            );
        }
        return sum;
    }

    @Override
    public BigDecimal getLastMonthMoneyByDeptIdAndNotAcceptance(String deptId, String yearMonth) {
        BigDecimal bgg = new BigDecimal("0");
        if(StrUtil.isBlank(deptId) || StrUtil.isBlank(yearMonth)){
//            return BigDecimal.ZERO;
            return bgg;
        }
        bgg = accountMoneyForMonthMapper.getLastMonthMoneyByDeptIdAndNotAcceptance(deptId,yearMonth);

        DiffMonthDeptMoneyDto diffMonthDeptMoneyDto =  accountMoneyForMonthFundsClassMapper.getDiffMonthDeptMoneyDtoAndNotChengDui(deptId,yearMonth);
        if(diffMonthDeptMoneyDto != null){
            bgg = bgg.add(diffMonthDeptMoneyDto.getDiffMoney());
        }

        return bgg;
    }
}
