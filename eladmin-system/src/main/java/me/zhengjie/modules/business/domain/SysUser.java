package me.zhengjie.modules.business.domain;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.ApiModelProperty;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.persistence.*;
import javax.validation.constraints.*;
import java.sql.Timestamp;
import java.io.Serializable;

/**
* @description /
* <AUTHOR>
* @date 2021-10-18
**/
@Data
@TableName("sys_user")
public class SysUser implements Serializable {


    @TableId
    @ApiModelProperty(value = "ID")
    private String userId;

    @ApiModelProperty(value = "部门名称")
    private Long deptId;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "昵称")
    private String nickName;

    @ApiModelProperty(value = "性别")
    private String gender;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "头像地址")
    private String avatarName;

    @ApiModelProperty(value = "头像真实路径")
    private String avatarPath;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "是否为admin账号")
    private Boolean isAdmin;

    @ApiModelProperty(value = "状态：1启用、0禁用")
    private Long enabled;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;

    @ApiModelProperty(value = "修改密码的时间")
    private Timestamp pwdResetTime;

    @ApiModelProperty(value = "创建日期")
    private Timestamp createTime;

    @ApiModelProperty(value = "更新时间")
    private Timestamp updateTime;

    @ApiModelProperty(value = "wxUserId")
    private String wxUserId;

    @ApiModelProperty(value = "wxPosition")
    private String wxPosition;

    @ApiModelProperty(value = "wxQrCode")
    private String wxQrCode;

    @ApiModelProperty(value = "wxStatus")
    private Integer wxStatus;

    private String directLeader;

    private String mainDepartment;

    private String bankNumber;

    @ApiModelProperty(value = "开户行", hidden = true)
    private String bankName;

    private Integer accModel;

    public void copy(SysUser source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }

    public String[] calcLeaders() {
        if (StrUtil.isBlank(this.directLeader)) {
            return new String[]{};
        }
        return this.directLeader.split("\\s*,\\s*");
    }
}
