/*
*  Copyright 2019-2020 <PERSON>
*
*  Licensed under the Apache License, Version 2.0 (the "License");
*  you may not use this file except in compliance with the License.
*  You may obtain a copy of the License at
*
*  http://www.apache.org/licenses/LICENSE-2.0
*
*  Unless required by applicable law or agreed to in writing, software
*  distributed under the License is distributed on an "AS IS" BASIS,
*  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*  See the License for the specific language governing permissions and
*  limitations under the License.
*/
package me.zhengjie.modules.business.rest;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zthzinfo.common.ResponseMapBuilder;
import me.zhengjie.annotation.Log;
import me.zhengjie.modules.business.domain.SysProcessDetail;
import me.zhengjie.modules.business.service.SysProcessDetailService;
import me.zhengjie.modules.business.service.dto.SysProcessDetailQueryCriteria;
import me.zhengjie.modules.system.domain.ComeToPaymentWater;
import org.springframework.data.domain.Pageable;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

/**
* @website https://el-admin.vip
* <AUTHOR>
* @date 2021-09-14
**/
@RestController
@RequiredArgsConstructor
@Api(tags = "sys_process管理")
@RequestMapping("/api/sysProcessDetail")
public class SysProcessDetailController {

    private final SysProcessDetailService sysProcessDetailService;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('sysProcessDetail:list')")
    public void download(HttpServletResponse response, SysProcessDetailQueryCriteria criteria) throws IOException {
        sysProcessDetailService.download(sysProcessDetailService.queryAll(criteria), response);
    }

    @GetMapping
    @Log("查询sys_process")
    @ApiOperation("查询sys_process")
    @PreAuthorize("@el.check('sysProcessDetail:list')")
    public ResponseEntity<Object> query(SysProcessDetailQueryCriteria criteria, Pageable pageable){
        return new ResponseEntity<>(sysProcessDetailService.queryAll(criteria,pageable),HttpStatus.OK);
    }

    @PostMapping
    @Log("新增sys_process")
    @ApiOperation("新增sys_process")
    @PreAuthorize("@el.check('sysProcessDetail:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody SysProcessDetail resources){
        return new ResponseEntity<>(sysProcessDetailService.save(resources),HttpStatus.CREATED);
    }

    @PutMapping
    @Log("修改sys_process")
    @ApiOperation("修改sys_process")
    @PreAuthorize("@el.check('sysProcessDetail:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody SysProcessDetail resources){
        sysProcessDetailService.updateById(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除sys_process")
    @ApiOperation("删除sys_process")
    @PreAuthorize("@el.check('sysProcessDetail:del')")
    @DeleteMapping
    public ResponseEntity<Object> delete(@RequestBody Integer[] ids) {
        sysProcessDetailService.removeByIds(Arrays.asList(ids));
        return new ResponseEntity<>(HttpStatus.OK);
    }
    
    @RequestMapping("/getProcessCodeList")
    public Map<String, Object> updateWaterStatus(){
    
        LambdaQueryWrapper<SysProcessDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(SysProcessDetail::getSpare1);
        queryWrapper.ne(SysProcessDetail::getSpare1,"");
        queryWrapper.eq(SysProcessDetail::getIsPayment,1);
        List<SysProcessDetail> list = sysProcessDetailService.list(queryWrapper);
        return ResponseMapBuilder.newBuilder()
                .put("list",list)
                .putSuccess()
                .getResult();
    }

    @GetMapping("/detailByCode")
    public ResponseEntity<Object> detailByCode(SysProcessDetailQueryCriteria criteria){
        return new ResponseEntity<>(sysProcessDetailService.queryAll(criteria),HttpStatus.OK);
    }

    @RequestMapping("/getProcessCodeListByCert")
    public Map<String, Object> getProcessCodeListByCert(){

        LambdaQueryWrapper<SysProcessDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(SysProcessDetail::getSpare1);
        queryWrapper.ne(SysProcessDetail::getSpare1,"");
        List<SysProcessDetail> list = sysProcessDetailService.list(queryWrapper);
        return ResponseMapBuilder.newBuilder()
                .put("list",list)
                .putSuccess()
                .getResult();
    }
}