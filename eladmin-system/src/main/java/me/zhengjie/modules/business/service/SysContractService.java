package me.zhengjie.modules.business.service;

import me.zhengjie.base.BaseService;
import me.zhengjie.modules.business.domain.SysContract;
import me.zhengjie.modules.business.service.dto.SysContractDto;
import me.zhengjie.modules.business.service.dto.SysContractQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

/**
* @description 服务接口
* <AUTHOR>
* @date 2021-12-06
**/
public interface SysContractService extends BaseService<SysContract> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SysContractQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SysContractDto>
    */
    List<SysContractDto> queryAll(SysContractQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SysContractDto> all, HttpServletResponse response) throws IOException;
}