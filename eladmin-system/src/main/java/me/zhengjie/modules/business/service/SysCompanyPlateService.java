package me.zhengjie.modules.business.service;

import me.zhengjie.base.BaseService;
import me.zhengjie.modules.business.domain.SysCompanyPlate;
import me.zhengjie.modules.business.service.bean.CompanyByPlateBean;
import me.zhengjie.modules.business.service.dto.SysCompanyPlateDto;
import me.zhengjie.modules.business.service.dto.SysCompanyPlateQueryCriteria;
import me.zhengjie.modules.wechat.code.domain.WxDepartment;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* @description 服务接口
* <AUTHOR>
* @date 2021-10-14
**/
public interface SysCompanyPlateService extends BaseService<SysCompanyPlate> {

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    Map<String,Object> queryAll(SysCompanyPlateQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<SysCompanyPlateDto>
    */
    List<SysCompanyPlateDto> queryAll(SysCompanyPlateQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<SysCompanyPlateDto> all, HttpServletResponse response) throws IOException;

    List<CompanyByPlateBean> getCompanyByPlate(Integer id);

    Long getPlateIdByDeptId(String deptId);

    List<WxDepartment> getUserCompany(String id);

    List<WxDepartment> getUserCompany(String id,String orCompanyId);

    List<CompanyByPlateBean> getCompanyByPlateIds(List<Integer> plateIds);

}
