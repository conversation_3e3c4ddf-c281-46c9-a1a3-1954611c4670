package me.zhengjie.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import me.zhengjie.base.BaseServiceImpl;
import me.zhengjie.modules.business.domain.SysProcess;
import me.zhengjie.modules.business.mapper.SysProcessMapper;
import me.zhengjie.modules.business.service.SysProcessService;
import me.zhengjie.modules.business.service.bean.ProcessForHomeBean;
import me.zhengjie.modules.business.service.bean.RoleByComId;
import me.zhengjie.modules.business.service.dto.SysProcessDto;
import me.zhengjie.modules.business.service.dto.SysProcessQueryCriteria;
import me.zhengjie.modules.business.service.mapstruct.SysProcessSMapper;
import me.zhengjie.utils.FileUtil;
import me.zhengjie.utils.QueryHelpPlus;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
* @description 服务实现
* <AUTHOR>
* @date 2021-09-14
**/
@Service
public class SysProcessServiceImpl extends BaseServiceImpl<SysProcessMapper,SysProcess> implements SysProcessService {

    @Resource
    private SysProcessSMapper sysProcessSMapper;
    @Resource
    private SysProcessMapper sysProcessMapper;

    @Override
    public Map<String,Object> queryAll(SysProcessQueryCriteria criteria, Pageable pageable){
        getPage(pageable);
        PageInfo<SysProcessDto> page = new PageInfo<>(queryAll(criteria));
        return toMap(page);
    }

    @Override
    public List<SysProcessDto> queryAll(SysProcessQueryCriteria criteria){
        return listToDto(list(QueryHelpPlus.getPredicate(SysProcess.class, criteria)),sysProcessSMapper);
    }

    @Override
    public void download(List<SysProcessDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (SysProcessDto sysProcess : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put(" name",  sysProcess.getName());
            map.put(" createBy",  sysProcess.getCreateBy());
            map.put(" createDate",  sysProcess.getCreateDate());
            map.put(" updateBy",  sysProcess.getUpdateBy());
            map.put(" updateDate",  sysProcess.getUpdateDate());
            map.put(" delFlag",  sysProcess.getDelFlag());
            map.put(" color",  sysProcess.getColor());
            map.put(" spare1",  sysProcess.getSpare1());
            map.put(" spare2",  sysProcess.getSpare2());
            map.put(" spare3",  sysProcess.getSpare3());
            map.put(" spare4",  sysProcess.getSpare4());
            map.put(" spare5",  sysProcess.getSpare5());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    @Override
    public Page<ProcessForHomeBean> getMyProcess(String id, String name,String summary,String accQuery, Date startTime, Date endTime, Integer pagesize, Integer pagenumber, Integer ongoing,String processIds) {
        List<String> pids=null;
        if(StrUtil.isNotBlank(processIds)){
            pids= Arrays.asList(processIds.split(","));
        }
        final List<String> pids1 = pids;
        Page<ProcessForHomeBean> page = PageHelper.startPage(pagenumber,pagesize).doSelectPage(() -> sysProcessMapper.getMyProcess(
                id,name,summary,accQuery,startTime,endTime,ongoing,pids1
        ));
        return page;
    }
    @Override
    public Page<ProcessForHomeBean> getMyProcessing(String id, String name, Date startTime, Date endTime, Integer pagesize, Integer pagenumber,List<String> rolelist,List<String> Deptids, List<String> rolelistByComId) {
        Page<ProcessForHomeBean> page = PageHelper.startPage(pagenumber,pagesize).doSelectPage(() -> sysProcessMapper.getMyProcessing(
                id,name,startTime,endTime,rolelist,Deptids,rolelistByComId
        ));
        return page;
    }

    @Override
    public Page<ProcessForHomeBean> getMyProcessed(String id, String name, Date startTime, Date endTime, Integer pagesize, Integer pagenumber,String summary,String status,String fukuanName) {
        Page<ProcessForHomeBean> page = PageHelper.startPage(pagenumber,pagesize).doSelectPage(() -> sysProcessMapper.getMyProcessed(
                id,name,startTime,endTime,summary,status,fukuanName
        ));
        return page;
    }

    @Override
    public Page<ProcessForHomeBean> getMyProcessByCcs(String id, String name, Date startTime, Date endTime, Integer pagesize, Integer pagenumber,String summary,String status,String fukuanName) {
        Page<ProcessForHomeBean> page = PageHelper.startPage(pagenumber,pagesize).doSelectPage(() -> sysProcessMapper.getMyProcessByCcs(
                id,name,startTime,endTime, summary, status, fukuanName
        ));
        return page;
    }

    @Override
    public List<RoleByComId> getmyrole(String id) {
        return sysProcessMapper.getuserroles(id);
    }
}
