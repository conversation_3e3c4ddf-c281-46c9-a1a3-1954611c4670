package com.dlcg.tms.client;

import cn.hutool.json.JSONArray;
import com.dlcg.tms.bean.FullShipLineInfo;
import com.dlcg.tms.entity.CustomerGoodsCostDetail;
import com.dlcg.tms.entity.ShipLineShipCost;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;

@FeignClient(value = "dlcg-tms", url = "${debug.dlcg-tms:}")
@RequestMapping(value = "/client/shipline")
public interface ShipLineClient {


    @RequestMapping("/getShipLineDingjingById")
    Map<String , Object> getShipLineDingjingById(
        @RequestParam(value = "shipLineId", required = false) String shipLineId,
        @RequestParam(value = "processId", required = false) String processId);

    @RequestMapping("/getShipLineShipCostByPayerId")
    Map<String , Object> getShipLineShipCostByPayerId(@RequestParam(value = "payerId", required = false) String payerId,
                                                      @RequestParam(value = "shipName", required = false) String shipName,
                                                      @RequestParam(value = "startShipTime", required = false) String startShipTime,
                                                      @RequestParam(value = "endShipTime", required = false) String endShipTime);
    @RequestMapping("/updateShipLineShipCostIsDeductById")
    Map<String, Object> updateShipLineShipCostIsDeductById(@RequestParam(value = "id", required = false) String id);

    @RequestMapping("/shipLinePageByStatus")
    Map<String, Object> shipLinePageByStatus(@RequestParam(value = "shipChuanQiTime", required = false) String shipChuanQiTime, @RequestParam(value = "shipName", required = false) String shipName, @RequestParam(value = "status", required = false) String status, @RequestParam(value = "pageNum", required = false) Integer pageNum, @RequestParam(value = "pageSize", required = false) Integer pageSize);
    // 保存船上费用
    @PostMapping("/saveShipLineShipCost")
    Map<String,Object> saveShipLineShipCost(@RequestBody ShipLineShipCost shipLineShipCost);

    // 修改船上费用
    @PostMapping("/updateShipLineShipCost")
    Map<String,Object> updateShipLineShipCost(@RequestBody ShipLineShipCost shipLineShipCost);

    @PostMapping("/updateGoodsCostDetail")
    Map<String,Object> updateGoodsCostDetail(@RequestBody CustomerGoodsCostDetail customerGoodsCostDetail);
    // 删除船上费用
    @RequestMapping("/deleteShipLineShipCost")
    Map<String,Object> deleteShipLineShipCost(@RequestParam(value = "imprestFundRecordId", required = false) String imprestFundRecordId);
    // 删除货物费用
    @RequestMapping("/deleteGoodsCostDetail")
    Map<String,Object> deleteGoodsCostDetail(@RequestParam(value = "imprestFundRecordId", required = false) String imprestFundRecordId);
    /**
     * 更新船期的合同状态
     */
    @RequestMapping("/updateContractStatus")
    Map<String, Object> updateContractStatus(
            @RequestParam(value = "shipLineId") String shipLineId,
            @RequestParam(value = "status") Integer status
    );

    /**
     * 获取租船员WxUserId
     * 根据船期ID查找租船员，再根据租船员ID找出WxUserId
     * @param shipLineId
     * @return
     */
    @RequestMapping("/getChartererWxUserIdByShipLineId")
    Map<String, Object> getChartererWxUserIdByShipLineId(
            @RequestParam(value = "shipLineId") String shipLineId
    );

    @PostMapping("/shipLineAndShipNameByShipLineIds")
    Map<String,Object> shipLineAndShipNameByShipLineIds(@RequestBody Map<String,Object> shipLineMap);

    @RequestMapping("/getShipLineInfo")
    FullShipLineInfo getShipLineInfo(@RequestParam(value = "shipLineId") String shiplineId);
    @RequestMapping("/getYewuCostomerCostNoComfirm")
    Map<String, Object> getYewuCostomerCostNoComfirm(@RequestParam(value = "shipLineId") String shipLineId);

    @RequestMapping("/getShipCostNoComfirm")
    Map<String, Object> getShipCostNoComfirm(@RequestParam(value = "shipLineId") String shipLineId);

    @PostMapping("/getShipCostNoComfirmByShipLineIds")
    Map<String, Object> getShipCostNoComfirmByShipLineIds(@RequestBody Map<String,Object> shipLineMap);

    @PostMapping("/getGoodsCostNoComfirmByShipLineIds")
    Map<String, Object> getGoodsCostNoComfirmByShipLineIds(@RequestBody Map<String,Object> goodsCostMap);

    @PostMapping("/saveCustomerGoodsCostDetail")
    Map<String , Object> saveCustomerGoodsCostDetail(
            @RequestBody CustomerGoodsCostDetail customerGoodsCostDetail
    );

    @RequestMapping("/getIsCompleteGoodsByFundId")
    Boolean getIsCompleteGoodsByFundId(@RequestParam(value = "fundId") String fundId);

    @RequestMapping("/getIsCompleteShipByFundId")
    Boolean getIsCompleteShipByFundId(@RequestParam(value = "fundId") String fundId);
    @PostMapping("/queryMoneyShipAndGoodsByRecordId")
    Map<String,Object> queryMoneyShipAndGoodsByRecordId(@RequestBody Map<String,Object> recordIdMap);
    @RequestMapping("/getFindWeixinNameByShipLineId")
    String getFindWeixinNameByShipLineId(@RequestParam(value = "shipLineId") String shipLineId);
    @RequestMapping("/getFindWeixinNameByShipCostId")
    String getFindWeixinNameByShipCostId(@RequestParam(value = "shipCostId") String shipCostId);
    @RequestMapping("/getFindWeixinNameByGoodsCostId")
    String getFindWeixinNameByGoodsCostId(@RequestParam(value = "goodsCostId") String goodsCostId);

    @RequestMapping("/getContractByShipLineId")
    String getContractByShipLineId(@RequestParam(value = "ids") String ids);


    @RequestMapping("/getPaymentListByShipName")
    Map<String, Object> getPaymentListByShipName(@RequestParam(value = "shipName") String shipName
            ,@RequestParam(value = "yearMonth") String yearMonth

    );
    @PostMapping("/updateCostAggregatePayment")
    Map<String, Object> updateCostAggregatePayment(@RequestBody JSONArray updateDatas);

}
