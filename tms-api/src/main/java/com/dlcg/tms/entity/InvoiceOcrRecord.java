package com.dlcg.tms.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <pre>
 *  发票识别表
 * </pre>
 * <AUTHOR>
 * @verison $Id: InvoiceOcrRecord v 0.1 2022-06-23 11:39:09
 */
@TableName("invoice_ocr_record")
public class InvoiceOcrRecord {

    /**
     * <pre>
     * 
     * </pre>
     */
    @TableId(type = IdType.ID_WORKER_STR)
    private String	id;

    /**
     * <pre>
     * 发票号
     * </pre>
     */
    private String	invoiceNo;

    /**
     * <pre>
     * 发票代码
     * </pre>
     */
    private String	invoiceCode;

    /**
     * <pre>
     * 发票日期
     * </pre>
     */
    private String invoiceDate;

    /**
     * <pre>
     * 发票不含税金额
     * </pre>
     */
    private BigDecimal invoicePrice;

    /**
     * <pre>
     * 发票税额
     * </pre>
     */
    private BigDecimal	invoicePriceTax;

    /**
     * <pre>
     * 发票含税金额
     * </pre>
     */
    private BigDecimal	invoicePriceTaxSum;

    /**
     * 发票税率
     */
    private String invoiceTax;

    /**
     * <pre>
     * 买方名称
     * </pre>
     */
    private String	buyerName;

    /**
     * <pre>
     * 买方纳税人识别号
     * </pre>
     */
    private String	buyerTaxpayerNo;

    /**
     * <pre>
     * 买方地址电话
     * </pre>
     */
    private String	buyerAddressTel;

    /**
     * <pre>
     * 买方开户行及账号
     * </pre>
     */
    private String	buyerAccount;

    /**
     * <pre>
     * 卖方名称
     * </pre>
     */
    private String	sellerName;

    /**
     * <pre>
     * 卖方纳税人识别号
     * </pre>
     */
    private String	sellerTaxpayerNo;

    /**
     * <pre>
     * 卖方地址电话
     * </pre>
     */
    private String	sellerAddressTel;

    /**
     * <pre>
     * 卖方开户行及账号
     * </pre>
     */
    private String	sellerAccount;

    /**
     * <pre>
     * 发票原图片
     * </pre>
     */
    private String	originFile;

    /**
     * <pre>
     * 收款人
     * </pre>
     */
    private String	invoicePayee;

    /**
     * <pre>
     * 开票人
     * </pre>
     */
    private String	invoiceDrawer;

    /**
     * <pre>
     * 复核
     * </pre>
     */
    private String	invoiceReview;

    /**
     * <pre>
     * 发票备注
     * </pre>
     */
    private String	invoiceRemark;

    /**
     * <pre>
     * 创建时间
     * </pre>
     */
    private Date	createTime;

    /**
     * <pre>
     * 修改时间
     * </pre>
     */
    private Date	updateTime;

    /**
     * <pre>
     * 记账月份
     * </pre>
     */
    private String	recordAccountMonth;

    /**
     * <pre>
     * 状态
     * </pre>
     */
    private Integer	status;

    /**
     * <pre>
     * 船舶ID
     * </pre>
     */
    private String	shipId;

    /**
     * <pre>
     * 船舶名称
     * </pre>
     */
    private String	shipName;

    /**
     * <pre>
     * 购买方ID
     * </pre>
     */
    private String	buyerId;

    /**
     * <pre>
     * 销售方ID
     * </pre>
     */
    private String	sellerId;

    /**
     * <pre>
     * 费用类型ID
     * </pre>
     */
    private String	costTypeId;

    /**
     * <pre>
     * 费用类型名称
     * </pre>
     */
    private String	costTypeName;

    /**
     * <pre>
     * 费用字典key
     * </pre>
     */
    private String	costDictKey;

    /**
     * 服务名称
     */
    private String serviceName;

    private String splitUserId;
    private Date splitTime;
    /**
     * 发票标识 10 网联 20 汽运
     */
    @TableField(exist = false)
    private Integer type;

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(String invoiceTax) {
        this.invoiceTax = invoiceTax;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getInvoiceNo() {
      return this.invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
      this.invoiceNo = invoiceNo;
    }

    public String getInvoiceCode() {
      return this.invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
      this.invoiceCode = invoiceCode;
    }

    public String getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(String invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public BigDecimal getInvoicePrice() {
      return this.invoicePrice;
    }

    public void setInvoicePrice(BigDecimal invoicePrice) {
      this.invoicePrice = invoicePrice;
    }

    public BigDecimal getInvoicePriceTax() {
      return this.invoicePriceTax;
    }

    public void setInvoicePriceTax(BigDecimal invoicePriceTax) {
      this.invoicePriceTax = invoicePriceTax;
    }

    public BigDecimal getInvoicePriceTaxSum() {
      return this.invoicePriceTaxSum;
    }

    public void setInvoicePriceTaxSum(BigDecimal invoicePriceTaxSum) {
      this.invoicePriceTaxSum = invoicePriceTaxSum;
    }

    public String getBuyerName() {
      return this.buyerName;
    }

    public void setBuyerName(String buyerName) {
      this.buyerName = buyerName;
    }

    public String getBuyerTaxpayerNo() {
      return this.buyerTaxpayerNo;
    }

    public void setBuyerTaxpayerNo(String buyerTaxpayerNo) {
      this.buyerTaxpayerNo = buyerTaxpayerNo;
    }

    public String getBuyerAddressTel() {
      return this.buyerAddressTel;
    }

    public void setBuyerAddressTel(String buyerAddressTel) {
      this.buyerAddressTel = buyerAddressTel;
    }

    public String getBuyerAccount() {
      return this.buyerAccount;
    }

    public void setBuyerAccount(String buyerAccount) {
      this.buyerAccount = buyerAccount;
    }

    public String getSellerName() {
      return this.sellerName;
    }

    public void setSellerName(String sellerName) {
      this.sellerName = sellerName;
    }

    public String getSellerTaxpayerNo() {
      return this.sellerTaxpayerNo;
    }

    public void setSellerTaxpayerNo(String sellerTaxpayerNo) {
      this.sellerTaxpayerNo = sellerTaxpayerNo;
    }

    public String getSellerAddressTel() {
      return this.sellerAddressTel;
    }

    public void setSellerAddressTel(String sellerAddressTel) {
      this.sellerAddressTel = sellerAddressTel;
    }

    public String getSellerAccount() {
      return this.sellerAccount;
    }

    public void setSellerAccount(String sellerAccount) {
      this.sellerAccount = sellerAccount;
    }

    public String getOriginFile() {
      return this.originFile;
    }

    public void setOriginFile(String originFile) {
      this.originFile = originFile;
    }

    public String getInvoicePayee() {
      return this.invoicePayee;
    }

    public void setInvoicePayee(String invoicePayee) {
      this.invoicePayee = invoicePayee;
    }

    public String getInvoiceDrawer() {
      return this.invoiceDrawer;
    }

    public void setInvoiceDrawer(String invoiceDrawer) {
      this.invoiceDrawer = invoiceDrawer;
    }

    public String getInvoiceReview() {
      return this.invoiceReview;
    }

    public void setInvoiceReview(String invoiceReview) {
      this.invoiceReview = invoiceReview;
    }

    public String getInvoiceRemark() {
      return this.invoiceRemark;
    }

    public void setInvoiceRemark(String invoiceRemark) {
      this.invoiceRemark = invoiceRemark;
    }

    public Date getCreateTime() {
      return this.createTime;
    }

    public void setCreateTime(Date createTime) {
      this.createTime = createTime;
    }

    public Date getUpdateTime() {
      return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
      this.updateTime = updateTime;
    }

    public String getRecordAccountMonth() {
      return this.recordAccountMonth;
    }

    public void setRecordAccountMonth(String recordAccountMonth) {
      this.recordAccountMonth = recordAccountMonth;
    }

    public Integer getStatus() {
      return this.status;
    }

    public void setStatus(Integer status) {
      this.status = status;
    }

    public String getShipId() {
      return this.shipId;
    }

    public void setShipId(String shipId) {
      this.shipId = shipId;
    }

    public String getShipName() {
      return this.shipName;
    }

    public void setShipName(String shipName) {
      this.shipName = shipName;
    }

    public String getBuyerId() {
      return this.buyerId;
    }

    public void setBuyerId(String buyerId) {
      this.buyerId = buyerId;
    }

    public String getSellerId() {
      return this.sellerId;
    }

    public void setSellerId(String sellerId) {
      this.sellerId = sellerId;
    }

    public String getCostTypeId() {
      return this.costTypeId;
    }

    public void setCostTypeId(String costTypeId) {
      this.costTypeId = costTypeId;
    }

    public String getCostTypeName() {
      return this.costTypeName;
    }

    public void setCostTypeName(String costTypeName) {
      this.costTypeName = costTypeName;
    }

    public String getCostDictKey() {
      return this.costDictKey;
    }

    public void setCostDictKey(String costDictKey) {
      this.costDictKey = costDictKey;
    }

    public String getSplitUserId() {
        return splitUserId;
    }

    public void setSplitUserId(String splitUserId) {
        this.splitUserId = splitUserId;
    }

    public Date getSplitTime() {
        return splitTime;
    }

    public void setSplitTime(Date splitTime) {
        this.splitTime = splitTime;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}