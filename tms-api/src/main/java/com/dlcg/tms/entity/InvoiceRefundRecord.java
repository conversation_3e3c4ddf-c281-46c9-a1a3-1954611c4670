package com.dlcg.tms.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-25
 */
@TableName("invoice_refund_record")
public class InvoiceRefundRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 退票id
     */
    private String id;

    /**
     * 发票id
     */
    private String invoiceId;

    /**
     * cost_agg_id
     */
    private String costId;

    /**
     * 1 退票中 2 退票成功 3 退票拒绝 4 退票撤回
     */
    private Integer status;

    /**
     * 发票金额
     */
    private BigDecimal amount;

    /**
     * 0 正常
     */
    private Boolean delFlag;

    private Date createTime;

    private String createBy;

    private String costData;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(String invoiceId) {
        this.invoiceId = invoiceId;
    }
    public String getCostId() {
        return costId;
    }

    public void setCostId(String costId) {
        this.costId = costId;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }
    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCostData() {
        return costData;
    }

    public void setCostData(String costData) {
        this.costData = costData;
    }

    @Override
    public String toString() {
        return "InvoiceRefundRecord{" +
            "id=" + id +
            ", invoiceId=" + invoiceId +
            ", costId=" + costId +
            ", status=" + status +
            ", amount=" + amount +
            ", delFlag=" + delFlag +
            ", createTime=" + createTime +
            ", createBy=" + createBy +
        "}";
    }
}
