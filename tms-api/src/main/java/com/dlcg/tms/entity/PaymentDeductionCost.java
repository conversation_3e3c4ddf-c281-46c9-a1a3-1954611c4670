package com.dlcg.tms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 付款抵扣表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-11
 */
@TableName("payment_deduction_cost")
public class PaymentDeductionCost implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 付款进票结算申请表id
     */
    private String paymentBillSettleId;

    /**
     * 抵扣来源id
     */
    private String costAggregationId;


    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    private String delFlag;

    private String shipName;
    
    private String voyageNumber;
    
    public String getShipName() {
        return shipName;
    }
    
    public void setShipName(String shipName) {
        this.shipName = shipName;
    }
    
    public String getVoyageNumber() {
        return voyageNumber;
    }
    
    public void setVoyageNumber(String voyageNumber) {
        this.voyageNumber = voyageNumber;
    }
    
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getPaymentBillSettleId() {
        return paymentBillSettleId;
    }

    public void setPaymentBillSettleId(String paymentBillSettleId) {
        this.paymentBillSettleId = paymentBillSettleId;
    }
    
    public String getCostAggregationId() {
        return costAggregationId;
    }
    
    public void setCostAggregationId(String costAggregationId) {
        this.costAggregationId = costAggregationId;
    }
    
    
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}
