package com.dlcg.tms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-07-10
 */
@TableName("sys_customer_secondlevel_invoice")
public class SysCustomerSecondlevelInvoice implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 合同名头id
     */
    private String customerInvocieid;

    /**
     * 二级客户id
     */
    private String customerSecondlevelid;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    /**
     * 备注
     */
    private String remark;

    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getCustomerInvocieid() {
        return customerInvocieid;
    }

    public void setCustomerInvocieid(String customerInvocieid) {
        this.customerInvocieid = customerInvocieid;
    }
    public String getCustomerSecondlevelid() {
        return customerSecondlevelid;
    }

    public void setCustomerSecondlevelid(String customerSecondlevelid) {
        this.customerSecondlevelid = customerSecondlevelid;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "SysCustomerSecondlevelInvoice{" +
            "id=" + id +
            ", customerInvocieid=" + customerInvocieid +
            ", customerSecondlevelid=" + customerSecondlevelid +
            ", createBy=" + createBy +
            ", createDate=" + createDate +
            ", updateBy=" + updateBy +
            ", updateDate=" + updateDate +
            ", remark=" + remark +
            ", delFlag=" + delFlag +
        "}";
    }
}
