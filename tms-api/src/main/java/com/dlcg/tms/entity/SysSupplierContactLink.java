package com.dlcg.tms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-21
 */
@TableName("sys_supplier_contact_link")
public class SysSupplierContactLink implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 供应商账户
     */
    private String sysSupplierContactId;

    /**
     * 联系人名称
     */
    private String name;

    /**
     * 手机
     */
    private String phone;

    /**
     * email
     */
    private String email;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    /**
     * 备注
     */
    private String remark;

    private String delFlag;

    private String address;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getSysSupplierContactId() {
        return sysSupplierContactId;
    }

    public void setSysSupplierContactId(String sysSupplierContactId) {
        this.sysSupplierContactId = sysSupplierContactId;
    }
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Override
    public String toString() {
        return "SysSupplierContactLink{" +
            "id=" + id +
            ", sysSupplierContactId=" + sysSupplierContactId +
            ", name=" + name +
            ", phone=" + phone +
            ", email=" + email +
            ", createBy=" + createBy +
            ", createDate=" + createDate +
            ", updateBy=" + updateBy +
            ", updateDate=" + updateDate +
            ", remark=" + remark +
            ", delFlag=" + delFlag +
            ", address=" + address +
            ", province=" + province +
            ", city=" + city +
        "}";
    }
}
