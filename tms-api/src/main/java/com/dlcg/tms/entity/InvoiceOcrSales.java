package com.dlcg.tms.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 发票识别表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@TableName("invoice_ocr_sales")
public class InvoiceOcrSales implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票日期
     */
    private String invoiceDate;

    /**
     * 发票不含税金额
     */
    private BigDecimal invoicePrice;

    /**
     * 发票税额
     */
    private BigDecimal invoicePriceTax;

    /**
     * 发票含税金额
     */
    private BigDecimal invoicePriceTaxSum;

    /**
     * 税率
     */
    private String invoiceTax;

    /**
     * 买方名称
     */
    private String buyerName;

    /**
     * 买方纳税人识别号
     */
    private String buyerTaxpayerNo;

    /**
     * 买方地址电话
     */
    private String buyerAddressTel;

    /**
     * 买方开户行及账号
     */
    private String buyerAccount;

    /**
     * 卖方名称
     */
    private String sellerName;

    /**
     * 卖方纳税人识别号
     */
    private String sellerTaxpayerNo;

    /**
     * 卖方地址电话
     */
    private String sellerAddressTel;

    /**
     * 卖方开户行及账号
     */
    private String sellerAccount;

    /**
     * 发票图片
     */
    private String originFile;

    /**
     * 收款人
     */
    private String invoicePayee;

    /**
     * 开票人
     */
    private String invoiceDrawer;

    /**
     * 复核
     */
    private String invoiceReview;

    /**
     * 发票备注
     */
    private String invoiceRemark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 记账月份
     */
    private String recordAccountMonth;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 船舶ID
     */
    private String shipId;

    /**
     * 船舶名称
     */
    private String shipName;

    /**
     * 购买方ID
     */
    private String buyerId;

    /**
     * 销售方ID
     */
    private String sellerId;

    /**
     * 费用类型ID
     */
    private String costTypeId;

    /**
     * 费用类型名称
     */
    private String costTypeName;

    /**
     * 费用字典key
     */
    private String costDictKey;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 模块名称
     */
    private String modelName;

    private String relevantId;

    private Integer delFlag;

    private String ocrData;

    private String goodsName;

    private String goodsWeight;

    private String goodsPrice;

    private String senderName;

    private String customerName;

    private String customerSimpleName;
    private String billMonth;
    /**
     * 发票标识 10 网联 20 汽运
     */
    @TableField(exist = false)
    private Integer type;

    public String getCustomerSimpleName() {
        return customerSimpleName;
    }

    public void setCustomerSimpleName(String customerSimpleName) {
        this.customerSimpleName = customerSimpleName;
    }

    public String getBillMonth() {
        return billMonth;
    }

    public void setBillMonth(String billMonth) {
        this.billMonth = billMonth;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getGoodsPrice() {
        return goodsPrice;
    }

    public void setGoodsPrice(String goodsPrice) {
        this.goodsPrice = goodsPrice;
    }

    public String getGoodsWeight() {
        return goodsWeight;
    }

    public void setGoodsWeight(String goodsWeight) {
        this.goodsWeight = goodsWeight;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    public String getOcrData() {
        return ocrData;
    }

    public void setOcrData(String ocrData) {
        this.ocrData = ocrData;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }
    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }
    public String getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(String invoiceDate) {
        this.invoiceDate = invoiceDate;
    }
    public BigDecimal getInvoicePrice() {
        return invoicePrice;
    }

    public void setInvoicePrice(BigDecimal invoicePrice) {
        this.invoicePrice = invoicePrice;
    }
    public BigDecimal getInvoicePriceTax() {
        return invoicePriceTax;
    }

    public void setInvoicePriceTax(BigDecimal invoicePriceTax) {
        this.invoicePriceTax = invoicePriceTax;
    }
    public BigDecimal getInvoicePriceTaxSum() {
        return invoicePriceTaxSum;
    }

    public void setInvoicePriceTaxSum(BigDecimal invoicePriceTaxSum) {
        this.invoicePriceTaxSum = invoicePriceTaxSum;
    }
    public String getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(String invoiceTax) {
        this.invoiceTax = invoiceTax;
    }
    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }
    public String getBuyerTaxpayerNo() {
        return buyerTaxpayerNo;
    }

    public void setBuyerTaxpayerNo(String buyerTaxpayerNo) {
        this.buyerTaxpayerNo = buyerTaxpayerNo;
    }
    public String getBuyerAddressTel() {
        return buyerAddressTel;
    }

    public void setBuyerAddressTel(String buyerAddressTel) {
        this.buyerAddressTel = buyerAddressTel;
    }
    public String getBuyerAccount() {
        return buyerAccount;
    }

    public void setBuyerAccount(String buyerAccount) {
        this.buyerAccount = buyerAccount;
    }
    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }
    public String getSellerTaxpayerNo() {
        return sellerTaxpayerNo;
    }

    public void setSellerTaxpayerNo(String sellerTaxpayerNo) {
        this.sellerTaxpayerNo = sellerTaxpayerNo;
    }
    public String getSellerAddressTel() {
        return sellerAddressTel;
    }

    public void setSellerAddressTel(String sellerAddressTel) {
        this.sellerAddressTel = sellerAddressTel;
    }
    public String getSellerAccount() {
        return sellerAccount;
    }

    public void setSellerAccount(String sellerAccount) {
        this.sellerAccount = sellerAccount;
    }
    public String getOriginFile() {
        return originFile;
    }

    public void setOriginFile(String originFile) {
        this.originFile = originFile;
    }
    public String getInvoicePayee() {
        return invoicePayee;
    }

    public void setInvoicePayee(String invoicePayee) {
        this.invoicePayee = invoicePayee;
    }
    public String getInvoiceDrawer() {
        return invoiceDrawer;
    }

    public void setInvoiceDrawer(String invoiceDrawer) {
        this.invoiceDrawer = invoiceDrawer;
    }
    public String getInvoiceReview() {
        return invoiceReview;
    }

    public void setInvoiceReview(String invoiceReview) {
        this.invoiceReview = invoiceReview;
    }
    public String getInvoiceRemark() {
        return invoiceRemark;
    }

    public void setInvoiceRemark(String invoiceRemark) {
        this.invoiceRemark = invoiceRemark;
    }
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
    public String getRecordAccountMonth() {
        return recordAccountMonth;
    }

    public void setRecordAccountMonth(String recordAccountMonth) {
        this.recordAccountMonth = recordAccountMonth;
    }
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getShipId() {
        return shipId;
    }

    public void setShipId(String shipId) {
        this.shipId = shipId;
    }
    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }
    public String getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(String buyerId) {
        this.buyerId = buyerId;
    }
    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }
    public String getCostTypeId() {
        return costTypeId;
    }

    public void setCostTypeId(String costTypeId) {
        this.costTypeId = costTypeId;
    }
    public String getCostTypeName() {
        return costTypeName;
    }

    public void setCostTypeName(String costTypeName) {
        this.costTypeName = costTypeName;
    }
    public String getCostDictKey() {
        return costDictKey;
    }

    public void setCostDictKey(String costDictKey) {
        this.costDictKey = costDictKey;
    }
    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }
    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getRelevantId() {
        return relevantId;
    }

    public void setRelevantId(String relevantId) {
        this.relevantId = relevantId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "InvoiceOcrSales{" +
            "id=" + id +
            ", invoiceNo=" + invoiceNo +
            ", invoiceCode=" + invoiceCode +
            ", invoiceDate=" + invoiceDate +
            ", invoicePrice=" + invoicePrice +
            ", invoicePriceTax=" + invoicePriceTax +
            ", invoicePriceTaxSum=" + invoicePriceTaxSum +
            ", invoiceTax=" + invoiceTax +
            ", buyerName=" + buyerName +
            ", buyerTaxpayerNo=" + buyerTaxpayerNo +
            ", buyerAddressTel=" + buyerAddressTel +
            ", buyerAccount=" + buyerAccount +
            ", sellerName=" + sellerName +
            ", sellerTaxpayerNo=" + sellerTaxpayerNo +
            ", sellerAddressTel=" + sellerAddressTel +
            ", sellerAccount=" + sellerAccount +
            ", originFile=" + originFile +
            ", invoicePayee=" + invoicePayee +
            ", invoiceDrawer=" + invoiceDrawer +
            ", invoiceReview=" + invoiceReview +
            ", invoiceRemark=" + invoiceRemark +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            ", recordAccountMonth=" + recordAccountMonth +
            ", status=" + status +
            ", shipId=" + shipId +
            ", shipName=" + shipName +
            ", buyerId=" + buyerId +
            ", sellerId=" + sellerId +
            ", costTypeId=" + costTypeId +
            ", costTypeName=" + costTypeName +
            ", costDictKey=" + costDictKey +
            ", serviceName=" + serviceName +
            ", modelName=" + modelName +
        "}";
    }
}
