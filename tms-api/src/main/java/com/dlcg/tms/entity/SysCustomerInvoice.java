package com.dlcg.tms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-08
 */
@TableName("sys_customer_invoice")
public class SysCustomerInvoice implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 客户类型
     */
    private Integer type;

    /**
     * 客户类型编码
     */
    private String code1;

    /**
     * 客户编码
     */
    private String code2;

    /**
     * 客户全称（2级客户）
     */
    private String customerSecondlevelId;

    /**
     * 登记日期
     */
    private Date registrationDate;

    /**
     * 货种
     */
    private String goodsType;

    /**
     * 合同名头（发票名头）
     */
    private String contractTitle;

    /**
     * 纳税识别号
     */
    private String taxIdentificationNumber;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 注册电话
     */
    private String registeredPhone;

    /**
     * 账户
     */
    private String account;

    /**
     * 开户行
     */
    private String openingBank;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    /**
     * 备注
     */
    private String remark;

    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
    public String getCode1() {
        return code1;
    }

    public void setCode1(String code1) {
        this.code1 = code1;
    }
    public String getCode2() {
        return code2;
    }

    public void setCode2(String code2) {
        this.code2 = code2;
    }
    public String getCustomerSecondlevelId() {
        return customerSecondlevelId;
    }

    public void setCustomerSecondlevelId(String customerSecondlevelId) {
        this.customerSecondlevelId = customerSecondlevelId;
    }
    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }
    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }
    public String getContractTitle() {
        return contractTitle;
    }

    public void setContractTitle(String contractTitle) {
        this.contractTitle = contractTitle;
    }
    public String getTaxIdentificationNumber() {
        return taxIdentificationNumber;
    }

    public void setTaxIdentificationNumber(String taxIdentificationNumber) {
        this.taxIdentificationNumber = taxIdentificationNumber;
    }
    public String getRegisteredAddress() {
        return registeredAddress;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }
    public String getRegisteredPhone() {
        return registeredPhone;
    }

    public void setRegisteredPhone(String registeredPhone) {
        this.registeredPhone = registeredPhone;
    }
    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }
    public String getOpeningBank() {
        return openingBank;
    }

    public void setOpeningBank(String openingBank) {
        this.openingBank = openingBank;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "SysCustomerInvoice{" +
            "id=" + id +
            ", type=" + type +
            ", code1=" + code1 +
            ", code2=" + code2 +
            ", customerSecondlevelId=" + customerSecondlevelId +
            ", registrationDate=" + registrationDate +
            ", goodsType=" + goodsType +
            ", contractTitle=" + contractTitle +
            ", taxIdentificationNumber=" + taxIdentificationNumber +
            ", registeredAddress=" + registeredAddress +
            ", registeredPhone=" + registeredPhone +
            ", account=" + account +
            ", openingBank=" + openingBank +
            ", createBy=" + createBy +
            ", createDate=" + createDate +
            ", updateBy=" + updateBy +
            ", updateDate=" + updateDate +
            ", remark=" + remark +
            ", delFlag=" + delFlag +
        "}";
    }
}
