package com.dlcg.tms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-02
 */
@TableName("shipping_demand_histroy")
public class ShippingDemandHistroy implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 需求时间
     */
    private String needTime;

    /**
     * 船名
     */
    private String shipName;

    /**
     * 货种
     */
    private String goodsType;

    /**
     * 货量
     */
    private String tonnage;

    /**
     * 船载重
     */
    private String shipTonnage;

    /**
     * 起始港
     */
    private String startPort;

    /**
     * 目的港
     */
    private String endPort;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    private String delFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 顺序
     */
    private Integer shunxu;

    private String shippingDemandId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getNeedTime() {
        return needTime;
    }

    public void setNeedTime(String needTime) {
        this.needTime = needTime;
    }
    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }
    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }
    public String getTonnage() {
        return tonnage;
    }

    public void setTonnage(String tonnage) {
        this.tonnage = tonnage;
    }
    public String getShipTonnage() {
        return shipTonnage;
    }

    public void setShipTonnage(String shipTonnage) {
        this.shipTonnage = shipTonnage;
    }
    public String getStartPort() {
        return startPort;
    }

    public void setStartPort(String startPort) {
        this.startPort = startPort;
    }
    public String getEndPort() {
        return endPort;
    }

    public void setEndPort(String endPort) {
        this.endPort = endPort;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public Integer getShunxu() {
        return shunxu;
    }

    public void setShunxu(Integer shunxu) {
        this.shunxu = shunxu;
    }
    public String getShippingDemandId() {
        return shippingDemandId;
    }

    public void setShippingDemandId(String shippingDemandId) {
        this.shippingDemandId = shippingDemandId;
    }

    @Override
    public String toString() {
        return "ShippingDemandHistroy{" +
            "id=" + id +
            ", needTime=" + needTime +
            ", shipName=" + shipName +
            ", goodsType=" + goodsType +
            ", tonnage=" + tonnage +
            ", shipTonnage=" + shipTonnage +
            ", startPort=" + startPort +
            ", endPort=" + endPort +
            ", createBy=" + createBy +
            ", createDate=" + createDate +
            ", updateBy=" + updateBy +
            ", updateDate=" + updateDate +
            ", delFlag=" + delFlag +
            ", remark=" + remark +
            ", shunxu=" + shunxu +
            ", shippingDemandId=" + shippingDemandId +
        "}";
    }
}
