package com.dlcg.tms.entity;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class CustomerGoodsCostContractVo extends CustomerGoodsCostContract{
    private String customerGoodsCostIds;
    private String customerGoodsCostDetailIds;
    private String contractTypeName;
    private String modelTypeName;
    // 船期id
    private String shipLineId;
    // 船名
    private String shipName;
    // 离港日期
    private String departureDate;

    private String createUserName;

    private String expiredTimeStr;


    private List<CustomerGoodsCostVo> customerGoodsCostVoList=new ArrayList<>();
}
