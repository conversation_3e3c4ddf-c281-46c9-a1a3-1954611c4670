-- auto Generated on 2020-09-21 17:31:25 
-- DROP TABLE IF EXISTS `standing_book`; 
CREATE TABLE standing_book(
    `id` VARCHAR(64)  NOT NULL COMMENT 'id',
    `cgc_id` VARCHAR(64) DEFAULT NULL COMMENT '流向ID',
    `shipline_id` VARCHAR(64) DEFAULT NULL COMMENT '船期ID',
    `month` VARCHAR(16) DEFAULT NULL COMMENT '月份',
    `out_date` DATETIME DEFAULT NULL COMMENT '出单日期',
    `departure_airport` DATETIME DEFAULT NULL COMMENT '离港日期',
    `voyage_no` VARCHAR(16) DEFAULT NULL COMMENT '航次号',
    `ship_name` VARCHAR(16) DEFAULT NULL COMMENT '船名',
    `shipowner_name` VARCHAR(16) DEFAULT NULL COMMENT '船东名称',
    `charterers` VARCHAR(16) DEFAULT NULL COMMENT '租船人',
    `shipowner_type` VARCHAR(16) DEFAULT NULL COMMENT '船东类型',
    `business_user` VARCHAR(16) DEFAULT NULL COMMENT '业务员',
    `goods_source` VARCHAR(16) DEFAULT NULL COMMENT '货源',
    `plan_collection` DATETIME DEFAULT NULL COMMENT '计划收款',
    `plan_payment` DATETIME DEFAULT NULL COMMENT '计划付款',
    `loading_dock` VARCHAR(16) DEFAULT NULL COMMENT '装货码头',
    `un_loading_dock` VARCHAR(16) DEFAULT NULL COMMENT '卸货码头',
    `start_port` VARCHAR(16) DEFAULT NULL COMMENT '起运港',
    `target_port` VARCHAR(16) DEFAULT NULL COMMENT '目的港',
    `ship_route` VARCHAR(16) DEFAULT NULL COMMENT '航线',
    `customer` VARCHAR(16) DEFAULT NULL COMMENT '客户',
    `customer_detail` VARCHAR(16) DEFAULT NULL COMMENT '客户明细',
    `varieties` VARCHAR(16) DEFAULT NULL COMMENT '品种',
    `subject` VARCHAR(16) DEFAULT NULL COMMENT '科目',
    `tonner` VARCHAR(16) DEFAULT NULL COMMENT '吨级',
    `weight` DECIMAL(12,2) DEFAULT NULL COMMENT '重量',
    `freight` DECIMAL(12,2) DEFAULT NULL COMMENT '运价',
    `income` DECIMAL(12,2) DEFAULT NULL COMMENT '收入金额',
    `invoice` VARCHAR(16) DEFAULT NULL COMMENT '发票',
    `contract` VARCHAR(16) DEFAULT NULL COMMENT '合同',
    `spending` DECIMAL(12,2) DEFAULT NULL COMMENT '支出',
    `spending_amount` DECIMAL(12,2) DEFAULT NULL COMMENT '支出金额',
    `spending_invoice` VARCHAR(16) DEFAULT NULL COMMENT '支出发票',
    `spending_company` VARCHAR(16) DEFAULT NULL COMMENT '支出公司',
    `ticket_price` DECIMAL(12,2) DEFAULT NULL COMMENT '买票价',
    `print_invoice_company` VARCHAR(16) DEFAULT NULL COMMENT '开票公司',
    `mat_money_company_ticket` VARCHAR(16) DEFAULT NULL COMMENT '垫资公司买票',
    `un_mat_money_company` VARCHAR(16) DEFAULT NULL COMMENT '未垫资公司',
    `cost_unit_price` DECIMAL(12,2) DEFAULT NULL COMMENT '成本单价',
    `cost_price` DECIMAL(12,2) DEFAULT NULL COMMENT '成本金额',
    `profits` DECIMAL(12,2) DEFAULT NULL COMMENT '利润',
    `goods_type` VARCHAR(16) DEFAULT NULL COMMENT '货类',
    `print_invoice_amount` DECIMAL(12,2) DEFAULT NULL COMMENT '开票金额',
    `received_amount` DECIMAL(12,2) DEFAULT NULL COMMENT '已收金额',
    `balance` DECIMAL(12,2) DEFAULT NULL COMMENT '余额',
    `collection_date` DATETIME DEFAULT NULL COMMENT '收款日期',
    `submit_date` DATETIME DEFAULT NULL COMMENT '提交日期',
    `contract_no` VARCHAR(16) DEFAULT NULL COMMENT '合同号',
    `bill_date` DECIMAL(12,2) DEFAULT NULL COMMENT '账期',
    `bill_age` VARCHAR(16) DEFAULT NULL COMMENT '账龄',
    `plan_receivable_week` INTEGER(11) DEFAULT NULL COMMENT '计划回款周',
    `actual_money_take_up_day` INTEGER(11) DEFAULT NULL COMMENT '实际资金占用天数',

    `create_time` DATETIME  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT 'standing_book';
