package com.dlcg.tms.entity;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Data
public class CustomerGoodsCostVo {
//    cgc.id,
//    s. `name`,
//    sl.eparture_time_date,
//    sl.ship_time,
//    sl.voyage_number,
//    sl.on_account_num,
//    sco. `name` as onename,
//    scs. `name`,
//    cgc.tonnage,
//    cgc.freight_no,
//    cgc.tax_freight,
//    cgc.daili_no,
//    cgc.tax_daili,
//    cgc.goods_cost_contract_id
    private String customerGoodsCostId;
    private String customerGoodsDetailId;
    private String shipName;
    private String shipId;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date departureTimeDate;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date shipTime;
    private String voyageNumber;
    private String onAccountNum;
    private String oneName; // 一级客户
    private String oneId; // 一级客户id
    private String twoName; // 二级客户
    private String twoId; // 二级客户id
    private Double tonnage; // 运量
    private BigDecimal freightNo; // 运价
    private Integer taxFreight; // 运价税率
    private String taxFreightStr; // 运价税率
    private BigDecimal dailiNo; // 代理费
    private Integer taxDaili; // 代理费税率
    private String taxDailiStr; // 代理费税率

    private BigDecimal priceNo; // 货物费用
    private Integer taxPrice; // 货物费用税率
    private String taxPriceStr; // 货物费用税率
    private BigDecimal priceTotal; // 货物费用合计

    private String abbName; // 供应商
    private String abbId; // 供应商id

    private String typeName; // 费用类型
    private String typeCode; // 费用类型code

    private Integer customerSupplier; // 0 供应商 1 客户

    private String goodsCostContractId; // 合同id
    // 合同附件url
    private String goodsCostContractUrls; // 合同附件url
    private String goodsCostContractMarkUrls; // 合同附件url
    private String goodsCostContractUrlNames; // 合同附件urlName

    // 签章附件
    private String goodsCostContractSignedUrls; // 合同附件url
    private String goodsCostContractSignedUrlNames; // 合同附件urlName

    private String danZhengUserId; // 单证人员id
    private String danZhengUserName; // 单证人员

    private String contractType; // 合同类型 货物收入合同 goodsCost 货物费用（支出）合同 goodsCostOut
    private String contractTypeName; // 合同类型 货物收入合同 goodsCost 货物费用（支出）合同 goodsCostOut
    private String contractModelType; // 合同模板类型 单航次 single 长协议 long
    private String contractModelTypeName; // 合同模板类型 单航次 single 长协议 long
    private String contractName; // 合同编号
    private String sectionModelType; // 标段类型

    private String contractRemark; // 合同备注
    private String contractExpiredTime; // 合同到期时间
    // 合同审批状态
    private String contractSpStatus; // 合同状态
    // 合同审批状态
    private String contractSpStatusName; // 合同状态
    // 长协合同
    private String supGoodsCostContractId; // 长协合同id
    //标段合同
    private String sectionGoodsCostContractId; //标段合同id

    private String waterMarkFileUrl; // 水印

    private String businessType;
    private String spStatus; //审核状态

    private List<AttachInfoBean> urlInfos=new ArrayList<>(); // 附件信息

    private List<AttachInfoBean> signedUrlInfos=new ArrayList<>(); // 已签名附件信息

    public static CustomerGoodsCostVo byCustomerGoodsCostContract(CustomerGoodsCostContract contract){
        CustomerGoodsCostVo vo=new CustomerGoodsCostVo();
        BeanUtil.copyProperties(contract,vo);
        vo.setGoodsCostContractId(contract.getId()+"");
        vo.setContractName(contract.getContractName());
        vo.setContractRemark(contract.getContractRemark());
        vo.setContractExpiredTime(DateUtil.format(contract.getExpiredTime(),"yyyy-MM-dd"));
        vo.setContractSpStatus(contract.getSpStatus());
        vo.setOneId(contract.getOthId());
        vo.setOneName(contract.getOthName());
        // 合同类型 货物收入合同 goodsCost 货物费用（支出）合同 goodsCostOut
//        if(cgcc.sp_status=1,'审批中',if(cgcc.sp_status=2 or cgcc.sp_status=5,'审批通过','未审批')) as contractSpStatusName,
        if(ObjectUtil.equal(contract.getSpStatus(),1)){
            vo.setContractSpStatusName("审批中");
        }else if(ObjectUtil.equal(contract.getSpStatus(),2) || ObjectUtil.equal(contract.getSpStatus(),5)){
            vo.setContractSpStatusName("审批通过");
        }else {
            vo.setContractSpStatusName("未审批");
        }
        vo.setContractType(contract.getContractType());
//        vo.setContractTypeName(contract.getContractTypeName());
//        if(cgcc.contract_type='goodsCost','货物合同',if(cgcc.contract_type='goodsCostOut','货物费用合同','')) as contractTypeName,
        if(ObjectUtil.equal(contract.getContractType(),"goodsCost")){
            vo.setContractTypeName("货物合同");
            vo.setTwoId(contract.getJiafangId());
            vo.setTwoName(contract.getJiafang());
        }else if(ObjectUtil.equal(contract.getContractType(),"goodsCostOut")){
            vo.setContractTypeName("货物费用合同");
            vo.setTwoId(contract.getYifangId());
            vo.setTwoName(contract.getYifang());
        }else if(ObjectUtil.equal(contract.getContractType(),"shipCost")){
            vo.setContractTypeName("船上收入合同");
            vo.setTwoId(contract.getJiafangId());
            vo.setTwoName(contract.getJiafang());
        }else if(ObjectUtil.equal(contract.getContractType(),"shipCostOut")){
            vo.setContractTypeName("船上支出合同");
            vo.setTwoId(contract.getYifangId());
            vo.setTwoName(contract.getYifang());
        }
        else{
            vo.setContractTypeName("");
            vo.setTwoId(contract.getJiafangId());
            vo.setTwoName(contract.getJiafang());
        }
        vo.setContractModelType(contract.getModelType());
//        if(cgcc.model_type='long','长协',if(cgcc.model_type='single','单航次','')) as contractModelTypeName,
        if(ObjectUtil.equal(contract.getModelType(),"long")){
            vo.setContractModelTypeName("长协");
        }else if(ObjectUtil.equal(contract.getModelType(),"single")){
            vo.setContractModelTypeName("单航次");
        }else if(ObjectUtil.equal(contract.getModelType(),"section")){
            vo.setContractModelTypeName("标段");
            if(ObjectUtil.equal(contract.getSectionModelType(),"section_zhaobiao")){
                vo.setContractModelTypeName("标段(招标货)");
            }
            if(ObjectUtil.equal(contract.getSectionModelType(),"section_nozhaobiao")){
                vo.setContractModelTypeName("标段(非招标货)");
            }
        }else{
            vo.setContractModelTypeName("");
        }
//        goodsCostContractUrlNames
//                goodsCostContractUrls
        vo.setGoodsCostContractUrls(contract.getUrl());
        vo.setGoodsCostContractUrlNames(contract.getUrlName());
        vo.setGoodsCostContractSignedUrls(contract.getSignedUrl());
        vo.setGoodsCostContractSignedUrlNames(contract.getSignedUrlName());
        if(StrUtil.isNotBlank(contract.getUrl())){
//            vo.getUrlInfos().addAll(AttachInfoBean.byUrls(Arrays.asList(contract.getUrl().split(",")),Arrays.asList(contract.getUrlName().split(","))));
            // contract.getUrlName() = null
                vo.getUrlInfos().addAll(AttachInfoBean.byUrls(Arrays.asList(contract.getUrl().split(",")),StrUtil.isNotBlank(contract.getUrlName())?Arrays.asList(contract.getUrlName().split(",")):null));


        }
        if(StrUtil.isNotBlank(contract.getSignedUrl())){
            vo.getSignedUrlInfos().addAll(AttachInfoBean.byUrls(Arrays.asList(contract.getSignedUrl().split(",")),StrUtil.isNotBlank(contract.getSignedUrlName())?Arrays.asList(contract.getSignedUrlName().split(",")):null));
        }
        return vo;
    }
}
