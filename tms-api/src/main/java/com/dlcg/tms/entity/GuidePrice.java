package com.dlcg.tms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-16
 */
@TableName("guide_price")
public class GuidePrice implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 航线
     */
    private String shipline;

    /**
     * 吨位
     */
    private String tonnage;

    /**
     * 租船价格
     */
    private String chartering;

    /**
     * 接货价格
     */
    private String transport;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    private String delFlag;

    /**
     * 备注
     */
    private String remark;

    private Integer shunxu;

    public Integer getShunxu() {
        return shunxu;
    }

    public void setShunxu(Integer shunxu) {
        this.shunxu = shunxu;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getShipline() {
        return shipline;
    }

    public void setShipline(String shipline) {
        this.shipline = shipline;
    }
    public String getTonnage() {
        return tonnage;
    }

    public void setTonnage(String tonnage) {
        this.tonnage = tonnage;
    }
    public String getChartering() {
        return chartering;
    }

    public void setChartering(String chartering) {
        this.chartering = chartering;
    }
    public String getTransport() {
        return transport;
    }

    public void setTransport(String transport) {
        this.transport = transport;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "GuidePrice{" +
            "id=" + id +
            ", shipline=" + shipline +
            ", tonnage=" + tonnage +
            ", chartering=" + chartering +
            ", transport=" + transport +
            ", createBy=" + createBy +
            ", createDate=" + createDate +
            ", updateBy=" + updateBy +
            ", updateDate=" + updateDate +
            ", delFlag=" + delFlag +
            ", remark=" + remark +
        "}";
    }
}
