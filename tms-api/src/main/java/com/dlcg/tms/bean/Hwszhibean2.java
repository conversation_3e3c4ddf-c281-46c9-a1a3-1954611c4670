package com.dlcg.tms.bean;

import java.math.BigDecimal;
import java.util.Date;

public class Hwszhibean2 {
    private String id;

    /**
     * 货源费用id
     */
    private String customerGoodsCostId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 费用名称
     */
    private String costName;
    private Integer incomeOut;

    public Integer getIncomeOut() {
        return incomeOut;
    }

    public void setIncomeOut(Integer incomeOut) {
        this.incomeOut = incomeOut;
    }

    private Integer invoice;

    public Integer getInvoice() {
        return invoice;
    }

    public void setInvoice(Integer invoice) {
        this.invoice = invoice;
    }

    /**
     * 价格
     */
    private BigDecimal price;

    public BigDecimal getPriceNo() {
        return priceNo;
    }

    public void setPriceNo(BigDecimal priceNo) {
        this.priceNo = priceNo;
    }

    /**
     * 价格不含税
     */
    private BigDecimal priceNo;

    /**
     * 发票类型
     */
    private String invoiceType;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    /**
     * 备注
     */
    private String remark;

    private String delFlag;

    private String gongsiname;

    private String leixing;

    private String tax;

    private String costname;

    private Integer shenpi;

    private Integer customerSupplier;

    private BigDecimal totalPriceNo;

    private String payer;

    public String getPayer() {
        return payer;
    }

    public void setPayer(String payer) {
        this.payer = payer;
    }

    public BigDecimal getTotalPriceNo() {
        return totalPriceNo;
    }

    public void setTotalPriceNo(BigDecimal totalPriceNo) {
        this.totalPriceNo = totalPriceNo;
    }

    public Integer getCustomerSupplier() {
        return customerSupplier;
    }

    public void setCustomerSupplier(Integer customerSupplier) {
        this.customerSupplier = customerSupplier;
    }

    public Integer getShenpi() {
        return shenpi;
    }

    public void setShenpi(Integer shenpi) {
        this.shenpi = shenpi;
    }

    public String getTax() {
        return tax;
    }

    public void setTax(String tax) {
        this.tax = tax;
    }

    public String getCostname() {
        return costname;
    }

    public void setCostname(String costname) {
        this.costname = costname;
    }

    public String getGongsiname() {
        return gongsiname;
    }

    public void setGongsiname(String gongsiname) {
        this.gongsiname = gongsiname;
    }

    public String getLeixing() {
        return leixing;
    }

    public void setLeixing(String leixing) {
        this.leixing = leixing;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    public String getCustomerGoodsCostId() {
        return customerGoodsCostId;
    }

    public void setCustomerGoodsCostId(String customerGoodsCostId) {
        this.customerGoodsCostId = customerGoodsCostId;
    }
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
    public String getCostName() {
        return costName;
    }

    public void setCostName(String costName) {
        this.costName = costName;
    }
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }
    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

}
