package com.dlcg.tms.bean;

import com.dlcg.tms.entity.CustomerGoods;
import com.dlcg.tms.entity.ShipLine;
import com.dlcg.tms.entity.Stowage;

import java.util.List;

public class StowageBean2 extends Stowage {
    
    private List<CustomerGoods> customerGoods;
    private String cusNameStr;
    private Integer isShow;
    private ShipLine shipLine;
    
    public List<CustomerGoods> getCustomerGoods() {
        return customerGoods;
    }
    
    public void setCustomerGoods(List<CustomerGoods> customerGoods) {
        this.customerGoods = customerGoods;
    }
    
    public String getCusNameStr() {
        return cusNameStr;
    }
    
    public void setCusNameStr(String cusNameStr) {
        this.cusNameStr = cusNameStr;
    }
    
    public Integer getIsShow() {
        return isShow;
    }
    
    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }
    
    public ShipLine getShipLine() {
        return shipLine;
    }
    
    public void setShipLine(ShipLine shipLine) {
        this.shipLine = shipLine;
    }
}
