package com.dlcg.tms.bean;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class InvoiceBean {
    private String costAggregationId;
    private String shipLineId;
    private String customerGoodsSourceId;
    private String customerGoodsCostId;
    private String customerGoodsConfirmId;
    private String cusSupName;

    private Date startTime;
    private String shipName;
    private String voyageNumber;
    private Integer cType;
    private String goodsType;
    private String costTypeName;
    
    private Integer lighter;

    private String businessUser;
    private String businessUserName;

    private String oneLevelName;
    private Integer businessType;

    public String getCusSupName() {
        return cusSupName;
    }
    
    public void setCusSupName(String cusSupName) {
        this.cusSupName = cusSupName;
    }
    
    public Integer getLighter() {
        return lighter;
    }
    
    public void setLighter(Integer lighter) {
        this.lighter = lighter;
    }
    
    public String getCostTypeName() {
        return costTypeName;
    }
    
    public void setCostTypeName(String costTypeName) {
        this.costTypeName = costTypeName;
    }
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date epartureTimeDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date shipIntoTime;

    private Double totalPrice;  // 总应收/应付

    private Double surplus;//剩余未付款
    private Double payable;//应付款：正数代表收票未付，负数代表欠票已付

    private Double sumSurplus;//累计杂费剩余未付款
    private Double sumPayable;//累计杂费应付款：正数代表收票未付，负数代表欠票已付


    private Double tonnage;
    private Double invoicePrice;//剩余开票总额
    private Double shipPrice;//船价；注：当前船期对应多条船价时，应取customer_goods_confirm表中当前客户流向所对应的船价
    private String startPort;//起始港id
    private String endPort;//目的港口id
    private String startPortName;//起始港口名称
    private String endPortName;//目的港口名称


    private Double shipPriceNoExcludingTax;//不含税船价，注：这个字段在用户录入船价时未录入，需要开发船价录入时录入该价格
    private Double taxAmount;//税费金额，计算得出||手工录入？
    private Double amountIncludingTax;//含税金额，计算得出||手工录入？

    private Double amountUnderApproval; // 审批中金额

    private String endLocal;

    private Double money;

    private String withTax;//含税单价
    private Double withoutTax;//不含税单价
    private Double unitWithTax;
    private String billTax;

    private String costName;

    private Double receivable;

    private Boolean checked;
    
    private String costTypeNameNew;
    
    private Integer isApplying;
//    受票方
    private String receiveBillCompany;

    private String goodsCostContractUrl; //合同地址
    private Integer goodsCostContractId; //合同地址

    private String supGoodsCostContractId; // 长协合同id

    public Integer getIsApplying() {
        return isApplying;
    }
    
    public void setIsApplying(Integer isApplying) {
        this.isApplying = isApplying;
    }
    
    public Double getUnitWithTax() {
        return unitWithTax;
    }
    
    public void setUnitWithTax(Double unitWithTax) {
        this.unitWithTax = unitWithTax;
    }
    
    public String getCostTypeNameNew() {
        return costTypeNameNew;
    }
    
    public void setCostTypeNameNew(String costTypeNameNew) {
        this.costTypeNameNew = costTypeNameNew;
    }
    
    public void setChecked(Boolean checked) {
        this.checked = checked;
    }

    public String getCostName() {
        return costName;
    }

    public void setCostName(String costName) {
        this.costName = costName;
    }

    public Double getReceivable() {
        return receivable;
    }

    public void setReceivable(Double receivable) {
        this.receivable = receivable;
    }

    public Boolean getChecked() {
        return checked;
    }

    public Double getMoney() {
        return money;
    }

    public void setMoney(Double money) {
        this.money = money;
    }

    public String getCostAggregationId() {
        return costAggregationId;
    }

    public void setCostAggregationId(String costAggregationId) {
        this.costAggregationId = costAggregationId;
    }

    public String getBillTax() {
        return billTax;
    }

    public void setBillTax(String billTax) {
        this.billTax = billTax;
    }
    
    public String getWithTax() {
        return withTax;
    }
    
    public void setWithTax(String withTax) {
        this.withTax = withTax;
    }
    
    public Double getWithoutTax() {
        return withoutTax;
    }

    public void setWithoutTax(Double withoutTax) {
        this.withoutTax = withoutTax;
    }

    public String getEndLocal() {
        return endLocal;
    }

    public void setEndLocal(String endLocal) {
        this.endLocal = endLocal;
    }

    public Date getEpartureTimeDate() {
        return epartureTimeDate;
    }

    public void setEpartureTimeDate(Date epartureTimeDate) {
        this.epartureTimeDate = epartureTimeDate;
    }

    public String getCustomerGoodsSourceId() {
        return customerGoodsSourceId;
    }

    public void setCustomerGoodsSourceId(String customerGoodsSourceId) {
        this.customerGoodsSourceId = customerGoodsSourceId;
    }

    public Double getShipPrice() {
        return shipPrice;
    }

    public void setShipPrice(Double shipPrice) {
        this.shipPrice = shipPrice;
    }

    public String getStartPort() {
        return startPort;
    }

    public void setStartPort(String startPort) {
        this.startPort = startPort;
    }

    public String getEndPort() {
        return endPort;
    }

    public void setEndPort(String endPort) {
        this.endPort = endPort;
    }

    public String getStartPortName() {
        return startPortName;
    }

    public void setStartPortName(String startPortName) {
        this.startPortName = startPortName;
    }

    public String getEndPortName() {
        return endPortName;
    }

    public void setEndPortName(String endPortName) {
        this.endPortName = endPortName;
    }

    public Double getShipPriceNoExcludingTax() {
        return shipPriceNoExcludingTax;
    }

    public void setShipPriceNoExcludingTax(Double shipPriceNoExcludingTax) {
        this.shipPriceNoExcludingTax = shipPriceNoExcludingTax;
    }

    public Double getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(Double taxAmount) {
        this.taxAmount = taxAmount;
    }

    public Double getAmountIncludingTax() {
        return amountIncludingTax;
    }

    public void setAmountIncludingTax(Double amountIncludingTax) {
        this.amountIncludingTax = amountIncludingTax;
    }

    public Double getSumSurplus() {
        return sumSurplus;
    }

    public void setSumSurplus(Double sumSurplus) {
        this.sumSurplus = sumSurplus;
    }

    public Double getSumPayable() {
        return sumPayable;
    }

    public void setSumPayable(Double sumPayable) {
        this.sumPayable = sumPayable;
    }

    public String getShipLineId() {
        return shipLineId;
    }

    public void setShipLineId(String shipLineId) {
        this.shipLineId = shipLineId;
    }

    public String getCustomerGoodsConfirmId() {
        return customerGoodsConfirmId;
    }

    public void setCustomerGoodsConfirmId(String customerGoodsConfirmId) {
        this.customerGoodsConfirmId = customerGoodsConfirmId;
    }

    public Double getSurplus() {
        return surplus;
    }

    public void setSurplus(Double surplus) {
        this.surplus = surplus;
    }

    public Double getPayable() {
        return payable;
    }

    public void setPayable(Double payable) {
        this.payable = payable;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getVoyageNumber() {
        return voyageNumber;
    }

    public void setVoyageNumber(String voyageNumber) {
        this.voyageNumber = voyageNumber;
    }

    public Integer getcType() {
        return cType;
    }

    public void setcType(Integer cType) {
        this.cType = cType;
    }

    public String getGoodsType() {
        return goodsType;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public Double getTonnage() {
        return tonnage;
    }

    public void setTonnage(Double tonnage) {
        this.tonnage = tonnage;
    }

    public Double getInvoicePrice() {
        return invoicePrice;
    }

    public void setInvoicePrice(Double invoicePrice) {
        this.invoicePrice = invoicePrice;
    }

    public String getBusinessUser() {
        return businessUser;
    }

    public void setBusinessUser(String businessUser) {
        this.businessUser = businessUser;
    }

    public String getBusinessUserName() {
        return businessUserName;
    }

    public void setBusinessUserName(String businessUserName) {
        this.businessUserName = businessUserName;
    }

    public String getOneLevelName() {
        return oneLevelName;
    }

    public void setOneLevelName(String oneLevelName) {
        this.oneLevelName = oneLevelName;
    }

    public Double getAmountUnderApproval() {
        return amountUnderApproval;
    }

    public void setAmountUnderApproval(Double amountUnderApproval) {
        this.amountUnderApproval = amountUnderApproval;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getReceiveBillCompany() {
        return receiveBillCompany;
    }

    public void setReceiveBillCompany(String receiveBillCompany) {
        this.receiveBillCompany = receiveBillCompany;
    }

    public String getGoodsCostContractUrl() {
        return goodsCostContractUrl;
    }

    public void setGoodsCostContractUrl(String goodsCostContractUrl) {
        this.goodsCostContractUrl = goodsCostContractUrl;
    }

    public Integer getGoodsCostContractId() {
        return goodsCostContractId;
    }

    public void setGoodsCostContractId(Integer goodsCostContractId) {
        this.goodsCostContractId = goodsCostContractId;
    }

    public String getCustomerGoodsCostId() {
        return customerGoodsCostId;
    }

    public void setCustomerGoodsCostId(String customerGoodsCostId) {
        this.customerGoodsCostId = customerGoodsCostId;
    }


    public Date getShipIntoTime() {
        return shipIntoTime;
    }

    public void setShipIntoTime(Date shipIntoTime) {
        this.shipIntoTime = shipIntoTime;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getSupGoodsCostContractId() {
        return supGoodsCostContractId;
    }

    public void setSupGoodsCostContractId(String supGoodsCostContractId) {
        this.supGoodsCostContractId = supGoodsCostContractId;
    }
}
