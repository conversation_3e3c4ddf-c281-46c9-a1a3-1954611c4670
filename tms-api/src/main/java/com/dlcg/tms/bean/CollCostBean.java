package com.dlcg.tms.bean;

import java.math.BigDecimal;

public class CollCostBean {

    private String costAggId;
    // 类型
    private String type;
    // 单价
    private BigDecimal price;
    // 税率
    private Integer taxRate;

    private String taxRateStr;

    private boolean customizeIsInfo=false; // 是否自定义消息

    public String getCostAggId() {
        return costAggId;
    }

    public void setCostAggId(String costAggId) {
        this.costAggId = costAggId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(Integer taxRate) {
        this.taxRate = taxRate;
    }

    public String getTaxRateStr() {
        return taxRateStr;
    }

    public void setTaxRateStr(String taxRateStr) {
        this.taxRateStr = taxRateStr;
    }

    public boolean getCustomizeIsInfo() {
        return customizeIsInfo;
    }

    public void setCustomizeIsInfo(boolean customizeIsInfo) {
        this.customizeIsInfo = customizeIsInfo;
    }
}
