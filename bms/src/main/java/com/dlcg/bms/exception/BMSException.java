package com.dlcg.bms.exception;

import com.dlcg.oa.bean.ExceptionInterface;

public enum BMSException implements ExceptionInterface {
	ERROR_TMP("99999", "服务器异常！请稍后再试")
	;

	private String code;
	private String desc;

	BMSException(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}


	public String getCode() {
		return this.code;
	}



	public String getDesc() {
		return this.desc;
	}


}
