package com.dlcg.bms.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dlcg.bms.base.BaseController;
import com.dlcg.bms.config.dozer.IGenerator;
import com.dlcg.bms.entity.CustomerAccountTopUpOrder;
import com.dlcg.bms.entity.CustomerAccountWater;
import com.dlcg.bms.entity.CustomerAdvancePaymentAccount;
import com.dlcg.bms.enums.*;
import com.dlcg.bms.model.dto.CustomerAdvancePaymentAccountWithCustomerNameDto;
import com.dlcg.bms.model.page.TableDataInfo;
import com.dlcg.bms.model.po.CustomerAdvancePaymentAccountListPo;
import com.dlcg.bms.model.vo.CustomerAccountTopUpOrderVo;
import com.dlcg.bms.model.vo.CustomerAccountWaterVo;
import com.dlcg.bms.service.ICustomerAccountTopUpOrderService;
import com.dlcg.bms.service.ICustomerAccountWaterService;
import com.dlcg.bms.service.ICustomerAdvancePaymentAccountService;
import com.dlcg.mobile.model.ResultBody;
import com.github.pagehelper.PageInfo;
import com.zthzinfo.common.ResponseMapBuilder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 预付款模块
 */
@RequestMapping("/customer-advance-payment-account")
@RestController
public class AdvancePaymentController extends BaseController {

    @Resource
    private ICustomerAdvancePaymentAccountService customerAdvancePaymentAccountService;
    @Resource
    private ICustomerAccountWaterService customerAccountWaterService;
    @Resource
    private ICustomerAccountTopUpOrderService customerAccountTopUpOrderService;
    @Resource
    private IGenerator generator;

    /**
     * 客户账户列表
     * @param customerName
     * @param beforeTime
     * @param afterTime
     * @return
     * @throws ParseException
     */
    @GetMapping("/list")
    public ResultBody list(
            @RequestParam(value = "customerName",required = false) String customerName,
            @RequestParam(value = "shipName",required = false) String shipName,
            @RequestParam(value = "beforeTime",required = false) String beforeTime,
            @RequestParam(value = "afterTime",required = false) String afterTime
    ) throws ParseException {
        startPage();
        CustomerAdvancePaymentAccountListPo po = new CustomerAdvancePaymentAccountListPo();
        if (StrUtil.isNotBlank(customerName)){
            // 客户名称
            po.setCustomerName(customerName);
        }
        if (StrUtil.isNotBlank(shipName)){
            po.setShipName(shipName);
        }
        if (StrUtil.isNotBlank(beforeTime)){
            po.setBeforeTime(beforeTime);
        }
        if (StrUtil.isNotBlank(afterTime)){
           po.setAfterTime(afterTime);
        }
        List<CustomerAdvancePaymentAccountWithCustomerNameDto> list = customerAdvancePaymentAccountService.selectListWithCustomerName(po);
        return ResultBody.ok().data(getDataTable(list));
    }

    /**
     * 预付款订单列表
     */
    @GetMapping("/detail/list")
    public ResultBody detailList(@RequestParam(value = "accountId",required = false) String accountId){
        Assert.notBlank(accountId);
        startPage();
        LambdaQueryWrapper<CustomerAccountTopUpOrder>  queryWrapper = new LambdaQueryWrapper<CustomerAccountTopUpOrder>()
                .eq(CustomerAccountTopUpOrder::getDelFlag, DelFlagEnum.NORMAL.getKey())
                .eq(CustomerAccountTopUpOrder::getAccountId,accountId)
                .orderByDesc(CustomerAccountTopUpOrder::getApprovalFinishTime)
                .orderByDesc(CustomerAccountTopUpOrder::getCreateTime);
        List<CustomerAccountTopUpOrder> list = customerAccountTopUpOrderService.list(queryWrapper);
        List<CustomerAccountTopUpOrderVo> result =  list.stream().map(item -> {
            CustomerAccountTopUpOrderVo vo = generator.convert(item, CustomerAccountTopUpOrderVo.class);
            vo.setApprovalStatusStr(Optional.ofNullable(item.getApprovalStatus())
                    .map(c -> ApprovalStatusEnum.getDescByKey(item.getApprovalStatus()))
                    .orElse(String.valueOf(item.getApprovalStatus())));
            return vo;
        }).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setRows(result);
        rspData.setTotal(new PageInfo(list).getTotal());
        return ResultBody.ok().data(rspData);
    }
    /**
     * 预付款订单列表
     */
    @GetMapping("/advance/list")
    public ResultBody advanceList(@RequestParam(value = "customerId",required = false) String customerId,
                                  @RequestParam(value = "accountId",required = false) String accountId){
        // Assert.notBlank(accountId);
        List<Integer> advanceApprovalStatus = new ArrayList<>();
        advanceApprovalStatus.add(1);
        advanceApprovalStatus.add(2);
        startPage();
        LambdaQueryWrapper<CustomerAccountTopUpOrder>  queryWrapper = new LambdaQueryWrapper<CustomerAccountTopUpOrder>()
                .eq(CustomerAccountTopUpOrder::getDelFlag, DelFlagEnum.NORMAL.getKey())
                .notIn(CustomerAccountTopUpOrder::getAdvanceApprovalStatus,advanceApprovalStatus )
                .orderByDesc(CustomerAccountTopUpOrder::getApprovalFinishTime)
                .orderByDesc(CustomerAccountTopUpOrder::getCreateTime);

        if (StrUtil.isNotBlank(customerId)){
            List<CustomerAdvancePaymentAccount> list = customerAdvancePaymentAccountService.lambdaQuery()
                    .eq(CustomerAdvancePaymentAccount::getCustomerId, customerId)
                    .list();
            if (list == null || list.size() == 0){
                return ResultBody.ok().data(new TableDataInfo());
            }
            queryWrapper.eq(CustomerAccountTopUpOrder::getAccountId,list.get(0).getId());
        }
        if (StrUtil.isNotBlank(accountId)){
            queryWrapper.eq(CustomerAccountTopUpOrder::getAccountId,accountId);
        }
        List<CustomerAccountTopUpOrder> list = customerAccountTopUpOrderService.list(queryWrapper);


        CustomerAdvancePaymentAccountListPo cp = new CustomerAdvancePaymentAccountListPo();
        List<CustomerAdvancePaymentAccountWithCustomerNameDto> accountList = customerAdvancePaymentAccountService.selectListWithCustomerName(cp);
        //accountList  以id为key 生成map
        Map<String,CustomerAdvancePaymentAccountWithCustomerNameDto> accountMap = accountList.stream().collect(Collectors.toMap(CustomerAdvancePaymentAccountWithCustomerNameDto::getId, Function.identity()));
        List<CustomerAccountTopUpOrderVo> result =  list.stream().map(item -> {
            CustomerAccountTopUpOrderVo vo = generator.convert(item, CustomerAccountTopUpOrderVo.class);
            vo.setApprovalStatusStr(Optional.ofNullable(item.getApprovalStatus())
                    .map(c -> ApprovalStatusEnum.getDescByKey(item.getApprovalStatus()))
                    .orElse(String.valueOf(item.getApprovalStatus())));
            vo.setCustomerName(accountMap.get(item.getAccountId()).getCustomerName());
            return vo;
        }).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setRows(result);
        rspData.setTotal(new PageInfo(list).getTotal());
        return ResultBody.ok().data(rspData);
    }

    /**
     * 预付款订单列表
     */
    @PostMapping("/advance/deduct")
    public ResultBody advanceDeduct(@RequestParam(value = "ids",required = false) String ids){

        Assert.notBlank(ids);
        //ids = "1,2,3"; 分隔成集合
        List<String> idList = Arrays.asList(ids.split(","));
        customerAccountTopUpOrderService.lambdaUpdate()
                .in(CustomerAccountTopUpOrder::getId,idList)
                .set(CustomerAccountTopUpOrder::getAdvanceApprovalStatus,1)
                .update();
        return ResultBody.ok();
    }

    /**
     * 流水明细列表
     */
    @GetMapping("/water/list")
    public ResultBody waterList(@RequestParam("accountId") String accountId){
        Assert.notBlank(accountId);
        startPage();
        List<CustomerAccountWater> list = customerAccountWaterService.list(new LambdaQueryWrapper<CustomerAccountWater>()
                .eq(CustomerAccountWater::getDelFlag, DelFlagEnum.NORMAL.getKey())
                .and(item -> item.nested(n -> n.eq(CustomerAccountWater::getSourceAccountId,accountId))
                            .or().nested(n -> n.eq(CustomerAccountWater::getTargetAccountId,accountId)))
                .orderByDesc(CustomerAccountWater::getCreateTime)
        );
        List<CustomerAccountWaterVo> result = list.stream().map(item -> {
            CustomerAccountWaterVo vo = generator.convert(item, CustomerAccountWaterVo.class);
            vo.setBusinessTypeStr(Optional.ofNullable(item.getBusinessType())
                    .map(c -> BusinessTypeEnum.getDescByKey(item.getBusinessType()))
                    .orElse(String.valueOf(item.getBusinessType())));
            vo.setProcessStatusStr(Optional.ofNullable(item.getProcessStatus())
                    .map(c -> ProcessStatusEnum.getDescByKey(item.getProcessStatus()))
                    .orElse(String.valueOf(item.getProcessStatus())));
            vo.setProcessResultStr(Optional.ofNullable(item.getProcessResult())
                    .map(c -> ProcessResultEnum.getDescByKey(item.getProcessResult()))
                    .orElse(String.valueOf(item.getProcessResult())));
            return vo;
        }).collect(Collectors.toList());
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setRows(result);
        rspData.setTotal(new PageInfo(list).getTotal());
        return ResultBody.ok().data(rspData);
    }
    
    /**
     * 查询预付款余额
     */
    @RequestMapping("/queryCharge")
    public Map<String,Object> queryCharge(@RequestParam(value = "sysSupplierId", required = false) String sysSupplierId){
        LambdaQueryWrapper<CustomerAdvancePaymentAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerAdvancePaymentAccount::getCustomerId,sysSupplierId);
        CustomerAdvancePaymentAccount customerAdvancePaymentAccount = customerAdvancePaymentAccountService.getOne(queryWrapper);
        return ResponseMapBuilder.newBuilder()
                .put("account",customerAdvancePaymentAccount)
                .putSuccess()
                .getResult();
    }
    /**
     * 查询是否有预付款账户
     */
    @RequestMapping("/isExistAdvancePaymentAccountByCustomerId")
    public Map<String,Object> queryAdvancePaymentAccount(@RequestParam(value = "customerId", required = false) String customerId){
        Boolean isExist = customerAdvancePaymentAccountService.isCustomerAdvancePaymentAccountExist(customerId);
        return ResponseMapBuilder.newBuilder()
                .put("data",isExist)
                .putSuccess()
                .getResult();
    }
}
