package com.dlcg.bms.service.impl;

import com.dlcg.bms.entity.AdvanceReceiveWater;
import com.dlcg.bms.mapper.AdvanceReceiveWaterMapper;
import com.dlcg.bms.model.vo.AdvanceReceiveWaterProcessVo;
import com.dlcg.bms.model.vo.AdvanceReceiveWaterVerifyVo;
import com.dlcg.bms.service.IAdvanceReceiveWaterService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dlcg.tms.entity.CostAggregation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-26
 */
@Service
public class AdvanceReceiveWaterServiceImpl extends ServiceImpl<AdvanceReceiveWaterMapper, AdvanceReceiveWater> implements IAdvanceReceiveWaterService {
    @Autowired
    AdvanceReceiveWaterMapper advanceReceiveWaterMapper;
    @Override
    public List<AdvanceReceiveWater> getSpStatus(List<String> waterIds) {
        return advanceReceiveWaterMapper.getSpStatus(waterIds);
    }
    
    @Override
    public List<AdvanceReceiveWaterVerifyVo> getVerifyList() {
        return advanceReceiveWaterMapper.getVerifyList();
    }
    
    @Override
    public List<CostAggregation> getCostList(List<String> costIds) {
        return advanceReceiveWaterMapper.getCostList(costIds);
    }
    
    @Override
    public List<AdvanceReceiveWaterVerifyVo> getDeductWater() {
        return advanceReceiveWaterMapper.getDeductWater();
    }
}
